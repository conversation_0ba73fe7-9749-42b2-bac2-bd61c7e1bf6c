<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-07 14:00:23
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-11 14:18:54
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\views\activities\pdd\modules\recharge-list-drawer.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import { ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { getPddRegisterList } from "@/service/api/pdd";
import moment from "moment";
import { formatter } from "element-plus";
import { getBrazilDate } from "@/utils/format";

const { t } = useI18n();

const props = defineProps<{
  visible: boolean;
  activityId?: number;
}>();

const emit = defineEmits<{
  (e: "update:visible", visible: boolean): void;
}>();

const internalVisible = ref(props.visible);
const loading = ref(false);
const data = ref([]);
const pagination = ref({
  total: 0,
  currentPage: 1,
  pageSize: 5,
  "current-change": (page: number) => {
    pagination.value.currentPage = page;
    getData();
  },
  "size-change": (size: number) => {
    pagination.value.pageSize = size;
    pagination.value.currentPage = 1;
    getData();
  },
});

const columns = [
  { type: "index", label: t("common.index"), minWidth: 60 },
  { prop: "invitee_id", label: "被邀请人ID", minWidth: 100 },
  {
    prop: "invitee_register_time",
    label: "被邀请人注册时间",
    minWidth: 160,
    formatter:(row: any) => {
      return moment(getBrazilDate(row.invitee_register_time)).format("YYYY-MM-DD HH:mm:ss");
    },
  },
  { prop: "invitee_username", label: "被邀请人昵称", minWidth: 100 },
  { prop: "inviter_id", label: "邀请人ID", minWidth: 100 },
  {
    prop: "inviter_register_time",
    label: "邀请人注册时间",
    minWidth: 160,
    formatter: (row: any) => {
      return moment(getBrazilDate(row.inviter_register_time)).format("YYYY-MM-DD HH:mm:ss");
    },
  },
  { prop: "inviter_username", label: "邀请人昵称", minWidth: 100 },
];

const getData = async () => {
  if (!props.activityId) return;

  loading.value = true;
  try {
    const { data: response } = await getPddRegisterList(props.activityId, {
      page: pagination.value.currentPage,
      size: pagination.value.pageSize,
    });
    console.log(response?.data);
    if (response?.data) {
      data.value = response.data.result || [];
      pagination.value.total =  pagination.value.total ? pagination.value.total:response.data.count || 0;
    }
  } catch (error) {
    console.error("Failed to fetch register list:", error);
  } finally {
    loading.value = false;
  }
};

watch(
  () => props.visible,
  (newVal) => {
    internalVisible.value = newVal;
    if (newVal && props.activityId) {
      getData();
    } else {
      data.value = [];
      pagination.value.currentPage = 1;
      pagination.value.total = 0;
    }
  },
);

watch(internalVisible, (newVal) => {
  emit("update:visible", newVal);
});

const handleClose = () => {
  internalVisible.value = false;
};
</script>

<template>
  <ElDialog
    v-model="internalVisible"
    title="注册人员列表"
    width="80%"
    @close="handleClose"
  >
    <div class="h-[500px] flex justify-center items-center">
      <ElTable
        v-loading="loading"
        height="calc(100vh - 400px)"
        class="sm:h-full"
        :data="data"
        row-key="id"
      >
        <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
      </ElTable>
    </div>
    <div class="mt-20px flex justify-start">
      <ElPagination
        v-if="pagination.total"
        layout="total,prev,pager,next,sizes"
        :total="pagination.total"
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        @current-change="pagination['current-change']"
        @size-change="pagination['size-change']"
      />
    </div>
  </ElDialog>
</template>

<style scoped>
/* 可以根據需要添加樣式 */
</style>
