// 游戏列表项接口
export interface GameItem {
  id: number
  game_name: string
  game_type: string
  game_uid: string
  manufacturer: string
  manufacturer_id: number
  icon: string
  description: string
  status: number
  sort_order: number
  title: string
  image: string
  provider: string
  is_collected: boolean
  is_favorite?: boolean
  collection_id?: number
}

// 分页数据接口
export interface PaginationData<T> {
  page_no: number
  page_size: number
  total: number
  data: T[]
}

// API响应接口
export interface ApiResponse<T> {
  code: number
  msg: string
  total: number
  data: T
}

// 游戏列表查询参数
export interface GameListParams {
  page_no?: number
  page_size?: number
  game_type?: string
  manufacturer_id?: string
  search_key?: string
}

export interface GameFavoritesResponse {
  system_hot_games: GameItem[];
  user_favorites: GameItem[];
}

export interface GameCollectionResponse {
  id: number;
  game_id: number;
  user_id: number;
  created_at: string;
  updated_at: string;
} 