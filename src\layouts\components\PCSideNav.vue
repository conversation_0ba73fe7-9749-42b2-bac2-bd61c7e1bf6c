<template>
  <div class="pc-side-nav">
    <!-- 游戏菜单 -->
    <v-list class="game-menu">
      <v-list-item v-for="item in gameMenuItems" :key="item.name" :to="item.path" class="game-item"
        :class="{ active: activeItem === item.path }" :ripple="false">
        <div class="item-content">
          <img :src="item.icon" class="item-icon" />
          <span class="item-title">{{ item.name }}</span>
        </div>
      </v-list-item>
    </v-list>

    <!-- 功能菜单 -->
    <v-list class="feature-menu">
      <v-list-item v-for="item in featureMenuItems" :key="item.name" class="feature-item"
        :class="{ active: activeItem === item.path }" :style="{ background: item.bgColor }"
        @click="handleNavClick(item)" :ripple="false">
        <template v-slot:append>
          <img :src="item.icon" class="item-icon" />
        </template>
        <v-list-item-title>{{ item.name }}</v-list-item-title>
      </v-list-item>
    </v-list>

    <!-- 底部按钮组 -->
    <div class="action-buttons">
      <v-btn v-for="btn in actionButtons" :key="btn.name" block :ripple="false" class="action-btn mb-2"
        :class="{ active: btn.path && activeItem === btn.path }" @click="handleActionClick(btn)">
        <img :src="btn.icon" class="btn-icon" />
        {{ btn.name }}
      </v-btn>
    </div>

    <!-- 添加弹窗组件 -->
    <BonusDialog 
      :show="showBonusDialog" 
      @update:show="showBonusDialog = $event"
      @exchange-success="handleExchangeSuccess" 
    />
    <BonusSuccessDialog 
      :show="showBonusSuccessDialog" 
      :amount="exchangeResult?.amount"
      :code="exchangeResult?.code"
      @update:show="showBonusSuccessDialog = $event" 
    />
    <TelegramShareDialog ref="telegramDialogRef" />
    <PinduoduoDialog ref="pinduoduoDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import BonusDialog from "@/components/BonusDialog.vue";
import BonusSuccessDialog from "@/components/BonusSuccessDialog.vue";
import TelegramShareDialog from "@/components/TelegramShareDialog.vue";
import PinduoduoDialog from "@/components/PinduoduoDialog.vue";
import { loginEvent, SHOW_LOGIN_EVENT } from "@/api/request";
import { useStore } from "vuex";
import gameIcon1 from "@/assets/images/game-icon-01.png";
import gameIcon2 from "@/assets/images/game-icon-02.png";
import gameIcon8 from "@/assets/images/game-icon-08.png";
import gameIcon9 from "@/assets/images/game-icon-09.png";
import gameIcon5 from "@/assets/images/game-icon-05.png";
import gameIcon7 from "@/assets/images/game-icon-07.png";
import gameIcon3 from "@/assets/images/game-icon-03.png";
import gameIcon4 from "@/assets/images/game-icon-04.png";
import menuIcon1 from "@/assets/images/menu-icon-01.png";
import menuIcon2 from "@/assets/images/menu-icon-02.png";
import menuIcon4 from "@/assets/images/menu-icon-04.png";
import menuIcon5 from "@/assets/images/menu-icon-05.png";
import menuIcon6 from "@/assets/images/menu-icon-06.png";
import shareIcon1 from "@/assets/images/share-icon-01.png";
import shareIcon2 from "@/assets/images/share-icon-02.png";

const router = useRouter();
const route = useRoute();
const store = useStore();
const isCollapsed = ref(false);
const showBonusDialog = ref(false);
const showBonusSuccessDialog = ref(false);
const telegramDialogRef = ref();
const pinduoduoDialogRef = ref();
const exchangeResult = ref<{ code: string; amount: number; message: string } | null>(null);
const activeItem = ref(route.fullPath); // 初始化為當前路由的 fullPath

// 导出状态供其他组件使用
defineExpose({
  isCollapsed,
});

// 監聽路由變化
watch(
  () => route.fullPath,
  (newFullPath) => {
    activeItem.value = newFullPath;
  }
);

const gameMenuItems = [
  { name: "Salão", icon: gameIcon9, path: "/home" },
  { name: "Bônus Slots", icon: gameIcon8, path: "/game-list?id=3" },
  { name: "Hot", icon: gameIcon2, path: "/game-list?id=1" },
  { name: "Slots", icon: gameIcon1, path: "/game-list?id=2" },
  { name: "Cassino ao Vivo", icon: gameIcon5, path: "/game-list?id=4" },
  // { name: "Coluna ao Vivo", icon: gameIcon6, path: "/game-list?id=5" },
  { name: "Pôquer", icon: gameIcon7, path: "/game-list?id=7" },
  { name: "Pescaria", icon: gameIcon3, path: "/game-list?id=8" },
  { name: "Coluna", icon: gameIcon4, path: "/game-list?id=9" },
];

const featureMenuItems = [
  {
    name: "Conexão",
    icon: menuIcon1,
    path: "/connection",
    bgColor: "linear-gradient( 90deg, #0078A2 0%, #35E79D 100%)",
  },
  {
    name: "Aposta",
    icon: menuIcon2,
    path: "/aposta",
    bgColor: "linear-gradient( 90deg, #E50C1A  0%,  #E47878 100%)",
  },
  // {
  //   name: 'Lista de cartões bancários',
  //   icon: menuIcon3,
  //   path: '/bank-cards',
  //   bgColor:'linear-gradient( 90deg, #fe7dae 0%, #fa5586 100%)'
  // },
  {
    name: "Nivel VIP",
    icon: menuIcon4,
    path: "/vip",
    bgColor: "linear-gradient( 90deg, #EE4700 0%,  #FFC735 100%)",
  },
  {
    name: "Bonus",
    icon: menuIcon5,
    path: "",
    bgColor: "linear-gradient( 90deg, #5A16B3 0%, #164ACD 100%)",
  },
  {
    name: "Ganhe 100 reais grátis",
    icon: menuIcon6,
    path: "",
    bgColor: "linear-gradient( 90deg, #FF4977 0%, #FE7DAE 100%)",
  },
  {
    name: "Convidar",
    icon: shareIcon1,
    path: "/invite",
    bgColor: "linear-gradient( 90deg, #2938B4 0%, #0072BF 100%)",
  },
  {
    name: "Junte-se a noe",
    icon: shareIcon2,
    path: "telegram",
    bgColor: "linear-gradient( 90deg, #B25A9F 0%, #D8285A 100%)",
  },
];

const actionButtons: any[] = [
  // { name: "Convidar", icon: shareIcon1, type: "invite", path: "/invite" },
  // { name: "Junte-se a noe", icon: shareIcon2, type: "telegram" },
  // { name: 'Baixar APP', icon: shareIcon3 }
];

// 遊戲菜單點擊事件 (移除)
// const handleGameNavClick = (item: any) => {
//   if (item.path) {
//     router.push(item.path);
//   }
// }

// 功能菜單點擊事件 (修改)
const handleNavClick = (item: any) => {
  if (
    item.name === "Bonus" ||
    item.name === "Bônus de login VIP" ||
    item.name === "meu bônus"
  ) {
    showBonusDialog.value = true;
    return;
  }

  if (item.name === "Ganhe 100 reais grátis") {
    // 检查用户是否已登录
    const isLoggedIn = store.getters["auth/isLoggedIn"];

    if (!isLoggedIn) {
      // 如果未登录，触发登录弹窗
      loginEvent.dispatchEvent(new Event(SHOW_LOGIN_EVENT));
    } else {
      pinduoduoDialogRef.value?.show();
    }

    return;
  }
  // 底部按鈕不影響側邊欄選中狀態，所以這裡不修改 activeItem
  if (item.name === "Junte-se a noe") {
    // 检查用户是否已登录
    const isLoggedIn = store.getters["auth/isLoggedIn"];

    if (!isLoggedIn) {
      // 如果未登录，触发登录弹窗
      loginEvent.dispatchEvent(new Event(SHOW_LOGIN_EVENT));
    } else {
      telegramDialogRef.value?.show();
    }

    return;
  }
  if (item.path) {
    router.push(item.path);
  }
};

// 处理兑换成功事件
const handleExchangeSuccess = (result: { code: string; amount: number; message: string }) => {
  exchangeResult.value = result;
  showBonusSuccessDialog.value = true;
};

// 底部按鈕點擊事件
const handleActionClick = (btn: any) => {
  // 底部按鈕不影響側邊欄選中狀態，所以這裡不修改 activeItem
  if (btn.type === "telegram") {
    // 检查用户是否已登录
    const isLoggedIn = store.getters["auth/isLoggedIn"];

    if (!isLoggedIn) {
      // 如果未登录，触发登录弹窗
      loginEvent.dispatchEvent(new Event(SHOW_LOGIN_EVENT));
    } else {
      telegramDialogRef.value?.show();
    }

    return;
  }
  if (btn.path) {
    router.push(btn.path);
  }
};
</script>

<style lang="scss" scoped>
.pc-side-nav {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 6px 12px;
  background: #0e753b;
  gap: 6px;
}

.game-menu {

  // margin-top:6px;
  :deep() {
    background: transparent !important;
    padding: 0;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;

    .v-list-item {
      height: 64px;
      border-radius: 12px !important;
      background: #30a965;
      padding: 0 !important;

      .item-icon {
        width: 30px;
        height: 30px;
      }

      &:hover {
        background: #187a43;
      }

      .v-list-item__content {
        padding: 0 !important;
      }

      &.active {
        background: linear-gradient(180deg, #07563b 0%, #2aa05d 100%);
        transform: scale(1.02);
        transition: all 0.3s ease;
      }
    }
  }
}

.item-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px;

  .menu-icon {
    color: #fff;
    opacity: 0.9;
  }

  .item-title {
    font-size: 12px;
    color: #fff;
    opacity: 0.9;
    text-align: center;
  }
}

.feature-menu {
  margin-top: 6px;

  :deep() {
    background: transparent !important;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 6px;

    .v-list-item {
      height: 48px;
      border-radius: 12px !important;
      padding: 0 16px;
      position: relative;
      overflow: hidden;

      .v-list-item-title {
        font-size: 16px;
        font-weight: 500;
        color: #fff;
        padding-left: 10px;
      }

      .item-icon {
        height: 40px;
        position: absolute;
        right: 8px;
        bottom: 0;
      }

      &.active {
        // transform: scale(1.02);
        // transition: all 0.3s ease;
      }
    }
  }
}

.action-buttons {
  margin-top: 6px;

  .action-btn {
    height: 48px;
    border-radius: 10px;
    background: #30a965 !important;
    color: #fff !important;
    font-family: Inter, Inter;
    font-weight: 600;
    font-size: 16px;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
    justify-content: start;
    text-indent: 0em !important;

    .btn-icon {
      width: 29px;
      margin-right: 12px;
    }

    &:hover {
      background: #187a43 !important;
    }

    &.active {
      background: linear-gradient(180deg, #07563b 0%, #2aa05d 100%) !important;
    }

    :deep(.v-btn__prepend) {
      margin-right: 8px;
    }
  }
}
</style>
