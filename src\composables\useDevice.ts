import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'

export enum DeviceType {
  PC = 'pc',
  MOBILE = 'mobile'
}

export interface DeviceConfig {
  type: DeviceType
  platform: string
  orientation?: 'portrait' | 'landscape'
  width: number
  height: number
}

export const useDevice = () => {
  const route = useRoute()
  const currentDevice = ref<DeviceConfig>({
    type: DeviceType.PC,
    platform: 'web',
    width: window.innerWidth,
    height: window.innerHeight
  })

  const isMobile = ref(false)
  const isTablet = ref(false)

  const updateDeviceInfo = () => {
    const width = window.innerWidth
    const height = window.innerHeight
    
    // 更新设备类型
    isMobile.value = width <= 768
    isTablet.value = width > 768 && width <= 1024
    
    currentDevice.value = {
      type: isMobile.value ? DeviceType.MOBILE : DeviceType.PC,
      platform: 'web',
      orientation: width > height ? 'landscape' : 'portrait',
      width,
      height
    }
  }

  // 监听窗口大小变化
  onMounted(() => {
    updateDeviceInfo()
    window.addEventListener('resize', updateDeviceInfo)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateDeviceInfo)
  })

  return {
    currentDevice,
    isMobile,
    isTablet
  }
}

// 根据设备类型获取对应的组件
export const getDeviceComponent = (pcComponent: string, mobileComponent: string) => {
  const { isMobile } = useDevice()
  return isMobile.value ? mobileComponent : pcComponent
}

// 根据设备类型获取对应的布局
export const getDeviceLayout = (pcLayout: string, mobileLayout: string) => {
  const { isMobile } = useDevice()
  return isMobile.value ? mobileLayout : pcLayout
} 