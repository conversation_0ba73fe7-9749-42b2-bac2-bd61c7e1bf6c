import { DeviceConfig, DeviceType, GameDeviceParams } from '@/types/device'

// 游戏厂商配置接口
interface GameVendorConfig {
  name: string;
  deviceParams: {
    pc: Record<string, any>;
    mobile: Record<string, any>;
  };
  orientationSupport: {
    pc: boolean;
    mobile: boolean;
  };
  platformRequirements: {
    minWidth: number;
    minHeight: number;
    required: string[];
  };
}

// 游戏厂商配置
const gameVendors: Record<string, GameVendorConfig> = {
  pgsoft: {
    name: 'PG Soft',
    deviceParams: {
      pc: {
        platform: 'desktop',
        ratio: '16:9',
        quality: 'high'
      },
      mobile: {
        platform: 'mobile',
        ratio: 'auto',
        quality: 'medium'
      }
    },
    orientationSupport: {
      pc: false,
      mobile: true
    },
    platformRequirements: {
      minWidth: 320,
      minHeight: 480,
      required: ['webgl', 'websocket']
    }
  },
  pragmatic: {
    name: 'Pragmatic Play',
    deviceParams: {
      pc: {
        platform: 'desktop',
        ratio: '16:9',
        quality: 'high'
      },
      mobile: {
        platform: 'mobile',
        ratio: 'auto',
        quality: 'medium'
      }
    },
    orientationSupport: {
      pc: false,
      mobile: true
    },
    platformRequirements: {
      minWidth: 360,
      minHeight: 640,
      required: ['webgl']
    }
  }
}

// 生成游戏启动参数
export const generateGameParams = (
  vendor: string,
  deviceConfig: DeviceConfig
): GameDeviceParams => {
  const vendorConfig = gameVendors[vendor] || gameVendors.pgsoft
  const deviceType = deviceConfig.type === DeviceType.PC ? 'pc' : 'mobile'
  
  return {
    deviceType: deviceConfig.type,
    platform: deviceConfig.platform,
    orientation: deviceConfig.orientation || 'landscape',
    screenWidth: deviceConfig.width,
    screenHeight: deviceConfig.height,
    isApp: false,
    ...vendorConfig.deviceParams[deviceType]
  }
}

// 检查设备是否满足游戏要求
export const checkDeviceRequirements = (
  vendor: string,
  deviceConfig: DeviceConfig
): boolean => {
  const vendorConfig = gameVendors[vendor] || gameVendors.pgsoft
  const { width, height } = deviceConfig
  
  return width >= vendorConfig.platformRequirements.minWidth &&
         height >= vendorConfig.platformRequirements.minHeight
}

// 获取游戏厂商配置
export const getVendorConfig = (vendor: string): GameVendorConfig => {
  return gameVendors[vendor] || gameVendors.pgsoft
} 