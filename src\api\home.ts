import request from './request'

export interface CarouselItem {
  id: number
  title: string
  position: number
  weight: number
  status: number
  image_url: string
  hyperlink: string
  created_at: number
  created_by: string
  updated_at: number
  updated_by: string
}

// 拼多多活动接口返回类型
export interface PddActivityResponse {
  status_code: number
  data: {
    content: string
    end_time: string
    id: number
    name: string 
    remark: string
    start_time: string
    status: number
  }
  count: number
}

export interface PddActivityContent {
  content: string
  end_time: string
  id: number
  name: string
  remark: string
  start_time: string
  status: number
  max_withdrawal_amount: string
  reward_digital: string
  reward_amount: string
  invitation_code:string
  activity_id:string
}

/**
 * 获取轮播图列表
 * @param params 查询参数
 * @returns 轮播图列表数据
 */
export function getCarouselList(params: {
  position: number
  sort?: string
  page: number
  size: number
}) {
  return request.Get<CarouselItem[]>('/carousel/list', { params })
}

// 获取拼多多活动
export function enterPddActivity() {
  return request.Post('/activity/pdd_participation', {})
} 