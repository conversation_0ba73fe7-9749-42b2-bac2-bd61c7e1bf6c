name: Test Environment CI/CD

on:
  workflow_dispatch:
    inputs:
      version:
        description: '输入版本号 (例如: v1.2.0)'
        required: true
        type: string

env:
  DOCKER_IMAGE: box777-web
  # 直接定义两台服务器的 IP
  SERVER_IP_1: **************
  SERVER_IP_2: *************
  SERVER_PORT: 22
  CONTAINER_NAME: box777-web

jobs:
  build-and-deploy:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: |
          npm install --legacy-peer-deps
      - name: Build application
        run: npm run build
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: |
            ${{ secrets.DOCKERHUB_USERNAME }}/${{ env.DOCKER_IMAGE }}:${{ github.event.inputs.version }}
            ${{ secrets.DOCKERHUB_USERNAME }}/${{ env.DOCKER_IMAGE }}:latest

      # 部署到第一台服务器
      - name: Deploy to Server 1
        uses: appleboy/ssh-action@master
        with:
          host: ${{ env.SERVER_IP_1 }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: ${{ env.SERVER_PORT }}
          script: |
            set -euxo pipefail
            docker stop ${{ env.CONTAINER_NAME }} || true
            docker rm ${{ env.CONTAINER_NAME }} || true
            docker pull ${{ secrets.DOCKERHUB_USERNAME }}/${{ env.DOCKER_IMAGE }}:${{ github.event.inputs.version }}
            docker run -d \
              --name ${{ env.CONTAINER_NAME }} \
              -p 8008:80 \
              --restart unless-stopped \
              ${{ secrets.DOCKERHUB_USERNAME }}/${{ env.DOCKER_IMAGE }}:${{ github.event.inputs.version }}
            docker images | awk '{print $1":"$2}' | grep "${{ secrets.DOCKERHUB_USERNAME }}/${{ env.DOCKER_IMAGE }}" | grep -v "${{ github.event.inputs.version }}" | xargs -r docker rmi || true

      # 部署到第二台服务器
      - name: Deploy to Server 2
        uses: appleboy/ssh-action@master
        with:
          host: ${{ env.SERVER_IP_2 }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: ${{ env.SERVER_PORT }}
          script: |
            set -euxo pipefail
            docker stop ${{ env.CONTAINER_NAME }} || true
            docker rm ${{ env.CONTAINER_NAME }} || true
            docker pull ${{ secrets.DOCKERHUB_USERNAME }}/${{ env.DOCKER_IMAGE }}:${{ github.event.inputs.version }}
            docker run -d \
              --name ${{ env.CONTAINER_NAME }} \
              -p 8008:80 \
              --restart unless-stopped \
              ${{ secrets.DOCKERHUB_USERNAME }}/${{ env.DOCKER_IMAGE }}:${{ github.event.inputs.version }}
            docker images | awk '{print $1":"$2}' | grep "${{ secrets.DOCKERHUB_USERNAME }}/${{ env.DOCKER_IMAGE }}" | grep -v "${{ github.event.inputs.version }}" | xargs -r docker rmi || true

  notify:
    runs-on: ubuntu-22.04
    needs: [build-and-deploy]
    if: always()
    steps:
      - name: Unified Feishu Notification
        env:
          SERVER_URL_1: http://${{ env.SERVER_IP_1 }}:8008
          SERVER_URL_2: http://${{ env.SERVER_IP_2 }}:8008
        run: |
          BUILD_STATUS="${{ needs.build-and-deploy.result }}"
          
          if [ "$BUILD_STATUS" = "success" ]; then
            TITLE="✅ 部署成功通知"
            TEMPLATE="green"
            EXTRA_INFO="访问地址:\n$SERVER_URL_1\n$SERVER_URL_2"
            STATUS="应用已更新至最新版本"
          else
            TITLE="❌ 部署失败通知"
            TEMPLATE="red"
            EXTRA_INFO="错误日志: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
            STATUS="部署失败"
          fi

          FEISHU_MSG='{
            "msg_type": "interactive",
            "card": {
              "header": {
                "title": { "tag": "plain_text", "content": "'"$TITLE"'" },
                "template": "'"$TEMPLATE"'"
              },
              "elements": [{
                "tag": "div",
                "text": {
                  "content": "仓库名: ${{ github.repository }}\n所在分支: ${{ github.ref_name }}\nWorkflow: ${{ github.workflow }}\n镜像版本: ${{ github.event.inputs.version }}\n日志链接: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}\n部署状态: '"$STATUS"'\n\n'"$EXTRA_INFO"'",
                  "tag": "lark_md"
                }
              }]
            }
          }'

          # 发送通知（带重试机制）
          for i in {1..3}; do
            if curl -X POST "${{ secrets.FEISHU_WEBHOOK }}" \
              -H 'Content-Type: application/json' \
              -d "$FEISHU_MSG"; then
              echo "通知发送成功"
              break
            else
              echo "通知发送失败，重试 $i/3..."
              sleep 2
              [ $i -eq 3 ] && exit 1
            fi
          done
