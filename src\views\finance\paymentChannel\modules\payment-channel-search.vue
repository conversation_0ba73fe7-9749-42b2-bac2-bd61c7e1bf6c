<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-06-27 09:23:46
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\views\finance\paymentChannel\modules\payment-channel-search.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="search-wrapper">
    <ElForm ref="formRef" :model="model">
      <ElRow :gutter="16">
        <ElCol :span="6">
          <ElFormItem label="通道类型" prop="channel_type">
            <ElSelect v-model="model.channel_type" placeholder="请选择通道类型" clearable>
              <ElOption v-for="item in channelTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="状态" prop="status">
            <ElSelect v-model="model.status" placeholder="请选择状态" clearable>
              <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface Props {
  model: Record<string, any>;
}

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref();

const channelTypeOptions = [
  { value: 'cashpay', label: 'CashPay' },
  { value: 'coingate', label: 'CoinGate' }
];

const statusOptions = [
  { value: 1, label: '启用' },
  { value: 0, label: '禁用' }
];

function handleReset() {
  formRef.value?.resetFields();
  emit('reset');
}

function handleSearch() {
  emit('search');
}
</script>

<style lang="scss" scoped>
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 18px;
    margin-right: 18px;
  }
}
</style>