<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-20 13:14:12
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-07-28 15:55:06
 * @FilePath: \betdoce-web\src\views\Mobile\Activity\PinduoduoNew.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="page">
    <div class="pdd-activity" :class="{ help: status === 'help' }">
      <!-- 添加中獎提示彈框 -->
      <div class="winning-alert" v-if="showWinningAlert">
        <div class="winning-content">
          <div class="winning-message">{{ winningData.message }}</div>
        </div>
      </div>

      <!-- 添加加載動畫 -->
      <v-overlay
        :model-value="loading"
        class="align-center justify-center"
        persistent
      >
        <img
          src="@/assets/images/h5/loading.gif"
          alt="loading"
          class="global-loading-img"
        />
        <!-- <v-progress-circular
          color="primary"
          indeterminate
          size="64"
        ></v-progress-circular> -->
      </v-overlay>

      <!-- 返回按钮 -->
      <div class="back-button" @click="handleBack">
        <v-icon>mdi-arrow-left</v-icon>
      </div>
      <template v-if="status !== 'ok'">
        <div class="reward-card-help" v-if="status === 'start'">
          <div class="reward-card-top">
            <div class="reward-amount">
              R<span style="font-size: 30px">$</span> {{ rules?.bonus_amount }}
            </div>
            <v-icon
              class="icon-tip"
              icon="mdi-alert-circle-outline"
              @click="() => (showRule = true)"
            ></v-icon>
          </div>
          <div class="reward-card-bottom-bg">
            <div class="reward-card-bottom reward-card">
              <div class="reward-title-tip">
                Abra o pacote de presente e receba imediatamente
              </div>

              <div
                class="open-button"
                @click="handleOpen"
                :class="{ disabled: isAnimating }"
              >
                <span>Abrir</span>
                <div class="button-glow"></div>
              </div>
              <!-- <div class="triangle"></div> -->
              <div class="open-tip">Abra e saque imediatamente</div>
            </div>
          </div>
        </div>
        <div
          class="reward-card-help"
          v-if="status === 'help' && registerNum === 0"
        >
          <div class="reward-card-top">
            <div class="reward-amount">
              R<span style="font-size: 30px">$</span>
              {{ activityStatus?.bonus_amount }}
            </div>
            <div class="reward-progress">
              {{ activityStatus?.current_progress }}%
            </div>
            <div class="progress-bar-wrap">
              <v-progress-linear
                class="progress-bar"
                :model-value="activityStatus?.current_progress"
                color="#faab59"
                height="10"
                rounded
              ></v-progress-linear>
              <span class="progress-tip">Nenhum amigo se juntou ainda</span>
            </div>
            <v-icon
              class="icon-tip"
              icon="mdi-alert-circle-outline"
              @click="() => (showRule = true)"
            ></v-icon>
          </div>
          <div class="reward-card-bottom-bg">
            <div class="reward-card-bottom">
              <div class="desc-text">
                Bônus de suporte recebido：<span style="font-size: 30px">R</span
                >${{ activityStatus?.current_amount / 100 || 0 }}
              </div>
              <p class="countdown">Restam {{ formatTime(countdown) }}</p>
              <v-btn class="activity-main-btn" @click="handleInvite">
                Copie o link paracompartilhar
              </v-btn>
            </div>
          </div>
        </div>
        <!-- 帮助状态 (help) - 80% 进度 -->
        <div
          class="reward-card-help"
          v-else-if="status === 'help' && registerNum > 0"
        >
          <div
            class="reward-card-top"
            :class="{
              need_recharge: isRechargeRequired(),
            }"
          >
            <div class="reward-amount">
              R<span style="font-size: 30px">$</span>
              {{ activityStatus?.bonus_amount }}
            </div>
            <div class="reward-progress">
              {{
                isRechargeRequired() ? 99.99 : activityStatus?.current_progress
              }}%
            </div>
            <div class="progress-bar-wrap">
              <v-progress-linear
                class="progress-bar"
                :model-value="
                  isRechargeRequired()
                    ? 99.99
                    : activityStatus?.current_progress
                "
                color="#faab59"
                height="10"
                rounded
              ></v-progress-linear>
              <span class="progress-tip"
                >Faltam apenas
                {{
                 ( 100 -
                  (isRechargeRequired()
                    ? 99.99
                    : activityStatus?.current_progress)).toFixed(2)
                }}% para concluir</span
              >
            </div>
            <v-icon
              class="icon-tip"
              icon="mdi-alert-circle-outline"
              @click="() => (showRule = true)"
            ></v-icon>
          </div>
          <div class="reward-card-bottom-bg">
            <div
              class="reward-card-bottom"
              :class="{
                'need_recharge-bottom': isRechargeRequired(),
              }"
            >
              <div class="friend-avatars">
                <div
                  class="avatar-item"
                  v-for="item in registerNum"
                  :key="item"
                >
                  <img
                    src="@/assets/images/avatar-tp.png"
                    class="avatar-tp"
                    alt="Avatar do Amigo"
                  />
                </div>
                <div class="avatar-item dot-more">
                  <img
                    src="@/assets/images/more-dot.png"
                    class="avatar-tp"
                    alt="Avatar do Amigo"
                  />
                </div>
              </div>
              <div class="desc-text">
                Bônus de suporte recebido：<span style="font-size: 30px">R</span
                >${{ activityStatus?.current_amount / 100 || 0 }}
              </div>
              <p class="countdown">Restam {{ formatTime(countdown) }}</p>
              <v-btn class="activity-main-btn" @click="handleInvite">
                Copie o link para compartilhar
              </v-btn>
              <template v-if="isRechargeRequired()">
                <div class="desc-text">
                  RecarregueR${{ activityStatus?.must_recharge_amount }} ,
                  conclua a erefa diretamente e ganhe o bonus
                </div>
                <v-btn class="activity-main-btn" @click="handleRecarregue">
                  Recarregue
                </v-btn>
                <p class="countdown">{{ formatTime(rechargeCountdown) }}</p>
              </template>
            </div>
          </div>
        </div>
      </template>

      <!-- 奖励卡片 (ok) - 100% 进度 -->
      <template v-else>
        <div class="reward-card-help">
          <!-- <div class="ok-img">
            <img src="@/assets/images/h5/pdd-ok-tp.png" />
          </div> -->
          <div class="reward-card-top">
            <div class="reward-amount">
              R<span style="font-size: 30px">$</span>
              {{ activityStatus?.bonus_amount }}
            </div>
            <div class="reward-progress">
              {{ activityStatus?.current_progress }}%
            </div>
            <div class="progress-bar-wrap">
              <v-progress-linear
                class="progress-bar"
                :model-value="activityStatus?.current_progress"
                color="#faab59"
                height="10"
                rounded
              ></v-progress-linear>
              <span class="progress-tip"
                >Faltam apenas {{ 100 - activityStatus?.current_progress }}%
                para concluir</span
              >
            </div>
            <v-icon
              class="icon-tip"
              icon="mdi-alert-circle-outline"
              @click="() => (showRule = true)"
            ></v-icon>
          </div>
          <div class="reward-card-bottom-bg">
            <div class="reward-card-bottom">
              <div class="friend-avatars">
                <div
                  class="avatar-item"
                  v-for="item in registerNum"
                  :key="item"
                >
                  <img
                    src="@/assets/images/avatar-tp.png"
                    class="avatar-tp"
                    alt="Avatar do Amigo"
                  />
                </div>
                <div class="avatar-item dot-more">
                  <img
                    src="@/assets/images/more-dot.png"
                    class="avatar-tp"
                    alt="Avatar do Amigo"
                  />
                </div>
              </div>
              <div class="desc-text">
                Bônus de suporte recebido：<span style="font-size: 30px">R</span
                >${{ activityStatus?.current_amount / 100 || 0 }}
              </div>
              <p class="countdown">Restam {{ formatTime(countdown) }}</p>
              <v-btn
                class="activity-main-btn"
                v-if="!isShare"
                @click="hendleShareTelegram"
              >
                Compartilhe no Telegram para concluir a tarefa
              </v-btn>
              <v-btn v-else class="activity-main-btn" @click="handleWithdraw">
                Ganhe mais
              </v-btn>
            </div>
          </div>
        </div>
      </template>
    </div>
    <RuleDialog
      :visible.sync="showRule"
      title="Regras de Atividade"
      :content="rules?.activity_rules"
      @update:visible="handleRuleDialogClose"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from "vue";
import { usePinduoduoActivity } from "@/composables/usePinduoduoActivity";
import { useRouter } from "vue-router";
import RuleDialog from "@/components/RuleDialog.vue";

const router = useRouter();
const {
  status,
  // activityData,
  activityStatus,
  countdown,
  loading,
  isAnimating,
  showWinningAlert,
  winningData,
  rules,
  showRule,
  registerNum,
  rechargeCountdown,
  isShare,
  handleOpen,
  handleInvite,
  handleRecarregue,
  handleWithdraw,
  hendleShareTelegram,
  formatTime,
  handleRuleDialogClose,
  getRules,
  // startCountdownTimer,
  clearAllTimers,
  isRechargeRequired,
  // startActivityUpdateTimer,
  initializeActivityData,
} = usePinduoduoActivity();

function handleBack() {
  router.back();
}

onMounted(async () => {
  await initializeActivityData();
  await getRules();
});

onUnmounted(() => {
  clearAllTimers();
  document.body.style.backgroundColor = "";
  const viewport = document.querySelector('meta[name="viewport"]');
  if (viewport) {
    viewport.setAttribute("content", "width=device-width, initial-scale=1");
  }
});
</script>

<style lang="scss" scoped>
.help {
  background-size: 100% auto !important;
}
.global-loading-img {
  width: 150px;
  height: 150px;
}
// 中獎提示彈框樣式
.winning-alert {
  position: fixed;
  top: 30px;
  /* 距離頂部一些距離 */
  left: 50%;
  transform: translateX(-50%);
  /* 水平居中 */
  z-index: 9999;
  background: rgba(0, 0, 0, 0.4);
  /* 黃色漸變 */
  border-radius: 12px;
  /* 圓角 */
  padding: 12px 20px;
  /* 調整內邊距 */
  width: 90%;
  /* 設置寬度 */
  max-width: 320px;
  /* 最大寬度 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  /* 添加陰影 */

  .winning-content {
    text-align: center;

    .winning-message {
      font-family: FZLanTingHeiDB-SC;
      /* 確保字體一致 */
      font-weight: 800;
      /* 加粗 */
      color: #fff;
      /* 紅色字體 */
      line-height: 1.4;
      /* 行高 */
    }

    .winning-count {
      font-size: 16px;
      margin-bottom: 4px;
    }

    .winning-message {
      font-size: 14px;
    }
  }
}

.help-box {
  bottom: 14vh !important;
}

.activity-main-btn {
  padding: 20px;
  height: auto;
  width: 80%;
  background: url("@/assets/images/h5/activity-button.png") no-repeat;
  /* Assuming gradient from image */
  background-size: 100% 100%;
  font-size: 18px;
  color: #fff;
  border-radius: 24px;
  box-shadow: none;
  text-transform: none;
  // margin-top: 16px;
}

.activity-main-btn :deep(.v-btn__content) {
  white-space: normal !important;
  /* Ensure text wraps */
  line-height: 1.2;
  /* Explicit line height */
}

// :deep(.v-overlay__content) {

//   border-radius: 20px;
// }
.page {
  background-color: #508336;
  height: 100vh;
}

.pdd-activity {
  .blck-bg {
    background: rgba(0, 0, 0, 0.8);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  min-height: 100vh;
  background: url("@/assets/images/h5/pdd-bg.png") no-repeat center center;
  background-size: 100% 100%;
  padding: 20px;
  position: relative;
  overflow: hidden;
  // 添加安全区域padding
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  border-radius: 20px;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
  }

  .back-button {
    position: fixed;
    top: max(env(safe-area-inset-top), 16px);
    left: 16px;
    z-index: 100;
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;

    &:active {
      background: rgba(0, 0, 0, 0.5);
    }
  }

  .reward-card {
    // position: relative;
    .icon-tip {
      color: #ab4728;
      position: absolute;
      top: 10px;
      right: 10px;
      cursor: pointer;
    }

    .reward-title-tip {
      font-family: RockoFLF, RockoFLF;
      font-weight: bold;
      font-size: 20px;
      color: #e0574d;
      // line-height: 69px;
      text-shadow: 2px 4px 4px rgba(0, 0, 0, 0.25);
      text-align: center;
      font-style: normal;
      text-transform: none;
    }

    .reward-amount {
      font-size: 30px;
      font-weight: bold;
      margin-bottom: 30px;
      font-family: Alibaba PuHuiTi;
      font-weight: 800;
      color: #f74644;
      text-align: center;
      width: 100%;
    }

    .open-button {
      width: 90px;
      height: 90px;
      background: url("@/assets/images/h5/pdd-open-btn.png") no-repeat center
        center;
      background-size: contain;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      position: relative;
      text-align: center;
      transition: transform 0.2s ease;

      &:active {
        transform: scale(0.95);
      }

      span {
        opacity: 1;
        font-size: 1.2rem;
        font-weight: 1000;
        text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
      }
    }

    .open-tip {
      font-size: 13px;
      color: #e0574d;
      background: url("@/assets/images/pdd-open-tip.png");
      background-size: 100% 100%;
      border-radius: 6px;
      padding: 4px 6px;
      display: inline-block;
      font-weight: 600;
      text-align: center;
      margin: 0 auto;
    }

    &.shake {
      animation: shakeCard 0.8s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
    }
  }

  .reward-card-help {
    background: transparent;
    box-shadow: none;
    padding: 0;
    width: 100%;
    max-width: 360px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    position: absolute;
    border-radius: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 2;

    .reward-card-top {
      width: 90%;
      background: linear-gradient(180deg, #e97b3a 0%, #f7b15a 100%);
      border-radius: 24px 24px 0 0;
      box-shadow: 0 4px 16px rgba(255, 153, 0, 0.18);
      padding: 32px 24px 18px 24px;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      border: 10px solid #f2aa62;
      border-bottom: none;
      margin-bottom: -50px;
      position: relative;

      // z-index:1;
      .icon-tip {
        color: #be3b16;
        position: absolute;
        top: 10px;
        right: 10px;
        cursor: pointer;
      }
    }
    .need_recharge {
      margin-bottom: -70px;
      padding: 32px 24px 32px 24px;
    }
    .reward-amount {
      font-size: 48px;
      color: #fff;
      margin-bottom: 8px;
      letter-spacing: 1px;
      font-family: RockoFLF, RockoFLF;
      font-weight: bold;
      text-shadow: 2px 4px 4px rgba(0, 0, 0, 0.25);
    }

    .reward-progress {
      font-size: 22px;
      color: #fff;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .progress-bar-wrap {
      width: 100%;
      margin-bottom: 8px;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .progress-bar {
      width: calc(100% - 16px);
      margin-left: 8px;
      overflow: inherit;

      :deep(.v-progress-linear__background) {
        background: #be3b16 !important;
        opacity: 1 !important;
        border-radius: 5px;
      }

      :deep(.v-progress-linear__determinate) {
        border-radius: 5px !important;
        background: #fff !important;

        &::after {
          content: "R$";
          position: absolute;
          display: block;
          background: url("@/assets/images/h5/progress-icon-bg.png") no-repeat
            center;
          background-size: 100% 100%;
          width: 21px;
          height: 21px;
          right: -10.5px;
          top: -6px;
          z-index: 9999;
          color: #fff;
          font-size: 0.7rem;
          font-weight: 800;
          line-height: 21px;
          text-align: center;
        }
      }
    }

    .progress-tip {
      font-size: 14px;
      color: #fff;
      margin-top: 2px;
      text-shadow: 0 1px 4px rgba(255, 153, 0, 0.18);
    }

    .reward-card-bottom-bg {
      width: 100%;
      background: #ffe9b0;
      // z-index: 1;
      border-radius: 24px;
      margin-top: -40px;
      box-shadow: 0 8px 32px -8px rgba(0, 0, 0, 0.4);

      .reward-card-bottom {
        margin-top: 40px;
        width: 100%;
        border-radius: 0 0 24px 24px;
        position: relative;
        // overflow: hidden;
        background-image: url("@/assets/images/bottom-bg2.png");
        background-size: 100% 100%;
        // box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        padding: 60px 24px 24px 24px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;

        // border-bottom-left-radius: 100% 80px;
        // border-bottom-right-radius: 100% 80px;
        // border-top-left-radius: 50% 40px;
        // border-top-right-radius: 50% 40px;
        // z-index: 1;
        .countdown {
          font-weight: 500;
          font-size: 16px;
          color: #de6f4c;
          line-height: 30px;
        }

        .friend-avatars {
          display: flex;
          justify-content: center;

          .empty-friends {
            font-family: FZLanTingHeiDB-SC;
            // font-weight: 800; /* Made bolder */
            font-size: 0.8rem;
            color: #ff5035;
            /* Adjusted color to red */
            text-align: center;
            margin: 30px 0 40px 0;
          }

          .avatar-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .avatar-tp {
              width: 50px;
              height: 50px;
              margin-right: 10px;
            }
          }

          .dot-more {
            width: 50px;
            height: 50px;
            margin-left: 5px;
          }
        }
      }
      .need_recharge-bottom {
        padding: 60px 24px 0px 24px;
        .activity-main-btn {
          margin-top: 0px;
        }
      }
      // .reward-card-bottom::before{
      //   content: "";
      //   position: absolute;
      //   display: block;
      //   top:-40px;
      //   width: 94%;
      //   height: 40px;
      //   background: #ffe9b0;
      //   z-index: 1;
      // }
    }

    .desc-text {
      font-size: 18px;
      color: #d97a2a;
      font-weight: 700;
      margin-bottom: 10px;
      text-align: center;
      text-shadow: 2px 4px 4px rgba(0, 0, 0, 0.25);
    }

    .activity-main-btn {
      width: 100%;
      max-width: 320px;
      height: 54px;
      background: linear-gradient(180deg, #ff7c3a 0%, #ff3a3a 100%);
      color: #fff;
      font-size: 17px;
      font-weight: 800;
      border-radius: 32px;
      box-shadow: 0 4px 16px rgba(255, 58, 58, 0.18);
      margin-top: 8px;
      text-shadow: 0 2px 8px rgba(255, 153, 0, 0.1);
      letter-spacing: 1px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background 0.2s;
    }

    .activity-main-btn:active {
      background: linear-gradient(180deg, #ff3a3a 0%, #ff7c3a 100%);
    }
  }

  .card-ok {
    bottom: 22vh;

    .ok-img {
      position: absolute;
      top: -220px;
      z-index: 999;
      width: 300px;
      height: 300px;
      overflow: hidden;
      left: calc(50% - 150px);
      text-align: center;

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("@/assets/images/h5/ok-img-bg.png") no-repeat center;
        background-size: 100% 100%;
        background-position: top;
        animation: rotateBackground 8s linear infinite;
        z-index: -2;
      }

      img {
        margin-top: 60px;
        width: 160px;
        position: relative;
        z-index: 1;
      }
    }

    .reward-header {
      // height: 160px;
      top: -90px;

      .header-radius {
        // height: 160px;
      }

      .header-main {
        // height: 160px;
      }

      .amount-text {
        // margin-top: 60px;
        font-size: 60px !important;
      }
    }

    .triangle {
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-top: 8px solid #fee2a2;
      margin: 0 auto 14px;
    }
  }

  .reward-box {
    position: absolute;
    overflow: hidden;
    width: 100%;
    height: auto;
    bottom: 22vh;
    height: 180px;
    right: -45px;
    z-index: 1;
    background: url("@/assets/images/h5/bg-box.png") no-repeat center bottom;
    background-size: 100% auto;
    margin-bottom: 6px;
  }

  .rules-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    padding: 20px;
    margin-top: 20px;

    h3 {
      color: #333;
      font-size: 16px;
      margin-bottom: 12px;
    }

    ol {
      padding-left: 20px;

      li {
        color: #666;
        font-size: 14px;
        margin-bottom: 8px;
        line-height: 1.4;
      }
    }
  }
}

@keyframes shakeCard {
  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(2px, 0, 0);
  }

  30%,
  50%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(4px, 0, 0);
  }
}

.coin-burst-layer {
  position: fixed;
  left: 50%;
  bottom: 40%;
  transform: translateX(-50%);
  width: 1px;
  height: 1px;
  pointer-events: none;
  z-index: 1000;

  .coin {
    position: absolute;
    width: 40px;
    height: 40px;
    animation: coinBurst 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    animation-delay: var(--delay);

    .coin-inner {
      width: 100%;
      height: 100%;
      background: radial-gradient(circle at 30%, #ffd700, #ffa500);
      border-radius: 50%;
      border: 2px solid #daa520;
      box-shadow: 0 0 10px rgba(255, 215, 0, 0.6);
      animation: coinRotate 0.6s linear infinite;

      &::after {
        content: "$";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #8b4513;
        font-weight: bold;
        font-size: 20px;
      }
    }
  }
}

@keyframes coinBurst {
  0% {
    transform: translate(0, 0) rotate(0) scale(0);
    opacity: 0;
  }

  10% {
    opacity: 1;
    transform: translate(0, 0) rotate(0) scale(var(--scale));
  }

  100% {
    transform: translate(var(--x), var(--y)) rotate(var(--rotate))
      scale(var(--scale));
    opacity: 0;
  }
}

@keyframes coinRotate {
  from {
    transform: rotateY(0deg);
  }

  to {
    transform: rotateY(360deg);
  }
}

@keyframes rotateBackground {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
