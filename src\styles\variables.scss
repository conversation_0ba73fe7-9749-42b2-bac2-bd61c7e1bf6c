// Vuetify Variables
@use 'sass:math' as math;

// Grid
$grid-gutter: 12px;
$container-padding-x: 12px;

// Breakpoints
$grid-breakpoints: (
  'xs': 0,
  'sm': 600px,
  'md': 960px,
  'lg': 1280px,
  'xl': 1920px
);

// Typography
$font-size-root: 16px;
$line-height-root: 1.5;

// Colors
$primary: #FF206E;
$secondary: #FF8FAF;
$accent: #FFA600;
$error: #FF5252;
$info: #2196F3;
$success: #4CAF50;
$warning: #FFC107;

// Custom Variables
$header-height: 64px;
$drawer-width: 280px;
$drawer-mobile-width: 100%;
$border-radius: 8px;

// Dark Theme Colors
$dark-background: #1B193D;
$dark-surface: #2B324D;
$dark-primary-text: rgba(255, 255, 255, 0.87);
$dark-secondary-text: rgba(255, 255, 255, 0.6);

// Gradients
$primary-gradient: linear-gradient(135deg, $primary 0%, $secondary 100%);
$accent-gradient: linear-gradient(90deg, $accent 0%, #FFD600 100%);

// Spacing
$spacing-unit: 8px;
$container-padding: 24px;

// Breakpoints
$breakpoint-sm: 600px;
$breakpoint-md: 960px;
$breakpoint-lg: 1264px;
$breakpoint-xl: 1904px;

// Border radius
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;

// Transitions
$transition-base: all 0.3s ease;

// Z-index
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// Shadows
$box-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
$box-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$box-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1); 