<template>
  <v-app>
    <component :is="currentLayout">
      <router-view v-slot="{ Component }">
        <component :is="Component" />
      </router-view>
    </component>
  </v-app>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent } from 'vue'
import { useDevice } from '@/composables/useDevice'

// 懒加载布局组件
const PCLayout = defineAsyncComponent(() => import('./PCLayout.vue'))
const MobileLayout = defineAsyncComponent(() => import('./MobileLayout.vue'))

const { isMobile } = useDevice()

// 根据设备类型选择布局
const currentLayout = computed(() => {
  return isMobile.value ? MobileLayout : PCLayout
})
</script> 