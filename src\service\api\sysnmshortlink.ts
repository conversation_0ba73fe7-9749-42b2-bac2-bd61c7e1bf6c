import { request } from '../request';

// 获取短链接列表
export function fetchGetShortLinkList(params: any) {
  return request({
    url: '/backend/shorturl/list',
    method: 'get',
    params
  });
}

// 创建短链接
export function fetchAddShortLink(data: any) {
  return request({
    url: '/backend/shorturl/create',
    method: 'post',
    data
  });
}

// 更新短链接
export function fetchUpdateShortLink(data: any) {
  return request({
    url: '/backend/shorturl/update',
    method: 'post',
    data
  });
}

// 获取短链接详情
export function fetchGetShortLinkDetail(data: any) {
  return request({
    url: '/backend/shorturl/detail',
    method: 'post',
    data
  });
}

// 删除短链接
export function deleteShortLink(data: any) {
  return request({
    url: '/backend/shorturl/delete',
    method: 'post',
    data
  });
}