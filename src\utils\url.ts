/**
 * 解析 URL 参数字符串为对象
 * @param paramString URL 参数字符串
 * @returns 解析后的对象
 */
export const parseUrlParams = (paramString: string): Record<string, string> => {
  const params: Record<string, string> = {}
  
  // 如果字符串为空，直接返回空对象
  if (!paramString) return params
  
  // 移除可能存在的问号
  const cleanParamString = paramString.startsWith('?') 
    ? paramString.slice(1) 
    : paramString
  
  // 分割参数对并解析
  cleanParamString.split('&').forEach(param => {
    const [key, value] = param.split('=')
    if (key && value) {
      params[key] = decodeURIComponent(value)
    }
  })
  
  return params
}

/**
 * 将对象转换为 URL 参数字符串
 * @param params 参数对象
 * @returns URL 参数字符串
 */
export const stringifyUrlParams = (params: Record<string, string>): string => {
  return Object.entries(params)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&')
} 

export function fixURL(urlInput: string) {
  try {
    // 获取输入的URL
    let url = urlInput.trim();

    // 移除开头可能的@符号
    if (url.startsWith("@")) {
      url = url.substring(1);
    }

    // 修复常见的转义字符
    url = url.replace(/\\u0026/g, "&");
    url = url.replace(/\\u003d/g, "=");
    url = url.replace(/\\u003f/g, "?");
    url = url.replace(/\\u002f/g, "/");

    // 修复波浪符号问题
    url = url.replace(/~+/g, "~");

    // 修复多个连续冒号
    url = url.replace(/:+/g, ":");

    // 修复错误的百分号编码
    url = url.replace(/%(?![0-9A-Fa-f]{2})/g, "%25");

    // 处理空格
    url = url.replace(/\s+/g, "%20");

    // 尝试创建一个URL对象以验证URL格式
    try {
      new URL(url);
    } catch (e) {
      // 如果创建URL对象失败，可能不是一个有效的URL
      // 尝试添加http://前缀
      if (!url.match(/^https?:\/\//i)) {
        url = "https://" + url;
        try {
          new URL(url);
        } catch (e) {
          throw new Error("无法识别的URL格式");
        }
      } else {
        throw new Error("无效的URL格式");
      }
    }
    return url;
  } catch (error) {
    // 显示错误信息
    // errorMessage.textContent = `错误: ${error.message}`;
    // errorMessage.style.display = "block";
    // gameLink.href = "#";
    // gameLink.textContent = "链接格式错误";
  }
}