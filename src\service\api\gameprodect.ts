import { request } from '../request';
import type { TransactionListParams, TransactionListResponse } from '@/typings/game';

export interface GameListParams {
  page?: number;
  size?: number;
  game_type?: string;
  game_name?: string;
  status?: number;
  platforms_list?: string[];
  manufacturer_id_list?: number[];
}

export interface GameItem {
  id: number;
  game_name: string;
  game_type: string;
  game_uid: string;
  manufacturer: string;
  manufacturer_id: number;
  icon: string;
  status: number;
  sort_order: number;
  platforms: string;
  tags: string;
  access_restriction: string;
  is_favorite: number;
  description: string;
  coin_type: string;
}

export interface GameListResponse {
  count: number;
  data: GameItem[];
  error_code: string;
  status_code: number;
}

export interface CreateGameParams {
  game_name: string;
  game_type: string;
  game_uid: string;
  manufacturer: string;
  manufacturer_id: number;
  icon: string;
  description?: string;
  status: number;
  sort_order: number;
  platforms: string;
  tags?: string;
  access_restriction?: string;
}

export interface UpdateGameParams extends CreateGameParams {
  id: number;
}

export interface DeleteGameParams {
  id: number;
}

export interface BatchDeleteGameParams {
  ids: number[];
}

/**
 * 获取游戏列表
 */
export function fetchGetGameList(params: GameListParams) {
  return request({
    url: '/backend/game/list',
    method: 'post',
    data: params
  });
}

/**
 * 获取游戏列表id
 */
export function fetchGetGameListId(params: GameListParams) {
  return request({
    url: '/backend/game/all_list_id',
    method: 'post',
    data: params
  });
}
/**
 * 创建游戏
 */
export function fetchCreateGame(data: CreateGameParams) {
  return request({
    url: '/backend/game/create',
    method: 'post',
    data
  });
}

/**
 * 更新游戏
 */
export function fetchUpdateGame(data: UpdateGameParams) {
  return request({
    url: '/backend/game/update',
    method: 'post',
    data
  });
}

/**
 * 删除游戏
 */
export function deleteGame(params: DeleteGameParams) {
  return request({
    url: '/backend/game/delete',
    method: 'post',
    params
  });
}

/**
 * 批量删除游戏
 */
export function batchDeleteGame(data: BatchDeleteGameParams) {
  return request({
    url: '/backend/game/batchDelete',
    method: 'post',
    data
  });
}


/**
 * 批量删除游戏
 */
export function getTransactionList(params: TransactionListParams) {
  return request({
    url: '/backend/game/getTransactionList',
    method: 'get',
    params
  });
}

export function batchUpdateGameStatus(data: { status: number; game_ids: number[] }) {
  return request({
    url: '/backend/game/batchUpdateStatus',
    method: 'post',
    data
  });
}

export function batchUpdateGameType(data: { game_type: string; game_ids: number[] }) {
  return request({
    url: '/backend/game/batchUpdateType',
    method: 'post',
    data
  });
}

// 获取游戏类型列表
export function fetchGetGameTypes(params: any) {
  request({
    url: '/backend/game/getGameTypes',
    method: 'get',
    params
  });
}
