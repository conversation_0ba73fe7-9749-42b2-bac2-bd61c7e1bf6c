/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-19 10:47:29
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-07-23 14:29:10
 * @FilePath: \betdoce-admind:\new_project\betdoce-web\src\api\user.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { RequestBody } from "alova";
import request from "./request";
export interface levelsResponse {
  slice?(arg0: number, arg1: number): unknown;
  level?: number;
  name?: string;
  max_withdrawal?: number;
  withdrawal_count?: number;
  monthly_charge?: number;
  monthly_bet?: number;
  bgColor?: string;
}

// vip等级列表
export const vipLevels = () => {
  return request.Get<[levelsResponse]>("/vip/levels", {});
};
// 添加银行卡
export const paymentAccount = (data: RequestBody | { cpf: string; pix: string; name: string; } | undefined) => {
  return request.Post("/user/paymentAccount", data);
};

// 获取已绑银行卡列表
export const bindPaymentAccountList = () => {
  return request.Get("/user/paymentAccountList", {params:{date:new Date()}});
};

// 获取vip进度表
export const vipProgress = () => {
  return request.Get("/user/vipProgress", {});
};