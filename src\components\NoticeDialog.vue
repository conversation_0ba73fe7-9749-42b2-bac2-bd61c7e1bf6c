<template>
  <v-dialog v-model="isOpen" max-width="500">
    <v-card class="notice-dialog">
      <v-card-title class="d-flex justify-space-between align-center pa-0 pl-4">
        {{ notice.subject }}
        <v-btn icon variant="text" @click="close">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text class="mt-4">
        <div class="notice-content">{{ notice.content }}</div>
        <div class="notice-time mt-4">{{ formatDate(notice.created_at) }}</div>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { formatDate } from '@/utils/date';
import type { Notice } from '@/types/notice';

const props = defineProps<{
  notice: Notice;
}>();

const emit = defineEmits<{
  (e: 'close'): void;
}>();

const isOpen = ref(true);

const close = () => {
  isOpen.value = false;
  emit('close');
};

watch(() => props.notice, () => {
  isOpen.value = true;
});
</script>

<style scoped lang="scss">
.notice-dialog {
  background: #202D60;
  color: #fff;
  
  :deep(.v-card-title) {
    color: #fff;
    font-size: 18px;
    padding: 16px;
  }
  
  :deep(.v-card-text) {
    padding: 16px;
  }
}

.notice-content {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  white-space: pre-wrap;
}

.notice-time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
}
</style> 