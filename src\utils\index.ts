export * from "./precision";
export * from "./date";
export * from "./validator";
export * from "./types";
import md5 from "blueimp-md5";
/**
 * 参数归一化接口定义
 */
export interface NormalizeParams {
  [key: string]: any;
}

export type ResponseData<T> = T extends never[] ? null : T;

export interface NormalizeResponse<T = any> {
  status_code: number;
  error_code: string;
  data: T;
  count?: number;
}

/**
 * 检查响应数据格式是否符合标准
 */
const isValidResponse = (data: unknown): data is NormalizeResponse => {
  return (
    data !== null &&
    typeof data === "object" &&
    "status_code" in data &&
    "error_code" in data &&
    "data" in data &&
    typeof (data as NormalizeResponse).status_code === "number" &&
    typeof (data as NormalizeResponse).error_code === "string"
  );
};

/**
 * 请求参数归一化
 * @param {NormalizeParams} params 原始参数
 * @returns {NormalizeParams} 归一化后的参数
 */
export const normalizeRequestParams = (
  params: NormalizeParams
): NormalizeParams => {
  if (!params || typeof params !== "object") {
    return {};
  }

  return Object.entries(params).reduce((acc: NormalizeParams, [key, value]) => {
    // 跳过 undefined 和 null
    if (value === undefined || value === null) {
      return acc;
    }

    // 处理数组
    if (Array.isArray(value)) {
      const filtered = value.filter(
        (item) => item !== undefined && item !== null
      );
      if (filtered.length > 0) {
        acc[key] = filtered;
      }
      return acc;
    }

    // 处理对象
    if (typeof value === "object") {
      const normalized = normalizeRequestParams(value);
      if (Object.keys(normalized).length > 0) {
        acc[key] = normalized;
      }
      return acc;
    }

    // 处理字符串
    if (typeof value === "string") {
      acc[key] = value.trim();
      return acc;
    }

    // 其他类型直接赋值
    acc[key] = value;
    return acc;
  }, {});
};

/**
 * 响应数据归一化
 * @template T
 * @param {unknown} data 原始响应数据
 * @returns {NormalizeResponse<T>} 归一化后的响应数据
 */
export const normalizeResponseData = <T>(
  data: unknown
): NormalizeResponse<T> => {
  // 如果已经是标准格式，直接返回
  if (isValidResponse(data)) {
    return data as NormalizeResponse<T>;
  }

  // 转换为标准格式
  return {
    status_code: 200,
    error_code: "",
    data: data as T,
    count: undefined,
  };
};

/**
 * 错误响应归一化
 */
export const normalizeErrorResponse = (
  error: Error | string
): NormalizeResponse => {
  const errorMessage = error instanceof Error ? error.message : error;
  return {
    status_code: 500,
    error_code: errorMessage,
    data: null,
    count: undefined,
  };
};

/**
 * 防抖函数
 * @param {F} fn
 * @param {number} delay
 * @returns {F}
 */
export const debounce = <F extends (...args: any[]) => any>(
  fn: F,
  delay: number = 300
): F => {
  let timer: NodeJS.Timeout | null = null;

  return function (this: any, ...args: Parameters<F>) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  } as F;
};

/**
 * 节流函数
 * @param {F} fn
 * @param {number} delay
 * @returns {F}
 */
export const throttle = <F extends (...args: any[]) => any>(
  fn: F,
  delay: number = 300
): F => {
  let last = 0;

  return function (this: any, ...args: Parameters<F>) {
    const now = Date.now();
    if (now - last > delay) {
      fn.apply(this, args);
      last = now;
    }
  } as F;
};

/**
 * 错误消息多语言
 */
export const errorMessages = {
  required: "Este campo é obrigatório",
  email: "Por favor, insira um e-mail válido",
  phone: "Por favor, insira um número de telefone válido",
  number: "Por favor, insira um número válido",
  url: "Por favor, insira uma URL válida",
  minLength: "O comprimento mínimo é {0}",
  maxLength: "O comprimento máximo é {0}",
  passwordMismatch: "As senhas não coincidem",
  invalidFormat: "Formato inválido",
};

function setMeta(attr: "name" | "property", key: string, value: string) {
  let meta = document.querySelector(`meta[${attr}="${key}"]`);
  if (!meta) {
    meta = document.createElement("meta");
    meta.setAttribute(attr, key);
    document.head.appendChild(meta);
  }
  meta.setAttribute("content", value);
}

/**
 * 更新页面元标签（兼容 @vueuse/head）
 * @param url 页面URL
 * @param image 图片URL
 * @param title 页面标题
 * @param description 页面描述
 */
export function updateMetaTags(
  url: string,
  image?: string,
  title?: string,
  description?: string
) {
  // // og 标签
  if (title) setMeta("property", "og:title", title);
  if (description) setMeta("property", "og:description", description);
  if (image) setMeta("property", "og:image", image);
  setMeta("property", "og:url", url);

  // // twitter 标签
  if (title) setMeta("name", "twitter:title", title);
  if (description) setMeta("name", "twitter:description", description);
  if (image) setMeta("name", "twitter:image", image);
  setMeta("name", "twitter:url", url);
}


/**
 * MD5 加密函数
 * @param {string} value 需要加密的字符串
 * @returns {string} 加密后的 MD5 字符串
 */
export function md5Encrypt(value: string): string {
  return md5(value);
}

// 格式化數字，支援自定義配置
export const formatNumber = (num: number) => {
  // 檢查是否為 NaN
  if (isNaN(num)) {
    return "0.00";
  }

  const decimals = 2,
    thousandsSeparator = ",",
    decimalSeparator = ".",
    locale = "en-US";

  // 使用 Intl.NumberFormat 進行格式化，啟用千分位分組，並指定 locale
  const formatter = new Intl.NumberFormat(locale, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
    useGrouping: true, // 啟用千分位分組
  });

  // 直接返回 Intl.NumberFormat 格式化後的結果
  return  formatter.format(num);
};