import { request } from '../request';

/**
 * 获取策略
 */
export function fetchGetGameStrategy(params?: any) {
  return request({
    url: '/backend/gameStrategy/all',
    method: 'get',
    params
  });
}


/**
 * 获取策略操作日志
 */
export function fetchGetGameStrategyLogs(params?: any) {
  return request({
    url: '/backend/gameStrategy/logs',
    method: 'get',
    params
  });
}


/**
 * 批量更改策略
 */
export function batchUpdate(data: any) {
  return request({
    url: '/backend/gameStrategy/batchUpdate',
    method: 'post',
    data
  });
}