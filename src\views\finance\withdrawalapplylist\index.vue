<script setup lang="tsx">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  fetchWithdrawalApplyList,
  updateWithdrawalApplyStatus,
} from "@/service/api/withdrawalApply";
import {
  getWithdrawalConfig,
  getWithdrawalTotal,
} from "@/service/api/withdrawal";
import moment from "moment";
import type {
  WithdrawalConfigResponse,
  WithdrawalConfigParams,
  WhitelistWithdrawalConfigParams,
  WithdrawalModeConfigParams,
} from "@/typings/withdrawal";
import { useAuth } from "@/hooks/business/auth";
import { getBrazilDate } from "@/utils/format";

const { hasAuth } = useAuth();

const config = ref<WithdrawalConfigResponse>({} as WithdrawalConfigResponse);
// 计算属性：当前模式
const currentMode = computed(() => {
  return config.value.FastTrackAmountThreshold > 0
    ? "快速免审模式"
    : "人工审核模式";
});
// 计算属性：今日总额（这里需要从其他地方获取）
const todayTotal = ref(0);

// 格式化金额
const formatAmount = (amount: number) => {
  return (amount / 100).toLocaleString("pt-BR", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

// 状态映射
const statusMap = {
  0: { label: "待审核", type: "warning" },
  1: { label: "已批准", type: "success" },
  2: { label: "处理中", type: "info" },
  3: { label: "已完成", type: "primary" },
  4: { label: "已拒绝", type: "danger" },
};

const statusClassMap = {
  0: "status-pending", // 待审核
  1: "status-approved", // 已批准
  2: "status-processing", // 处理中
  3: "status-finished", // 已完成
  4: "status-rejected", // 已拒绝
};

// 搜索表单
const searchForm = reactive({
  uuid: "",
  status: "",
  is_withdrawal: "",
  transaction_no: "",
  order_no: "",
});

const statusOptions = [
  { label: "所有状态", value: "" },
  { label: "待审核", value: 0 },
  { label: "已批准", value: 1 },
  { label: "处理中", value: 2 },
  { label: "已完成", value: 3 },
  { label: "已拒绝", value: 4 },
];

const isFirstOptions = [
  { label: "全部", value: "" },
  { label: "是", value: 1 },
  { label: "否", value: 0 },
];

// 数据
const tableData = ref<any[]>([]);
const loading = ref(false);
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);

// 弹窗相关
const approveDialogVisible = ref(false);
const rejectDialogVisible = ref(false);
const currentRow = ref<any>(null);
const rejectReason = ref("");
const rejectReasonOptions = [
  "银行卡账户信息不匹配",
  "涉嫌违规操作",
  "提现金额异常",
  "其他原因",
];

const detailDrawerVisible = ref(false);
const detailRow = ref<any>(null);

// 获取数据
async function getData() {
  loading.value = true;
  try {
    const { response } = await fetchWithdrawalApplyList({
      ...searchForm,
      page: page.value,
      size: pageSize.value,
    });
    console.log(response);
    tableData.value = response?.data.data || [];
    total.value = response?.data.count || 0;
  } finally {
    loading.value = false;
  }
}

function handleSearch() {
  page.value = 1;
  getData();
}

function handleReset() {
  searchForm.uuid = "";
  searchForm.status = "";
  searchForm.is_withdrawal = "";
  searchForm.transaction_no = "";
  searchForm.order_no = "";
  handleSearch();
}

function openApproveDialog(row: any) {
  currentRow.value = row;
  approveDialogVisible.value = true;
}

function openRejectDialog(row: any) {
  currentRow.value = row;
  rejectReason.value = "";
  rejectDialogVisible.value = true;
}

function openDetailDrawer(row: any) {
  detailRow.value = row;
  detailDrawerVisible.value = true;
}

async function handleApprove() {
  if (!currentRow.value) return;
  const { id } = currentRow.value;
  try {
    await updateWithdrawalApplyStatus({ id, status: 1 });
    ElMessage.success("批准成功");
    approveDialogVisible.value = false;
    getData();
  } catch (e) {
    ElMessage.error("操作失败");
  }
}

async function handleReject() {
  if (!currentRow.value) return;
  if (!rejectReason.value) {
    ElMessage.warning("请选择拒绝原因");
    return;
  }
  const { id } = currentRow.value;
  try {
    await updateWithdrawalApplyStatus({
      id,
      status: 4,
      reason: rejectReason.value,
    });
    ElMessage.success("已拒绝");
    rejectDialogVisible.value = false;
    getData();
  } catch (e) {
    ElMessage.error("操作失败");
  }
}

// 获取配置
const getConfig = async () => {
  loading.value = true;
  try {
    const res = await getWithdrawalConfig();
    const total = await getWithdrawalTotal();
    config.value = res.data?.data;
    todayTotal.value = total.data?.data?.total_amount || 0;
  } catch (error) {
    console.error("获取配置失败:", error);
    ElMessage.error("获取配置失败");
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  getData();
  getConfig();
});
</script>

<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <!-- 顶部信息 -->
    <ElAlert
      type="info"
      show-icon
      :closable="false"
      class="mode-alert"
      style="margin-bottom: 5px"
    >
      当前系统提现模式：<b style="color: #1890ff">{{ currentMode }}</b>
      （今日提现总额：<b style="color: #1890ff"
        >{{ formatAmount(todayTotal) }} BRL</b
      >）
    </ElAlert>
    <!-- 搜索栏 -->
    <div class="search-wrapper">
      <ElForm :inline="true" :model="searchForm" class="search-form">
        <ElRow :gutter="16">
          <ElCol :span="5">
            <ElFormItem label="用户ID">
              <ElInput
                v-model="searchForm.uuid"
                placeholder="用户ID"
                clearable
                style="width: 100%"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="5">
            <ElFormItem label="状态">
              <ElSelect
                v-model="searchForm.status"
                placeholder="所有状态"
                clearable
                style="width: 100%"
              >
                <ElOption
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="5">
            <ElFormItem label="是否首提">
              <ElSelect
                v-model="searchForm.is_withdrawal"
                placeholder="是否首提"
                clearable
                style="width: 100%"
              >
                <ElOption
                  v-for="item in isFirstOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="5">
            <ElFormItem label="交易流水号">
              <ElInput
                v-model="searchForm.transaction_no"
                placeholder="交易流水号"
                clearable
                style="width: 100%"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="5">
            <ElFormItem label="订单号">
              <ElInput
                v-model="searchForm.order_no"
                placeholder="订单号"
                clearable
                style="width: 100%"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="4" class="flex-end">
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElCol>
        </ElRow>
      </ElForm>
    </div>
    <!-- 表格卡片 -->
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-60px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full main-table"
          :data="tableData"
          row-key="id"
        >
          <ElTableColumn prop="id" label="ID" width="60" />
          <ElTableColumn prop="uuid" label="用户ID" />
          <ElTableColumn prop="withdrawal_amount" label="提现金额" width="110">
            <template #default="{ row }">
              R${{ row.withdrawal_amount / 100 }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="payment_method" label="收款方式" width="100" />
          <ElTableColumn prop="is_withdrawal" label="是否首次提现" width="110">
            <template #default="{ row }">
              <ElTag :type="row.is_withdrawal ? 'success' : 'info'">{{
                row.is_withdrawal ? "是" : "否"
              }}</ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="transaction_number" label="交易流水号" />
          <ElTableColumn prop="order_number" label="订单号" />
          <ElTableColumn prop="created_at" label="申请时间">
            <template #default="{ row }">
              {{ moment(getBrazilDate(row.created_at)).format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </ElTableColumn>
          <!-- <ElTableColumn prop="status" label="状态" width="100">
            <template #default="{ row }">
              <ElTag :type="statusMap[row.status]?.type">{{ statusMap[row.status]?.label }}</ElTag>
            </template>
          </ElTableColumn> -->

          <ElTableColumn prop="status" label="状态" width="100">
            <template #default="{ row }">
              <span :class="['status-span', statusClassMap[row.status]]">
                {{ statusMap[row.status]?.label }}
              </span>
            </template>
          </ElTableColumn>

          <ElTableColumn label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <ElButton
                v-if="row.status === 0 && hasAuth(2)"
                type="success"
                size="small"
                @click="openApproveDialog(row)"
                >批准</ElButton
              >
              <ElButton
                v-if="row.status === 0 && hasAuth(2)"
                type="danger"
                size="small"
                @click="openRejectDialog(row)"
                >拒绝</ElButton
              >
              <ElButton
                v-if="hasAuth(4)"
                type="primary"
                size="small"
                @click="openDetailDrawer(row)"
                >详情</ElButton
              >
            </template>
          </ElTableColumn>
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          background
          v-if="total > 0"
          layout="total,prev,pager,next,sizes"
          :total="total"
          :current-page="page"
          :page-size="pageSize"
          @update:current-page="
            (val) => {
              page = val;
              getData();
            }
          "
          @update:page-size="
            (val) => {
              pageSize = val;
              page = 1;
              getData();
            }
          "
        />
      </div>
      <!-- 批准Drawer -->
      <ElDrawer
        v-model="approveDialogVisible"
        title="批准提现申请"
        size="400px"
        direction="rtl"
        append-to-body
      >
        <div v-if="currentRow">
          <ElForm label-width="90px" class="dialog-info-form">
            <ElFormItem label="申请ID">{{ currentRow.id }}</ElFormItem>
            <ElFormItem label="用户ID">{{ currentRow.uuid }}</ElFormItem>
            <ElFormItem label="提现金额"
              >R${{ currentRow.withdrawal_amount / 100 }}</ElFormItem
            >
            <ElFormItem label="收款方式">{{
              currentRow.payment_method
            }}</ElFormItem>
          </ElForm>
        </div>
        <template #footer>
          <div class="drawer-footer">
            <ElButton @click="approveDialogVisible = false">取消</ElButton>
            <ElButton type="success" @click="handleApprove">确认批准</ElButton>
          </div>
        </template>
      </ElDrawer>
      <!-- 拒绝Drawer -->
      <ElDrawer
        v-model="rejectDialogVisible"
        title="拒绝提现申请"
        size="400px"
        direction="rtl"
        append-to-body
      >
        <div v-if="currentRow">
          <ElForm label-width="90px" class="dialog-info-form">
            <ElFormItem label="申请ID">{{ currentRow.id }}</ElFormItem>
            <ElFormItem label="用户ID">{{ currentRow.uuid }}</ElFormItem>
            <ElFormItem label="提现金额"
              >R${{ currentRow.withdrawal_amount / 100 }}</ElFormItem
            >
            <ElFormItem label="收款方式">{{
              currentRow.payment_method
            }}</ElFormItem>
          </ElForm>
        </div>
        <ElForm class="mt-2">
          <ElFormItem label="拒绝原因">
            <ElSelect
              v-model="rejectReason"
              placeholder="请选择拒绝原因"
              style="width: 100%"
            >
              <ElOption
                v-for="item in rejectReasonOptions"
                :key="item"
                :label="item"
                :value="item"
              />
            </ElSelect>
          </ElFormItem>
        </ElForm>
        <template #footer>
          <div class="drawer-footer">
            <ElButton @click="rejectDialogVisible = false">取消</ElButton>
            <ElButton type="danger" @click="handleReject">确认拒绝</ElButton>
          </div>
        </template>
      </ElDrawer>
      <!-- 详情抽屉 -->
      <ElDrawer
        v-model="detailDrawerVisible"
        title="提现申请详情"
        width="400px"
        :close-on-click-modal="false"
        append-to-body
      >
        <div v-if="detailRow">
          <ElForm label-width="110px" class="dialog-info-form">
            <ElFormItem label="申请ID">{{ detailRow.id }}</ElFormItem>
            <ElFormItem label="用户ID">{{ detailRow.uuid }}</ElFormItem>
            <ElFormItem label="提现金额"
              >R${{ detailRow.withdrawal_amount / 100 }}</ElFormItem
            >
            <ElFormItem label="收款方式">{{
              detailRow.payment_method
            }}</ElFormItem>
            <ElFormItem label="是否首次提现">{{
              detailRow.is_withdrawal ? "是" : "否"
            }}</ElFormItem>
            <ElFormItem label="交易流水号">{{
              detailRow.transaction_number
            }}</ElFormItem>
            <ElFormItem label="订单号">{{ detailRow.order_number }}</ElFormItem>
            <ElFormItem label="申请时间">
              {{
                moment(getBrazilDate(detailRow.created_at)).format("YYYY-MM-DD HH:mm:ss")
              }}</ElFormItem
            >
            <!-- <ElFormItem label="状态">
              <ElTag :type="statusMap[detailRow.status]?.type">{{ statusMap[detailRow.status]?.label }}</ElTag>
            </ElFormItem> -->
            <ElFormItem label="状态">
              <div class="flex items-center">
                <span
                  :class="['status-span', statusClassMap[detailRow.status]]"
                >
                  {{ statusMap[detailRow.status]?.label }}
                </span>
              </div>
            </ElFormItem>
            <ElFormItem
              label="原因"
              v-if="detailRow.status === 4 && detailRow.reason"
            >
              {{ detailRow.reason }}
            </ElFormItem>
          </ElForm>
        </div>
      </ElDrawer>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
}

.sm\:flex-1-hidden {
  flex: 1;
  min-height: 0;
}

.card-wrapper {
  min-height: 400px;
}

.status-span {
  display: inline-block;
  padding: 0px 12px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  color: #fff;
}
.status-pending {
  background: #909399;
} // info
.status-approved {
  background: #67c23a;
} // success
.status-processing {
  background: #e6a23c;
} // warning
.status-finished {
  background: #409eff;
} // primary
.status-rejected {
  background: #f56c6c;
} // danger
</style>
