---
sidebar_position: 5
---

import Image from '@theme/IdealImage';
import bundler from './img/bundler-white.png';
import entrypoint from './img/entrypoint-white.png';
import paymaster from './img/paymaster-white.png';
import normalTx from './img/normal-tx-white.png';
import tx4337 from './img/4337tx-white.png';

# 兼容 ERC-4337

## 什么是 ERC-4337?

目前智能合约钱包缺乏规范的标准，各家钱包都按照各自需求实现交易组装和 gas 代付机制等功能，因此智能合约钱包之间的用户很难进行迁移。为此，我们需要制定一个统一的标准来规范智能合约钱包。

[**ERC-4337**](https://eips.ethereum.org/EIPS/eip-4337) 是 Vitalik 等人提出的以太坊改进提案，其目的是实现账户抽象，且无需更改以太坊共识层协议。

ERC-4337 定义了一系列新的规范。例如，将交易转变为 `UserOperation` 对象。通过 `Bundler`，可以将多个用户的意图、签名和其他数据打包，传递至 `Entry Point` 以进行批量验证和执行交易。引入 `Paymaster` 机制，实现去中心化的交易手续费代付功能。

ERC-4337 整体协议非常复杂。为了帮助大家理解整个流程，我们将其拆分成几个部分：

### UserOperation & Bundler

<Image img={bundler} />

首先，让我们了解 ERC-4337 交易发送的整个流程。

合约钱包用户发送交易，我们把合约钱包用户签名的交易称为 `UserOperation`。虽然 UserOperation 在结构上略有区别于 EOA 签名的交易，但我们可以简单地将两者视为等价。

为了将 `UserOperation` 提交至链上，合约需要借助第三方角色 `Bundler`。`Bundler` 类似于 User 和矿工之间的中介人，帮助将 `UserOperation` 批量打包成交易并提交至链上。

详细流程如下：

1. 用户对一笔交易进行签名（称之为 UserOperation）。
2. 签名交易将被发送到 UserOperation Mempool（也称为最新的 ERC-4337 协议中的 Alternative Mempool），它类似于 Tx Mempool。
3. Bundler 会从中挑选交易，并将其捆绑成一笔 Bundle Transaction。
4. 然后，Bundler 将此交易提交到链上。（实际执行过程还涉及 Entry Point 的执行和验证，我们将在下面继续展开）。

### Entry Point

<Image img={entrypoint} />

然后，让我们简单了解一下交易提交至链上后是如何进行验证和执行的。Entry Point Smart Contract（简称 Entry Point）是 `UserOperation` 调用验证的统一入口。 `Bundler` 会调用 `Entry Point` 中的 handleOps 函数，将交易提交至链上的 `Entry Point` 进行验证和执行。

当然，为了提高交易上链的成功率，`Bundler` 在捆绑好 Bundle Transaction 之后，会先在本地使用 RPC Call 调用 `Entry Point` 中的 simulateValidation()。这样做可以确保这些 `UserOperation` 的签名没有问题以及 Gas Fee 可以正常支付。

### Paymaster

<Image img={paymaster} />

最后，我们将详细介绍 `Bundler` 在调用 Entry Point 中的 `handleOps` 函数后会执行的操作。此外，我们还将介绍一个非常重要的角色 `Paymaster`。`Paymaster` 是帮助用户支付 gas 费用的角色，它可以实现用户可使用任意代币支付 gas 的功能。

详细流程如下：

1. Bundler 调用 Entry Point 中的 handleOps 函数。
2. EntryPoint 将 UserOperation 作为参数调用 UniPass Contract 中的 validateUserOp，验证所有需要验证的交易。只有在验证成功的情况下才会继续执行后续操作。
3. 此时 Entry Point 会先确认 UserOperation 中指定的 Paymaster 的状况，例如是否拥有足够的 ETH 来支付这笔交易的手续费。
4. 接着，Entry Point 调用 Paymaster Contract 中的 validatePaymasterUserOp，确认 Paymaster 愿意支付这笔交易的手续费。如果 Paymaster 愿意支付，那么这笔交易就会继续，否则就会失败。
5. 然后调用 UniPass Contract 并执行 UserOperation 本身指定的内容。
6. 接着调用 Paymaster Contract 的 Post-Op，Post-Op 处理直接赞助或划扣用户 ERC20 代币代付等自定义逻辑。
7. Entry Point 在 Paymaster 质押的 ETH 余额中扣减交易所需支付的 gas fee。
8. 最后 Entry Point 收集所有交易所需支付的 gas fee 总额 refund 给 Bundler ，交易执行完毕。

:::tip 📌 关于 Paymaster 为这笔交易支付 gas，可能有以下几种情况：
1. Paymaster 赞助了本次交易的 gas fee
2. Paymaster 扣除了用户在 Paymaster 中预存质押的 ETH/ERC20 代币或扣除了用户授权给 Paymaster 的 ERC20 代币
:::

## 在 UniPass Wallet 中开启 4337 Module

UniPass Wallet 是基于 UniPass Contract 开发的一个低门槛的支持社交恢复的钱包。目前已经支持开启 4337 Module，您可以在[如何在 UniPass Wallet 中开启 4337 Module](../wallet/06-erc-4337.md)查看相关介绍。