<script setup lang="tsx">
import { ElButton } from 'element-plus';
import { getShortUrlList } from '@/service/api/shorturl';
import { useTable } from '@/hooks/common/table';
import moment from 'moment';
import { getBrazilDate } from '@/utils/format';

// 定义搜索参数接口
interface SearchParams {
  current?: number;
  size?: number;
  sort?: string;
}

// 表格Hook配置
const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: getShortUrlList,
  apiParams: {
    page: 1,
    size: 20,
    sort: '-id'
  } as SearchParams,
  columns: () => [
    { prop: 'index', label: '序号', width: 64 },
    { prop: 'key', label: '活动标识', minWidth: 120, showOverflowTooltip: true },
    { prop: 'short_link', label: '短链接', minWidth: 180, showOverflowTooltip: true },
    { prop: 'origin_link', label: '原始链接', minWidth: 300, showOverflowTooltip: true },
    { prop: 'pv', label: '访问量', width: 100 },
    { prop: 'uv', label: '独立访客', width: 100 },
    {
      prop: 'create_at',
      label: '创建时间',
      width: 180,
      formatter: (row:any) =>moment(getBrazilDate(row?.create_at)).format("YYYY-MM-DD HH:mm:ss") 
    }
  ]
});

defineOptions({ name: 'InvitationManage' });
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto">
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>短链列表</p>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :loading="loading"
            @refresh="getData"
          >
            <span style="width: 1px;height: 35px;background: #e5e6eb;"></span>
          </TableHeaderOperation>
        </div>
      </template>

      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>

        <div class="mt-20px flex justify-start">
          <ElPagination
            v-if="mobilePagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="mobilePagination"
            @current-change="mobilePagination['current-change']"
            @size-change="mobilePagination['size-change']"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border: none;
  border-radius: 4px;
  .ht50 {
    height: calc(100% - 50px);
  }
}

.search-card {
  :deep(.el-form--inline) {
    .el-form-item {
      margin-right: 16px;
      margin-bottom: 16px;

      &:last-child {
        margin-right: 0;
      }

      .el-input,
      .el-select,
      .el-date-editor {
        width: 200px;
      }
    }
  }
}
</style>
