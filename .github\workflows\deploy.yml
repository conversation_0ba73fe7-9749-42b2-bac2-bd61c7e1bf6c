name: Test Environment CI/CD

on:
  push:
    branches: [ "develop" ]
  pull_request:
    branches: [ "develop" ]

env:
  DOCKER_IMAGE: ${{ secrets.DOCKERHUB_USERNAME }}/betdoce-admin
  TEST_SERVER_HOST: ***********
  TEST_SERVER_PORT: 12321
  PROJECT_PATH: /project
  APP_PORT: 8090

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Install dependencies
        run: pnpm install

      - name: Build application
        run: pnpm build
        env:
          NODE_ENV: production

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: |
            ${{ secrets.DOCKERHUB_USERNAME }}/betdoce-admin:latest
            ${{ secrets.DOCKERHUB_USERNAME }}/betdoce-admin:test-${{ github.sha }}

      - name: Deploy to test server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ env.TEST_SERVER_HOST }}
          username: ${{ secrets.TEST_SERVER_USER }}
          key: ${{ secrets.TEST_SERVER_SSH_KEY }}
          port: ${{ env.TEST_SERVER_PORT }}
          script: |
            mkdir -p ${{ env.PROJECT_PATH }}
            cd ${{ env.PROJECT_PATH }}
            docker stop test-app || true
            docker rm test-app || true
            IMAGE="${{ secrets.DOCKERHUB_USERNAME }}/soybean-admin:test-${{ github.sha }}"
            docker pull $IMAGE
            docker run -d --name test-app \
              -p ${{ env.APP_PORT }}:80 \
              -v ${{ env.PROJECT_PATH }}:/usr/share/nginx/html \
              --restart unless-stopped \
              $IMAGE
