<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <ManufacturerSearch
      v-model:model="searchParams"
      @reset="resetSearchParams"
      @search="getDataByPage"
    >
      <template #table-operation>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
    </ManufacturerSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-55px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="checkedRowKeys = $event.map((row) => row.id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
      <ManufacturerOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </ElCard>
  </div>
</template>

<script setup lang="tsx">
import { tagMap, statusOptions } from "@/constants/common";
import { ElButton, ElPopconfirm, ElTag, ElImage } from "element-plus";
import { useTable, useTableOperate } from "@/hooks/common/table";
import {
  fetchGetManufacturerList,
  fetchDeleteManufacturer,
} from "@/service/api/manufacturer";
import ManufacturerSearch from "./modules/manufacturer-search.vue";
import ManufacturerOperateDrawer from "./modules/manufacturer-operate-drawer.vue";
import { useAuth } from "@/hooks/business/auth";
import { useI18n } from "vue-i18n";
import { ref } from "vue";

const { t } = useI18n();
const { hasAuth } = useAuth();

defineOptions({ name: "ManufacturerManage" });

// 游戏类型选项
const gameTypeOptions = [
  { label: "赠金游戏", value: "BonusSlots", labelValue: "BonusSlots" },
  { label: "热门游戏", value: "hot", labelValue: "hot" },
  { label: "老虎机", value: "slots", labelValue: "slots" },
  { label: "真人", value: "livecasino", labelValue: "livecasino" },
  { label: "卡牌", value: "Cartoes", labelValue: "Pôquer" },
  { label: "捕鱼", value: "Pescaria", labelValue: "Pescaria" },
  { label: "电玩", value: "Coluna", labelValue: "Coluna" },
];
const channelMap = {
  1: "灰度Gaming",
  2: "PG",
  3: "OMG总代",
};
const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  columnChecks,
} = useTable({
  apiFn: fetchGetManufacturerList,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 20,
    status: undefined,
    name: undefined,
    channel: undefined,
    game_manufacturer_types: undefined,
  },
  columns: () => [
    { type: "selection", width: 48 },
    { prop: "index", label: "序号", width: 64 },
    { prop: "name", label: "厂商名称", minWidth: 150 },
    {
      prop: "icon",
      label: "图标",
      width: 100,
      formatter: (row) => (
        <ElImage
          preview-teleported="body"
          src={row.icon}
          preview-src-list={[row.icon]}
          fit="cover"
          class="h-40px"
        />
      ),
    },
    { prop: "devices", label: "设备", minWidth: 150 },
    {
      prop: "channel",
      label: "渠道",
      minWidth: 150,
      formatter: (row: any) => channelMap[row.channel] || row.channel,
    },
    { prop: "sort_order", label: "排序", width: 100 },
    {
      prop: "game_manufacturer_type",
      label: "游戏类型",
      minWidth: 120,
      formatter: (row: any) => {
        if (!row.game_manufacturer_type) return "-";

        // 按逗号分割成数组
        const gameTypes = row.game_manufacturer_type.split(",");

        // 循环查找对应的标签并拼接
        const labels = gameTypes.map((type) => {
          const option = gameTypeOptions.find(
            (item) => item.value === type.trim(),
          );
          return option ? option.label : type.trim();
        });

        return labels.join(", ");
      },
    },
    {
      prop: "status",
      label: "状态",
      width: 100,
      formatter: (row: any) => {
        if (row.status === undefined) {
          return "";
        }
        const label = statusOptions.find(
          (item) => item.value === row.status,
        )?.label;
        return <ElTag type={tagMap[row.status]}>{label}</ElTag>;
      },
    },
    {
      width: 140,
      prop: "operate",
      label: "操作",
      align: "center",
      formatter: (row: any) => (
        <div class="flex-center">
          {hasAuth(3) && (
            <ElButton
              type="primary"
              plain
              size="small"
              onClick={() => edit(row.id)}
            >
              {t("common.edit")}
            </ElButton>
          )}
          {hasAuth(2) && (
            <ElPopconfirm
              title={t("common.confirmDelete")}
              onConfirm={() => handleDelete(row.id)}
            >
              {{
                reference: () => (
                  <ElButton type="danger" plain size="small">
                    {t("common.delete")}
                  </ElButton>
                ),
              }}
            </ElPopconfirm>
          )}
        </div>
      ),
    },
  ],
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  onDeleted,
  checkedRowKeys,
} = useTableOperate(data, getData, "id");

async function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.warning("请选择要删除的厂商");
    return;
  }
  console.log(checkedRowKeys.value);
  handleDelete(checkedRowKeys.value.join(","));
}

async function handleDelete(id: number | string) {
  const { error } = await fetchDeleteManufacturer({ id: id.toString() });
  if (!error) {
    window.$message?.success("删除成功");
    getData();
  }
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
