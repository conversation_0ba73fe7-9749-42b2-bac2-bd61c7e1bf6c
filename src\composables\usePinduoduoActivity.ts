import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import {
  getActivityDetails,
  joinPddActivity,
  closeTask,
  copyCheck,
} from "@/api/activity";
import { enterPddActivity, type PddActivityContent } from "@/api/home";
import { showError, showSuccess, showWarning } from "@/utils/toast";
import { updateMetaTags } from "@/utils";

/**
 * 活动相关公共逻辑 composable
 * @param options.isDialog 是否为弹窗模式
 * @param options.onClose 弹窗关闭回调
 */
export function usePinduoduoActivity(options?: {
  isDialog?: boolean;
  onClose?: () => void;
}) {
  // 规则弹窗
  const showRule = ref(false);
  function handleRuleDialogClose(val: any) {
    showRule.value = val;
  }

  // 状态管理
  const router = useRouter();
  const store = useStore();
  const status = ref("");
  const activityData = ref<PddActivityContent | null>(null);
  const countdown = ref(0);
  const isAnimating = ref(false);
  const activityStatus = ref<any>(null);
  const loading = ref(options?.isDialog ? false : true);
  const isShare = ref(false);
  const rules = ref();

  // 中奖提示相关
  const showWinningAlert = ref(false);
  const winningData = ref<{ message: string }>({ message: "" });

  // 充值倒计时
  const rechargeCountdown = ref(0);
  const rechargeTimer = ref<number | null>(null);

  // 定时器
  const timer = ref<number | null>(null);
  const activityTimer = ref<number | null>(null);
  const winningTimer = ref<number | null>(null);
  // 页面模式下用对象管理
  const timers = options?.isDialog
    ? null
    : {
        activity: null as number | null,
        winning: null as number | null,
        countdown: null as number | null,
      };

  // 计算注册人数（最多2个头像）
  const registerNum = computed(() => {
    const num = activityStatus.value?.current_register_num || 0;
    return num > 2 ? 2 : num;
  });

  // 生成中奖数据
  function generateWinningData() {
    const messages = [
      `Recentemente, ${Math.floor(
        Math.random() * 1000
      )} pessoas sacaram dinheiro com sucesso! Retire seu pacote de presente em dinheiro agora`,
    ];
    winningData.value = {
      message: messages[Math.floor(Math.random() * messages.length)],
    };
  }

  // 中奖提示定时器
  function showWinningAlertWithDelay() {
    const showAlert = () => {
      generateWinningData();
      showWinningAlert.value = true;
      setTimeout(() => {
        showWinningAlert.value = false;
      }, 3000);
    };
    if (options?.isDialog) {
      winningTimer.value = window.setInterval(showAlert, 10000);
    } else if (timers) {
      timers.winning = window.setInterval(showAlert, 10000);
    }
  }
  // 充值条件判断
  function isRechargeRequired() {
    return (
      activityStatus.value?.need_recharge === 1 &&
      activityStatus.value?.target_register_num <=
        activityStatus.value?.current_register_num
    );
  }
  // 活动数据更新
  async function updateActivityData() {
    if (!activityData.value?.invitation_code) return;
    try {
      const res = await getActivityDetails({
        invitation_code: activityData.value.invitation_code,
      });
      activityStatus.value = res;
      countdown.value = Math.max(0, res.end_time - Date.now());
      isShare.value = res.is_copy === 1;
      let newStatus;
      if (res.is_completed === 1) {
        newStatus = "ok";
      } else if (res.need_recharge) {
        newStatus =
          res.current_recharge_amount >= res.must_recharge_amount * 100 &&
          res.current_progress === 100
            ? "ok"
            : "help";
      } else {
        newStatus = res.current_progress === 100 ? "ok" : "help";
      }
      if (newStatus !== status.value) {
        status.value = newStatus;
      }
      // if (res.is_completed !== 3 && status.value !== "ok") {
      //   clearAllTimers();
      //   status.value = "start";
      //   sessionStorage.removeItem("pddActivityData");
      //   const response = (await enterPddActivity()) as PddActivityContent;
      //   activityData.value = response;
      //   return;
      // }
      if (res.recharge_end_time > 0) {
        rechargeCountdown.value = Math.max(
          0,
          res.recharge_end_time - Date.now()
        );
        startRechargeCountdown();
      } else {
        clearRechargeTimer();
        rechargeCountdown.value = 0;
      }
      if (!countdown.value) {
        showWarning(
          "A contagem regressiva do evento terminou. Por favor, participe novamente do evento."
        );
        clearAllTimers();
        loading.value = true;
        await closeTask({
          invitation_code: activityData.value.invitation_code,
        });
        loading.value = false;
        status.value = "start";
        sessionStorage.removeItem("pddActivityData");
        const response = (await enterPddActivity()) as PddActivityContent;
        activityData.value = response;
      }
    } catch (error) {
      console.error("活动数据更新失败:", error);
    }
  }

  // 清理所有定时器
  function clearAllTimers() {
    if (options?.isDialog) {
      [timer, activityTimer, winningTimer].forEach((timerRef) => {
        if (timerRef.value) {
          window.clearInterval(timerRef.value);
          timerRef.value = null;
        }
      });
    } else if (timers) {
      Object.values(timers).forEach((t) => {
        if (t) window.clearInterval(t);
      });
      Object.keys(timers).forEach((key) => {
        timers[key as keyof typeof timers] = null;
      });
    }
    showWinningAlert.value = false;
    clearRechargeTimer();
  }

  // 启动倒计时
  function startCountdownTimer() {
    if (options?.isDialog) {
      if (timer.value) window.clearInterval(timer.value);
      timer.value = window.setInterval(async () => {
        if (countdown.value > 0) {
          countdown.value -= 1000;
        } else {
          await clearAllTimers();
          updateActivityData();
        }
      }, 1000);
    } else if (timers) {
      if (timers.countdown) window.clearInterval(timers.countdown);
      timers.countdown = window.setInterval(async () => {
        if (countdown.value > 0) {
          countdown.value -= 1000;
        } else {
          await clearAllTimers();
          updateActivityData();
        }
      }, 1000);
    }
  }

  // 启动活动数据定时器
  function startActivityUpdateTimer() {
    if (options?.isDialog) {
      if (activityTimer.value) window.clearInterval(activityTimer.value);
      activityTimer.value = window.setInterval(updateActivityData, 30000);
    } else if (timers) {
      if (timers.activity) window.clearInterval(timers.activity);
      timers.activity = window.setInterval(updateActivityData, 30000);
    }
  }

  // 复制到剪贴板 - 移动端优化版本
  async function copyToClipboard(text: string): Promise<boolean> {
    console.log("开始复制内容:", text.substring(0, 100) + "...");
    console.log("完整内容:", text);

    // 检测移动端
    const isMobile =
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      );
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

    console.log("设备信息:", { isMobile, isIOS });

    // 强制清理所有可能的临时元素
    const existingElements = document.querySelectorAll(
      "textarea[data-clipboard-temp], input[data-clipboard-temp]"
    );
    existingElements.forEach((el) => el.remove());

    // 方法1: 尝试现代剪贴板API
    try {
      if (navigator.clipboard && window.isSecureContext) {
        console.log("使用现代剪贴板API");

        // 移动端需要更长的延迟
        const delay = isMobile ? 50 : 10;

        // 先清空剪贴板
        await navigator.clipboard.writeText("");
        await new Promise((resolve) => setTimeout(resolve, delay));

        // 再写入新内容
        await navigator.clipboard.writeText(text);
        await new Promise((resolve) => setTimeout(resolve, delay * 2));

        console.log("现代API复制完成");
        return true;
      }
    } catch (error) {
      console.log("现代API失败:", error);
    }

    // 方法2: Fallback到传统方法 - 移动端优化
    console.log("使用传统复制方法");
    return new Promise((resolve) => {
      // 移动端使用textarea，桌面端使用input
      const element = isMobile
        ? document.createElement("textarea")
        : document.createElement("input");

      if (!isMobile) {
        (element as HTMLInputElement).type = "text";
      }

      element.value = text;
      element.setAttribute("data-clipboard-temp", "true");

      // 移动端需要特殊的样式处理
      if (isMobile) {
        element.style.position = "fixed";
        element.style.left = "0";
        element.style.top = "0";
        element.style.width = "1px";
        element.style.height = "1px";
        element.style.padding = "0";
        element.style.border = "none";
        element.style.outline = "none";
        element.style.boxShadow = "none";
        element.style.background = "transparent";
        element.style.fontSize = "16px"; // 防止iOS缩放
        element.style.opacity = "0";
        element.style.zIndex = "-1";
      } else {
        element.style.position = "fixed";
        element.style.left = "-9999px";
        element.style.top = "-9999px";
        element.style.opacity = "0";
        element.style.zIndex = "-1";
      }

      document.body.appendChild(element);

      // 强制刷新DOM
      element.offsetHeight;

      const delay = isMobile ? 200 : 100;
      setTimeout(() => {
        try {
          element.focus();
          element.select();

          if (element.setSelectionRange) {
            element.setSelectionRange(0, element.value.length);
          }

          // 移动端需要更多尝试
          const maxAttempts = isMobile ? 5 : 3;
          let success = false;

          for (let i = 0; i < maxAttempts; i++) {
            try {
              success = document.execCommand("copy");
              if (success) {
                console.log(`复制成功，尝试次数: ${i + 1}`);
                break;
              }
            } catch (e) {
              console.log(`复制尝试 ${i + 1} 失败:`, e);
            }

            // 移动端在重试前稍作等待
            if (isMobile && i < maxAttempts - 1) {
              // 使用同步等待，避免async问题
              const start = Date.now();
              while (Date.now() - start < 50) {
                // 简单的同步等待
              }
            }
          }

          console.log("传统方法复制结果:", success);
          resolve(success);
        } catch (error) {
          console.log("传统方法失败:", error);
          resolve(false);
        } finally {
          // 清理元素
          const cleanupDelay = isMobile ? 300 : 200;
          setTimeout(() => {
            if (element.parentNode) {
              element.parentNode.removeChild(element);
            }
          }, cleanupDelay);
        }
      }, delay);
    });
  }

  // 格式化时间
  function formatTime(ms: number): string {
    if (!ms || ms <= 0) return "00:00:00";
    const hours = Math.floor(ms / 3600000);
    const minutes = Math.floor((ms % 3600000) / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  }

  // 充值倒计时定时器
  function startRechargeCountdown() {
    clearRechargeTimer();
    rechargeTimer.value = window.setInterval(() => {
      if (rechargeCountdown.value > 0) {
        rechargeCountdown.value -= 1000;
      } else {
        clearRechargeTimer();
        rechargeCountdown.value = 0;
      }
    }, 1000);
  }
  function clearRechargeTimer() {
    if (rechargeTimer.value) {
      window.clearInterval(rechargeTimer.value);
      rechargeTimer.value = null;
    }
  }

  // 活动规则
  async function getRules() {
    const response = (await enterPddActivity()) as PddActivityContent;
    rules.value = response;
  }

  // 事件处理
  async function handleOpen() {
    if (isAnimating.value) return;
    isAnimating.value = true;
    try {
      if (!activityData.value?.activity_id) {
        showWarning("Nenhuma atividade iniciada");
        return;
      }
      loading.value = true;
      const response = await joinPddActivity({
        activity_id: activityData.value.activity_id,
      });
      if (response?.invitation_code) {
        activityData.value = response;
        sessionStorage.setItem("pddActivityData", JSON.stringify(response));
        await updateActivityData();
        showSuccess("Atividade iniciada com sucesso!");
        loading.value = false;
        startActivityUpdateTimer();
        startCountdownTimer();
      }
    } catch (error: any) {
      console.error("Falha ao participar da atividade:", error);
      showError(error.message || "Falha ao participar da atividade");
    } finally {
      isAnimating.value = false;
      loading.value = false;
    }
  }
  // 复制邀请链接
  async function handleInvite() {
    if (!activityStatus.value?.short) return;
    const shareUrl = `${window.location.origin}/s/${activityStatus.value.short}`;

    // 添加调试信息
    console.log("邀请链接内容:", shareUrl);

    const success = await copyToClipboard(shareUrl);
    if (success) {
      updateMetaTags(
        shareUrl,
        "https://images.box777bet.com/box777/uploads/20250621/1750476525514840353.png"
      );
      showSuccess("Link copiado para a área de transferência!");
    } else {
      showError("Falha ao copiar o link");
    }
  }
  // 充值跳转
  async function handleRecarregue() {
    if (!activityStatus.value?.short) return;
    router.push("/records");
    if (options?.isDialog && options.onClose) options.onClose();
  }
  async function handleWithdraw() {
    if (!isShare.value) {
      showWarning(
        "Clique no botão para compartilhar no Telegram primeiro e conclua os requisitos de retirada."
      );
      return;
    }
    sessionStorage.removeItem("pddActivityData");
    showSuccess("Saque iniciado!");
    clearAllTimers();
    const response = (await enterPddActivity()) as PddActivityContent;
    activityData.value = response;
    handleOpen();
  }
  // 分享tg群
  async function hendleShareTelegram() {
    isShare.value = true;
    await copyCheck({ invitation_code: activityStatus.value?.invitation_code });

    // 构建分享内容 - 移动端友好的换行处理
    const baseUrl = `${window.location.origin}/s/${activityStatus.value.short}`;
    const copywriting = activityStatus.value.share_copywriting || "";
    // const imageUrl = `https://images.box777bet.com/${activityStatus.value.file_path}`;

    // 检测移动端并使用适当的换行符
    const isMobile =
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      );

    // 移动端使用双换行确保换行效果，桌面端使用单换行
    const lineBreak = isMobile ? "\n\n" : "\n";

    // 构建分享内容，确保每部分都在新行
    const parts = [baseUrl];
    if (copywriting.trim()) {
      parts.push(copywriting.trim());
    }
    // if (imageUrl) {
    //   parts.push(imageUrl);
    // }

    const shareUrl = parts.join(lineBreak);

    // 添加调试信息
    console.log("设备类型:", isMobile ? "移动端" : "桌面端");
    console.log("分享内容部分:", parts);
    console.log("使用的换行符:", JSON.stringify(lineBreak));
    console.log("Telegram分享内容:", shareUrl);
    console.log("内容长度:", shareUrl.length);

    const success = await copyToClipboard(shareUrl);
    if (success) {
      updateMetaTags(
        `${window.location.origin}/s/${activityStatus.value.short}`
      );
      showSuccess("Link copiado para a área de transferência!");
    } else {
      showError("Falha ao copiar o link");
    }
    try {
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      if (isIOS && activityStatus.value?.tg_link) {
        setTimeout(() => {
          window.location.href = activityStatus.value.tg_link;
        }, 100);
      } else if (activityStatus.value?.tg_link) {
        window.open(activityStatus.value.tg_link, "_blank");
      }
    } catch (error) {
      console.error("获取人工客服链接失败:", error);
    }
  }

  // 活动初始化
  async function initializeActivityData() {
    try {
      loading.value = true;
      const response = (await enterPddActivity()) as PddActivityContent;
      activityData.value = response;
      if (activityData.value?.id) {
        await updateActivityData();
        startCountdownTimer();
        startActivityUpdateTimer();
      } else {
        const response = (await enterPddActivity()) as PddActivityContent;
        activityData.value = response;
        status.value = "start";
      }
    } catch (error) {
      console.error("活动初始化失败:", error);
      showError("Falha ao carregar atividade");
    } finally {
      loading.value = false;
    }
  }

  // 状态监听（中奖提示）
  watch(status, (newValue) => {
    if (newValue === "help") {
      showWinningAlertWithDelay();
    } else if (newValue === "ok") {
      clearAllTimers();
      showWinningAlert.value = false;
    } else {
      showWinningAlert.value = false;
    }
  });

  // 规则弹窗监听（弹窗模式下）
  if (options?.isDialog) {
    const dialog = ref(false);
    function show() {
      dialog.value = true;
      status.value = "";
    }
    function closeDialog() {
      dialog.value = false;
      if (options?.onClose) options?.onClose();
    }
    watch(dialog, (newValue) => {
      if (newValue) {
        initializeActivityData();
        getRules();
      } else {
        clearAllTimers();
        status.value = "start";
        loading.value = true;
        activityStatus.value = null;
        activityData.value = null;
        countdown.value = 0;
        showWinningAlert.value = false;
      }
    });
    return {
      // 状态
      dialog,
      show,
      closeDialog,
      status,
      activityData,
      activityStatus,
      countdown,
      loading,
      isAnimating,
      showWinningAlert,
      winningData,
      rules,
      showRule,
      registerNum,
      rechargeCountdown,
      isShare,
      // 方法
      handleOpen,
      handleInvite,
      handleRecarregue,
      handleWithdraw,
      hendleShareTelegram,
      formatTime,
      handleRuleDialogClose,
      getRules,
      isRechargeRequired,
      // 计时器
      startCountdownTimer,
      clearAllTimers,
      startActivityUpdateTimer,
      initializeActivityData,
    };
  } else {
    // 页面模式下生命周期钩子由外部调用
    return {
      // 状态
      status,
      activityData,
      activityStatus,
      countdown,
      loading,
      isAnimating,
      showWinningAlert,
      winningData,
      rules,
      showRule,
      registerNum,
      rechargeCountdown,
      isShare,
      // 方法
      handleOpen,
      handleInvite,
      handleRecarregue,
      handleWithdraw,
      hendleShareTelegram,
      formatTime,
      handleRuleDialogClose,
      getRules,
      isRechargeRequired,
      // 计时器
      startCountdownTimer,
      clearAllTimers,
      startActivityUpdateTimer,
      initializeActivityData,
    };
  }
}
