<script setup lang="ts">
import { ref, defineProps, defineEmits } from "vue";

interface Props {
  show: boolean;
  dialogObj: {
    id: number;
    title: string;
    content: string;
    image_url: string;
    hyperlink: string;
    show_type: number;
    weight: number;
    created_at: string;
    confirm_text: string;
  };
}

const props = defineProps<Props>();
const emit = defineEmits(["update:show"]);

const redeemCode = ref("");
const isLoading = ref(false);

const handleClose = () => {
  emit("update:show", false);
  redeemCode.value = "";
};

const handlePular = async () => {
  console.log(props.dialogObj.hyperlink)
  try {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    if (isIOS) {
      // iOS需要用户交互触发window.open
      setTimeout(() => {
        window.location.href = props.dialogObj.hyperlink;
      }, 100);
    } else {
      window.open(props.dialogObj.hyperlink, "_self");
    }

    // emit("update:show", false);
  } catch (error: any) {
    // console.error("Exchange code error:", error);
    // showError(error?.response?.data?.message || "Falha ao resgatar o código");
  } finally {
    isLoading.value = false;
  }
};
</script>

<template>
  <v-dialog
    :model-value="show"
    @update:model-value="emit('update:show', $event)"
    width="366"
    @click:outside="handleClose"
    class="bonus-dialog"
  >
    <v-btn
      icon="mdi-close"
      variant="text"
      size="small"
      class="close-btn"
      @click="handleClose"
    />
    <v-card class="bonus-card pa-4">
      <div class="dialog-content">
        <div class="dialog-header mb-4">
          <div class="dialog-title">{{ props.dialogObj.title }}</div>
        </div>

        <div class="dialog-subtitle mb-4">
          {{ props.dialogObj.content }}
        </div>
        <div class="dialog-subtitle mb-4">
          <v-img
            aspect-ratio="16/9"
            cover
            :src="props.dialogObj.image_url"
          ></v-img>
        </div>
        <div class="dialog-footer">
          <v-btn
            block
            color="primary"
            :loading="isLoading"
            class="redeem-btn"
            @click="handlePular"
          >
            {{ props.dialogObj.confirm_text || "Pular" }}
          </v-btn>
        </div>
      </div>
    </v-card>
  </v-dialog>
</template>

<style lang="scss" scoped>
.bonus-dialog {
  :deep(.v-overlay__content) {
    align-items: center;
    padding: 24px;
    background: #1e2332;
    border-radius: 20px;
  }
}

.bonus-card {
  width: 100%;
  background: #1e2332;
  border-radius: 12px;
  margin-top: 30px;
}

.close-btn {
  position: absolute;
  height: 22px;
  width: 22px;
  background: #c9cad8 !important;
  color: #2b324d;
  right: 24px;
  z-index: 9999;
  :deep(.v-icon) {
    font-size: 18px;
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
}

.dialog-header {
  text-align: center;
}

.dialog-title {
  color: #fff;
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 8px;
}

.dialog-subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.redeem-input {
  :deep(.v-field) {
    height: 46px;
    line-height: 46px;
    border-radius: 8px;
    background: #2b324d;
    .v-field__input {
      color: #fff;
      font-size: 16px;
      padding: 0 16px;
      height: 46px;
      line-height: 46px;
    }
    .v-field__outline {
      display: none;
    }
  }
}

.dialog-footer {
  margin-top: 8px;
}

.redeem-btn {
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  text-transform: none;
  letter-spacing: 0.5px;
  background: linear-gradient(0deg, #c9b737, #2abb27);
  border-radius: 22px;
}
</style>
