<template>
  <div class="withdrawal-config-page" v-loading="loading">
    <div class="page-header">
      <div class="breadcrumb">
        <span class="back-link" @click="$router.back()">
          <ElIcon><ArrowLeft /></ElIcon>
          返回列表
        </span>
        <ElIcon><CaretRight class="divider-icon" /></ElIcon>
        <span class="current-page">提现配置</span>
      </div>
    </div>
    <div class="config-row top-row">
      <ElCard class="config-card mode-card">
        <div class="config-header">
          <ElIcon><Setting /></ElIcon>
          <span class="config-title">提现模式配置</span>
          <ElButton
            type="warning"
            size="small"
            class="edit-btn"
            @click="onEdit('mode')"
          >
            <ElIcon><EditPen /></ElIcon>编辑
          </ElButton>
        </div>
        <div class="mode-content">
          <div class="mode-section">
            <div class="mode-title">快速免审模式</div>
            <div class="mode-desc">
              当日总提现金额小于
              <b>{{ formatAmount(config.FastTrackAmountThreshold) }} BRL</b>
              时，自动进入快速免审模式
            </div>
          </div>
          <div class="mode-section">
            <div class="mode-title">人工审核模式</div>
            <ul class="mode-desc">
              <li>
                当日提现总金额大于等于
                <b
                  >{{ formatAmount(config.ManualReviewAmountThreshold) }} BRL</b
                >
                时
              </li>
              <li>
                当日提现次数大于等于
                <b>{{ config.ManualReviewCountThreshold }}</b> 次
              </li>
              <li>
                单笔提现金额大于等于
                <b>{{ formatAmount(config.ManualReviewSingleAmount) }} BRL</b>
                时
              </li>
            </ul>
          </div>
          <ElAlert
            type="info"
            show-icon
            :closable="false"
            class="mode-alert"
            style="margin-top: 16px"
          >
            当前系统提现模式：<b style="color: #1890ff">{{ currentMode }}</b>
            （今日提现总额：<b style="color: #1890ff"
              >{{ formatAmount(todayTotal) }} BRL</b
            >）
          </ElAlert>
        </div>
      </ElCard>
      <ElCard class="config-card">
        <div class="config-header">
          <ElIcon><UserFilled /></ElIcon>
          <span class="config-title">白名单提现配置</span>
          <ElButton
            type="warning"
            size="small"
            class="edit-btn"
            @click="onEdit('white')"
          >
            <ElIcon><EditPen /></ElIcon>编辑
          </ElButton>
        </div>
        <div class="config-content">
          <div class="config-item">
            <span class="config-label">每日提现次数限制</span>
            <span class="config-value"
              >{{ config.WhitelistDailyCountLimit }}
              <span class="config-unit">次</span></span
            >
          </div>
          <div class="config-item">
            <span class="config-label">每日提现总额限制</span>
            <span class="config-value"
              >{{ formatAmount(config.WhitelistDailyAmountLimit) }}
              <span class="config-unit">BRL</span></span
            >
          </div>
          <div class="config-item">
            <span class="config-label">单笔提现限额</span>
            <span class="config-value"
              >{{ formatAmount(config.WhitelistSingleAmountLimit) }}
              <span class="config-unit">BRL</span></span
            >
          </div>
          <div class="config-item">
            <span class="config-label">最小提现金额</span>
            <span class="config-value"
              >{{ formatAmount(config.WhitelistMinAmountLimit) }}
              <span class="config-unit">BRL</span></span
            >
          </div>
        </div>
      </ElCard>

      <!-- <ElCard class="config-card">
        <div class="config-header">
          <ElIcon><UserFilled /></ElIcon>
          <span class="config-title">非白名单提现配置</span>
          <ElButton type="warning" size="small" class="edit-btn" @click="onEdit('normal')">
            <ElIcon><EditPen /></ElIcon>编辑
          </ElButton>
        </div>
        <div class="config-content">
          <div class="config-item">
            <span class="config-label">每日提现次数限制</span>
            <span class="config-value">{{ config.NonWhitelistDailyCountLimit }} <span class="config-unit">次</span></span>
          </div>
          <div class="config-item">
            <span class="config-label">每日提现总额限制</span>
            <span class="config-value">{{ formatAmount(config.NonWhitelistDailyAmountLimit) }} <span class="config-unit">BRL</span></span>
          </div>
          <div class="config-item">
            <span class="config-label">单笔提现限额</span>
            <span class="config-value">{{ formatAmount(config.NonWhitelistSingleAmountLimit) }} <span class="config-unit">BRL</span></span>
          </div>
          <div class="config-item">
            <span class="config-label">最小提现金额</span>
            <span class="config-value">{{ formatAmount(config.NonWhitelistMinAmountLimit) }} <span class="config-unit">BRL</span></span>
          </div>
        </div>
      </ElCard> -->
    </div>

    <!-- 白名单管理表格模块 -->
    <ElCard class="whitelist-card">
      <div class="config-header">
        <ElIcon><Tickets /></ElIcon>
        <span class="config-title">白名单管理</span>
        <ElButton
          type="primary"
          size="small"
          @click="onAddWhitelist"
          class="add-whitelist-btn"
        >
          <ElIcon><Plus /></ElIcon>新增
        </ElButton>
      </div>
      <div class="search-form-container">
        <ElForm :inline="true" :model="whitelistSearchForm" class="search-form">
          <ElFormItem label="用户ID">
            <ElInput
              v-model="whitelistSearchForm.user_id"
              placeholder="请输入"
              clearable
            />
          </ElFormItem>
          <ElFormItem label="类型">
            <ElSelect
              v-model="whitelistSearchForm.type"
              style="width: 100px"
              placeholder="全部"
              clearable
            >
              <ElOption label="全部" value="all" />
              <ElOption label="用户" value="user" />
              <ElOption label="代理商" value="agent" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="时间">
            <ElDatePicker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="x"
              @change="
                (val: [string, string] | null) => {
                  if (val) {
                    whitelistSearchForm.start_time = val[0];
                    whitelistSearchForm.end_time = val[1];
                  } else {
                    whitelistSearchForm.start_time = undefined;
                    whitelistSearchForm.end_time = undefined;
                  }
                }
              "
            />
          </ElFormItem>
          <ElFormItem>
            <ElButton type="primary" @click="onWhitelistSearch">搜索</ElButton>
            <ElButton @click="onWhitelistReset">重置</ElButton>
          </ElFormItem>
        </ElForm>
      </div>

      <ElTable
        :data="whitelistTableData"
        v-loading="whitelistLoading"
        border
        style="width: 100%"
      >
        <ElTableColumn prop="id" label="序号" width="80" align="center" />
        <ElTableColumn prop="type" label="类型" width="100" align="center">
          <template #default="scope">
            {{ scope.row.type === "user" ? "用户" : "代理商" }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="user_id"
          label="用户ID/代理商ID"
          width="140"
          align="center"
        >
          <template #default="scope">
            {{
              scope.row.type === "user" ? scope.row.user_id : scope.row.agent_id
            }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="phone" label="手机号" width="140" align="center">
          <template #default="scope">
            {{
              scope.row.type === "user"
                ? scope.row.phone
                : scope.row.agent_phone
            }}
          </template>
        </ElTableColumn>
        <ElTableColumn label="提现限制" align="center">
          <template #default="scope">
            <div class="limit-info">
              <div>每日次数: {{ scope.row.daily_count_limit }}次</div>
              <div>
                每日总额: {{ formatAmount(scope.row.daily_amount_limit) }} BRL
              </div>
              <div>
                单笔限额: {{ formatAmount(scope.row.single_amount_limit) }} BRL
              </div>
              <div>
                最小金额: {{ formatAmount(scope.row.min_amount_limit) }} BRL
              </div>
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="created_at"
          label="添加时间"
          width="180"
          align="center"
        >
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" width="100" align="center">
          <template #default="scope">
            <ElButton
              type="danger"
              link
              @click="onRemoveWhitelist(scope.row.id)"
              >移除</ElButton
            >
          </template>
        </ElTableColumn>
      </ElTable>

      <div class="pagination-container">
        <ElPagination
          v-model:current-page="whitelistPagination.page"
          v-model:page-size="whitelistPagination.size"
          :page-sizes="[5, 10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="whitelistTotal"
          @size-change="handleWhitelistSizeChange"
          @current-change="handleWhitelistPageChange"
        />
      </div>
    </ElCard>

    <!-- 编辑抽屉 -->
    <ElDrawer
      v-model="drawerVisible"
      :title="drawerTitle"
      size="500px"
      :destroy-on-close="true"
    >
      <ElForm
        ref="formRef"
        :model="editForm"
        :rules="rules"
        label-width="180px"
        class="edit-form"
      >
        <template v-if="editType === 'white'">
          <ElFormItem
            label="每日提现次数限制"
            prop="whitelist_daily_count_limit"
          >
            <ElInputNumber
              v-model="editForm.whitelist_daily_count_limit"
              :min="1"
            />
          </ElFormItem>
          <ElFormItem
            label="每日提现总额限制"
            prop="whitelist_daily_amount_limit"
          >
            <ElInputNumber
              v-model="editForm.whitelist_daily_amount_limit"
              :min="0"
              :precision="2"
              :step="100"
            /><span style="margin-left: 10px">BRL</span>
          </ElFormItem>
          <ElFormItem label="单笔提现限额" prop="whitelist_single_amount_limit">
            <ElInputNumber
              v-model="editForm.whitelist_single_amount_limit"
              :min="0"
              :precision="2"
              :step="100"
            /><span style="margin-left: 10px">BRL</span>
          </ElFormItem>
          <ElFormItem label="最小提现金额" prop="whitelist_min_amount_limit">
            <ElInputNumber
              v-model="editForm.whitelist_min_amount_limit"
              :min="0"
              :precision="2"
              :step="10"
            /><span style="margin-left: 10px">BRL</span>
          </ElFormItem>
        </template>
        <template v-else-if="editType === 'normal'">
          <ElFormItem
            label="每日提现次数限制"
            prop="non_whitelist_daily_count_limit"
          >
            <ElInputNumber
              v-model="editForm.non_whitelist_daily_count_limit"
              :min="1"
            />
          </ElFormItem>
          <ElFormItem
            label="每日提现总额限制"
            prop="non_whitelist_daily_amount_limit"
          >
            <ElInputNumber
              v-model="editForm.non_whitelist_daily_amount_limit"
              :min="0"
              :precision="2"
              :step="100"
            /><span style="margin-left: 10px">BRL</span>
          </ElFormItem>
          <ElFormItem
            label="单笔提现限额"
            prop="non_whitelist_single_amount_limit"
          >
            <ElInputNumber
              v-model="editForm.non_whitelist_single_amount_limit"
              :min="0"
              :precision="2"
              :step="100"
            /><span style="margin-left: 10px">BRL</span>
          </ElFormItem>
          <ElFormItem
            label="最小提现金额"
            prop="non_whitelist_min_amount_limit"
          >
            <ElInputNumber
              v-model="editForm.non_whitelist_min_amount_limit"
              :min="0"
              :precision="2"
              :step="10"
            /><span style="margin-left: 10px">BRL</span>
          </ElFormItem>
        </template>
        <template v-else-if="editType === 'mode'">
          <ElFormItem
            label="快速免审模式阀值"
            prop="fast_track_amount_threshold"
          >
            <ElInputNumber
              v-model="editForm.fast_track_amount_threshold"
              :min="0"
              :precision="2"
              :step="1000"
            /><span style="margin-left: 10px">BRL</span>
          </ElFormItem>
          <ElFormItem
            label="人工审核总金额阀值"
            prop="manual_review_amount_threshold"
          >
            <ElInputNumber
              v-model="editForm.manual_review_amount_threshold"
              :min="0"
              :precision="2"
              :step="1000"
            /><span style="margin-left: 10px">BRL</span>
          </ElFormItem>
          <ElFormItem
            label="人工审核次数阀值"
            prop="manual_review_count_threshold"
          >
            <ElInputNumber
              v-model="editForm.manual_review_count_threshold"
              :min="1"
            />
          </ElFormItem>
          <ElFormItem
            label="人工审核单笔金额阀值"
            prop="manual_review_single_amount"
          >
            <ElInputNumber
              v-model="editForm.manual_review_single_amount"
              :min="0"
              :precision="2"
              :step="1000"
            /><span style="margin-left: 10px">BRL</span>
          </ElFormItem>
        </template>
      </ElForm>
      <template #footer>
        <div class="drawer-footer">
          <ElButton @click="drawerVisible = false">取消</ElButton>
          <ElButton type="primary" :loading="loading" @click="handleSave"
            >保存</ElButton
          >
        </div>
      </template>
    </ElDrawer>

    <!-- 新增白名单抽屉 -->
    <ElDrawer
      v-model="addWhitelistDrawerVisible"
      title="新增白名单"
      size="500px"
      :destroy-on-close="true"
    >
      <ElForm
        ref="addWhitelistFormRef"
        :model="addWhitelistForm"
        :rules="addWhitelistRules"
        label-width="180px"
        class="add-whitelist-form"
      >
        <ElFormItem label="用户类型" prop="type">
          <ElRadioGroup v-model="addWhitelistForm.type">
            <ElRadio label="user">用户</ElRadio>
            <ElRadio label="agent">代理商</ElRadio>
          </ElRadioGroup>
        </ElFormItem>
        <ElFormItem label="选择对象" prop="id">
          <ElSelect
            v-model="addWhitelistForm.selectedIds"
            multiple
            filterable
            remote
            :remote-method="handleSearch"
            :loading="searchLoading"
            placeholder="请输入ID搜索"
            clearable
            class="whitelist-select"
          >
            <ElOption
              v-for="item in searchResults"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            />
          </ElSelect>
        </ElFormItem>
        <ElDivider content-position="left">提现限制</ElDivider>
        <ElFormItem label="每日提现次数限制" prop="daily_count_limit">
          <ElInputNumber
            v-model="addWhitelistForm.daily_count_limit"
            :min="1"
          />
        </ElFormItem>
        <ElFormItem label="每日提现总额限制" prop="daily_amount_limit">
          <ElInputNumber
            v-model="addWhitelistForm.daily_amount_limit"
            :min="0"
            :precision="2"
            :step="100"
          /><span style="margin-left: 10px">BRL</span>
        </ElFormItem>
        <ElFormItem label="单笔提现限额" prop="single_amount_limit">
          <ElInputNumber
            v-model="addWhitelistForm.single_amount_limit"
            :min="0"
            :precision="2"
            :step="100"
          /><span style="margin-left: 10px">BRL</span>
        </ElFormItem>
        <ElFormItem label="最小提现金额" prop="min_amount_limit">
          <ElInputNumber
            v-model="addWhitelistForm.min_amount_limit"
            :min="0"
            :precision="2"
            :step="10"
          /><span style="margin-left: 10px">BRL</span>
        </ElFormItem>
      </ElForm>
      <template #footer>
        <div class="drawer-footer">
          <ElButton @click="addWhitelistDrawerVisible = false">取消</ElButton>
          <ElButton
            type="primary"
            :loading="loading"
            @click="handleAddWhitelistSave"
            >保存</ElButton
          >
        </div>
      </template>
    </ElDrawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import moment from "moment";
import {
  EditPen,
  UserFilled,
  Setting,
  ArrowLeft,
  CaretRight,
  Tickets,
  Plus,
  Search,
} from "@element-plus/icons-vue";
import {
  getWithdrawalConfig,
  updateWithdrawalConfig,
  updateWhitelistWithdrawalConfig,
  updateWithdrawalModeConfig,
  getWithdrawalTotal,
  getWhitelist,
  addWhitelist,
  removeWhitelist,
  getWhitelistSearch,
} from "@/service/api/withdrawal";
import type {
  WithdrawalConfigResponse,
  WithdrawalConfigParams,
  WhitelistWithdrawalConfigParams,
  WithdrawalModeConfigParams,
  WhitelistRecord,
  WhitelistListResponse,
  WhitelistSearchParams,
  AddWhitelistParams,
} from "@/typings/withdrawal";
import {getBrazilDate} from "@/utils/format";

// 合并所有编辑表单可能的字段
interface CombinedEditForm
  extends WhitelistWithdrawalConfigParams,
    WithdrawalConfigParams,
    WithdrawalModeConfigParams {}

const config = ref<WithdrawalConfigResponse>({} as WithdrawalConfigResponse);
const loading = ref(false);
const drawerVisible = ref(false);
const editType = ref<"white" | "normal" | "mode">("white");
const formRef = ref();
const addWhitelistFormRef = ref();

// 编辑表单数据
const editForm = ref<Partial<CombinedEditForm>>({});

// 新增白名单表单数据
interface AddWhitelistForm {
  type: "user" | "agent";
  id: string;
  selectedIds: string[];
  daily_count_limit: number;
  daily_amount_limit: number;
  single_amount_limit: number;
  min_amount_limit: number;
}

interface SearchResult {
  id: string;
  label: string;
}
const searchLoading = ref(false);
const searchResults = ref<SearchResult[]>([]);

const dateRange = ref();
const addWhitelistForm = ref<AddWhitelistForm>({
  type: "user",
  id: "",
  selectedIds: [],
  daily_count_limit: 1,
  daily_amount_limit: 0,
  single_amount_limit: 0,
  min_amount_limit: 0,
});

// 處理搜索
const handleSearch = async (query: string) => {
  if (query === "") {
    searchResults.value = [];
    return;
  }

  searchLoading.value = true;
  try {
    const params = {
      type: addWhitelistForm.value.type,
      keyword: query,
    };
    const res = await getWhitelistSearch(params);
    console.log(res.data);
    if (res?.data?.data && Array.isArray(res?.data?.data)) {
      searchResults.value = res?.data?.data.map((item) => ({
        id: item.id,
        label: `${item.id} - ${item.name || item.phone || "未知"}`,
      }));
    }
  } catch (error) {
    console.error("搜索失敗:", error);
    ElMessage.error("搜索失敗");
  } finally {
    searchLoading.value = false;
  }
};

// 表单验证规则
const rules = {
  whitelist_daily_count_limit: [
    { required: true, message: "请输入每日提现次数限制", trigger: "blur" },
  ],
  whitelist_daily_amount_limit: [
    { required: true, message: "请输入每日提现总额限制", trigger: "blur" },
  ],
  whitelist_single_amount_limit: [
    { required: true, message: "请输入单笔提现限额", trigger: "blur" },
  ],
  whitelist_min_amount_limit: [
    { required: true, message: "请输入最小提现金额", trigger: "blur" },
  ],
  non_whitelist_daily_count_limit: [
    { required: true, message: "请输入每日提现次数限制", trigger: "blur" },
  ],
  non_whitelist_daily_amount_limit: [
    { required: true, message: "请输入每日提现总额限制", trigger: "blur" },
  ],
  non_whitelist_single_amount_limit: [
    { required: true, message: "请输入单笔提现限额", trigger: "blur" },
  ],
  non_whitelist_min_amount_limit: [
    { required: true, message: "请输入最小提现金额", trigger: "blur" },
  ],
  fast_track_amount_threshold: [
    { required: true, message: "请输入快速免审模式阈值", trigger: "blur" },
  ],
  manual_review_amount_threshold: [
    { required: true, message: "请输入人工审核总金额阈值", trigger: "blur" },
  ],
  manual_review_count_threshold: [
    { required: true, message: "请输入人工审核次数阈值", trigger: "blur" },
  ],
  manual_review_single_amount: [
    { required: true, message: "请输入人工审核单笔金额阈值", trigger: "blur" },
  ],
};

// 新增白名单表单验证规则
const addWhitelistRules = {
  type: [{ required: true, message: "請選擇用戶類型", trigger: "change" }],
  selectedIds: [
    {
      required: true,
      message: "請選擇至少一個用戶或代理商",
      trigger: "change",
    },
    {
      validator: (rule: any, value: string[], callback: any) => {
        if (value && value.length > 10) {
          callback(new Error("最多只能選擇 10 個用戶或代理商"));
        } else {
          callback();
        }
      },
      trigger: "change",
    },
  ],
  daily_count_limit: [
    { required: true, message: "請輸入每日提現次數限制", trigger: "blur" },
  ],
  daily_amount_limit: [
    { required: true, message: "請輸入每日提現總額限制", trigger: "blur" },
  ],
  single_amount_limit: [
    { required: true, message: "請輸入單筆提現限額", trigger: "blur" },
  ],
  min_amount_limit: [
    { required: true, message: "請輸入最小提現金額", trigger: "blur" },
  ],
};

// 计算属性：当前模式
const currentMode = computed(() => {
  return config.value.FastTrackAmountThreshold > 0
    ? "快速免审模式"
    : "人工审核模式";
});

// 计算属性：今日总额（这里需要从其他地方获取）
const todayTotal = ref(0);

// 格式化金额
const formatAmount = (amount: number) => {
  return (amount / 100).toLocaleString("pt-BR", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

// 白名单表格数据
const whitelistTableData = ref<WhitelistRecord[]>([]);
const whitelistLoading = ref(false);
const whitelistTotal = ref(0);
const whitelistSearchForm = ref<WhitelistSearchParams>({
  page: 1,
  size: 10,
  type: "all",
});

// 白名单分页数据
const whitelistPagination = ref({
  page: 1,
  size: 10,
});

// 获取配置
const getConfig = async () => {
  loading.value = true;
  try {
    const res = await getWithdrawalConfig();
    const total = await getWithdrawalTotal();
    if (res.data) {
      config.value = res.data.data;
    }
    if (total.data?.data) {
      todayTotal.value = total.data.data.total_amount || 0;
    }
  } catch (error) {
    console.error("获取配置失败:", error);
    ElMessage.error("获取配置失败");
  } finally {
    loading.value = false;
  }
};

// 获取白名单表格数据
const getWhitelistTableData = async () => {
  whitelistLoading.value = true;
  try {
    const params = {
      ...whitelistSearchForm.value,
      page: whitelistPagination.value.page,
      size: whitelistPagination.value.size,
    };
    const res = await getWhitelist(params);
    if (res) {
      whitelistTableData.value = res.data.data || [];
      whitelistTotal.value = res.data.count || 0;
    }
  } catch (error) {
    console.error("獲取白名單列表失敗:", error);
    ElMessage.error("獲取白名單列表失敗");
  } finally {
    whitelistLoading.value = false;
  }
};

// 白名单搜索
const onWhitelistSearch = () => {
  whitelistPagination.value.page = 1;
  getWhitelistTableData();
};

// 白名单重置
const onWhitelistReset = () => {
  whitelistSearchForm.value = {
    type: "all",
  };
  dateRange.value = undefined;
  whitelistPagination.value.page = 1;
  whitelistPagination.value.size = 10;
  getWhitelistTableData();
};

// 白名单分页大小改变
const handleWhitelistSizeChange = (val: number) => {
  whitelistPagination.value.size = val;
  getWhitelistTableData();
};

// 白名单当前页改变
const handleWhitelistPageChange = (val: number) => {
  whitelistPagination.value.page = val;
  getWhitelistTableData();
};

// 新增白名单
const addWhitelistDrawerVisible = ref(false);

const onAddWhitelist = () => {
  addWhitelistDrawerVisible.value = true;
  if (addWhitelistFormRef.value) {
    addWhitelistFormRef.value.resetFields();
  }
  addWhitelistForm.value = {
    type: "user",
    id: "",
    selectedIds: [],
    daily_count_limit: 1,
    daily_amount_limit: 0,
    single_amount_limit: 0,
    min_amount_limit: 0,
  };
};

// 處理新增白名單保存
const handleAddWhitelistSave = async () => {
  if (!addWhitelistFormRef.value) return;

  await addWhitelistFormRef.value.validate(async (valid: boolean) => {
    if (!valid) return;

    // 檢查選中的 ID 數量
    if (addWhitelistForm.value.selectedIds.length === 0) {
      ElMessage.error("請至少選擇一個用戶或代理商");
      return;
    }

    if (addWhitelistForm.value.selectedIds.length > 10) {
      ElMessage.error("最多只能選擇 10 個用戶或代理商");
      return;
    }

    loading.value = true;
    try {
      const params: AddWhitelistParams = {
        user_type: addWhitelistForm.value.type,
        target_ids: addWhitelistForm.value.selectedIds,
        withdrawal_limits: {
          daily_count_limit: addWhitelistForm.value.daily_count_limit,
          daily_amount_limit: addWhitelistForm.value.daily_amount_limit,
          single_amount_limit: addWhitelistForm.value.single_amount_limit,
          min_amount_limit: addWhitelistForm.value.min_amount_limit,
        },
      };
      await addWhitelist(params);

      ElMessage.success("新增白名單成功");
      addWhitelistDrawerVisible.value = false;
      await getWhitelistTableData();
    } catch (error) {
      console.error("新增白名單失敗:", error);
      ElMessage.error("新增白名單失敗");
    } finally {
      loading.value = false;
    }
  });
};

// 移除白名单
const onRemoveWhitelist = (id: number) => {
  ElMessageBox.confirm("确定要移除此白名单吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        await removeWhitelist(id);
        ElMessage.success("移除成功");
        getWhitelistTableData();
      } catch (error) {
        console.error("移除白名单失败:", error);
        ElMessage.error("移除白名单失败");
      }
    })
    .catch(() => {
      ElMessage.info("取消移除");
    });
};

// 编辑按钮点击
const onEdit = (type: "white" | "normal" | "mode") => {
  editType.value = type;
  drawerVisible.value = true;

  // 根据类型设置表单初始值
  if (type === "white") {
    editForm.value = {
      whitelist_daily_count_limit: config.value.WhitelistDailyCountLimit,
      whitelist_daily_amount_limit: config.value.WhitelistDailyAmountLimit,
      whitelist_single_amount_limit: config.value.WhitelistSingleAmountLimit,
      whitelist_min_amount_limit: config.value.WhitelistMinAmountLimit,
    } as WhitelistWithdrawalConfigParams;
  } else if (type === "normal") {
    editForm.value = {
      non_whitelist_daily_count_limit: config.value.NonWhitelistDailyCountLimit,
      non_whitelist_daily_amount_limit:
        config.value.NonWhitelistDailyAmountLimit,
      non_whitelist_single_amount_limit:
        config.value.NonWhitelistSingleAmountLimit,
      non_whitelist_min_amount_limit: config.value.NonWhitelistMinAmountLimit,
    } as WithdrawalConfigParams;
  } else {
    editForm.value = {
      fast_track_amount_threshold: config.value.FastTrackAmountThreshold,
      manual_review_amount_threshold: config.value.ManualReviewAmountThreshold,
      manual_review_count_threshold: config.value.ManualReviewCountThreshold,
      manual_review_single_amount: config.value.ManualReviewSingleAmount,
    } as WithdrawalModeConfigParams;
  }
};

// 保存配置
const handleSave = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid: boolean) => {
    if (!valid) return;

    loading.value = true;
    try {
      if (editType.value === "white") {
        await updateWhitelistWithdrawalConfig(
          editForm.value as WhitelistWithdrawalConfigParams,
        );
      } else if (editType.value === "normal") {
        await updateWithdrawalConfig(editForm.value as WithdrawalConfigParams);
      } else {
        await updateWithdrawalModeConfig(
          editForm.value as WithdrawalModeConfigParams,
        );
      }

      ElMessage.success("保存成功");
      drawerVisible.value = false;
      await getConfig();
    } catch (error) {
      console.error("保存失败:", error);
      ElMessage.error("保存失败");
    } finally {
      loading.value = false;
    }
  });
};

// 计算属性：抽屉标题
const drawerTitle = computed(() => {
  const titles = {
    white: "编辑白名单提现配置",
    normal: "编辑非白名单提现配置",
    mode: "编辑提现模式配置",
  };
  return titles[editType.value];
});

// 格式化日期
const formatDate = (timestamp: number) => {
  return moment(getBrazilDate(timestamp)).format("YYYY-MM-DD HH:mm:ss");
};

onMounted(() => {
  getConfig();
  getWhitelistTableData(); // 页面加载时获取白名单数据
});

defineOptions({ name: "WithdrawalManagement" });
</script>

<style scoped>
.withdrawal-config-page {
  padding: 24px;
  background: #f5f6fa;
  min-height: 100vh;
}

.config-row {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.top-row {
  width: 100%;
}

.config-card {
  flex: 1 1 0;
  min-width: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: stretch;
}

.mode-card {
  width: 100%;
  max-width: 100%;
}

.config-header {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}

.config-title {
  margin-left: 8px;
  flex: 1;
}

.edit-btn {
  margin-left: auto;
}

.config-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.config-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
}

.config-label {
  color: #888;
  min-width: 140px;
}

.config-value {
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.config-unit {
  color: #888;
  font-size: 14px;
  margin-left: 2px;
}

.mode-content {
  margin-top: 8px;
}

.mode-section {
  margin-bottom: 12px;
}

.mode-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.mode-desc {
  color: #444;
  font-size: 15px;
}

.mode-alert {
  margin-top: 16px;
}

.drawer-footer {
  padding: 20px;
  text-align: right;
}

.page-header {
  margin-bottom: 24px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.back-link {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: color 0.3s;
}

.back-link:hover {
  color: var(--el-color-primary);
}

.divider-icon {
  margin: 0 8px;
  font-size: 12px;
  color: #909399;
}

.current-page {
  font-weight: 500;
  color: #303133;
}

.whitelist-card {
  margin-top: 24px;
}

.search-form-container {
  margin-bottom: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.add-whitelist-btn {
  margin-left: 10px;
}

.limit-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px;
}

.limit-info div {
  font-size: 14px;
  color: #606266;
}

.whitelist-select {
  width: 100%;
}

.el-select__tags {
  flex-wrap: wrap;
}
</style>
