<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import {
  createExchangeCode,
  updateExchangeCode,
} from "@/service/api/exchangeCode";
import type {
  CreateExchangeCodeParams,
  UpdateExchangeCodeParams,
  ExchangeCodeItem,
} from "@/service/api/exchangeCode";
import { getBrazilDate, getBrazilTime } from "@/utils/format";

interface Props {
  visible: boolean;
  row?: ExchangeCodeItem | null;
}

interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "submitted"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();

const modelValue = computed({
  get: () => props.visible,
  set: (value) => emit("update:visible", value),
});

const amountTypeOptions = [
  { label: "赠金", value: 1 },
  { label: "现金", value: 2 },
  { label: "打码提现", value: 3 },
];

const participantOptions = [
  { label: "全部", value: 1 },
  { label: "VIP", value: 2 },
  { label: "普通用户", value: 3 },
];

const formData = reactive<
  Omit<CreateExchangeCodeParams & UpdateExchangeCodeParams, "deadline_time"> & {
    deadline_time: number | undefined;
    bonus: number;
    random_bonus1: number;
    random_bonus2: number;
    random_bonus3: number;
    random_bonus4: number;
    random_bonus5: number;
    random_bonus6: number;
    user_type: number;
  }
>({
  redemption_code_id: 0,
  redemption_pass: "",
  code_count: 0,
  amount_type: 1,
  is_random_amount: 0,
  bonus: 0,
  random_bonus1: 0,
  random_percent1: 0,
  random_bonus2: 0,
  random_percent2: 0,
  random_bonus3: 0,
  random_percent3: 0,
  random_bonus4: 0,
  random_percent4: 0,
  random_bonus5: 0,
  random_percent5: 0,
  random_bonus6: 0,
  random_percent6: 0,
  deadline_time: undefined,
  user_type: 1,
});

// 表单验证规则
const rules = reactive<FormRules>({
  redemption_pass: [
    { required: true, message: "请输入兑换码", trigger: "blur" },
  ],
  code_count: [
    { required: true, message: "请输入生成数量", trigger: "blur" },
    { type: "number", min: 1, message: "生成数量必须大于0", trigger: "blur" },
  ],
  amount_type: [
    { required: true, message: "请选择金额类型", trigger: "change" },
  ],
  deadline_time: [
    { required: true, message: "请选择截止时间", trigger: "change" },
  ],
  bonus: [{ required: true, message: "请选择固定金额", trigger: "change" }],
  user_type: [{ required: true, message: "请选择参与对象", trigger: "change" }],
});

const resetForm = () => {
  Object.assign(formData, {
    redemption_code_id: undefined,
    redemption_pass: "",
    code_count: undefined,
    amount_type: undefined,
    is_random_amount: undefined,
    bonus: undefined,
    random_bonus1: undefined,
    random_percent1: undefined,
    random_bonus2: undefined,
    random_percent2: undefined,
    random_bonus3: undefined,
    random_percent3: undefined,
    random_bonus4: undefined,
    random_percent4: undefined,
    random_bonus5: undefined,
    random_percent5: undefined,
    random_bonus6: undefined,
    random_percent6: undefined,
    deadline_time: undefined,
    user_type: undefined,
  });
};

// 监听抽屉显示状态
watch(
  () => props.visible,
  (newVal) => {
    if (!newVal) {
      // 抽屉关闭时重置表单
      resetForm();
    } else if (newVal && props.row) {
      // 编辑模式
      formData.redemption_code_id = props.row.id;
      formData.user_type = props.row.user_type;
      formData.redemption_pass = props.row.redemption_pass;
      formData.code_count = props.row.code_count;
      formData.amount_type = props.row.amount_type;
      formData.is_random_amount = props.row.bonus === 0 ? 1 : 0;
      formData.bonus = props.row.bonus ? props.row.bonus / 100 : 0;

      // 处理截止时间
      if (props.row.deadline_time) {
        const date = getBrazilDate(props.row.deadline_time);
        formData.deadline_time = date.getTime() / 1000;
      } else {
        formData.deadline_time = undefined;
      }

      // 解析随机金额配置
      if (props.row.random_odds) {
        try {
          const oddsList = props.row.random_odds
            .split(";")
            .filter(Boolean)
            .map((item) => {
              const [bonus, percentStr] = item.split(":");
              const percent = Number(percentStr?.replace("%", ""));
              return {
                bonus: (Number(bonus) || 0) / 100,
                percent: isNaN(percent) ? 0 : percent,
              };
            });

          // 填充随机金额配置
          oddsList.forEach((item, index) => {
            const bonusKey =
              `random_bonus${index + 1}` as keyof typeof formData;
            const percentKey =
              `random_percent${index + 1}` as keyof typeof formData;
            formData[bonusKey] = item.bonus;
            formData[percentKey] = item.percent;
          });
        } catch (e) {
          console.error("Parse random_odds error:", e);
        }
      }
    }
  },
);

// 监听编辑行数据变化
// watch(
//   () => props.row,
//   (newVal) => {
//     if (newVal) {
//       // 编辑模式
//       formData.redemption_code_id = newVal.id;
//       formData.redemption_pass = newVal.redemption_pass;
//       formData.code_count = newVal.code_count;
//       formData.amount_type = newVal.amount_type;
//       formData.is_random_amount = newVal.bonus === 0 ? 1 : 0;
//       formData.bonus = newVal.bonus ? newVal.bonus / 100 : 0;

//       // 处理截止时间
//       if (newVal.deadline_time) {
//         const date = new Date(newVal.deadline_time);
//         formData.deadline_time = date.getTime() / 1000;
//       } else {
//         formData.deadline_time = undefined;
//       }

//       // 解析随机金额配置
//       if (newVal.random_odds) {
//         try {
//           const oddsList = newVal.random_odds
//             .split(";")
//             .filter(Boolean)
//             .map((item) => {
//               const [bonus, percentStr] = item.split(":");
//               const percent = Number(percentStr?.replace("%", ""));
//               return {
//                 bonus: (Number(bonus) || 0) / 100,
//                 percent: isNaN(percent) ? 0 : percent,
//               };
//             });

//           // 填充随机金额配置
//           oddsList.forEach((item, index) => {
//             const bonusKey =
//               `random_bonus${index + 1}` as keyof typeof formData;
//             const percentKey =
//               `random_percent${index + 1}` as keyof typeof formData;
//             formData[bonusKey] = item.bonus;
//             formData[percentKey] = item.percent;
//           });
//         } catch (e) {
//           console.error("Parse random_odds error:", e);
//         }
//       }
//     } else {
//       // 新建模式，重置表单
//       resetForm();
//     }
//   },
//   { immediate: true },
// );

const validateRandomAmount = () => {
  if (formData.is_random_amount === 1) {
    // 检查是否至少填写了一组金额和概率
    let hasValidPair = false;
    let totalPercent = 0;

    for (let i = 1; i <= 6; i++) {
      const bonus = Number(
        formData[`random_bonus${i}` as keyof typeof formData],
      );
      const percent = Number(
        formData[`random_percent${i}` as keyof typeof formData],
      );

      // 如果填写了金额，对应的概率也必须填写
      if (bonus > 0 && !percent) {
        ElMessage.error(`金额${i}已填写，请填写对应的概率`);
        return false;
      }

      // 如果填写了概率，对应的金额也必须填写
      if (percent > 0 && !bonus) {
        ElMessage.error(`概率${i}已填写，请填写对应的金额`);
        return false;
      }

      // 记录有效的金额概率对
      if (bonus > 0 && percent > 0) {
        hasValidPair = true;
        totalPercent += percent;
      }
    }

    if (!hasValidPair) {
      ElMessage.error("随机金额模式下至少需要填写一组金额和概率");
      return false;
    }

    if (Math.abs(totalPercent - 100) > 0.1) {
      ElMessage.error(
        `概率之和必须为100%，当前总和：${totalPercent.toFixed(1)}%`,
      );
      return false;
    }
  }
  return true;
};

const handleClose = () => {
  emit("update:visible", false);
  // 关闭时重置表单
  resetForm();
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      // 验证随机金额配置
      if (!validateRandomAmount()) {
        return;
      }

      // 验证截止时间
      if (!formData.deadline_time) {
        ElMessage.error("请选择截止时间");
        return;
      }
      // 验证固定金额
      if (formData.is_random_amount === 0 && !formData.bonus) {
        ElMessage.error("请选择固定金额");
        return;
      }

      try {
        // 准备提交数据，所有金额相关字段乘以100，时间转换为整数
        const submitData: CreateExchangeCodeParams | UpdateExchangeCodeParams =
          {
            ...formData,
            deadline_time: parseInt(formData.deadline_time.toString()),
            bonus: formData.bonus * 100,
            random_bonus1: formData.random_bonus1 * 100,
            random_bonus2: formData.random_bonus2 * 100,
            random_bonus3: formData.random_bonus3 * 100,
            random_bonus4: formData.random_bonus4 * 100,
            random_bonus5: formData.random_bonus5 * 100,
            random_bonus6: formData.random_bonus6 * 100,
          };

        if (formData.redemption_code_id) {
          // 编辑模式
          await updateExchangeCode(submitData as UpdateExchangeCodeParams);
        } else {
          // 新建模式
          await createExchangeCode(submitData as CreateExchangeCodeParams);
        }
        handleClose();
        emit("submitted");
      } catch (error) {
        console.error("Save exchange code failed:", error);
      }
    }
  });
};

const disabledDate = (time: Date) => {
  return time.getTime() < Date.now();
};

const shortcuts = [
  {
    text: "今天结束",
    value: () => {
      const date = getBrazilTime();
      date.setHours(23, 59, 59, 999);
      return date;
    },
  },
  {
    text: "明天结束",
    value: () => {
      const date = getBrazilTime();
      date.setDate(date.getDate() + 1);
      date.setHours(23, 59, 59, 999);
      return date;
    },
  },
  {
    text: "一周后",
    value: () => {
      const date = getBrazilTime();
      date.setDate(date.getDate() + 7);
      date.setHours(23, 59, 59, 999);
      return date;
    },
  },
  {
    text: "一个月后",
    value: () => {
      const date = getBrazilTime();
      date.setMonth(date.getMonth() + 1);
      date.setHours(23, 59, 59, 999);
      return date;
    },
  },
];

const handleDateChange = (val: string | number | undefined) => {
  if (val) {
    // 将选择的日期设置为当天的23:59:59
    const date = getBrazilDate(Number(val) * 1000);
    date.setHours(23, 59, 59, 999);
    formData.deadline_time = Math.floor(date.getTime() / 1000);
  } else {
    formData.deadline_time = undefined;
  }
};
</script>

<template>
  <ElDrawer
    v-model="modelValue"
    :title="formData.redemption_code_id ? '编辑兑换码' : '新增兑换码'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    size="600px"
  >
    <ElForm ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <ElFormItem label="参与对象" prop="user_type">
        <ElSelect
          v-model="formData.user_type"
          placeholder="请选择参与对象"
          style="width: 200px"
        >
          <ElOption
            v-for="option in participantOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="兑换码" prop="redemption_pass">
        <ElInput
          v-model="formData.redemption_pass"
          placeholder="请输入兑换码"
        />
      </ElFormItem>

      <ElFormItem label="生成数量" prop="code_count">
        <ElInputNumber
          v-model="formData.code_count"
          :min="1"
          :controls="true"
          style="width: 200px"
          placeholder="请输入生成数量"
        />
      </ElFormItem>

      <ElFormItem label="金额类型" prop="amount_type">
        <ElSelect
          v-model="formData.amount_type"
          placeholder="请选择金额类型"
          style="width: 200px"
        >
          <ElOption
            v-for="option in amountTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </ElSelect>
      </ElFormItem>

      <ElFormItem label="随机金额" prop="is_random_amount">
        <ElSwitch
          v-model="formData.is_random_amount"
          :active-value="1"
          :inactive-value="0"
        />
      </ElFormItem>

      <template v-if="formData.is_random_amount === 0">
        <ElFormItem label="固定金额" prop="bonus" required>
          <ElInputNumber
            v-model="formData.bonus"
            :min="0"
            :precision="0"
            :step="1"
            :controls="true"
            style="width: 200px"
            placeholder="请输入固定金额"
          />
        </ElFormItem>
      </template>

      <template v-if="formData.is_random_amount === 1">
        <div class="random-amount-title">
          随机金额配置（至少填写一组完整的金额和概率，概率之和必须为100%）
        </div>
        <div class="random-amount-list">
          <div v-for="i in 6" :key="i" class="random-amount-item">
            <div class="amount-input">
              <span class="label">金额{{ i }}</span>
              <ElInputNumber
                v-model="formData['random_bonus' + i]"
                :min="0"
                :precision="0"
                :step="1"
                :controls="true"
                style="width: 200px"
                :placeholder="'请输入金额' + i"
              />
            </div>
            <div class="percent-input">
              <span class="label">概率{{ i }}</span>
              <div class="percent-wrapper">
                <ElInputNumber
                  v-model="formData['random_percent' + i]"
                  :min="0"
                  :max="100"
                  :precision="1"
                  :step="0.1"
                  :controls="true"
                  style="width: 120px"
                  :placeholder="'请输入概率' + i"
                  controls-position="right"
                />
                <span class="percent-symbol">%</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <ElFormItem label="截止时间" prop="deadline_time" required>
        <ElDatePicker
          v-model="formData.deadline_time"
          type="date"
          format="YYYY-MM-DD"
          value-format="X"
          :shortcuts="shortcuts"
          :disabled-date="disabledDate"
          placeholder="选择截止时间"
          style="width: 240px"
        />
      </ElFormItem>

      <ElFormItem>
        <ElButton type="primary" @click="handleSubmit">确定</ElButton>
        <ElButton @click="handleClose">取消</ElButton>
      </ElFormItem>
    </ElForm>
  </ElDrawer>
</template>

<style lang="scss" scoped>
:deep(.el-drawer__body) {
  padding: 20px;
  overflow-y: auto;
}

.random-amount-title {
  font-size: 14px;
  color: var(--el-text-color-primary);
  margin: 0 0 16px 0;
  padding-left: 100px;
}

.random-amount-list {
  .random-amount-item {
    display: flex;
    align-items: center;
    gap: 40px;
    margin-bottom: 12px;
    padding: 0 20px 20px;

    &:last-child {
      margin-bottom: 0;
    }

    .amount-input,
    .percent-input {
      display: flex;
      align-items: center;

      .label {
        width: 50px;
        text-align: right;
        font-size: 14px;
        color: var(--el-text-color-regular);
        flex-shrink: 0;
        margin-right: 12px;
      }
    }

    .amount-input {
      flex: 1;
    }

    .percent-input {
      flex: 1;

      .percent-wrapper {
        flex: 1;
        display: flex;
        align-items: center;

        .percent-symbol {
          color: var(--el-text-color-regular);
          margin: 0 0 0 8px;
          font-size: 14px;
        }

        :deep(.el-input-number) {
          .el-input__wrapper {
            padding-right: 30px;
          }

          .el-input-number__decrease,
          .el-input-number__increase {
            width: 24px;
          }
        }
      }
    }
  }
}

:deep(.el-input-number.is-controls-right) {
  .el-input__wrapper {
    padding-left: 11px;
  }
}
</style>
