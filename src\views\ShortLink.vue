<template>
  <div class="short-link">
    <loading-spinner :show="loading" text="Carregando..." />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import LoadingSpinner from '@/components/LoadingSpinner.vue'
import { loginEvent, SHOW_LOGIN_EVENT } from '@/api/request'

const route = useRoute()
const router = useRouter()
const store = useStore()
const loading = ref(true)
const shortCode = route.params.shortCode

onMounted(async () => {
  try {
    // 存储短链代码到 localStorage
    if (shortCode) {
      localStorage.setItem('inviteCode', shortCode as string)
    }
    
    // 检查用户是否已登录
    const isLoggedIn = store.getters['auth/isLoggedIn']
    
    if (!isLoggedIn) {
      // 如果未登录，触发登录弹窗
      loginEvent.dispatchEvent(new Event(SHOW_LOGIN_EVENT))
    }
    
    // 无论是否登录，都跳转到首页
    await router.push('/home')
    window.location.replace('/home')
  } catch (error) {
    console.error('Error handling short link:', error)
    router.push('/home')
    window.location.replace('/home')
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.short-link {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}
</style> 