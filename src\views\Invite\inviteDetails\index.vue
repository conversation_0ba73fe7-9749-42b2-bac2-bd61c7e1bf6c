<template>
  <v-container class="invite-container" max-width="940">
    <!-- 邀请概览区块 -->
    <v-card class="invite-overview-section mb-4">
      <div class="section-header">Visão geral do convite</div>
      <v-card-text class="overview-content">
        <v-table density="compact" class="transparent-table">
          <thead>
            <tr>
              <th class="text-center">Registros<br />hoje</th>
              <th class="text-center">Dep<PERSON><PERSON>s<br />hoje</th>
              <th class="text-center">Valor do<br />depósito hoje</th>
              <th class="text-center">Comissão<br />hoje</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="text-center">
                <div class="flex-cloumn">
                  <span
                    class="item-plus"
                    v-if="overviewData?.registered_compared_to_previous > 0"
                    >+{{ overviewData.registered_compared_to_previous }}</span
                  >
                  <span>{{ overviewData?.total_registered_today || 0 }}</span>
                </div>
              </td>
              <td class="text-center">
                <div class="flex-cloumn">
                  <span
                    class="item-plus"
                    v-if="overviewData?.deposited_compared_to_previous > 0"
                    >+{{ overviewData.deposited_compared_to_previous }}</span
                  >
                  <span>{{ overviewData?.total_deposited_today || 0 }}</span>
                </div>
              </td>
              <td class="text-center">
                <div class="flex-cloumn">
                  <span
                    class="item-plus"
                    v-if="overviewData?.deposit_amount_compared_to_previous > 0"
                    >+{{
                      overviewData.deposit_amount_compared_to_previous
                    }}</span
                  >
                  <span>{{
                    overviewData?.total_deposit_amount_today || 0
                  }}</span>
                </div>
              </td>
              <td class="text-center">
                <div class="flex-cloumn">
                  <span
                    class="item-plus"
                    v-if="overviewData?.commission_compared_to_previous > 0"
                    >+{{ overviewData.commission_compared_to_previous }}</span
                  >
                  <span>{{ overviewData?.total_commission_today || 0 }}</span>
                </div>
              </td>
            </tr>
          </tbody>
        </v-table>
      </v-card-text>
    </v-card>

    <!-- 分级概览区块 -->
    <v-card class="tier-overview-section mb-4">
      <div class="section-header">Visão geral da classificação</div>
      <v-card-text class="tier-table">
        <v-table density="compact" class="transparent-table">
          <thead>
            <tr>
              <th class="text-center">Nível</th>
              <th class="text-center">Total de<br />usuários</th>
              <th class="text-center">Usuários<br />registrados</th>
              <th class="text-center">Usuários com<br />depósito</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="!overviewData?.level_stats?.length">
              <td colspan="4" class="text-center">Nenhum dado disponível</td>
            </tr>
            <tr v-for="level in overviewData?.level_stats" :key="level.level">
              <td class="text-center">{{ level.level }}</td>
              <td class="text-center">{{ level.total_users }}</td>
              <td class="text-center">{{ level.registered_users }}</td>
              <td class="text-center">{{ level.deposited_users }}</td>
            </tr>
          </tbody>
        </v-table>
      </v-card-text>
    </v-card>

    <!-- 奖金说明区块 -->
    <v-card class="bonus-description-section mb-4">
      <div class="section-header">Descrição do bônus</div>
      <v-card-text class="rules-content">
        <div class="rules-text">
          <div class="rules-list">
            <div class="rule-item">
              1. Sua renda consistirá em duas partes: [Bônus de convite] +
              [Comissão de apostas] 【Recompensa de convite】: O primeiro
              depositante que você convidar receberá um bônus em dinheiro de R$
              10. 【Comissão de Apostas】: Esta será sua renda principal e você
              receberá uma porcentagem diferente de comissão de cada aposta
              feita pelos jogadores que você convidar.
            </div>
            <div class="rule-item">
              2. Você receberá uma comissão por cada aposta que convidar os
              usuários a fazer, independentemente de eles ganharem ou perderem.
            </div>
          </div>
        </div>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
// 移除所有与图片功能不直接相关的引入、变量和函数
import { showSuccess, showError } from "@/utils/toast";
import { useStore } from "vuex";
import { overview } from "@/api/auth";

interface LevelStat {
  level: number;
  total_users: number;
  registered_users: number;
  deposited_users: number;
}

interface ApiResponse {
  total_registered_today: number;
  total_registered_yesterday: number;
  registered_compared_to_previous: number;
  total_deposited_today: number;
  total_deposited_yesterday: number;
  deposited_compared_to_previous: number;
  total_deposit_amount_today: number;
  total_deposit_amount_yesterday: number;
  deposit_amount_compared_to_previous: number;
  total_commission_today: number;
  total_commission_yesterday: number;
  commission_compared_to_previous: number;
  level_stats: LevelStat[];
}

const store = useStore();
const overviewData = ref<ApiResponse | null>(null);

const getOverview = async () => {
  try {
    const response = (await overview({
      query_inviter_uuid: store.state.auth?.user?.uuid,
    })) as ApiResponse;
    if (response) {
      overviewData.value = response;
    }
  } catch (error) {
    console.error("Failed to fetch overview:", error);
    showError("Falha ao obter dados. Por favor, tente novamente mais tarde.");
  }
};

onMounted(() => {
  getOverview();
});
</script>

<style lang="scss" scoped>
:deep(.v-input__details) {
  display: none;
}
:deep(.v-textarea .v-field--active textarea) {
  font-size: 14px;
}
.invite-container {
  position: relative;
  padding: 16px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  .section-header {
    color: white;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    padding: 16px;
    background: linear-gradient(87deg, #e5d51f, #3b922a, #e5d51f);
    border-radius: 30px 30px 0 0;

    .sub-header {
      font-size: 14px;
      margin-top: 4px;
      font-weight: normal;
    }
  }
}

.invite-overview-section {
  background: #2C5B40 !important;
  border-radius: 30px;

  .overview-content {
    padding: 8px;
    text-align: center;

    .transparent-table {
      background-color: transparent !important;
      border-radius: 30px;
      overflow: hidden;

      th {
        color: white !important;
        background-color: #253922 !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        height: auto !important;
        vertical-align: top !important;
        white-space: normal !important;
        padding: 8px !important;
      }

      td {
        color: #ffdf00 !important;
        border-bottom: none !important;
        font-size: 16px !important;
        font-weight: 500 !important;
        padding: 8px !important;
        background-color: #253922 !important;

        .item-plus {
          font-size: 10px;
          color: red;
          margin-left: 4px;
          font-weight: normal !important;
          position: absolute;
          top: -8px;
          right: 10px;
        }
        .flex-cloumn {
          position: relative;
        }
      }
    }
  }
}

.tier-overview-section {
  background: #2C5B40 !important;
  border-radius: 30px;
  margin-bottom: 16px;

  .tier-table {
    padding: 8px;

    .transparent-table {
      background-color: transparent !important;
      border-radius: 30px;
      overflow: hidden;

      th {
        color: white !important;
        background-color: #253922 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        height: auto !important;
        vertical-align: top !important;
        white-space: normal !important;
      }

      td {
        color: white !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        font-size: 14px !important;
        background-color: #253922 !important;

        &:last-child {
          border-bottom: none !important;
        }

        &:nth-child(5) {
          color: #ffdf00 !important;
        }
      }
    }
  }
}

.bonus-description-section {
  background: #2C5B40 !important;
  border-radius: 30px;
  margin-bottom: 16px;

  .rules-content {
    padding: 8px;

    .rules-text {
      background: #253922;
      padding: 12px;
      border-radius: 30px;
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
      line-height: 1.5;

      .rules-list .rule-item {
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {

  .invite-container {
    // padding: 4px;

    .section-header {
      font-size: 14px;
      padding: 12px;

      .sub-header {
        font-size: 12px;
      }
    }

    .invite-overview-section {
      .overview-content {
        // padding: 0 8px;

        .transparent-table {
          th,
          td {
            font-size: 10px !important;
            padding: 6px !important;
          }
          td {
            font-size: 14px !important;
          }
        }
      }
    }

    .tier-overview-section {
      .tier-table {
        .transparent-table {
          th,
          td {
            font-size: 12px !important;
            padding: 8px !important;
          }
        }
      }
    }

    .bonus-description-section {
      .rules-content {
        padding: 12px;

        .rules-text {
          padding: 10px;

          .rules-list .rule-item {
            font-size: 12px;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 375px) {
  .invite-container {
    padding: 8px;

    .invite-overview-section {
      .overview-content {
        padding: 4px;
        .transparent-table {
          th,
          td {
            font-size: 9px !important;
            padding: 4px !important;
          }
          td {
            font-size: 12px !important;
          }
        }
      }
    }

    .tier-overview-section {
      .tier-table {
        .transparent-table {
          th,
          td {
            font-size: 10px !important;
            padding: 6px !important;
          }
        }
      }
    }
  }
}
</style>
