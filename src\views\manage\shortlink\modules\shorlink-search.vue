<template>
  <ElCard class="card-wrapper">
    <ElCollapse v-model="activeName">
      <ElCollapseItem :title="$t('common.search')" name="source-search">
        <ElForm ref="formRef" :model="model" label-width="80px">
          <ElRow :gutter="16">
            <ElCol :span="8">
              <ElFormItem label="标题" prop="title">
                <ElInput v-model="model.title" placeholder="请输入标题" clearable @keyup.enter="handleSearch" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <ElFormItem label="状态" prop="status">
                <ElSelect v-model="model.status" placeholder="请选择状态" clearable>
                  <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label"
                    :value="item.value" />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <ElCol :span="8">
              <div class="flex justify-end">
                <ElButton @click="handleReset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </ElButton>
                <ElButton type="primary" plain @click="handleSearch">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </ElButton>
              </div>
            </ElCol>
          </ElRow>
        </ElForm>
      </ElCollapseItem>
    </ElCollapse>
  </ElCard>
</template>


<script setup lang="ts">
import { ref } from 'vue';
import { statusOptions } from '@/constants/common';
import { ElForm, ElFormItem, ElSelect, ElInput, ElButton } from 'element-plus';
import { $t } from '@/locales';

const activeName = ref('source-search');

interface Props {
  model: Record<string, any>;
}

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref();


function handleReset() {
  formRef.value?.resetFields();
  emit('reset');
}

function handleSearch() {
  emit('search');
}
</script>

<style lang="scss" scoped>    
</style>
