<template>
  <div class="user-info-page">
    <!-- 用户基本信息 -->
    <div class="user-info">
      <div class="top-info">
        <div class="d-flex align-center mb-2">
          <v-avatar
            size="60"
            :image="userInfo?.avatar || userTx"
            color="primary"
          ></v-avatar>
          <div class="ml-4">
            <div class="text-h6">{{ userInfo?.nickname || "usuário" }}</div>
            <div class="d-flex align-center">
              <span class="text-caption text-grey"
                >ID: {{ userInfo?.uuid }}</span
              >
              <img src="@/assets/images/user-copy-icon.png" @click="copyToClipboard(userInfo?.uuid)" class="cody-icon" />
            </div>
          </div>
        </div>
        <div class="user-edit-btn d-flex align-center" @click="handleEdit">
          <img src="@/assets/images/user-user-icon.png" class="user-icon" />
          <span>Editar</span>
        </div>
      </div>

      <!-- 账户余额 -->
      <v-card class="balance-card mb-2 mx-4">
        <div
          class="d-flex justify-space-between align-center px-4 balance-title"
        >
          <span class="text-subtitle-1">Saldo atual da conta</span>
          <span class="text-h6"
            >R$ {{ formatNumber(userWallet?.total_balance / 100) }}</span
          >
        </div>
        <div class="pa-4">
          <div class="d-flex justify-space-between mb-2">
            <div class="d-flex flex-column">
              <span class="text-caption">Dinheiro</span>
              <span class="text-caption balance-text"
                >R$ {{ formatNumber(userWallet?.cash_balance / 100) }}</span
              >
            </div>
            <div class="d-flex flex-column">
              <span class="text-caption">Bónus</span>
              <span class="text-caption balance-text"
                >R$ {{ formatNumber(userWallet?.digital_balance / 100) }}</span
              >
            </div>
          </div>
          <div class="text-caption mt-1 text-right">
            <span class="text-color"
              >R${{ formatNumber(withdraw?.daily_withdrawal_limit) }}</span
            >
          </div>
          <div class="d-flex align-center">
            <span class="mr-3">Hoje</span>
            <v-progress-linear
              :model-value="withdraw?.withdrawal_ratio"
              color="#FFDF00"
              height="9"
              rounded
              class="balance-progress"
            ></v-progress-linear>
          </div>
          <!-- <div class="balance-bottom d-flex align-center justify-lg-start mt-4">
            <img src="@/assets/images/purse-icon.png" />
            <span
              >Saldo sacado: R$
              {{ formatNumber(withdraw?.total_withdraw_amount || 0) }}+{{
                formatNumber(withdraw?.daily_withdraw_amount || 0)
              }}</span
            >
          </div> -->
        </div>
      </v-card>

      <!-- VIP等级 -->
      <v-card class="vip-card mb-2 mx-4">
        <template v-if="vipLoading">
          <div class="d-flex justify-center pa-4">
            <v-progress-circular
              indeterminate
              color="#FFDF00"
            ></v-progress-circular>
          </div>
        </template>
        <template v-else>
          <div class="d-flex justify-space-between align-center px-4 vip-title">
            <div class="d-flex align-center">
              <img src="@/assets/images/user-title-icon.png" class="vip-icon" />
              <span class="text-subtitle-2">NIVEL VIP</span>
            </div>
            <div
              class="d-flex justify-end align-center"
              style="cursor: pointer"
              @click="handleVip"
            >
              <span>VIP {{ userInfo?.level }}</span>
              <img
                src="@/assets/images/user-right-icon.png"
                class="right-icon"
              />
            </div>
          </div>
          <v-divider></v-divider>
          <div class="pa-4">
            <div class="text-caption mb-1">
              Proximo nivel
              <span class="text-color"
                >{{ vipProgressObj?.deposit_progress }}%</span
              >
            </div>
            <v-progress-linear
              :model-value="vipProgressObj?.deposit_progress"
              color="#FFDF00"
              height="9"
              rounded
            ></v-progress-linear>
            <div class="text-caption mt-1">
              <div class="text-tips">
                Depositos totais nos ultimos
                {{ [0, 1, 2, 3, 4, 5].includes(userInfo?.level) ? 30 : 60 }}
                dfias: R$
                {{ vipProgressObj?.total_deposit_amount / 100 }}
              </div>
            </div>
            <div>
              <div class="text-caption mt-1">
                Proximo nivel
                <span class="text-color"
                  >{{ vipProgressObj?.bet_progress }}%</span
                >
              </div>
              <v-progress-linear
                :model-value="vipProgressObj?.bet_progress"
                color="#FFDF00"
                height="9"
                rounded
              ></v-progress-linear>
            </div>
            <div class="text-tips mt-1">
              Pontos de apostas nos ltimos
              {{ [0, 1, 2, 3, 4, 5].includes(userInfo?.level) ? 30 : 60 }}
              dias:{{ vipProgressObj?.total_bet_amount / 100 }}
            </div>
          </div>
        </template>
      </v-card>
      <!-- 移动端菜单列表 -->
      <v-card class="vip-card mb-4 mx-4">
        <v-list class="menu-list">
          <v-list-item
            v-for="item in menuItems"
            :key="item.name"
            :ripple="false"
            class="menu-item"
            @click="handleNavClick(item)"
          >
            <template v-slot:prepend>
              <img :src="item.icon" class="icon-img" />
            </template>
            <v-list-item-title>{{ item.name }}</v-list-item-title>
            <template v-slot:append>
              <v-icon size="20" color="grey-lighten-1"
                >mdi-chevron-right</v-icon
              >
            </template>
          </v-list-item>
        </v-list>
      </v-card>
    </div>

    <!-- 退出按钮 -->
    <div class="pr-4 pl-4">
      <v-btn color="error" class="logout-btn mr-4" block @click="handleLogout">
        Sair
      </v-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import { vipProgress } from "@/api/user";
import userTx from "@/assets/images/tx-icon.png";
import maueIcon02 from "@/assets/images/h5/maue-icon-02.png";
import maueIcon03 from "@/assets/images/h5/maue-icon-03.png";
import maueIcon04 from "@/assets/images/h5/maue-icon-04.png";
import maueIcon05 from "@/assets/images/h5/maue-icon-05.png";
import { formatNumber } from "@/utils/index";
import { showSuccess } from "@/utils/toast";

const store = useStore();
const router = useRouter();

const userInfo = computed(() => store.state.auth.user || {});
const userWallet = computed(() => store.state.auth.userWallet || {});
const withdraw = computed(() => {
  const userInfoString = localStorage.getItem("userInfo");
  console.log(JSON.parse(userInfoString || "{}"));
  return userInfoString ? JSON.parse(userInfoString) : null;
});

// 处理退出登录
const handleLogout = () => {
  store.dispatch("auth/logout");
  setTimeout(() => {
    router.push("/home");
  }, 1000);
};

// 处理编辑按钮点击
const handleEdit = () => {
  router.push("/user/profile");
};

// 处理VIP按钮点击
const handleVip = () => {
  router.push("/vip");
};
// 获取vip进度数据
const vipProgressObj = ref();
const vipLoading = ref(false);
const getVipProgress = () => {
  vipLoading.value = true;
  vipProgress()
    .then((res) => {
      vipProgressObj.value = res;
    })
    .finally(() => {
      vipLoading.value = false;
    });
};

const menuItems = [
  {
    name: "Depósito",
    icon: maueIcon02,
    path: "/records",
  },
  {
    name: "Retirar",
    icon: maueIcon03,
    path: "/withdraw",
  },
  {
    name: "Registro de saldo",
    icon: maueIcon04,
    path: "/deposit",
  },
  {
    name: "meu bônus",
    icon: maueIcon05,
    path: "/bonus",
  },
];

const handleNavClick = (item: any) => {
  if (item.path) {
    router.push(item.path);
  }
};
  // 复制到剪贴板
  async function copyToClipboard(text: string): Promise<boolean> {
    try {
      await navigator.clipboard.writeText(text);
       showSuccess("Sucesso de cópia");
      return true;
    } catch {
      const textarea = document.createElement("textarea");
      textarea.value = text;
      textarea.style.position = "fixed";
      textarea.style.opacity = "0";
      document.body.appendChild(textarea);
      textarea.select();
      try {
        document.execCommand("copy");
         showSuccess("Sucesso de cópia");
        return true;
      } catch {
        return false;
      } finally {
        document.body.removeChild(textarea);
      }
    }
   
  }
onMounted(async () => {
  await store.dispatch("auth/fetchUserInfo"); // 更新用户信息
  getVipProgress();
});
</script>

<style lang="scss" scoped>
.user-info-page {
  min-height: 100%;
  padding: 20px 0;
  :deep(.v-list) {
    padding: 0;
  }
  .menu-list {
    display: flex;
    flex-direction: column;
    height: auto;
    overflow: auto;
    // gap: 4px;
    .menu-item {
      height: 36px !important;
      min-height: 36px !important;
      line-height: 36px !important;
      padding: 0 16px;
      border-radius: 8px !important;
      background: #152151;
      .icon-img {
        width: 22px;
        height: 22px;
        margin-right: 8px;
      }
    }
  }
  .cody-icon {
    width: 16px;
    height: 16px;
    margin-left: 4px;
  }

  .top-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    // margin-bottom: 20px;

    .user-edit-btn {
      height: 28px;
      background: linear-gradient(0deg, #c9b737, #2abb27);
      display: flex;
      align-items: center;
      border-radius: 14px;
      justify-content: center;
      padding: 0 8px;
      font-size: 13px;
      line-height: 28px;
      cursor: pointer;

      .user-icon {
        width: 13px;
        margin-right: 4px;
      }

      span {
        padding-top: 2px;
      }
    }
  }

  :deep(.v-card) {
    border-radius: 12px;
    color: white;
  }
}

.balance-card,
.vip-card {
  background: #161e43 !important;

  .balance-title {
    background: linear-gradient(0deg, #c9b737, #2abb27);
    height: 40px;
    line-height: 40px;

    .text-h6 {
      color: #ffdf00;
    }
  }

  .balance-progress {
    border-radius: 5px;
    overflow: inherit;

    :deep(.v-progress-linear__background) {
      background: #41496e !important;
      opacity: 1 !important;
      border-radius: 5px;
    }
  }

  :deep(.balance-progress .v-progress-linear__determinate) {
    border-radius: 5px !important;

    &::after {
      content: "";
      position: absolute;
      display: block;
      background: url("@/assets/images/purse-icon.png") no-repeat center;
      background-size: 100% 100%;
      width: 21px;
      height: 21px;
      right: -10.5px;
      top: -6px;
      z-index: 9999;
    }
  }

  .balance-text {
    color: #f8ce41;
    font-size: 14px;
  }

  .balance-bottom {
    height: 30px;
    border-radius: 5px;
    border: 2px solid #41496e;
    padding: 0 14px;
    font-size: 13px;

    img {
      width: 20px;
      margin-right: 4px;
    }
  }

  .vip-title {
    height: 40px;
    background: url("@/assets/images/user-title-bg.png") no-repeat center;
    background-size: 100% 100%;
    font-size: 15px;

    .vip-icon {
      width: 20px;
      margin-right: 4px;
    }

    .right-icon {
      width: 10px;
      margin-left: 4px;
    }
  }

  .text-tips {
    font-weight: 400;
    font-size: 10px;
    color: #d3cbd8;
    line-height: 19px;
  }

  .text-color {
    color: #ffdf00;
  }
}

.logout-btn {
  height: 40px !important;
  background: #161e43 !important;
  color: #ffdf00 !important;
}
</style>
