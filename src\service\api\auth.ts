import {request} from '../request';

/**
 * Login
 *
 * @param userName User name
 * @param password Password
 */
export function fetchLogin(userName: string, password: string, google_code: string) {
  return request<Api.Auth.UserInfo>({
    url: '/backend/login',
    method: 'post',
    data: {
      user_name:userName,
      password:password,
      google_code:google_code
    }
  });
}

/** 获取用户信息 */
export function fetchGetUserInfo() {
  return request<any>({
    url: '/backend/admin/getPermissions',
    method: 'get'
  });
}

/** 获取谷歌验证码 */
export function fetchGetGoogleSecret(params:any) {
  return request<any>({
    url: '/backend/google-secret-by-userName',
    method: 'get',
    params
  });
}

/** 绑定谷歌验证码 */
export function fetchBindGoogleSecret(params:any) {
  return request<any>({
    url: '/backend/bind-google-secret',
    method: 'post',
    params
  });
}
/**
 * Refresh token
 *
 * @param refreshToken Refresh token
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/refreshToken',
    method: 'post',
    data: {
      refreshToken
    }
  });
}

/**
 * return custom backend error
 *
 * @param code error code
 * @param msg error message
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } });
}
