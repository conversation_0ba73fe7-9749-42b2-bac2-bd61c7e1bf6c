<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-20 15:50:00
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-07-07 21:55:00
 * @FilePath: \betdoce-admind:\new_project\betdoce-web\src\components\mobile\FloatButtons.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="float-buttons">
    <div
      class="draggable-button"
      :style="pwaStyle"
      @mousedown="e => onDragStart(e, 'pwa')"
      @touchstart="e => onDragStart(e, 'pwa')"
    >
      <PWAInstallButton :isImage="true" />
    </div>
    <TelegramShareDialog ref="telegramDialogRef" />
    <div
      class="draggable-button"
      :style="ttStyle"
      @mousedown="e => onDragStart(e, 'tt')"
      @touchstart="e => onDragStart(e, 'tt')"
    >
      <div class="float-button tt-share" @click="handleTTShare">
        <img src="@/assets/images/h5/tt-icon.png" alt="TT Share" />
      </div>
    </div>
    <div
      class="draggable-button"
      :style="csStyle"
      @mousedown="e => onDragStart(e, 'cs')"
      @touchstart="e => onDragStart(e, 'cs')"
    >
      <div class="float-button customer-service" @click="handleCustomerService">
        <img src="@/assets/images/h5/customer-icon.png" alt="Customer Service" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { ref, computed } from "vue";
import { subscriptions } from "@/api/customer-service.ts";
import TelegramShareDialog from "@/components/TelegramShareDialog.vue";
import PWAInstallButton from "@/components/PWAInstallButton.vue";
import type { CSSProperties } from 'vue';

const router = useRouter();
const telegramDialogRef = ref();
const telegramLink = ref("https://t.me/"); // 替换为实际的 Telegram 频道链接

// 拖拽相关
const pwaPos = ref({ x: 0, y: 0 });
const ttPos = ref({ x: 0, y: 60 });
const csPos = ref({ x: 0, y: 120 });
const dragging = ref(false);
const dragType = ref<'pwa'|'tt'|'cs'|null>(null);
const offset = ref({ x: 0, y: 0 });

const pwaStyle = computed<CSSProperties>(() => ({
  position: 'absolute' as CSSProperties['position'],
  left: `${pwaPos.value.x}px`,
  top: `${pwaPos.value.y}px`,
  zIndex: 101
}));
const ttStyle = computed<CSSProperties>(() => ({
  position: 'absolute' as CSSProperties['position'],
  left: `${ttPos.value.x}px`,
  top: `${ttPos.value.y}px`,
  zIndex: 101
}));
const csStyle = computed<CSSProperties>(() => ({
  position: 'absolute' as CSSProperties['position'],
  left: `${csPos.value.x}px`,
  top: `${csPos.value.y}px`,
  zIndex: 101
}));

const onDragStart = (e: MouseEvent | TouchEvent, type: 'pwa'|'tt'|'cs') => {
  dragging.value = true;
  dragType.value = type;
  let clientX = 0, clientY = 0;
  if (e instanceof TouchEvent) {
    clientX = e.touches[0].clientX;
    clientY = e.touches[0].clientY;
  } else {
    clientX = e.clientX;
    clientY = e.clientY;
  }
  let pos;
  if (type === 'pwa') pos = pwaPos.value;
  else if (type === 'tt') pos = ttPos.value;
  else pos = csPos.value;
  offset.value = {
    x: clientX - pos.x,
    y: clientY - pos.y
  };
  window.addEventListener("mousemove", onDragging);
  window.addEventListener("mouseup", onDragEnd);
  window.addEventListener("touchmove", onDragging, { passive: false });
  window.addEventListener("touchend", onDragEnd);
};

const onDragging = (e: MouseEvent | TouchEvent) => {
  if (!dragging.value || !dragType.value) return;
  let clientX = 0, clientY = 0;
  if (e instanceof TouchEvent) {
    clientX = e.touches[0].clientX;
    clientY = e.touches[0].clientY;
    e.preventDefault();
  } else {
    clientX = e.clientX;
    clientY = e.clientY;
  }
  if (dragType.value === 'pwa') {
    pwaPos.value.x = clientX - offset.value.x;
    pwaPos.value.y = clientY - offset.value.y;
  } else if (dragType.value === 'tt') {
    ttPos.value.x = clientX - offset.value.x;
    ttPos.value.y = clientY - offset.value.y;
  } else if (dragType.value === 'cs') {
    csPos.value.x = clientX - offset.value.x;
    csPos.value.y = clientY - offset.value.y;
  }
};

const onDragEnd = () => {
  dragging.value = false;
  dragType.value = null;
  window.removeEventListener("mousemove", onDragging);
  window.removeEventListener("mouseup", onDragEnd);
  window.removeEventListener("touchmove", onDragging);
  window.removeEventListener("touchend", onDragEnd);
};

// 处理TT分享
const handleTTShare = async () => {
  telegramDialogRef.value?.show();
};

//pwd安装
const handlePwd = () => {};

// 处理客服入口
const handleCustomerService = () => {
  router.push("/customer-service");
};
</script>

<style lang="scss" scoped>
.float-buttons {
  position: fixed;
  right: 16px;
  bottom: 120px;
  width: 48px;
  height: 180px;
  z-index: 100;
  /* 移除flex相关样式，避免类型不兼容 */
}
.draggable-button {
  width: 48px;
  height: 48px;
  cursor: grab;
}
.float-button {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
  &:active {
    transform: scale(0.95);
  }
  img {
    width: 48px;
    height: 48px;
  }
}
</style>
