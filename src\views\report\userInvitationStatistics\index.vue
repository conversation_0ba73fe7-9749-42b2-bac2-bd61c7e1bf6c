<script setup lang="tsx">
import {
  ElCard,
  ElPagination,
  ElTable,
  ElTableColumn,
  ElButton,
} from "element-plus";
import { useRouter } from "vue-router";
// import { getWalletList } from "@/service/api/wallet";
import { getInviteReport } from "@/service/api/report";
import { useTable } from "@/hooks/common/table";
import { useAuth } from "@/hooks/business/auth";
import { ref, computed } from "vue";

import UserInvitationStatisticsSearch from "./components/UserInvitationStatisticsSearch.vue";
import moment from "moment";
import { getBrazilDate } from "@/utils/format";

const { hasAuth } = useAuth();
const router = useRouter();

// 搜索参数
const currentSearchParams = ref({});

// 表格Hook配置
const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  getDataByPageSize,
  mobilePagination,
  searchParams,
  resetSearchParams,
} = useTable<any>({
  apiFn: getInviteReport,
  apiParams: {
    page: 1,
    size: 20,
    user_id: "",
    phone: "",
    registration_source: "",
    sort_by: "",
  },
  columns: () => [
    { prop: "index", label: "序号", width: 64 },
    { prop: "user_id", label: "用户ID", minWidth: 120 },
    { prop: "phone", label: "手机号", minWidth: 120 },
    {
      prop: "registration_time",
      label: "注册时间",
      width: 180,
      formatter: (row: any) => moment(getBrazilDate(row.registration_time)).format("YYYY-MM-DD HH:mm:ss"),
    },
    {
      prop: "level1_invite_count",
      label: "一级邀请人数",
      width: 120,
      formatter: (row: any) => String(row.level1_invite_count || 0),
    },
    {
      prop: "level1_first_charge_count",
      label: "一级首充人数",
      width: 120,
      formatter: (row: any) => String(row.level1_first_charge_count || 0),
    },
    {
      prop: "level1_first_charge_amount",
      label: "一级首充金额",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.level1_first_charge_amount || 0) / 100).toFixed(2)}`,
    },
    {
      prop: "level1_total_recharge",
      label: "一级累计充值",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.level1_total_recharge || 0) / 100).toFixed(2)}`,
    },
    {
      prop: "level1_total_withdraw",
      label: "一级累计提现",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.level1_total_withdraw || 0) / 100).toFixed(2)}`,
    },
    {
      prop: "level1_profit_amount",
      label: "一级营收",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.level1_profit_amount || 0) / 100).toFixed(2)}`,
    },
    {
      prop: "level2_invite_count",
      label: "二级邀请人数",
      width: 120,
      formatter: (row: any) => String(row.level2_invite_count || 0),
    },
    {
      prop: "level2_first_charge_count",
      label: "二级首充人数",
      width: 120,
      formatter: (row: any) => String(row.level2_first_charge_count || 0),
    },
    {
      prop: "level2_first_charge_amount",
      label: "二级首充金额",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.level2_first_charge_amount || 0) / 100).toFixed(2)}`,
    },
    {
      prop: "level2_total_recharge",
      label: "二级累计充值",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.level2_total_recharge || 0) / 100).toFixed(2)}`,
    },
    {
      prop: "level2_total_withdraw",
      label: "二级累计提现",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.level2_total_withdraw || 0) / 100).toFixed(2)}`,
    },
    {
      prop: "level2_profit_amount",
      label: "二级营收",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.level2_profit_amount || 0) / 100).toFixed(2)}`,
    },
    {
      prop: "level3_invite_count",
      label: "三级邀请人数",
      width: 120,
      formatter: (row: any) => String(row.level3_invite_count || 0),
    },
    {
      prop: "level3_first_charge_count",
      label: "三级首充人数",
      width: 120,
      formatter: (row: any) => String(row.level3_first_charge_count || 0),
    },
    {
      prop: "level3_first_charge_amount",
      label: "三级首充金额",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.level3_first_charge_amount || 0) / 100).toFixed(2)}`,
    },
    {
      prop: "level3_total_recharge",
      label: "三级累计充值",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.level3_total_recharge || 0) / 100).toFixed(2)}`,
    },
    {
      prop: "level3_total_withdraw",
      label: "三级累计提现",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.level3_total_withdraw || 0) / 100).toFixed(2)}`,
    },
    {
      prop: "level3_profit_amount",
      label: "三级营收",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.level3_profit_amount || 0) / 100).toFixed(2)}`,
    },
    {
      prop: "total_invite_count",
      label: "全部邀请人数",
      width: 120,
      formatter: (row: any) => String(row.total_invite_count || 0),
    },
    {
      prop: "total_first_charge_count",
      label: "全部首充人数",
      width: 120,
      formatter: (row: any) => String(row.total_first_charge_count || 0),
    },
    {
      prop: "total_first_charge_amount",
      label: "全部首充金额",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.total_first_charge_amount || 0) / 100).toFixed(2)}`,
    },
    {
      prop: "total_recharge_amount",
      label: "全部累计充值",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.total_recharge_amount || 0) / 100).toFixed(2)}`,
    },
    {
      prop: "total_withdraw_amount",
      label: "全部累计提现",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.total_withdraw_amount || 0) / 100).toFixed(2)}`,
    },
    {
      prop: "total_profit_amount",
      label: "全部营收",
      width: 120,
      formatter: (row: any) =>
        `R$${((row.total_profit_amount || 0) / 100).toFixed(2)}`,
    },
  ],
});

// 处理搜索事件
const handleSearch = (params: any) => {
  getData();
};

// 处理重置事件
const handleReset = () => {
  resetSearchParams();
};

defineOptions({ name: "UserInvitationStatistics" });
</script>

<template>
  <div
    class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto"
  >
    <ElCard class="search-card">
      <UserInvitationStatisticsSearch
        v-model:model="searchParams"
        @search="handleSearch"
        @reset="handleReset"
      />
    </ElCard>

    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>用户邀请统计</p>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :loading="loading"
            @refresh="getData"
          >
            <span style="width: 1px; height: 35px; background: #e5e6eb"></span>
          </TableHeaderOperation>
        </div>
      </template>

      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="user_id"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>

        <div class="mt-20px flex justify-start">
          <ElPagination
            v-if="mobilePagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="mobilePagination"
           @current-change="mobilePagination['current-change']"
            @size-change="mobilePagination['size-change']"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}

.search-card {
  :deep(.el-form--inline) {
    .el-form-item {
      margin-right: 16px;
      margin-bottom: 16px;

      &:last-child {
        margin-right: 0;
      }

      .el-input,
      .el-select {
        width: 200px;
      }
    }
  }
}
</style>
