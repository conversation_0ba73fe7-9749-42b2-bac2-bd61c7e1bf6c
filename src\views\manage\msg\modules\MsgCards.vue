<template>
  <div class="cards-container" >
    <ElCard v-for="card in cardData" :key="card.id" class="card-item">
      <div class="flex justify-between items-start">
        <div>
          <div class="font-bold text-lg">{{ card.name }}</div>
          <div class="text-sm text-gray-500">{{ card.id }}</div>
        </div>
        <div class="text-xs text-gray-500"> {{ moment(card.end_date).format('YYYY-MM-DD HH:mm:ss') }}</div>
      </div>
      <div class="flex justify-between items-center mt-10px">
        <div class="text-center">
          <div :class="[card.remain === 0 ? 'text-red-500' : 'text-red-500', 'font-bold', 'text-xl']">{{ card.remain }}</div>
          <div class="text-sm text-gray-500">余量 (条)</div>
        </div>
        <div class="text-center">
          <div class="text-green-500 font-bold text-xl">{{ card.successful }}</div>
          <div class="text-sm text-gray-500">成功发送 (条)</div>
        </div>
        <div class="text-center">
          <div class="text-gray-700 font-bold text-xl">{{ card.failed }}</div>
          <div class="text-sm text-gray-500">发送失败 (条)</div>
        </div>
      </div>
      <div class="flex justify-between items-center mt-10px pt-10px border-t border-gray-200">
        <div :class="[card.state === '启用中' ? 'text-green-500' : card.state === '停用中' ? 'text-red-500' : 'text-gray-500', 'font-bold']">{{ card.state }}</div>
        <ElSwitch
          v-model="card.state"
          :disabled="card.state === '已过期'"
          size="large"
          inline-prompt
          active-text="启用"
          inactive-text="停用"
          :active-value="'启用中'"
          :inactive-value="'停用中'"
          @change="handleStatusChange(card)"
        />
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import{packageStatus} from "@/service/api/syssms"
import moment from "moment";

interface CardData {
  id: number;
  name: string;
  channel_id: string;
  total: number;
  sent: number;
  successful: number;
  start_date: string;
  end_date: string;
  status: number;
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
  remain: number;
  failed: number;
  state: string;
}

const props = defineProps<{
  cardData: CardData[]
}>();


const isProgrammaticChange = ref(false);

// 監聽 cardData 的變化
watch(
  () => props.cardData,
  () => {
    isProgrammaticChange.value = true;
    // 這裡如果有需要可以做其他初始化
    setTimeout(() => {
      isProgrammaticChange.value = false;
    }, 0);
  },
  { deep: true }
);

const handleStatusChange = async (card: CardData) => {
  if (isProgrammaticChange.value) {
    // 程式資料變動，不處理
    return;
  }
  if (card.state === '已过期') {
    ElMessage.warning('已过期套餐无法操作');
    card.state = '停用中'; // 保持停用
    return;
  }
  // 调用后端接口更新状态
  try {
    const status = card.state === '启用中' ? 2 : 1;
    await packageStatus({ id: card.id, status });
    ElMessage.success(`套餐 ${card.id} 已${card.state}`);
  } catch (e) {
    ElMessage.error('状态更新失败');
    // 回滚状态
    card.state = card.state === '启用中' ? '停用中' : '启用中';
  }
};
</script>

<style scoped>
.cards-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 20px 0;
  width: 100%;
}

.card-item {
  flex: 0 0 calc(33.333% - 14px);
  min-width: 300px;
  transition: transform 0.2s ease-in-out;
}

.card-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

@media screen and (max-width: 1200px) {
  .card-item {
    flex: 0 0 calc(50% - 10px);
  }
}

@media screen and (max-width: 768px) {
  .card-item {
    flex: 0 0 100%;
  }
}

.flex {
  display: flex;
}
.flex-wrap {
  flex-wrap: wrap;
}
.gap-10px {
  gap: 10px;
}
.pb-10px {
  padding-bottom: 10px;
}
.w-300px {
  width: 300px;
}
.justify-between {
  justify-content: space-between;
}
.items-start {
  align-items: flex-start;
}
.font-bold {
  font-weight: bold;
}
.text-lg {
  font-size: 1.125rem; /* 18px */
}
.text-sm {
  font-size: 0.875rem; /* 14px */
}
.text-gray-500 {
  color: #6b7280;
}
.text-xs {
  font-size: 0.75rem; /* 12px */
}
.items-center {
  align-items: center;
}
.mt-10px {
  margin-top: 10px;
}
.text-center {
  text-align: center;
}
.text-red-500 {
  color: #ef4444;
}
.text-green-500 {
  color: #22c55e;
}
.text-xl {
  font-size: 1.25rem; /* 20px */
}
.text-gray-700 {
  color: #374151;
}
.pt-10px {
  padding-top: 10px;
}
.border-t {
  border-top-width: 1px;
}
.border-gray-200 {
  border-color: #e5e7eb;
}
</style>
