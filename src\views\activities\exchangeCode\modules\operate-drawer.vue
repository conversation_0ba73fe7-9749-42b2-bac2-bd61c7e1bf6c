<template>
  <ElDrawer v-model="visible" :title="title" :size="500">
    <ElForm ref="formRef" :model="model" :rules="rules" label-position="top">
      <ElFormItem label="钱币类型" prop="coinType">
        <ElSelect v-model="model.coinType" placeholder="请选择钱币类型" class="w-full">
          <ElOption
            v-for="item in coinTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem>

      <ElFormItem label="本期兑换码数量" prop="codeCount">
        <ElInput
          v-model.number="model.codeCount"
          placeholder="请输入本期兑换码数量"
          type="number"
        />
      </ElFormItem>

      <ElFormItem label="兑换密码" prop="password">
        <ElInput
          v-model="model.password"
          placeholder="请输入兑换密码"
          :maxlength="6"
          show-word-limit
        />
        <template #tip>
          <span class="text-gray-400 text-sm">不填默认生成的模式，填写则按照新的模式预制一定6位数</span>
        </template>
      </ElFormItem>

      <ElFormItem label="金额" prop="amount">
        <ElInput
          v-model="model.amount"
          type="number"
          :rows="3"
          placeholder="请输入金额"
        />
        <template #tip>
          <span class="text-gray-400 text-sm">金额百分比说明：{"20":20%,"30":30%,"40":50%"} 表示20元占20%, 30元占30%, 40元占50%</span>
        </template>
      </ElFormItem>

      <ElFormItem label="截止时间" prop="endTime">
        <ElDatePicker
          v-model="model.endTime"
          type="datetime"
          placeholder="请选择截止时间"
          class="w-full"
        />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElSpace :size="16">
        <ElButton @click="closeDrawer">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit">确认</ElButton>
      </ElSpace>
    </template>
  </ElDrawer>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useBoolean } from '@sa/hooks';
import { useForm, useFormRules } from '@/hooks/common/form';
import { $t } from '@/locales';
import { fetchAddRole, fetchUpdateRole } from "@/service/api";
import { enableStatusOptions } from '@/constants/business';

defineOptions({ name: 'CodeOperateDrawer' });

interface Model {
  coinType: string;
  codeCount: string | number;
  password: string;
  amount: string;
  endTime: string;
}

interface Props {
  operateType: 'add' | 'edit';
  rowData?: Partial<Model>;
}

const props = withDefaults(defineProps<Props>(), {
  rowData: () => ({})
});

const emit = defineEmits<{
  (e: 'submitted'): void;
}>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useForm();
const { defaultRequiredRule } = useFormRules();
const { bool: menuAuthVisible, setTrue: openMenuAuthModal } = useBoolean();
const { bool: buttonAuthVisible, setTrue: openButtonAuthModal } = useBoolean();

const title = computed(() => {
  const titles: Record<Props['operateType'], string> = {
    add: '新增兑换码',
    edit: '编辑兑换码'
  };
  return titles[props.operateType];
});

// 线币类型选项
const coinTypeOptions = [
  { label: '赠送赠金', value: 'gift' },
  { label: '赠送现金', value: 'quota' }
];

function createDefaultModel(): Model {
  return {
    coinType: '',
    codeCount: '',
    password: '',
    amount: '',
    endTime: ''
  };
}

const model = ref<Model>(createDefaultModel());

const rules = {
  coinType: defaultRequiredRule,
  codeCount: [
    defaultRequiredRule,
    { type: 'number', message: '请输入有效的数字', trigger: 'blur' }
  ],
  password: defaultRequiredRule,
  amount: defaultRequiredRule,
  endTime: defaultRequiredRule
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    model.value = createDefaultModel();
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    model.value = {
      ...createDefaultModel(),
      ...props.rowData
    };
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  const submitData = {
    ...model.value,
  };
  try {
    if (props.operateType === 'edit') {
      // TODO: 替换为实际的更新API
      window.$message?.success('更新成功');
      closeDrawer();
      emit('submitted');
    } else {
      // TODO: 替换为实际的添加API
      window.$message?.success('添加成功');
      closeDrawer();
      emit('submitted');
    }
  } catch (error) {
    window.$message?.error('操作失败');
  }
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<style scoped>
:deep(.el-date-editor.el-input) {
  width: 100%;
}
</style>
