<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { $t } from '@/locales'
import { fetchAddCustomerKeyword, fetchUpdateCustomerKeyword } from '@/service/api/customerFaq'

interface Props {
  operateType: 'add' | 'edit'
  rowData?: any
}

interface Emits {
  (e: 'submitted'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = defineModel<boolean>('visible', {
  default: false
})

const title = computed(() => {
  const operateTypeMap = {
    add: '添加常见问题',
    edit: '编辑常见问题'
  }
  return operateTypeMap[props.operateType]
})

const formRef = ref()
const loading = ref(false)

// 问题类型选项
const keywordTypeOptions = [
  { label: '欢迎语', value: 1 },
  { label: '充值问题', value: 2 },
  { label: '提现问题', value: 3 },
  { label: '账户问题', value: 4 },
  { label: '游戏问题', value: 5 },
  { label: '活动优惠', value: 6 },
  { label: '其他问题', value: 7 }
]

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
]

const form = ref({
  keyword: '',
  response: '',
  keyword_type: 1,
  status: 1,
  weight: 1
})

function closeDrawer() {
  visible.value = false
}

function resetForm() {
  form.value = {
    keyword: '',
    response: '',
    keyword_type: 1,
    status: 1,
    weight: 1
  }
}

function setForm() {
  if (props.operateType === 'edit' && props.rowData) {
    form.value = {
      keyword: props.rowData.keyword || '',
      response: props.rowData.response || '',
      keyword_type: props.rowData.keyword_type || 1,
      status: props.rowData.status ?? 1,
      weight: props.rowData.weight || 1
    }
  }
}

async function handleSubmit() {
  await formRef.value?.validate()
  loading.value = true
  try {
    if (props.operateType === 'add') {
      const { error } = await fetchAddCustomerKeyword(form.value)
      if (!error) {
        ElMessage.success('添加成功')
        closeDrawer()
        emit('submitted')
      }
    } else {
      const { error } = await fetchUpdateCustomerKeyword({
        ...form.value,
        id: props.rowData?.id
      })
      if (!error) {
        ElMessage.success('更新成功')
        closeDrawer()
        emit('submitted')
      }
    }
  } catch (error) {
    console.error('操作失败:', error)
  } finally {
    loading.value = false
  }
}

watch(visible, val => {
  if (val) {
    setForm()
  } else {
    resetForm()
  }
})
</script>

<template>
  <ElDrawer
    v-model="visible"
    :title="title"
    :size="400"
    destroy-on-close
    @close="closeDrawer"
  >
    <ElForm
      ref="formRef"
      :model="form"
      label-width="100px"
      label-position="top"
    >
      <ElFormItem
        label="问题类型"
        prop="keyword_type"
        :rules="[{ required: true, message: '请选择问题类型' }]"
      >
        <ElSelect
          v-model="form.keyword_type"
          placeholder="请选择问题类型"
          style="width: 100%"
        >
          <ElOption
            v-for="item in keywordTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem>

      <ElFormItem
        label="问题"
        prop="keyword"
        :rules="[{ required: true, message: '请输入问题内容' }]"
      >
        <ElInput
          v-model="form.keyword"
          type="textarea"
          :rows="3"
          placeholder="请输入问题内容"
          maxlength="500"
          show-word-limit
        />
      </ElFormItem>

      <ElFormItem
        label="答案"
        prop="response"
        :rules="[{ required: true, message: '请输入答案内容' }]"
      >
        <ElInput
          v-model="form.response"
          type="textarea"
          :rows="5"
          placeholder="请输入答案内容"
          maxlength="1000"
          show-word-limit
        />
      </ElFormItem>

      <ElFormItem
        label="权重"
        prop="weight"
        :rules="[{ required: true, message: '请输入权重' }]"
      >
        <ElInputNumber
          v-model="form.weight"
          :min="1"
          :max="999"
          placeholder="请输入权重"
          style="width: 100%"
        />
        <div class="text-xs text-gray-500 mt-1">权重越大，排序越靠前</div>
      </ElFormItem>

      <ElFormItem
        label="状态"
        prop="status"
        :rules="[{ required: true, message: '请选择状态' }]"
      >
        <ElRadioGroup v-model="form.status">
          <ElRadio
            v-for="item in statusOptions"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </ElRadio>
        </ElRadioGroup>
      </ElFormItem>
    </ElForm>

    <template #footer>
      <ElSpace class="w-full justify-end">
        <ElButton @click="closeDrawer">{{ $t('common.cancel') }}</ElButton>
        <ElButton type="primary" :loading="loading" @click="handleSubmit">
          {{ $t('common.confirm') }}
        </ElButton>
      </ElSpace>
    </template>
  </ElDrawer>
</template>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
