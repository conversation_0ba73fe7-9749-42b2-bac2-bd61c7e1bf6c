<template>
  <v-container class="account-container" max-width="500">
    <div class="account-form">
      <!-- 标题 -->
      <div class="header-info d-flex align-center mb-6">
        <v-btn icon="mdi-arrow-left" variant="text" @click="router.back()" class="mr-4"></v-btn>
        <div class="title">Informações da conta</div>
      </div>

      <!-- 提示信息 -->
      <div class="info-banner d-flex align-center mb-6">
        <v-icon icon="mdi-information" color="primary" class="mr-2"></v-icon>
        <span>Por favor, preencha as informações corretas, caso contrário, a retirada falhará!</span>
      </div>

      <!-- 表单字段 -->
      <div class="form-section">
        <div class="field-label mb-2">Nome do usuário:</div>
        <v-text-field
          v-model="formData.username"
          placeholder="Digite seu nome"
          variant="outlined"
          class="mb-4"
          hide-details
          :error-messages="errors.username"
          @input="validateUsername"
        ></v-text-field>

        <div class="field-label mb-2">Código CPF:</div>
        <v-text-field
          v-model="formData.CPF"
          placeholder="Digite seu CPF"
          variant="outlined"
          class="mb-4"
          v-mask="'###.###.###-##'"
          hide-details
          :error-messages="errors.CPF"
          @input="validateCPF"
        ></v-text-field>

        <div class="field-label mb-2">Tipo Pix:</div>
        <v-select
          v-model="formData.accountType"
          :items="pixTypes"
          variant="outlined"
          class="mb-4"
          hide-details
        ></v-select>

        <div class="field-label mb-2">{{ getPixLabel }}:</div>
        <v-text-field
          v-model="formData.pix"
          :placeholder="getPixPlaceholder"
          variant="outlined"
          class="mb-6"
          hide-pix
          :error-messages="errors.pix"
          @input="validatePixAccount"
        ></v-text-field>
      </div>

      <!-- 提交按钮 -->
      <v-btn
        block
        class="submit-btn"
        color="primary"
        :loading="isLoading"
        :disabled="!isFormValid"
        @click="handleSubmit"
      >
        Enviar
      </v-btn>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { showSuccess, showError } from '@/utils/toast'
import { updateUserProfile } from '@/api/auth'

const router = useRouter()
const store = useStore()
const isLoading = ref(false)

// PIX类型选项
const pixTypes = [
  { title: 'CPF', value: 3 },
  { title: 'Email', value: 1 },
  { title: 'Telefone', value: 2 }
]

// 表单数据
const formData = ref({
  id: '',
  username: '',
  CPF: '',
  accountType: '',
  pix: ''
})

// 表单错误信息
const errors = ref({
  username: '',
  CPF: '',
  pix: '',
  accountType: ''
})

// 从store获取用户信息
const userInfo = computed(() => store.state.auth.user)

// 监听用户信息变化，更新表单数据
watch(userInfo, (newUserInfo) => {
  if (newUserInfo) {
    formData.value = {
      id: newUserInfo.id,
      username: newUserInfo.username || '',
      CPF: newUserInfo.CPF || '',
      accountType: parseInt(newUserInfo.accountType) || '',
      pix: newUserInfo.pix || ''
    }
  }
}, { immediate: true })

// 验证用户名
const validateUsername = () => {
  if (!formData.value.username) {
    errors.value.username = 'O nome do usuário é obrigatório'
    return false
  }
  errors.value.username = ''
  return true
}

// 验证CPF
const validateCPF = () => {
  const cpf = formData.value.CPF.replace(/\D/g, '')
  if (!cpf) {
    errors.value.CPF = 'O CPF é obrigatório'
    return false
  }
  if (cpf.length !== 11) {
    errors.value.CPF = 'CPF inválido'
    return false
  }
  errors.value.CPF = ''
  return true
}

// 验证PIX账号
const validatePixAccount = () => {
  if (!formData.value.pix) {
    errors.value.pix = 'A conta PIX é obrigatória'
    return false
  }
  
  switch (formData.value.accountType) {
    case '3': // CPF
      if (formData.value.pix.replace(/\D/g, '').length !== 11) {
        errors.value.pix = 'CPF inválido'
        return false
      }
      break
    case '1': // EMAIL
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.value.pix)) {
        errors.value.pix = 'Email inválido'
        return false
      }
      break
    case '2': // PHONE
      if (!/^\d{10,11}$/.test(formData.value.pix.replace(/\D/g, ''))) {
        errors.value.pix = 'Telefone inválido'
        return false
      }
      break
  }
  
  errors.value.pix = ''
  return true
}

// 表单验证
const isFormValid = computed(() => {
  return !errors.value.username && 
         !errors.value.CPF && 
         !errors.value.pix &&
         !errors.value.accountType &&
         formData.value.username &&
         formData.value.CPF &&
         formData.value.pix &&
         formData.value.accountType
})

// 根据PIX类型获取label
const getPixLabel = computed(() => {
  switch (formData.value.accountType) {
    case '3':
      return 'CPF'
    case '1':
      return 'Email'
    case '2':
      return 'Telefone'
    default:
      return 'Conta'
  }
})

// 根据PIX类型获取placeholder
const getPixPlaceholder = computed(() => {
  switch (formData.value.accountType) {
    case '3':
      return 'Digite seu CPF'
    case '1':
      return 'Digite seu email'
    case '2':
      return 'Digite seu telefone'
    default:
      return ''
  }
})

// 提交表单
const handleSubmit = async () => {
  // 验证表单
  if (!validateUsername() || !validateCPF() || !validatePixAccount()) {
    return
  }

  try {
    isLoading.value = true
    const response = await updateUserProfile({
      id:userInfo.value.id,
      username: formData.value.username,
      CPF: formData.value.CPF,
      accountType: parseInt(formData.value.accountType),
      pix: formData.value.pix
    })

    if (response) {
      // 更新Vuex中的用户信息
      await store.dispatch('auth/fetchUserInfo')
      showSuccess('Informações salvas com sucesso!')
      router.back()
    } else {
      throw new Error(response.message || 'Falha ao salvar informações')
    }
  } catch (error: any) {
    showError(error.message || 'Falha ao salvar informações')
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  // 初始化表单数据
  const userInfo = store.state.auth.user
  if (userInfo) {
    formData.value = {
      id: userInfo.id,
      username: userInfo.username || '',
      CPF: userInfo.CPF || '',
      accountType: parseInt(userInfo.accountType) || '',
      pix: userInfo.pix || ''
    }
  }
})
</script>

<style lang="scss" scoped>
.account-container {

  padding: 16px;
  min-height: calc(100vh - 64px);
  color: white;

  .header-info {
    .title {
      font-size: 20px;
      font-weight: 500;
    }
  }

  .info-banner {
    background: rgba(255, 67, 102, 0.1);
    border-radius: 8px;
    padding: 12px;
    font-size: 14px;
    color: #FF4366;
  }

  .form-section {
    .field-label {
      font-size: 15px;
      color: #FFFFFF;
    }
  }

  :deep() {
    .v-field__outline {
      border-radius: 8px !important;
      border-color: #333948 !important;
      .v-field__outline__start,
      .v-field__outline__notch,
      .v-field__outline__end {
        border-color: #333948 !important;
        &::before, &::after {
          border-color: #333948 !important;
        }
      }
    }

    .v-field__input {
      min-height: 44px !important;
      padding: 0 16px !important;
      color: #CFD3DC;
    }

    .v-field__field {
      height: 44px;
      line-height: 44px;
    }
  }

  .submit-btn {
    background: linear-gradient(180deg, #A93959, #E5719E);
    border-radius: 20px;
    height: 44px;
    font-size: 16px;
  }
}
</style> 