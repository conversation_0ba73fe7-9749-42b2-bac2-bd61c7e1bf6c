<template>
  <v-container class="withdraw-container" max-width="500">
    <!-- 提现表单 -->
    <div class="withdraw-form">
      <!-- 标题和金额信息 -->
      <div class="header-info d-flex justify-space-between align-center">
        <div class="title">Re<PERSON><PERSON> din<PERSON>iro</div>
        <v-btn
          class="details-btn text-none text-subtitle-1"
          @click="showRecordsDialog = true"
        >
          Detalhes da retirada
        </v-btn>
      </div>

      <!-- 金额显示 -->
      <v-text-field
        :label="'Retirada minima R$ ' + limits?.min_amount"
        class="amount-input"
        variant="outlined"
        v-model="submit.amount"
        type="number"
        :error-messages="amountError"
        @input="validateAmount"
      >
        <template #prepend-inner>
          <span class="label">R$</span>
        </template>
      </v-text-field>
      <div class="amount-info d-flex justify-space-between align-center">
        <div class="left-text">
          Saldo atual: R${{ formatNumber(user_with_amount / 100) }}
        </div>
        <!--          <div class="right-text">Saldo que puede retirar: R${{ formatNumber(availableBalance) }}</div>-->
      </div>
      <div class="section-title mt-4">Escolha o metodo de retirada</div>
      <!-- 提现方式选择 -->
      <v-card variant="text" class="mt-1">
        <div
          class="d-flex justify-space-between align-center withdraw-card pa-2"
        >
          Canal de pagamento rápido
          <img src="@/assets/images/check-icon.png" />
        </div>
      </v-card>
      <div class="section-title mt-4">Escolha a conta bancaria</div>
      <!-- 银行账户选择 -->
      <v-text-field
        :placeholder="submit.cpf ? '' : '+Adicionar informações de retirada'"
        class="amount-input"
        variant="outlined"
        v-model="submit.cpf"
        readonly
        @click="
          () => {
            return submit.cpf ? '' : router.push('/bank-cards');
          }
        "
      >
      </v-text-field>

      <!-- 提交按钮 -->
      <v-btn
        block
        class="submit-btn mt-8 text-none text-subtitle-1"
        color="primary"
        :loading="isLoading"
        @click="handleSubmit"
      >
        Enviar
      </v-btn>
      <div class="section-title mt-8 mb-2">Retirar dinheiro</div>
      <!-- 提现规则 -->
      <div class="rules-section pa-4">
        <div class="rules-list">
          <div class="rule-item">
            1. O valor que pode ser sacado só pode ser aumentado por meio de
            apostas
          </div>
          <div class="rule-item">
            2. O valor minimo de saque é de 50 reais,e cada valor de saque é um
            valor especificado
          </div>
          <div class="rule-item">
            3. O saldo da conta corrente precisa ser apostado para atingir o
            valor que pode ser retirado
          </div>
          <div class="rule-item">
            4. Depois que a recarga for bem-sucedida, você poderá obter o valor
            de retirada imediatamente para cada aposta
          </div>
          <div class="rule-item">
            5. Comissão de depósitos de convite, comissão de apostas em equipe
            pode ser retirada diretamente
          </div>
          <div class="rule-item">
            6. Com exceção das comissões de convite e comissões de apostas em
            equipe todos os bônus não podem ser sacados, você precisa apostar
            para sacar e o poder sacar após atingir o valor
          </div>
        </div>
      </div>
    </div>

    <!-- 提现记录弹窗 -->
    <v-dialog
      v-model="showRecordsDialog"
      max-width="500"
      class="records-dialog"
    >
      <div style="position: relative">
        <v-btn
          icon="mdi-close"
          variant="text"
          size="small"
          @click="showRecordsDialog = false"
          class="close-btn"
        ></v-btn>
        <v-card class="records-card mt-8">
          <v-card-text class="pa-0">
            <div class="balance-info section-title pa-4">
              <div class="balance-amount">
                R${{ formatNumber(totalWithdrawn) }}
              </div>
              <div class="balance-label">Montante total de retirada</div>
            </div>

            <v-list class="records-list pa-0">
              <v-list-item
                v-for="(record, index) in withdrawRecords"
                :key="index"
                class="record-item"
              >
                <div class="record-content">
                  <div class="record-main">
                    <div class="record-title">
                      {{ record.title }}
                      <span
                        class="record-status"
                        :class="
                          record.status === 'completed'
                            ? 'status-success'
                            : 'status-failed'
                        "
                      >
                        {{
                          record.status === "completed" ? "Concluído" : "Falhou"
                        }}
                      </span>
                    </div>
                    <div
                      class="record-amount"
                      :class="{ negative: record.amount < 0 }"
                    >
                      {{ record.amount > 0 ? "+" : ""
                      }}{{ formatNumber(record.amount) }}
                    </div>
                  </div>
                  <div class="record-sub">
                    <div class="record-time">{{ record.time }}</div>
                    <div class="record-balance">
                      saldo：{{ formatNumber(record.balance) }}
                    </div>
                  </div>
                </div>
              </v-list-item>
            </v-list>

            <div class="no-more-data pa-4 text-center" v-if="!hasMore">
              sem mais dados
            </div>
            <!-- 加载更多 -->
            <div v-else class="text-center pa-4">
              <v-btn :loading="loading" @click="loadMore" class="more-btn">
                Carregar mais
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </div>
    </v-dialog>
    <!-- <BindCardDialog
      :show="showBindCardDialog"
      @update:show="handleUpdate"
      :type="'add'"
    ></BindCardDialog> -->
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useStore } from "vuex";
import { showSuccess, showError, showWarning } from "@/utils/toast";
import { bindPaymentAccountList } from "@/api/user";
// import BindCardDialog from "@/views/BankCardlist/components/BindCardDialog.vue";
import {
  withdrawal,
  getWithdrawalRecords,
  getUserWithdrawalInfo,
  withdrawalLimits,
} from "@/api/wallet";

const router = useRouter();
const route = useRoute();
const store = useStore();

// Define interfaces for API responses
interface UserInfo {
  id: number;
  pix: string;
  accountType: string;
  cpf: string;
}

interface UserWithdrawalInfoResponse {
  total_balance: number;
  cpf: string;
  user_id: number;
  user_with_amount: number;
}

interface WithdrawalRecord {
  withdrawal_time: string;
  amount: number;
  remaining_balance: number;
  status: string;
  order_no: string;
  account_num: string;
  account_type: string;
}

interface WithdrawalRecordsResponse {
  records: WithdrawalRecord[];
  total: number;
}

// 从store获取用户信息
const userInfo = computed<UserInfo | null>(() => store.state.auth.user);
// const walletInfo = computed<UserInfo | null>(() => store.state.auth.userWallet);
const user_with_amount = ref();
const submit = ref({
  amount: "",
  account_num: userInfo.value?.pix || "",
  account_type: userInfo.value?.accountType || "CPF",
  currency: "BRL",
  user_id: userInfo.value?.id || 0,
  cpf: userInfo.value?.CPF || "",
});

// 监听用户信息变化，更新表单数据
watch(
  userInfo,
  (newUserInfo) => {
    if (newUserInfo) {
      submit.value.account_num = newUserInfo.pix || "";
      submit.value.account_type = newUserInfo.accountType || "CPF";
      submit.value.cpf = newUserInfo.CPF || "";
      submit.value.user_id = newUserInfo.id;
    }
  },
  { immediate: true }
);

const showRecordsDialog = ref(false);
const isLoading = ref(false);
const amountError = ref("");
const accountNumError = ref("");

// function validateAccountNum() {
//   if (submit.value.account_type === "CPF") {
//     // Validação simples de CPF (pode ser aprimorada conforme necessário)
//     const cpfRegex = /^\d{11}$/;
//     accountNumError.value = cpfRegex.test(submit.value.account_num)
//       ? ""
//       : "Formato de CPF incorreto, deve conter 11 dígitos";
//   } else if (submit.value.account_type === "PHONE") {
//     // Validação de telefone (número brasileiro geralmente tem 11 dígitos, sem +55)
//     const phoneRegex = /^\d{11}$/;
//     accountNumError.value = phoneRegex.test(submit.value.account_num)
//       ? ""
//       : "Formato de telefone incorreto, deve conter 11 dígitos";
//   } else if (submit.value.account_type === "EMAIL") {
//     // Validação de e-mail
//     const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
//     accountNumError.value = emailRegex.test(submit.value.account_num)
//       ? ""
//       : "Formato de e-mail incorreto";
//   }
//   return !accountNumError.value;
// }
const handleAccountTypeChange = () => {
  accountNumError.value = "";
  submit.value.account_num = "";
};
// 加载用户提现信息
const loadUserWithdrawalInfo = async () => {
  try {
    // Ensure user_id is available before making the API call
    if (userInfo.value?.id === undefined) {
      console.error("User ID is not available.");
      showError("User ID is not available.");
      return;
    }

    const response = (await getUserWithdrawalInfo({
      user_id: userInfo.value.id, // Use the non-undefined user_id
    })) as UserWithdrawalInfoResponse; // Cast response to interface
    if (response) {
      // 更新表单数据
      submit.value.cpf = response.cpf || "";
      submit.value.user_id = response.user_id || 0;
      user_with_amount.value = response.user_with_amount;
    }
  } catch (error) {
    console.error("Failed to load user withdrawal info:", error);
  }
};

// 从store获取余额信息
const availableBalance = ref(0);
const totalWithdrawn = ref(0);

// 提现记录数据
const withdrawRecords = ref<any[]>([]);
const withdrawRecordsSuccesss = ref(false);
const loading = ref(false);
const hasMore = ref(true);
const currentPage = ref(1);
const pageSize = 10;

// 加载提现记录
const loadWithdrawalRecords = async (page = 1) => {
  try {
    loading.value = true;
    // Ensure user_id is available before making the API call
    if (userInfo.value?.id === undefined) {
      console.error("User ID for records is not available.");
      showError("User ID for records is not available.");
      loading.value = false;
      return;
    }
    const response = (await getWithdrawalRecords({
      user_id: userInfo.value.id, // Use the non-undefined user_id
      page, // Add page and page_size to parameters
      page_size: pageSize,
    })) as unknown as WithdrawalRecordsResponse; // Cast response to interface

    // Explicitly check if records and total properties exist in the response
    if (response && response.records && response.total !== undefined) {
      const records = response.records;
      const total = response.total;
      const newRecords = records.map((record: WithdrawalRecord) => ({
        title: "Retirada",
        time: record.withdrawal_time,
        amount: -Number(record.amount) / 100,
        balance: Number(record.remaining_balance) / 100,
        status: record.status,
        order_no: record.order_no,
        account_num: record.account_num,
        account_type: record.account_type,
      }));

      if (page === 1) {
        withdrawRecords.value = newRecords;
      } else {
        withdrawRecords.value = [...withdrawRecords.value, ...newRecords];
      }

      hasMore.value = withdrawRecords.value.length < total;
      currentPage.value = page;
      totalWithdrawn.value = withdrawRecords.value.reduce(
        (sum, record) => sum + Math.abs(record.amount),
        0
      );
      withdrawRecordsSuccesss.value =
        withdrawRecords.value.filter((e) => e.status === "completed").length >
        0;
    }
  } catch (error) {
    console.error("Failed to load withdrawal records:", error);
    showError("Falha ao carregar registros de retirada");
  } finally {
    loading.value = false;
  }
};

// 加载更多
const loadMore = () => {
  if (!loading.value && hasMore.value) {
    loadWithdrawalRecords(currentPage.value + 1);
  }
};

// 格式化数字
const formatNumber = (value: number) => {
  return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

// 验证金额
const validateAmount = () => {
  const amount = Number(submit.value.amount);
  if (amount < limits.value.min_amount) {
    amountError.value =
      "O valor mínimo de retirada é R$ " + limits.value.min_amount;
    return false;
  }
  if (amount > user_with_amount.value / 100) {
    amountError.value = "Saldo insuficiente";
    return false;
  }
  amountError.value = "";
  return true;
};

// 表单验证
const isFormValid = computed(() => {
  if (
    submit.value.amount &&
    submit.value.cpf &&
    !amountError.value &&
    !accountNumError.value
  ) {
  }

  return (
    submit.value.amount &&
    submit.value.cpf &&
    !amountError.value &&
    !accountNumError.value
  );
});

// 提交提现
const handleSubmit = async () => {
  if (!validateAmount()) return;
  if (limits.value?.used_daily_count >= limits.value?.daily_count_limit) {
    showWarning("O número de retiradas disponíveis hoje foi esgotado");
    return;
  }
  try {
    isLoading.value = true;
    const amount = Number(submit.value.amount) * 100; // 转换为分
    const response = await withdrawal({
      ...submit.value,
      amount,
    });

    if (response) {
      showSuccess("Retirada solicitada com sucesso!");
      // 清空表单
      submit.value.amount = "";
      // 重新加载提现记录和用户信息
      loadWithdrawalRecords();
      loadUserWithdrawalInfo();
      await store.dispatch("auth/fetchUserInfo");
    } else {
      throw new Error("Falha ao processar a retirada");
    }
  } catch (error: any) {
    showError(error || "Falha ao processar a retirada");
  } finally {
    isLoading.value = false;
  }
};

const limits = ref();
// 获取提现规则
const withdrawalLimitsObj = async () => {
  limits.value = await withdrawalLimits();
  console.log(limits.value);
};

// 刷新所有数据
const refreshData = () => {
  currentPage.value = 1;
  hasMore.value = true;
  loadUserWithdrawalInfo();
  loadWithdrawalRecords(1);
};

// 监听路由变化
watch(
  () => route.fullPath,
  (newPath, oldPath) => {
    console.log("路由變化", {
      newPath,
      oldPath,
      currentPath: route.path,
      isWithdrawPage: route.path === "/withdraw",
    });

    // 當進入或切換到提現頁面時刷新數據
    if (route.path === "/withdraw") {
      console.log("觸發數據刷新");
      refreshData();
    }
  },
  { immediate: true } // 立即執行一次，處理直接進入頁面的情況
);
// 打开绑卡弹窗
const handleBindBank = () => {
  showBindCardDialog.value = true;
};
// 获取绑定银行卡列表
const bankOptions = ref();
const bank = ref();
const showBindCardDialog = ref(false);
const getBindPaymentAccountList = () => {
  bindPaymentAccountList().then((res) => {
    bankOptions.value = res;
    submit.value.account_num = res[0]?.pix;
    submit.value.cpf = res[0]?.cpf;
  });
};
// 绑卡更新
const handleUpdate = (e) => {
  showBindCardDialog.value = false;
  if (e) {
    getBindPaymentAccountList();
  }
};
// 银行卡选择更新
const handleUpdateBank = (e) => {
  // submit.value.account_num = bankOptions.value?.filter((i) => i.cpf === e)[0]?.pix;
  // submit.value.account_num = submit.value.pix;
};

const accountTypeOptions = [
  { label: "CPF", value: "CPF" },
  { label: "Telefone", value: "PHONE" },
  { label: "E-mail", value: "EMAIL" },
];

onMounted(() => {
  console.log("組件掛載，執行初始數據刷新");
  getBindPaymentAccountList();
  withdrawalLimitsObj();
  refreshData();
});
</script>

<style lang="scss" scoped>
.records-dialog {
  .close-btn {
    position: absolute;
    height: 22px;
    width: 22px;
    background: #c9cad8 !important;
    color: #2b324d;
    right: 10px;
    z-index: 9999;
    :deep(.v-icon) {
      font-size: 18px;
    }
  }
  .records-card {
    background: #1b1a4b;
    border-radius: 8px;
    height: auto;
    max-height: 70vh;
    overflow: hidden;
  }

  .dialog-title {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .title-text {
      color: white;
      font-size: 18px;
      font-weight: 500;
    }

    .close-btn {
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .balance-info {
    text-align: center;
    padding: 24px 0;
    background: linear-gradient(87deg, #e5d51f, #3b922a, #e5d51f);

    .balance-amount {
      font-size: 32px;
      font-weight: bold;
      color: white;
      margin-bottom: 8px;
    }

    .balance-label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .records-list {
    background: #1b1a4b;
    height: auto;
    max-height: calc(70vh - 200px);
    overflow: auto;
    .record-item {
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
      padding: 16px;
    }

    .record-content {
      width: 100%;
    }

    .record-main {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
    }

    .record-title {
      font-size: 15px;
      color: white;
    }

    .record-amount {
      font-size: 16px;
      font-weight: 500;
      color: #11d1d1;

      &.negative {
        color: #ffdf00;
      }
    }

    .record-sub {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;
    }

    .record-time,
    .record-balance {
      font-size: 13px;
      color: rgba(255, 255, 255, 0.5);
    }

    .record-status {
      display: inline-block;
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;

      &.status-success {
        background: rgba(39, 174, 96, 0.1);
        color: #27ae60;
      }

      &.status-failed {
        background: rgba(235, 87, 87, 0.1);
        color: #eb5757;
      }
    }
  }

  .no-more-data {
    color: rgba(255, 255, 255, 0.5);
    font-size: 14px;
    background: #1b1a4b;
  }
}
.withdraw-container {
  min-height: calc(100vh - 64px);
  .header-info {
    padding: 12px 0;
    .details-btn {
      border-radius: 15px;
      background: #303142;
      height: 30px;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
    }
  }
  .amount-input {
    :deep() {
      .v-input__control {
        border-radius: 8px !important;
        height: 44px !important;
        background: #1a1b4d;
      }
      .v-field__overlay {
        border-radius: 8px !important;
      }
      .v-field-label {
        color: #cfd3dc;
      }
      .v-field__outline {
        border-radius: 8px !important;
        border-color: #333948 !important;
        .v-field__outline__start,
        .v-field__outline__notch,
        .v-field__outline__end {
          border-color: #333948 !important;
          &::before {
            border-color: #333948 !important;
          }
          &::after {
            border-color: #333948 !important;
          }
        }
      }
      .v-field__field {
        height: 44px;
        line-height: 44px;
      }
      .v-field__input {
        padding: 0 16px !important;
        min-height: 44px;
        height: 30px;
      }
      .label {
        color: #ffdf00;
      }
      .v-text-field__prefix {
        padding-top: 0;
        padding-bottom: 0;
        min-height: auto;
      }
    }
  }
  .withdraw-card {
    background: #333948;
    border-radius: 8px;
    color: #cfd3dc;
    font-size: 17px;
    img {
      width: 22px;
    }
  }
  .section-title {
    font-weight: 400;
    font-size: 15px;
    color: #ffffff;
  }
  .rules-section {
    background: #343f6b;
    border-radius: 14px;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 21px;
  }
  .submit-btn {
    background: linear-gradient(0deg, #c9b737, #2abb27);
    border-radius: 20px;
  }
}
.more-btn {
  background: linear-gradient(0deg, #c9b737, #2abb27);
  border-radius: 15px;
}
</style>
