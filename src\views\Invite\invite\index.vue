<template>
  <v-container class="invite-container" max-width="940">
    <!-- 全局加载状态 -->
    <v-overlay v-model="isLoading" class="align-center justify-center">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay>

    <!-- 平台奖励统计 -->
    <div class="rewards-section">
      <div class="section-header">
        <div class="reward-title">
          Convide amigos para ganhar dinheiro. Quanto mais você convidar, mais
          você ganhará.
        </div>
        <div class="reward-amount">Total Earned R$ {{ totalReward }}</div>
      </div>
      <!-- <div class="rewards-list">
        <div class="reward-item">
        
          <div class="reward-content">
          
            <div class="reward-count">
              {{ totalCount + totalCountCoding }} pessoas recebidas
            </div>
          </div>
        </div>
      </div> -->
    </div>

    <!-- 邀请链接区域 - 使用keep-alive缓存 -->
    <keep-alive>
      <div class="invite-section" :key="'invite-section'">
        <div class="section-header">
          Convide amiyos para ganhar dinheiro
          <div class="sub-header">{{ invite_rule }}</div>
        </div>

        <div class="link-area">
          <div class="link-tip">
            Clique no botão para copiar o link do convite e compartilhá-lo
            através de qualquer software social
          </div>
          <div class="link-box d-flex justify-space-between align-center">
            <div class="link-text">{{ inviteLink || "Carregando..." }}</div>
            <v-btn
              class="copy-btn text-none text-subtitle-1"
              @click="copyLink"
              :loading="generating"
              :disabled="!inviteLink"
              >Link de cópia</v-btn
            >
          </div>
          <!-- <v-textarea
              class="link-desc"
              :model-value="inviteLinkText"
              variant="solo-inverted"
              no-resize
              rows="2"
            ></v-textarea> -->
          <div class="link-desc">
            {{ inviteLinkText || "Carregando descrição..." }}
          </div>
        </div>
      </div>
    </keep-alive>

    <!-- 新增的邀请/打码清单區塊 -->
    <div class="invite-list-section">
      <v-tabs v-model="tab" dark grow selected-class="active" :mobile="true">
        <v-tab value="invite"
          ><span class="invite-font"> Lista de Convidados </span></v-tab
        >
        <v-tab value="coding">
          <span class="invite-font"> Tabela de Comissao de Apostas </span>
        </v-tab>
      </v-tabs>

      <v-window v-model="tab">
        <v-window-item value="invite">
          <div class="total-commission">
            <div class="total-commission-title">Comissões totais</div>
            <div class="total-commission-amount">
              R$ {{ totalInviteCommission }}
            </div>
          </div>
          <v-data-table
            :headers="inviteHeaders"
            :items="inviteList"
            class="elevation-1"
            hide-default-footer
            density="compact"
            mobile-breakpoint="0"
            v-model:sort-by="sortBy"
            @update:sort-by="handleInviteSort"
            :items-per-page="inviteList?.length"
            no-data-text="Nenhum dado disponível"
            :loading="loadingInviteList"
          >
            <template v-slot:item="{ item }">
              <tr>
                <td>
                  {{ item.uuid }}
                  <!-- <v-chip
                    v-if="item.isNew"
                    color="red"
                    label
                    x-small
                    class="ml-1"
                    >NEW</v-chip
                  > -->
                </td>
                <td>{{ item.invite_level }}</td>
                <td>{{ $filters.date(item.register_time) }}</td>
                <td class="commission-cell">+{{ item.commission }}</td>
              </tr>
            </template>
          </v-data-table>
          <div class="view-more-btn-bg">
            <v-btn
              class="view-more-btn mb-3"
              @click="loadMoreCurrentInvit"
              :loading="loadingMoreCurrent"
              :disabled="!hasMoreCurrent || loadingMoreCurrent"
            >
              {{ hasMoreCurrent ? "Ver mais" : "Não há mais dados" }}
            </v-btn>
          </div>
        </v-window-item>

        <v-window-item value="coding">
          <div class="total-commission">
            <div class="total-commission-title">Comissões totais</div>
            <div class="total-commission-amount">
              R$ {{ totalCodingCommission }}
            </div>
          </div>
          <v-data-table
            :headers="codingHeaders"
            :items="codingList"
            hide-default-footer
            class="elevation-1"
            density="compact"
            mobile-breakpoint="0"
            v-model:sort-by="sortByCoding"
            @update:sort-by="handleCodingSort"
            :items-per-page="codingList?.length"
            no-data-text="Nenhum dado disponível"
            :loading="loadingCodingList"
          >
            <template v-slot:item="{ item }">
              <tr>
                <td>{{ item.uuid }}</td>
                <td>{{ item.invite_level }}</td>
                <td>{{ item.total_bet_amount }}</td>
                <td>{{ $filters.date(item.last_bet_date) }}</td>
                <td class="commission-cell">+{{ item.commission_earned }}</td>
              </tr>
            </template>
          </v-data-table>
          <div class="view-more-btn-bg">
            <v-btn
              class="view-more-btn mb-3"
              @click="loadMoreCurrentCoding"
              :loading="loadingMoreCoding"
              :disabled="!hasMoreCoding || loadingMoreCoding"
            >
              {{ hasMoreCoding ? "Ver mais" : "Não há mais dados" }}
            </v-btn>
          </div>
        </v-window-item>
      </v-window>
    </div>

    <!-- 邀请规则说明 -->
    <div class="rules-section">
      <div class="section-header">Regras de bonus</div>
      <div style="text-align: center" class="mt-4">
        <img
          src="@/assets/images/invite-pg.png"
          class="rules-img"
          alt="Invite Rules"
        />
      </div>
      <div class="rules-content">
        <div class="rules-text">
          <div class="rules-title">
            Regras de comissão de convite da plataforma:
          </div>
          <div class="rules-list">
            <div class="rule-item">
              1. Convide novos usuários para se juntarem a nós e concluam o
              primeiro depósito, você podeobter uma recompensa em dinheiro de 10
              reais
            </div>
            <div class="rule-item">
              2. Quando você convida A (novo usuário) para se juntar a nós e
              concluir o depósito, A setornará seu membro de equipe de nível 1,
              e quando A convida B (novo usuário) para se juntar a nós e
              concluir o depósito, B se tornará seu nível 2 membro de equipe,
              quando B convida C(novo usuário) para se juntar a nós e concluir o
              depósito, C torna-se seu membro da equipe denível 3.
            </div>
            <div class="rule-item">
              3. Os membros da equipe de nível 1 podem obter 0,3% de comissão do
              valor da aposta sempreque apostarem, os membros da equipe de nível
              2 podem obter 0,2% de comissão do valor daposta sempre que
              apostarem, os membros da equipe de nível 3 podem receber
              apostassempre que apostarem 0,1% de comissão sobre a quantia.4. A
              comissão de apostas será depositada diretamente em sua conta antes
              das 6 horas do diaseguinte.
            </div>
          </div>
        </div>
      </div>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  reactive,
  watchEffect,
  computed,
  onActivated,
  onDeactivated,
} from "vue";
import { showSuccess } from "@/utils/toast";
import { useStore } from "vuex";
import {
  invertList,
  scanCodeList,
  type InviteListItem,
  type ScanCodeListItem,
  type InviteListParams,
} from "@/api/auth";
import { updateMetaTags } from "@/utils";

const store = useStore();
const inviteLink = ref("");
const inviteLinkText = ref(""); // Texto do link de convite
const generating = ref(false);

// 加载状态
const isLoading = ref(false);
const loadingInviteList = ref(false);
const loadingCodingList = ref(false);

// 数据缓存状态
const isDataCached = ref(false);
const cacheTimestamp = ref(0);
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存时间

const totalReward = computed(() => {
  return (
    Number(totalInviteCommission.value) + Number(totalCodingCommission.value)
  );
});
const totalCount = ref(0);
const totalCountCoding = ref(0);
const invite_rule = ref("");

// Novo: Dados relacionados à lista de convidados/apostas
const tab = ref("invite"); // Lista de convidados selecionada por padrão
const totalInviteCommission = ref("0.00"); // Dados de comissão total, precisam ser substituídos pela fonte de dados real
const totalCodingCommission = ref("0.00"); // Dados de comissão total, precisam ser substituídos pela fonte de dados real
const sortBy = ref<{ key: string; order: "asc" | "desc" }[]>([]);
const sortByCoding = ref<{ key: string; order: "asc" | "desc" }[]>([]);
const inviteHeaders = [
  {
    title: "ID do usuário",
    key: "uuid",
    align: "center" as const,
    sortable: false,
  },
  {
    title: "Nível",
    key: "invite_level",
    align: "center" as const,
    sortable: true,
  },
  {
    title: "Horário de inscrição",
    key: "register_time",
    align: "center" as const,
    sortable: true,
  },
  {
    title: "Comissão",
    key: "commission",
    align: "center" as const,
    sortable: true,
  },
];

const inviteList = ref<InviteListItem[]>([]);

const codingHeaders = [
  {
    title: "ID do usuário",
    key: "uuid",
    align: "center" as const,
    sortable: false,
  },
  {
    title: "Nível",
    key: "invite_level",
    align: "center" as const,
    sortable: true,
  },
  {
    title: "Quantidade codificada",
    key: "total_bet_amount",
    align: "center" as const,
    sortable: true,
  },
  {
    title: "Tempo de codificação",
    key: "last_bet_date",
    align: "center" as const,
    sortable: true,
  },
  {
    title: "Comissão",
    key: "commission_earned",
    align: "center" as const,
    sortable: true,
  },
];

const codingList = ref<ScanCodeListItem[]>([]);
const state = reactive({
  query: {
    page: 1,
    page_size: 10,
    sort_by: "",
    sort_order: "",
    query_inviter_uuid: store.state.auth?.user?.uuid,
    // query_inviter_uuid: 100000123,
  } as InviteListParams,
  queryCoding: {
    page: 1,
    page_size: 10,
    sort_by: "",
    sort_order: "",
    query_inviter_uuid: store.state.auth?.user?.uuid,
    // query_inviter_uuid: 100000123,
  } as InviteListParams,
});

// 加载状态
const loadingMoreCurrent = ref(false);
const loadingMoreCoding = ref(false);

// 是否还有更多数据
const hasMoreCurrent = ref(true);
const hasMoreCoding = ref(true);

// 检查缓存是否有效
const isCacheValid = () => {
  return (
    isDataCached.value && Date.now() - cacheTimestamp.value < CACHE_DURATION
  );
};

// 初始化邀请链接数据
const initializeInviteData = async () => {
  try {
    // 检查缓存是否有效
    if (isCacheValid()) {
      console.log("使用缓存数据");
      return;
    }

    isLoading.value = true;
    // 先请求邀请列表数据，这会同时获取邀请链接
    await invertListData();
    // 再请求扫码列表数据
    await scanCodeListData();

    // 设置缓存状态
    isDataCached.value = true;
    cacheTimestamp.value = Date.now();
  } catch (error) {
    console.error("Failed to initialize invite data:", error);
  } finally {
    isLoading.value = false;
  }
};

// 清除缓存
const clearCache = () => {
  isDataCached.value = false;
  cacheTimestamp.value = 0;
};

// 强制刷新数据
const refreshData = async () => {
  clearCache();
  await initializeInviteData();
};

// 重置分頁
const resetPagination = () => {
  console.log(3423);
  if (tab.value === "invite") {
    inviteList.value = [];
    state.query.page = 1;
  } else {
    state.queryCoding.page = 1;
    codingList.value = [];
  }
};

// 加載更多邀請數據
const loadMoreCurrentInvit = async () => {
  if (loadingMoreCurrent.value || !hasMoreCurrent.value) return;
  loadingMoreCurrent.value = true;
  state.query.page++;
  await invertListData();
  loadingMoreCurrent.value = false;
};

// 加載更多打碼數據
const loadMoreCurrentCoding = async () => {
  if (loadingMoreCoding.value || !hasMoreCoding.value) return;
  loadingMoreCoding.value = true;
  state.queryCoding.page++;
  await scanCodeListData();
  loadingMoreCoding.value = false;
};

// 獲取邀請清單
const invertListData = async () => {
  try {
    loadingInviteList.value = true;
    const res = await invertList(state.query);
    if (state.query.page === 1) {
      totalCount.value = res.total || 0;
      inviteList.value = res.list || [];
    } else {
      inviteList.value = [...inviteList.value, ...(res.list || [])];
    }
    hasMoreCurrent.value = totalCount.value > inviteList.value.length;
    totalInviteCommission.value = res.total_commission || "0.00";
    inviteLinkText.value = res.invite_ctx || "";
    invite_rule.value = res.invite_rule || "";
    if (res.invite_link) {
      generateInviteLink({ short_link: res.invite_link });
    }
  } catch (error) {
    console.error("Failed to get invite list:", error);
  } finally {
    loadingInviteList.value = false;
  }
};

// 獲取打碼清單
const scanCodeListData = async () => {
  try {
    loadingCodingList.value = true;
    const res = await scanCodeList(state.queryCoding);
    if (state.queryCoding.page === 1) {
      totalCountCoding.value = res.total || 0;
      codingList.value = res.list || [];
    } else {
      codingList.value = [...codingList.value, ...(res.list || [])];
    }
    hasMoreCoding.value = totalCountCoding.value > codingList.value.length;
    totalCodingCommission.value = res.total_bet_commission || "0.00";
  } catch (error) {
    console.error("Failed to get scan code list:", error);
  } finally {
    loadingCodingList.value = false;
  }
};

// 生成邀请链接
const generateInviteLink = async (res: { short_link?: string }) => {
  try {
    generating.value = true;
    const userId = store.state.auth.user?.uuid;
    if (!userId) return;

    if (res?.short_link) {
      // 构建完整的邀请链接
      // inviteLink.value = `${window.location.origin}/s/${res.short_link}`;
      inviteLink.value = res.short_link;
    }
  } catch (error) {
    console.error("Failed to generate invite link:", error);
  } finally {
    generating.value = false;
  }
};

// 复制邀请链接
const copyLink = async () => {
  // 如果还没有生成链接，先生成
  if (!inviteLink.value) {
    // await Aaa();
  }

  try {
    if (navigator.clipboard && window.isSecureContext) {
      // 现代浏览器，安全上下文下
      await navigator.clipboard.writeText(inviteLink.value);
    } else {
      // 兼容 iOS/Safari/旧浏览器
      const textarea = document.createElement("textarea");
      textarea.value = inviteLink.value;
      // 防止页面滚动
      textarea.style.position = "fixed";
      textarea.style.top = "0";
      textarea.style.left = "0";
      textarea.style.width = "2em";
      textarea.style.height = "2em";
      textarea.style.padding = "0";
      textarea.style.border = "none";
      textarea.style.outline = "none";
      textarea.style.boxShadow = "none";
      textarea.style.background = "transparent";
      document.body.appendChild(textarea);
      textarea.focus();
      textarea.select();
      try {
        document.execCommand("copy");
      } catch (err) {
        console.error("execCommand Error:", err);
      }
      document.body.removeChild(textarea);
    }
    updateMetaTags(
      inviteLink.value,
      "https://images.box777bet.com/box777/uploads/20250621/1750476723207023850.png"
    );
    showSuccess("Link copiado com sucesso!");
  } catch (err) {
    console.error("Failed to copy:", err);
  }
};

// 處理邀請列表排序
const handleInviteSort = (event: {
  sortBy: { key: string; order: "asc" | "desc" }[];
}) => {
  state.query.sort_by = sortBy.value[0]?.key || "";
  state.query.sort_order = sortBy.value[0]?.order || "";
  resetPagination();
  invertListData();
};

// 處理打碼列表排序
const handleCodingSort = (event: {
  sortBy: { key: string; order: "asc" | "desc" }[];
}) => {
  state.queryCoding.sort_by = sortByCoding.value[0]?.key || "";
  state.queryCoding.sort_order = sortByCoding.value[0]?.order || "";
  resetPagination();
  scanCodeListData();
};

// keep-alive 激活时的处理
onActivated(() => {
  console.log("组件被激活，检查缓存状态");
  // 如果缓存过期，重新加载数据
  if (!isCacheValid()) {
    console.log("缓存已过期，重新加载数据");
    initializeInviteData();
  }
});

// keep-alive 停用时的处理
onDeactivated(() => {
  console.log("组件被停用，保持缓存状态");
});

onMounted(() => {
  // 在页面加载时初始化邀请链接数据
  initializeInviteData();
});

// Carregar mais dados do ranking
const loadMore = () => {
  // TODO: Implementar lógica de carregar mais
};
</script>

<style lang="scss" scoped>
:deep(.v-input__details) {
  display: none;
}
:deep(.v-textarea .v-field--active textarea) {
  font-size: 14px;
}
.invite-container {
  position: relative;
  padding: 16px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  .view-more-btn-bg {
    display: flex;
    justify-content: center;
    background-color: #2c5b40;
  }
  .view-more-btn {
    background: linear-gradient(0deg, #c9b737, #2abb27);
    border-radius: 22px;
    min-width: 160px;
    color: #fff !important;
    height: 44px !important;
    font-size: 18px;
    text-transform: none;
    margin-top: 16px;
  }
  .section-header {
    color: white;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    padding: 16px;
    background: linear-gradient(87deg, #e5d51f, #3b922a, #e5d51f);
    border-radius: 30px 30px 0 0;

    .sub-header {
      font-size: 14px;
      margin-top: 4px;
      font-weight: normal;
    }
  }

  .invite-section {
    background: #2c5b40;
    border-radius: 30px;
    margin-bottom: 16px;

    .link-area {
      padding: 16px;

      .link-tip {
        color: #d1b146;
        font-size: 14px;
        margin-bottom: 12px;
      }

      .link-box {
        border-radius: 30px;
        margin-bottom: 12px;

        .link-text {
          flex: 1;
          color: white;
          margin-right: 12px;
          background: #253922;
          height: 40px;
          line-height: 40px;
          padding: 0 14px;
          border-radius: 30px;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .copy-btn {
          background: linear-gradient(180deg, #a1fa78, #24c404);
          color: white;
          border-radius: 20px;
          height: 40px;
          min-width: 120px;
        }
      }

      .link-desc {
        color: rgba(255, 255, 255, 0.6);
        font-size: 14px !important;
        line-height: 1.4;
        background: #253922;
        padding: 8px 14px;
        border-radius: 30px;
      }
    }
  }

  .rewards-section {
    // background: #2c5b40;
    border-radius: 30px;
    margin-bottom: 16px;
    .section-header {
      border-radius: 40px;
    }
    .rewards-list {
      padding: 16px;

      .reward-item {
        background: #253922;
        border-radius: 30px;
        padding: 12px;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .reward-title {
          color: white;
          margin-bottom: 8px;
        }

        .reward-content {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .reward-amount {
            color: #ffdf00;
            font-size: 18px;
            font-weight: 500;
          }

          .reward-count {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
          }
        }
      }
    }
  }

  .ranking-section {
    background: #2c5b40;
    border-radius: 30px;
    margin-bottom: 16px;

    .ranking-table {
      padding: 0 4px;
      overflow-x: auto;

      .table-header {
        display: grid;
        grid-template-columns: 80px 1fr 1fr 1fr;
        gap: 10px;
        padding: 12px 8px;
        color: white;
        font-size: 14px;
        font-weight: 500;
        background: #1d2654;
        text-align: center;
      }

      .table-row {
        display: grid;
        grid-template-columns: 80px 1fr 1fr 1fr;
        gap: 10px;
        padding: 12px 8px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 14px;
        text-align: center;

        .col {
          display: flex;
          align-items: center;
          justify-content: center;

          &.bonus {
            color: #ffdf00;
          }
        }
      }
    }

    .ranking-tip {
      padding: 12px 16px;
      color: rgba(255, 255, 255, 0.6);
      font-size: 14px;
      line-height: 1.4;
      background: #253922;
      margin: 12px;
      border-radius: 30px;
    }
  }

  .invite-list-section {
    background: #10200d;
    border-radius: 30px;
    margin-bottom: 16px;
    overflow: hidden; // Garantir efeito de borda arredondada

    .active {
      background: linear-gradient(87deg, #e5d51f, #3b922a, #e5d51f);
      border: none;
    }
    .total-commission {
      color: white; /* 將文字顏色設定為白色 */
      font-size: initial; /* 由子元素控制字體大小 */
      font-weight: 500;
      text-align: center;
      padding: 16px;
      background: #2c5b40; /* 設定總金額區域為左右深色、中間淺色的漸變，使用指定的顏色 */
      display: flex; /* 使用 Flexbox */
      flex-direction: column; /* 垂直排列 */
      align-items: center; /* 水平居中 */

      .total-commission-title {
        font-size: 24px; /* 標題字體大小 */
        // margin-bottom: 4px; /* 標題與金額之間的間距 */
      }

      .total-commission-amount {
        font-size: 24px; /* 金額字體大小 */
        font-weight: bold; /* 金額字體加粗 */
        color: #ffdf00; /* 金額文字顏色設定為黃色 */
      }
    }

    .v-data-table {
      background-color: #2c5b40 !important;
      color: white !important;
      max-height: 400px; // 添加最大高度
      overflow-y: scroll; // 恢復垂直滾動功能
      // 隱藏 Webkit 瀏覽器的滾動條 (Chrome, Safari)
      &::-webkit-scrollbar {
        display: none;
      }
      // 隱藏 Firefox 瀏覽器的滾動條
      scrollbar-width: none;

      // 隱藏 IE 和 Edge 瀏覽器的滾動條
      -ms-overflow-style: none;
      :deep(.v-table__wrapper) {
        &::-webkit-scrollbar {
          display: none;
        }
        // 隱藏 Firefox 瀏覽器的滾動條
        scrollbar-width: none;

        // 隱藏 IE 和 Edge 瀏覽器的滾動條
        -ms-overflow-style: none;
        // background-color: #2C5B40 !important;
      }

      :deep(th) {
        color: white !important;

        // background-color: #2ecd62 !important; /* 將表頭背景色改為深綠色 */
        position: sticky; // 固定表頭
        top: 0;
        z-index: 1;
      }

      :deep(td) {
        color: white !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        text-align: center !important;
      }

      // 添加交替的行背景色
      :deep(tbody tr) {
        // background: linear-gradient(
        //   to right,
        //   #55d290,
        //   #1e9154
        // ) !important; /* 設定表格行為漸變背景色 */
      }

      .commission-cell {
        // color: #ffdf00 !important; /* 将佣金颜色设置为黄色 */
      }
    }
  }

  .rules-section {
    // width: 100%;
    background: #2c5b40;
    border-radius: 30px;
    margin-bottom: 16px;

    .rules-img {
      width: 100%;
      height: auto;
      padding: 16px;
      box-sizing: border-box;
      // margin: 16px;
    }

    .rules-content {
      padding: 16px;

      .rules-text {
        background: #253922;
        padding: 12px;
        border-radius: 30px;

        .rules-title {
          color: #ffdf00;
          margin-bottom: 12px;
          font-weight: 500;
        }

        .rules-list {
          color: rgba(255, 255, 255, 0.8);
          font-size: 14px;
          line-height: 1.5;

          .rule-item {
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }

  .bottom-nav {
    position: sticky;
    width: 100%;
    bottom: 0;
    display: flex;
    justify-content: space-around;
    padding: 8px 16px;
    border-radius: 30px;
    margin-top: 16px;

    .nav-btn {
      flex: 1;
      margin: 0 8px;
      height: 44px;
      border-radius: 22px;
      color: rgba(255, 255, 255, 0.6);
      background: transparent;

      &.active {
        color: white;
        background: linear-gradient(0deg, #c9b737, #2abb27);
      }
    }
  }
}

// Adaptação para celular
@media screen and (max-width: 768px) {
  .invite-container {
    padding: 12px;

    .section-header {
      font-size: 14px;
      padding: 12px;

      .sub-header {
        font-size: 12px;
      }
    }
    .invite-section {
      .link-area {
        padding: 12px;

        .link-tip {
          font-size: 12px;
        }

        .link-box {
          .link-text {
            font-size: 12px;
            height: 36px;
            line-height: 36px;
          }

          .copy-btn {
            height: 36px;
            min-width: 100px;
            font-size: 12px;
          }
        }

        .link-desc {
          font-size: 12px;
        }
      }
    }

    .rewards-section {
      .rewards-list {
        padding: 12px;

        .reward-item {
          padding: 10px;

          .reward-title {
            font-size: 14px;
          }

          .reward-content {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .reward-amount {
              font-size: 16px;
            }

            .reward-count {
              font-size: 12px;
            }
          }
        }
      }
    }

    .ranking-section {
      .ranking-table {
        .table-header {
          grid-template-columns: 60px 1fr 1fr 1fr;
          gap: 8px;
          padding: 8px;
          font-size: 12px;
        }

        .table-row {
          grid-template-columns: 60px 1fr 1fr 1fr;
          gap: 8px;
          padding: 8px;
          font-size: 12px;

          .col {
            img {
              width: 36px;
            }
          }
        }
      }

      .ranking-tip {
        padding: 10px 12px;
        font-size: 12px;
        margin: 8px;
      }
    }

    .invite-list-section {
      font-size: 12px !important;
      :deep(.v-tabs--density-default) {
        height: 60px !important;
      }
      :deep(.v-btn) {
        height: 60px !important;
      }
      .view-more-btn-bg {
        .view-more-btn {
          height: 35px !important;
        }
      }
      .invite-font {
        display: inline-block;
        width: 100px;
        word-wrap: break-word;
        white-space: normal;
        font-size: 12px;
      }
      .total-commission {
        font-size: initial; /* 重置字體大小 */
        padding: 12px;

        .total-commission-title {
          font-size: 14px; /* 標題字體大小 */
        }

        .total-commission-amount {
          font-size: 20px; /* 金額字體大小 */
        }
      }

      .v-data-table {
        :deep(th),
        :deep(td) {
          font-size: 12px !important;
          padding: 2px !important;
        }
      }
    }

    .rules-section {
      .rules-img {
        padding: 12px;
        box-sizing: border-box;
      }

      .rules-content {
        padding: 12px;

        .rules-text {
          padding: 10px;

          .rules-title {
            font-size: 14px;
          }

          .rules-list {
            font-size: 12px;
          }
        }
      }
    }

    .bottom-nav {
      padding: 6px 12px;

      .nav-btn {
        height: 40px;
        font-size: 12px;

        img {
          width: 18px;
        }
      }
    }
  }
}

// Adaptação para celular pequeno
@media screen and (max-width: 375px) {
  .invite-container {
    padding: 8px;

    .ranking-section {
      .ranking-table {
        .table-header {
          grid-template-columns: 50px 1fr 1fr 1fr;
          font-size: 11px;
        }

        .table-row {
          grid-template-columns: 50px 1fr 1fr 1fr;
          font-size: 11px;

          .col {
            img {
              width: 32px;
            }
          }
        }
      }
    }

    .invite-list-section {
      :deep(.v-tabs--density-default) {
        height: 60px !important;
      }
      :deep(.v-btn) {
        height: 60px !important;
      }
      .view-more-btn-bg {
        .view-more-btn {
          height: 35px !important;
        }
      }
      .total-commission {
        padding: 10px;

        .total-commission-title {
          font-size: 12px; /* 標題字體大小 */
        }

        .total-commission-amount {
          font-size: 18px; /* 金額字體大小 */
        }
      }
      .v-data-table {
        :deep(th),
        :deep(td) {
          font-size: 10px !important;
          padding: 6px !important;
        }
      }
    }
  }
}
</style>
