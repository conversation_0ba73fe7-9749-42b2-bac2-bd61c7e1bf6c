/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-19 10:47:30
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-05-28 18:18:22
 * @FilePath: \betdoce-admind:\new_project\betdoce-web\src\utils\toast.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ref } from "vue";

export type ToastType = "success" | "error" | "warning" | "info";

interface ToastState {
  show: boolean;
  text: string;
  color: ToastType;
  timeout: number;
}

// 创建响应式的 toast 状态
export const toast = ref<ToastState>({
  show: false,
  text: "",
  color: "info",
  timeout: 3000,
});

// 显示消息的基础函数
const showToast = (
  text: string,
  color: ToastType = "info",
  timeout: number = 3000
) => {
  // 如果当前正在显示消息，先关闭它
  if (toast.value.show) {
    toast.value.show = false;
  }

  // 使用 setTimeout 确保状态更新被正确处理
  setTimeout(() => {
    toast.value = {
      show: true,
      text,
      color,
      timeout,
    };
  }, 50);
};

// 成功消息
export const showSuccess = (text: string, timeout: number = 3000) => {
  if (!text) return;
  showToast(text, "success", timeout);
};

// 错误消息
export const showError = (text: string, timeout: number = 3000) => {
  if (!text) return;
  showToast(text, "error", timeout);
};

// 警告消息
export const showWarning = (text: string, timeout: number = 3000) => {
  if (!text) return;
  showToast(text, "warning", timeout);
};

// 信息消息
export const showInfo = (text: string, timeout: number = 3000) => {
  if (!text) return;
  showToast(text, "info", timeout);
};

// 关闭消息
export const closeToast = () => {
  if (toast.value.show) {
    toast.value.show = false;
  }
};

// 导出所有函数
export default {
  toast,
  showSuccess,
  showError,
  showWarning,
  showInfo,
  closeToast,
};
