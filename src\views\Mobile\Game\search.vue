<template>
    <div class="mobile-game-search">
        <!-- Header with <PERSON> -->
        <div class="header">
            <!-- Search and Filter Bar -->
            <div class="filter-section">
                <v-text-field
                    v-model="searchQuery"
                    placeholder="Digite o name do jogo"
                    variant="outlined"
                    density="comfortable"
                    hide-details
                    class="search-input"
                    @input="handleSearch"
                >
                    <template #prepend-inner>
                        <img
                            src="@/assets/images/query-icon.png"
                            class="search-icon"
                        />
                    </template>
                </v-text-field>
            </div>
        </div>
        <!-- Empty State -->
        <div style="width: 100%" v-if="searchResults.length > 0">
            <div class="games-grid">
                <div
                    v-for="game in searchResults"
                    :key="game.id"
                    class="game-card"
                    @click="navigateToGame(game)"
                >
                    <v-img
                        :src="game.icon"
                        :alt="game.game_name"
                        class="game-image"
                        cover
                        aspect-ratio="0.84"
                        lazy-src="https://via.placeholder.com/40x40?text=..."
                    >
                        <template #error>
                            <!-- 图片加载失败时什么都不显示 -->
                        </template>
                    </v-img>
                    <div class="game-title">{{ game.game_name }}</div>
                    <div class="game-provider">{{ game.manufacturer }}</div>
                </div>
            </div>
        </div>
        <div
            v-if="!searchResults.length && searchQuery && !loading"
            class="empty-state"
        >
            <v-icon size="64" color="grey">mdi-gamepad-variant-outline</v-icon>
            <div class="empty-text">Nenhum jogo encontrado</div>
        </div>
        <div v-if="loading" class="loading-container">
            <v-progress-circular
                indeterminate
                color="primary"
            ></v-progress-circular>
        </div>
        <div class="game-list-class">
            <div
                v-for="item in manufacturers"
                :key="item.id"
                class="filter-chip"
                :class="`filter-active-1`"
                @click="handleManufacturerSelect(item.name)"
            >
                <img v-if="item.icon" :src="item.icon" class="filter-icon" />
                <span v-else>{{ item.name }}</span>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { getManufacturer, searchGetGameList } from "@/api/game";
import type { GameItem } from "@/types/game";

// 自定义防抖函数
const debounce = <T extends (...args: any[]) => any>(fn: T, delay: number) => {
    let timer: ReturnType<typeof setTimeout> | null = null;
    return function (this: any, ...args: Parameters<T>) {
        if (timer) {
            clearTimeout(timer);
        }
        timer = setTimeout(() => {
            fn.apply(this, args);
        }, delay);
    };
};

const router = useRouter();
const loading = ref(false);
const searchQuery = ref("");
const searchResults = ref<GameItem[]>([]);
const currentPage = ref(1);
const pageSize = 10;

// 厂商相关
const manufacturers = ref([]);
const selectedManufacturer = ref("");

// 加载厂商列表
const loadManufacturers = async () => {
    try {
        const response = await getManufacturer();
        if (response) {
            manufacturers.value = response;
        }
    } catch (error) {
        console.error("Failed to load manufacturers:", error);
    }
};

// 选择厂商
const handleManufacturerSelect = (name: string) => {
    console.log(name);
    router.push({
        name: "GameSearchListMobile",
        query: {
            keyword: name,
        },
    });
};

// 搜索防抖处理
const handleSearch = async () => {
    if (!searchQuery.value.trim()) {
        searchResults.value = [];
        return;
    }
    loading.value = true;
    try {
        const response = await searchGetGameList({
            search_key: searchQuery.value.trim(),
            page_no: 1,
            page_size: 30,
        });
        if (response?.data) {
            searchResults.value = response.data;
        } else {
            searchResults.value = [];
        }
    } catch (e) {
        searchResults.value = [];
    } finally {
        loading.value = false;
    }
};

// 加载更多
const hasMore = ref(true);

// script 部分增加跳转方法
const navigateToGame = (game: GameItem) => {
    router.push(`/m/game/${game.game_uid}?uuid=` + game.id);
};

onMounted(() => {
    loadManufacturers();
});
</script>

<style lang="scss" scoped>
// :root, body, #app {
//     width: 100vw !important;
//     min-width: 0 !important;
//     margin: 0 !important;
//     padding: 0 !important;
//     box-sizing: border-box !important;
//     overflow-x: hidden !important;
// }

.mobile-game-search {
    width: 100vw;
    max-width: 100vw;
    min-width: 0;
    margin: 0 auto;
    padding: 8px;
    background: #110e3b;
    min-height: 100vh;
    color: white;
    overflow-x: hidden;
    box-sizing: border-box;
}

.header {
    margin-bottom: 20px;
    .back-btn {
        color: white;
    }

    .search-box {
        flex: 1;
    }

    .search-input {
        width: 100%;
        height: 36px;
        background: #1c1d2f;
        margin-right: 4px;
        border-radius: 8px;
        :deep() {
            .v-field__field {
                height: 36px;
            }
            input {
                padding: 0 8px;
                height: 36px !important;
                line-height: 36px !important;
                min-height: 36px;
            }
            .search-icon {
                width: 19px;
                height: auto;
            }
            .v-field__outline {
                display: none;
            }
        }
    }
}

.games-grid {
    width: 100%;

    margin: 0 auto 16px auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 12px;
    box-sizing: border-box;
    justify-items: center;
}

.game-card {
    width: 30%;
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #18154a;
    border-radius: 12px;

    padding: 8px 0;
    box-sizing: border-box;
    cursor: pointer;
    transition: transform 0.2s;
    &:hover {
        transform: scale(1.05);
    }
}

.game-image {
    width: 100%;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: #222;
    border-radius: 10px;
}

.game-title {
    font-size: 14px;
    margin-top: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    width: 100%;
}

.game-provider {
    font-size: 13px;
    color: #ffdf00;
    margin-top: 4px;
    text-align: center;
    width: 100%;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.empty-state,
.initial-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    color: rgba(255, 255, 255, 0.5);
    text-align: center;

    .empty-text,
    .initial-text {
        margin-top: 16px;
        font-size: 16px;
    }
}

// 添加加载更多按钮样式
.load-more-container {
    padding: 16px 0;
    text-align: center;
}

.load-more-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.2);
    height: 44px;
    border-radius: 22px;
    font-size: 14px;

    &:hover {
        background: rgba(255, 255, 255, 0.15);
    }
}

.game-list-class {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 4px;
    height: auto;
    .filter-chip {
        width: 108px;
        height: 60px;
        line-height: 20px;
        padding: 0 6px;
        text-align: center;
        border-radius: 8px;
        background: #303141;
        margin-bottom: 8px;
        cursor: pointer;
        border: 1px solid #303141;
        word-wrap: break-word;
        display: flex;
        justify-content: center;
        align-items: center;
        .filter-icon {
            width: 100px;
            height: 100%;
        }
    }
    .filter-active-1 {
        background: linear-gradient(35deg, #7c3f5b, #db5b96, #7c3f5b);
        border-color: #b00080;
    }
    .filter-active-2 {
        background: linear-gradient(35deg, #e6870b, #e4ba74, #e6870b);
        border-color: #b74e00;
    }
    .filter-active-3 {
        background: linear-gradient(35deg, #1768cd, #3f82cc, #1768cd);
        border-color: #003e9c;
    }
    .filter-active-4 {
        background: linear-gradient(35deg, #17407e, #2063a3, #17407e);
        border-color: #0049a3;
    }
    .filter-active-5 {
        background: linear-gradient(35deg, #06a57c, #0cc393, #06a57c);
        border-color: #00690f;
    }
}
</style>
