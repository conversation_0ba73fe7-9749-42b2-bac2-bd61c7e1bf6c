<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { enableStatusOptions } from '@/constants/business';
import {fetchAddPaymentChannel,fetchUpdatePaymentChannel} from '@/service/api/walletPayChannel';
import { ro } from 'element-plus/es/locale';

interface Props {
  /** the type of operation */
  operateType: UI.TableOperateType;
  /** the edit row data */
  rowData?: any | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const title = computed(() => {
  const titles: Record<UI.TableOperateType, string> = {
    add: "新增支付通道",
    edit: "编辑支付通道"
  };
  return titles[props.operateType];
});

const formRef = ref();
const formData = reactive({
  channel_name:"",
  channel_type: '',
  system_fees: 0,
  platform_fees: 0,
  min_recharge_amount: 0,
  max_recharge_amount: 0,
  coingate:{
    name:'',
    api_token:"",
    is_test:"",
    base_url:"",
    price_currency:"USD",
    receive_currency:"USDT"
  },
  cashpay:{
    name:'',
    sign_key:"",
    base_url:"",
    app_id:"",
    cpf:'',
    timeout:30
  },
  status: 1
});

const rules = {
  channel_name: [{ required: true, message: '请输入通道名称', trigger: 'blur' }],
  channel_type: [{ required: true, message: '请选择通道类型', trigger: 'change' }],
  system_fees: [{ required: true, message: '请输入提现手续费', trigger: 'blur' }],
  platform_fees: [{ required: true, message: '请输入平台手续费', trigger: 'blur' }],
  min_recharge_amount: [{ required: true, message: '请输入最小充值金额', trigger: 'blur' }],
  min_recharge_amount: [{ required: true, message: '请输入最大充值金额', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  'coingate.name': [{ required: true, message: '请输入支付名称', trigger: 'blur' }],
  'coingate.api_token': [{ required: true, message: '请输入API Token', trigger: 'blur' }],
  'coingate.base_url': [{ required: true, message: '请输入Base URL', trigger: 'blur' }],
  'cashpay.name': [{ required: true, message: '请输入支付名称', trigger: 'blur' }],
  'cashpay.sign_key': [{ required: true, message: '请输入签名密钥', trigger: 'blur' }],
  'cashpay.base_url': [{ required: true, message: '请输入Base URL', trigger: 'blur' }],
  'cashpay.app_id': [{ required: true, message: '请输入APP ID', trigger: 'blur' }],
  'cashpay.timeout': [{ required: true, message: '请输入超时时间', trigger: 'blur' }]
};

const channelTypeOptions = [
  { value: 'cashpay', label: 'CashPay' },
  { value: 'coingate', label: 'CoinGate' }
];

async function fetchDetail() {
  if (!props.rowData?.id) return;
  try {
    Object.assign(formData,props.rowData)
    formData[formData.channel_type]= JSON.parse(formData.config_params);
  } catch (error) {
    window.$message?.error('获取详情失败');
  }
}

function closeDrawer(){
  visible.value = false;
}

async function handleSubmit() {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    let params = {}
    if(formData.channel_type === 'coingate'){
      params = {
        channel_name: formData.channel_name,
        channel_type: formData.channel_type,
        system_fees: formData.system_fees,
        platform_fees: formData.platform_fees,
        min_recharge_amount: formData.min_recharge_amount,
        max_recharge_amount: formData.max_recharge_amount,
        status: formData.status ? 1 : 0,
        config_params: JSON.stringify(formData.coingate)
      }
    } else if(formData.channel_type === 'cashpay'){
      params = {
        channel_name: formData.channel_name,
        channel_type: formData.channel_type,
        system_fees: formData.system_fees,
        platform_fees: formData.platform_fees,
        min_recharge_amount: formData.min_recharge_amount,
        max_recharge_amount: formData.max_recharge_amount,
        status: formData.status ? 1 : 0,
        config_params: JSON.stringify(formData.cashpay)
      }
    }
    if (props.operateType === 'edit') {
      // 这里应该调用更新的API
      const {error} = await fetchUpdatePaymentChannel({
        id: props?.rowData?.id,
        ...params
      });
      if (!error){
      window.$message?.success("更新成功");
      closeDrawer();
      emit('submitted');
      }
    } else {

      // 这里应该调用添加的API
      const {error} = await fetchAddPaymentChannel(params);
      if (!error){
      window.$message?.success("创建成功");
      closeDrawer();
      emit('submitted');
      }
    }
  } catch (error) {
    window.$message?.error("请检查输入内容");
  }
}

watch(visible, async () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
  }
});

/** 编辑时更新表单数据 */
function handleUpdateModelWhenEdit() {
  // 重置表单数据
  if (props.operateType === 'edit' && props.rowData) {
    console.log(props.rowData)
    fetchDetail()
  } else {
    Object.assign(formData, {
      channel_name: '',
      channel_type: '',
      system_fees: 5,
      platform_fees: 10,
      min_recharge_amount: 50,
      max_recharge_amount: 6000,
      status: 1,
      coingate: {
        name: '',
        api_token: '',
        is_test: '',
        base_url: '',
        price_currency: 'USD',
        receive_currency: 'USDT'
      },
      cashpay: {
        name: '',
        sign_key: '',
        base_url: '',
        app_id:'',
        cpf:'',
        timeout: 30
      }
    });
  }
}
</script>

<template>
  <ElDrawer
    :size="360"
    v-model="visible"
    :title="title"
    :close-on-click-modal="false"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
    >
      <ElFormItem label="支付名称" prop="channel_name">
        <ElInput v-model="formData.channel_name" placeholder="请输入通道名称" />
      </ElFormItem>
      <ElFormItem label="通道类型" prop="channel_type">
        <ElSelect v-model="formData.channel_type" placeholder="请选择通道类型" style="width: 100%">
          <ElOption v-for="item in channelTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </ElSelect>
      </ElFormItem>
      <template  v-if="formData.channel_type === 'coingate'">
          <ElFormItem label="支付名称" prop="coingate.name">
            <ElInput v-model="formData.coingate.name" placeholder="请输入支付名称" />
          </ElFormItem>
          <ElFormItem label="API TOKEN" prop="coingate.api_token">
            <ElInput v-model="formData.coingate.api_token" placeholder="请输入API TOKEN" />
          </ElFormItem>
          <ElFormItem label="Base URL" prop="coingate.base_url">
            <ElInput v-model="formData.coingate.base_url" placeholder="例如：https://api.trc20service.com" />
          </ElFormItem>
          <ElFormItem label="汇率定价">
            <ElSelect v-model="formData.coingate.price_currency" placeholder="请选择通道类型" style="width: 100%">
              <ElOption label="USD" value="USD" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="支付货币类型">
            <ElSelect v-model="formData.coingate.receive_currency" placeholder="请选择通道类型" style="width: 100%">
              <ElOption label="USDT" value="USDT" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="测试环境">
            <ElCheckbox v-model="formData.coingate.is_test" />
          </ElFormItem>
      </template>
      <template v-if="formData.channel_type === 'cashpay'">
        <ElFormItem label="支付名称" prop="cashpay.name">
          <ElInput v-model="formData.cashpay.name" placeholder="请输入支付名称" />
        </ElFormItem>
        <ElFormItem label="APP ID" prop="cashpay.app_id">
          <ElInput v-model="formData.cashpay.app_id" placeholder="请输入APP ID" />
        </ElFormItem>
        <ElFormItem label="CPF" prop="cashpay.cpf">
          <ElInput v-model="formData.cashpay.cpf" placeholder="请输入CPF" />
        </ElFormItem>
        <ElFormItem label="签名密钥" prop="cashpay.sign_key">
          <ElInput v-model="formData.cashpay.sign_key" placeholder="请输入签名密钥" />
        </ElFormItem>
        <ElFormItem label="Base URL" prop="cashpay.base_url">
          <ElInput v-model="formData.cashpay.base_url" placeholder="请输入Base URL" />
        </ElFormItem>
        <ElFormItem label="超时时间(秒)" prop="cashpay.timeout">
          <ElInputNumber v-model="formData.cashpay.timeout" :min="1" style="width: 100%" placeholder="请输入超时时间" />
        </ElFormItem>
      </template>
      <ElFormItem label="提现手续费(%)" prop="system_fees">
        <ElInputNumber v-model="formData.system_fees" :min="0" :precision="1" style="width: 100%" placeholder="请输入提现手续费" />
      </ElFormItem>

      <ElFormItem label="平台手续费(%)" prop="platform_fees">
        <ElInputNumber v-model="formData.platform_fees" :min="0" :precision="1" style="width: 100%" placeholder="请输入平台手续费" />
      </ElFormItem>

      <ElFormItem label="最小充值金额" prop="min_recharge_amount">
        <ElInputNumber v-model="formData.min_recharge_amount" :min="0" style="width: 100%" placeholder="请输入最小充值金额" />
      </ElFormItem>

      <ElFormItem label="最大充值金额" prop="max_recharge_amount">
        <ElInputNumber v-model="formData.max_recharge_amount" :min="0" style="width: 100%" placeholder="请输入最大充值金额" />
      </ElFormItem>
      <ElFormItem label="状态" prop="status">
        <ElRadioGroup v-model="formData.status">
          <ElRadio v-for="{ label, value } in enableStatusOptions" :key="value" :value="value" :label="label" />
        </ElRadioGroup>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="flex justify-end gap-2">
        <ElButton @click="visible = false">取消</ElButton>
        <ElButton
          type="primary"
          @click="handleSubmit"
        >
          确定
        </ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<style scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}
</style>