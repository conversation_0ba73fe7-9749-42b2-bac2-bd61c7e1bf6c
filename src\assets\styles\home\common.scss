// 公共样式
.home-container {
  background: #0e753b;
  min-height: 100vh;
  padding: 0 !important;
}

.flier-wrapper {
  width: 100%;
  max-width: 1140px;
  margin: 0 auto;
  background: #ffdf00;
  border-radius: 8px;
}

.content-wrapper {
  width: 100%;
  max-width: 1140px;
  margin: 0 auto;

  border-radius: 5px;
}

// 系统公告样式
.notice-container {
  display: flex;
  justify-content: center;
  border-radius: 20px;
  overflow: hidden;
  z-index: 1;
}

.notice-bar {
  width: 100%;
  max-width: 1140px;
  margin: 0 auto;
  height: 40px;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  padding: 0 24px;
  &.notice-bar-huodong {
    background: #ffdf00;
    .notice-item {
      color: #ff206e !important;
    }
  }
}

.notice-icon {
  width: 23px;
  margin-right: 12px;
  flex-shrink: 0;
}

.notice-scroll {
  flex: 1;
  overflow: hidden;
  height: 24px;
}

.scroll-content {
  display: flex;
  white-space: nowrap;
  will-change: transform;
}

.notice-items {
  display: inline-flex;
  align-items: center;
}

.notice-item {
  color: rgba(255, 255, 255, 0.8);
  font-size: 18px;
  padding-right: 16px;
  cursor: pointer;

  &.important {
    color: #ff206e;
    font-weight: 500;
  }

  .separator {
    margin: 0 16px;
    color: rgba(255, 255, 255, 0.3);
  }
}
// 搜索框基础样式
.search-field {
  :deep() {
    .search-icon {
      width: 18px;
    }
    .v-field {
      border-radius: 24px !important;
      height: 40px;
    }

    .v-field__input {
      color: white !important;
      font-size: 14px;
      padding: 0 8px;
    }

    .v-field__prepend-inner {
      color: rgba(255, 255, 255, 0.7);
    }

    .v-field__outline {
      --v-field-border-opacity: 0.2;
      --v-field-border-width: 0;
      border-radius: 24px !important;
    }

    input::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }
  }
}
