<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { $t } from '@/locales';
import { useForm, useFormRules } from '@/hooks/common/form';
import { enableStatusOptions, userGenderOptions } from '@/constants/business';
import { translateOptions } from '@/utils/common';

defineOptions({ name: 'UserSearch' });

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const activeName = ref(['user-search']);
const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useForm();

const model = defineModel<Api.SystemManage.UserSearchParams>('model', { required: true });

type RuleKey = Extract<keyof Api.SystemManage.UserSearchParams>;

const rules = computed<Record<RuleKey, App.Global.FormRule>>(() => {
  const { patternRules } = useFormRules(); // inside computed to make locale reactive
  return {
    userEmail: patternRules.email,
    userPhone: patternRules.phone
  };
});

const searchModel = reactive({
  user_name: '',
  phone: '',
  gender: undefined as Api.SystemManage.UserGender | undefined,
  status: undefined as Api.Common.EnableStatus | undefined,
  role_id: '',
  level: ''
});

const searchFormItems: FormItem[] = [
  {
    type: 'input',
    formItemProps: {
      prop: 'user_name',
      label: '用户名'
    }
  },
  {
    type: 'input',
    formItemProps: {
      prop: 'phone',
      label: '手机号'
    }
  },
  {
    type: 'select',
    formItemProps: {
      prop: 'gender',
      label: '性别'
    },
    selectProps: {
      options: [
        { label: '男', value: 0 },
        { label: '女', value: 1 }
      ]
    }
  },
  {
    type: 'select',
    formItemProps: {
      prop: 'status',
      label: '状态'
    },
    selectProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }
  },
];

async function handleReset() {
  await restoreValidation();
  emit('reset');
}

async function handleSearch() {
  await validate();
  emit('search');
}
</script>

<template>
  <div class="search-wrapper">
    <ElForm ref="formRef" :model="model" :rules="rules">
      <ElRow :gutter="24">
        <ElCol :span="6">
          <ElFormItem :label="$t('page.manage.user.userName')" prop="user_name">
            <ElInput v-model="model.user_name" :placeholder="$t('page.manage.user.form.userName')" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem :label="$t('page.manage.user.userGender')" prop="userGender">
            <ElSelect v-model="model.gender" clearable :placeholder="$t('page.manage.user.form.userGender')">
              <ElOption v-for="(item, idx) in translateOptions(userGenderOptions)" :key="idx" :label="item.label"
                :value="item.value"></ElOption>
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem :label="$t('page.manage.user.userPhone')" prop="phone">
            <ElInput v-model="model.phone" :placeholder="$t('page.manage.user.form.userPhone')" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem :label="$t('page.manage.user.userStatus')" prop="status">
            <ElSelect v-model="model.status" clearable :placeholder="$t('page.manage.user.form.userStatus')">
              <ElOption v-for="{ label, value } in translateOptions(enableStatusOptions)" :key="value"
                :label="label" :value="value"></ElOption>
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow>
        <ElCol :span="8">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="16">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<style scoped></style>
