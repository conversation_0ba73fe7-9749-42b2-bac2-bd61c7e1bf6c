import { request } from '../request';

// 获取用户列表
export function fetchGetAccountList(params: any) {
  return request({
    url: '/backend/user/list',
    method: 'get',
    params
  });
}

// 获取用户详情
export function fetchGetAccountDetail(data: any) {
  return request({
    url: '/backend/user/show',
    method: 'post',
    data
  });
}

// 更新用户信息
export function fetchUpdateAccount(data: any) {
  return request({
    url: '/backend/user/update',
    method: 'post',
    data
  });
}

// 拉黑用户
export function fetchBlackAccount(data: any) {
  return request({
    url: '/backend/user/doblack',
    method: 'post',
    data
  });
}

// 获取用户操作日志
export function fetchGetAccountOperationLog(params: any) {
  return request({
    url: '/backend/account_operation_log',
    method: 'get',
    params
  });
}

// 修改打码提现金额
export function fetchUpdateCashWithdrawableFromBetVolume(data: { user_id: number; amount: number }) {
  return request({
    url: '/backend/user/updateCashWithdrawableFromBetVolume',
    method: 'post',
    data
  });
}
