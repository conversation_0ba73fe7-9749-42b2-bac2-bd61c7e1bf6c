<template>
  <el-dialog
    :visible.sync="visible"
    width="900px"
    title="账号列表"
    @close="handleClose"
    append-to-body
  >
    <div class="account-list-dialog">
      <!-- 查询条件 -->
      <div class="search-bar">
        <el-input v-model="searchForm.accountId" placeholder="账号ID" size="small" style="width: 180px; margin-right: 10px;" />
        <el-input v-model="searchForm.account" placeholder="账号" size="small" style="width: 180px; margin-right: 10px;" />
        <el-button type="primary" size="small" @click="fetchData">搜索</el-button>
        <el-button size="small" @click="resetSearch">重置</el-button>
        <el-button type="primary" size="small" style="float: right;" @click="handleAdd">新增</el-button>
      </div>
      <!-- 批量操作 -->
      <div class="batch-bar" style="margin: 10px 0;">
        <el-link type="primary" @click="handleBatchDelete">批量删除</el-link>
        <el-link type="primary" style="margin-left: 10px;" @click="handleBatchDisable">批量禁用</el-link>
      </div>
      <!-- 表格 -->
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
        height="350"
        v-loading="loading"
        element-loading-text="載入中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.8)"
      >
        <el-table-column type="selection" width="40" />
        <el-table-column prop="index" label="序号" width="50" />
        <el-table-column prop="accountId" label="账号ID" width="120" />
        <el-table-column prop="accountName" label="账号名" width="120" />
        <el-table-column prop="account" label="账号" width="120" />
        <el-table-column prop="password" label="密码" width="100" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <span :style="{ color: row.status === '启用中' ? '#409EFF' : '#F56C6C' }">{{ row.status }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="{ row }">
            <el-link type="primary" @click="handleEnable(row)" v-if="row.status !== '启用中'">启用</el-link>
            <el-link type="danger" @click="handleDisable(row)" v-if="row.status === '启用中'">禁用</el-link>
            <el-link type="info" @click="handleLog(row)">日志</el-link>
            <el-link type="primary" @click="handleEdit(row)">编辑</el-link>
            <el-link type="danger" @click="handleDelete(row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination-bar" style="margin-top: 10px; text-align: right;">
        <el-pagination
          background
          layout="sizes, total, prev, pager, next"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
          :page-sizes="[5, 10, 20, 50]"
        />
      </div>
    </div>
    <AccountLogDialog v-model:visible="logDialogVisible" :accountId="logAccountId" />
    <AccountEditDialog v-model:visible="editDialogVisible" :editData="editData" @saved="fetchData" />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive } from 'vue';
import AccountLogDialog from './AccountLogDialog.vue';
import AccountEditDialog from './AccountEditDialog.vue';

const props = defineProps<{
  visible: boolean;
  agentId: number | null;
}>();
const emits = defineEmits(['update:visible', 'closed']);

const searchForm = reactive({
  accountId: '',
  account: '',
});
const tableData = ref<any[]>([]);
const total = ref(0);
const pageSize = ref(5);
const currentPage = ref(1);
const selectedRows = ref<any[]>([]);
const logDialogVisible = ref(false);
const logAccountId = ref('');
const editDialogVisible = ref(false);
const editData = ref({});
const loading = ref(false);

watch(
  () => props.visible,
  (val) => {
    if (val && props.agentId) {
      fetchData();
    }
  }
);

async function fetchData() {
  loading.value = true;
  try {
    // TODO: 請根據實際API替換
    // 這裡用假數據模擬
    await new Promise(resolve => setTimeout(resolve, 500)); // 模擬API延遲
    tableData.value = Array.from({ length: pageSize.value }, (_, i) => ({
      index: (currentPage.value - 1) * pageSize.value + i + 1,
      accountId: '*********',
      accountName: '測試用戶',
      account: '*********',
      password: '99199',
      status: i % 2 === 0 ? '禁用' : '啟用中',
    }));
    total.value = 999;
  } finally {
    loading.value = false;
  }
}

function resetSearch() {
  searchForm.accountId = '';
  searchForm.account = '';
  fetchData();
}

function handleSelectionChange(val: any[]) {
  selectedRows.value = val;
}
function handleBatchDelete() {
  // TODO: 批量删除
}
function handleBatchDisable() {
  // TODO: 批量禁用
}
function handleEnable(row: any) {
  // TODO: 启用
}
function handleDisable(row: any) {
  // TODO: 禁用
}
function handleLog(row: any) {
  logAccountId.value = row.accountId;
  logDialogVisible.value = true;
}
function handleEdit(row: any) {
  editData.value = { ...row };
  editDialogVisible.value = true;
}
function handleDelete(row: any) {
  // TODO: 删除
}
function handleAdd() {
  editData.value = {};
  editDialogVisible.value = true;
}
function handleSizeChange(size: number) {
  pageSize.value = size;
  currentPage.value = 1;
  fetchData();
}
function handlePageChange(page: number) {
  currentPage.value = page;
  fetchData();
}
function handleClose() {
  emits('update:visible', false);
  emits('closed');
}
</script>

<style scoped>
.account-list-dialog .search-bar {
  margin-bottom: 10px;
}
.account-list-dialog .el-link {
  margin-right: 8px;
}
</style>
