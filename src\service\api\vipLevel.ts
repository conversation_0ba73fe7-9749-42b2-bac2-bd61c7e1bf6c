import { request } from '../request';

// VIP等级管理相关接口

/**
 * 获取VIP等级列表
 */
export function fetchGetVipLevelList(params?: any) {
  return request({
    url: '/backend/vip/levelList',
    method: 'get',
    params
  });
}

/**
 * 获取VIP签到配置
 */
export function fetchGetVipSigninConfig(params?: any) {
  return request({
    url: '/backend/vip/signinConfig',
    method: 'get',
    params
  });
}

/**
 * 更新VIP等级配置
 */
export function fetchUpdateVipLevel(data: any) {
  return request({
    url: '/backend/vip/updateLevel',
    method: 'post',
    data
  });
}

/**
 * 更新VIP签到配置
 */
export function fetchUpdateVipSignin(data: any) {
  return request({
    url: '/backend/vip/updateSignin',
    method: 'post',
    data
  });
}

/**
 * 批量更新VIP等级配置
 */
export function fetchBatchUpdateVipLevel(data: any) {
  return request({
    url: '/backend/vip/levelBatchUpdate',
    method: 'post',
    data
  });
}

/**
 * 更新VIP签到奖励配置
 */
export function fetchUpdateVipSigninReward(data: any) {
  return request({
    url: '/backend/vip/signinUpdate',
    method: 'post',
    data
  });
}
