import { request } from '../request';

// 获取厂商列表
export function fetchGetManufacturerList(data: any) {
  return request({
    url: '/backend/manufacturer/getManufacturerList',
    method: 'post',
    data
  });
}

// 获取厂商详情
export function fetchGetManufacturerById(data: any) {
  return request({
    url: '/backend/manufacturer/getManufacturerByID',
    method: 'post',
    data
  });
}

// 创建厂商
export function fetchCreateManufacturer(data: any) {
  return request({
    url: '/backend/manufacturer/createManufacturer',
    method: 'post',
    data
  });
}

// 更新厂商
export function fetchUpdateManufacturer(data: any) {
  return request({
    url: '/backend/manufacturer/updateManufacturer',
    method: 'post',
    data
  });
}

// 删除厂商
export function fetchDeleteManufacturer(data: any) {
  return request({
    url: '/backend/manufacturer/deleteManufacturer',
    method: 'post',
    data
  });
}

// 获取厂商字典
export function fetchGetManufacturerDict() {
  return request({
    url: '/backend/manufacturer/getManufacturerDict',
    method: 'get'
  });
}