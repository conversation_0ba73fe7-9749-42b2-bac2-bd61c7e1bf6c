<template>
    <v-container fluid class="game-list-container pa-0" max-width="100vw">
        <div class="content-wrapper">
            <div class="game-list-class" v-if="!['1'].includes(gameClass)&&manufacturers.length">
                <div v-for="item in manufacturers" :key="item.id" class="filter-chip" :class="item.id == selectedManufacturer
                    ? `filter-active-${gameClass}`
                    : ''
                    " @click="handleManufacturerSelect(item.id)">
                    <img v-if="item.icon" :src="item.icon" class="filter-icon" loading="lazy" />
                    <span v-else>{{ item.name }}</span>
                </div>
            </div>
            <!-- Header Section -->
            <div class="content-section" :class="{ 'no-class': ['1'].includes(gameClass)||!manufacturers.length }">
                <div class="d-flex justify-space-between align-center content-section-title"
                    :class="`content-title-${gameClass}`">
                    <div>{{ gameClassLabel[gameClass] }}</div>
                </div>
                <div class="game-grid mt-4">
                    <div v-for="game in games" :key="game.id" v-memo="[game.id]" class="game-column">
                        <v-hover v-slot="{ isHovering, props }">
                            <v-card v-bind="props" :elevation="isHovering ? 8 : 2" class="game-card"
                                @click="handleGameClick(game)">
                                <div class="game-image-wrapper">
                                    <v-img :src="game.icon" aspect-ratio="0.84" cover class="game-image"
                                        :class="{ 'image-hover': isHovering }"
                                        lazy-src="https://via.placeholder.com/40x40?text=..." />
                                    <div v-if="isHovering" class="play-overlay">
                                        <v-btn icon="mdi-play-circle" variant="text" size="x-large"
                                            color="white"></v-btn>
                                    </div>
                                </div>

                                <v-card-text class="pa-1">
                                    <div class="game-title">
                                        {{ game.game_name }}
                                    </div>
                                    <div class="game-provider">
                                        {{ game.manufacturer }}
                                    </div>
                                </v-card-text>
                            </v-card>
                        </v-hover>
                    </div>
                </div>

                <!-- 加载更多 -->
                <div v-if="hasMore" class="text-center pa-4">
                    <v-btn :loading="loading" @click="loadMore" class="more-btn">
                        Carregar mais
                    </v-btn>
                </div>
            </div>
        </div>
    </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { getGameList, getManufacturer } from "@/api/game";
import type { GameItem } from "@/types/game";
import { useRoute, useRouter } from "vue-router";
import { getToken } from "@/utils/auth.ts";
import { loginEvent, SHOW_LOGIN_EVENT } from "@/api/request.ts";
const route = useRoute();
const router = useRouter();
// 定义厂家类型
interface Manufacturer {
    id: string | number;
    icon?: string;
    name: string;
}
// 状态变量
const searchQuery = ref("");
const games = ref<GameItem[]>([]);
const pageNo = ref(1);
const PAGE_SIZE = 30; // 常量
const total = ref(0);
const loading = ref(false);
let isInitialized = false;
const gameClass = ref("");
const manufacturers = ref<Manufacturer[]>([]);
const selectedManufacturer = ref<string | number>("");

// 监听路由变化
watch(
    route,
    (newVal) => {
        if (newVal.query?.id) {
            gameClass.value = newVal.query.id as string;
            if (isInitialized) {
                fetchGames();
            }
        }
    },
    { immediate: true }
);

// 计算是否有更多数据
const hasMore = computed(() => games.value.length < total.value);
const gameClassLabel: Record<string, string> = {
    "1": "Hot",
    "2": "Slots",
    "3": "Bônus Slots",
    "4": "Cassino ao Vivo",
    "7": "Cartões",
    "8": "Pescaria",
    "9": "Coluna",
};

// 获取游戏列表
const fetchGames = async (isLoadMore = false) => {
    if (loading.value) return;
    loading.value = true;
    try {
        const params = {
            page_no: pageNo.value,
            page_size: PAGE_SIZE,
            game_type: gameClass.value,
            manufacturer_id: selectedManufacturer.value.toString(),
            search_key: searchQuery.value,
        };
        if (gameClass.value == "1") {
            delete params.manufacturer_id;
        }
        const res = await getGameList(params);
        if (res.data) {
            if (isLoadMore) {
                games.value.push(...res.data);
            } else {
                games.value = res.data;
            }
            total.value = res.total;
        }
    } catch (error) {
        console.error("Falha ao buscar jogos:", error);
    } finally {
        loading.value = false;
    }
};
// 加载更多
const loadMore = () => {
    if (loading.value || !hasMore.value) return;
    pageNo.value++;
    fetchGames(true);
};

// 游戏点击处理
const handleGameClick = (game: GameItem) => {
    if (!getToken()) {
        // 如果未登录，跳转到登录页
        loginEvent.dispatchEvent(new Event(SHOW_LOGIN_EVENT));
        return;
    }
    router.push(`/m/game/${game.game_uid}?uuid=` + game.id);
};

// 加载厂家列表
const loadManufacturers = async () => {
    // console.log("getManufacturer", gameClass.value)
    try {
        const response = await getManufacturer({
            manufacturer_type: gameClass.value,
        });
        if (response && Array.isArray(response)) {
            manufacturers.value = response;
            selectedManufacturer.value = manufacturers.value[0]?.id || "";
        } else {
            manufacturers.value = [];
            selectedManufacturer.value = "";
        }
    } catch (error) {
        console.error("Failed to load manufacturers:", error);
    }
};

// 选择厂家
const handleManufacturerSelect = (manufacturer: string | number) => {
    console.log(manufacturer);
    selectedManufacturer.value = manufacturer;
    // 重新加载游戏列表
    fetchGames();
};
onMounted(async () => {
    if (route.query.id) {
        gameClass.value = route.query.id as string;
    }
    await loadManufacturers();
    fetchGames();
    isInitialized = true;
});
</script>

<style lang="scss" scoped>
.game-list-container {
    .more-btn {
        background: linear-gradient(0deg, #c9b737, #2abb27);
        border-radius: 15px;
    }

    min-height: 100%;

    .content-wrapper {
        .game-list-class {
            background: #30a965;
            padding: 8px;
            width: 108px;
            max-height: calc(100vh - 120px);
            float: left;
            overflow-y: scroll;
            border-radius: 0 8px 8px 0;
            // 隐藏滚动条（兼容主流浏览器）
            scrollbar-width: none;
            /* Firefox */
            -ms-overflow-style: none;

            /* IE 10+ */
            &::-webkit-scrollbar {
                display: none;
                /* Chrome/Safari/Webkit */
            }

            .filter-icon {
                width: 90px;
            }

            .filter-chip {
                width: 92px;
                height: 60px;
                overflow: hidden;
                line-height: 20px;
                padding: 0 6px;
                text-align: center;
                border-radius: 8px;
                background: #303141;
                margin-bottom: 8px;
                cursor: pointer;
                border: 1px solid #303141;
                word-wrap: break-word;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .filter-active-1 {
                background: linear-gradient(35deg, #7c3f5b, #db5b96, #7c3f5b);
                border-color: #b00080;
            }

            .filter-active-2 {
                background: linear-gradient(35deg, #e6870b, #e4ba74, #e6870b);
                border-color: #b74e00;
            }

            .filter-active-3 {
                background: linear-gradient(35deg, #1768cd, #3f82cc, #1768cd);
                border-color: #003e9c;
            }

            .filter-active-4 {
                background: linear-gradient(35deg, #17407e, #2063a3, #17407e);
                border-color: #0049a3;
            }

            .filter-active-7 {
                background: linear-gradient(35deg, #06a57c, #0cc393, #06a57c);
                border-color: #00690f;
            }

            .filter-active-9 {
                background: linear-gradient(35deg, #732448, #d13680, #732448);
                border-color: #d13680;
            }

            .filter-active-8 {
                background: linear-gradient(35deg, #732448, #d13680, #732448);
                border-color: #d13680;
            }
        }

        .content-section {
            width: calc(100vw - 128px);
            overflow: hidden;
            float: left;
            margin-left: 8px;
            padding-bottom: 60px;

            &.no-class {
                width: 100vw !important;
                margin: 0 !important;
                padding: 0 10px 80px;
                box-sizing: border-box;

                .game-card {
                    width: calc((100vw - 48px) / 3);
                    cursor: pointer;
                    transition: transform 0.2s;

                    &:hover {
                        transform: scale(1.05);
                    }
                }
            }

            .game-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 8px;
            }

            .content-section-title {
                height: 44px;
                line-height: 44px;
                padding-left: 25px;
                font-weight: 400;
                font-size: 20px;
                color: #ffffff;
                border-radius: 8px;
            }

            .content-title-1 {
                background: linear-gradient(-87deg, #3613b8, #87365b);
            }

            .content-title-2 {
                background: linear-gradient(-87deg, #e6870b, #e4bc78);
            }

            .content-title-3 {
                background: linear-gradient(-87deg, #1684ff, #4ba7fe);
            }

            .content-title-4 {
                background: linear-gradient(-87deg, #2063a3, #17407e);
            }

            .content-title-7 {
                background: linear-gradient(-87deg, #09ae83, #0cc393);
            }

            .content-title-9 {
                background: linear-gradient(-87deg, #3b1661, #87365b);
            }

            .content-title-8 {
                background: linear-gradient(-87deg, #3b1661, #87365b);
            }
        }
    }
}

.game-card {
    width: calc((100vw - 148px) / 3);
    position: relative;
    background: rgba(31, 41, 55, 0.7);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    cursor: pointer;
    // transition: transform 0.2s;

    &:hover {
        // transform: scale(1.05);
        // transform: translateY(-8px);
    }
}

.game-image-wrapper {
    position: relative;
    overflow: hidden;

    .game-image {
        transition: transform 0.3s ease;
    }

    .image-hover {
        transform: scale(1.05);
    }
}

.play-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;

    .v-btn {
        transform: scale(0.8);
        transition: transform 0.3s ease;
    }

    &:hover {
        opacity: 1;

        .v-btn {
            transform: scale(1);
        }
    }
}

.game-title {
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 4px;
}

.game-provider {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.6rem;
    display: flex;
    align-items: center;
    gap: 4px;
}
</style>
