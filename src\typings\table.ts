// 通用分页查询参数
export interface CommonSearchParams {
  current?: number;
  size?: number;
  sort?: string;
}

// 通用表格列配置
export interface TableColumn<T = any> {
  title: string;
  dataIndex: keyof T;
  key?: string;
  width?: number | string;
  fixed?: 'left' | 'right';
  align?: 'left' | 'center' | 'right';
  ellipsis?: boolean;
  render?: (text: any, record: T, index: number) => any;
}

// 通用表格数据响应
export interface TableResponse<T = any> {
  status_code: number;
  data: T[];
  count: number;
}
