<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { <PERSON><PERSON><PERSON>, <PERSON>Row, <PERSON>Col } from "element-plus";
import { getDailyReport, getDailyTransferReport } from "@/service/api/report";
import type { DailyReportData } from "@/typings/report";
import * as echarts from "echarts";
import { localStg } from "@/utils/storage";
import { useRouter } from "vue-router";
import { getBrazilTime } from "@/utils/format";
const router = useRouter();

const isManage = localStg.get("isManage");
if (isManage !== "true") {
  router.replace("/agent/merchantmanagement");
}
// 当前时间显示
const currentTime = ref("");

function updateTime() {
  const now = getBrazilTime();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");

  currentTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

let timer: number;

// 数据概览
const overviewData = ref<DailyReportData>({
  messages_pending_reply: 0,
  withdrawals_pending: 0,
  code_amount: 0,
  platform_tax: 0,
  platform_balance: 0,
  online_users: 0,
  registered_users: 0,
  invited_users: 0,
  recharge_users: 0,
  first_recharge_users: 0,
  first_recharge_amount: 0,
  invitation_reward: 0,
  total_recharge_amount: 0,
  total_withdrawal_amount: 0,
  platform_revenue: 0,
  gift_cash: 0,
  gift_bonus: 0,
  withdrawal_users: 0,
  game_betting: 0,
  game_winning: 0,
  total_recharge_gift_cash: 0,
  game_betting_winning_diff: 0,
  recharge_orders: 0,
  withdrawal_orders: 0,
  invitation_first_recharge_users: 0,
  total_cash_withdrawable: 0, //打码提现金额
  total_digital_balance: 0, //赠金
  total_cash_balance: 0, //现金
});

// 最近7天数据
const last7DaysData = ref<any[]>([]);
let userChart: echarts.ECharts | null = null;
let moneyChart: echarts.ECharts | null = null;
let gameChart: echarts.ECharts | null = null;

// 卡片颜色配置
const cardColors = {
  users: {
    background: "linear-gradient(135deg, #1890FF 0%, #36CBCB 100%)",
    boxShadow: "0 4px 20px 0 rgba(24, 144, 255, 0.1)",
  },
  money: {
    background: "linear-gradient(135deg, #722ED1 0%, #EB2F96 100%)",
    boxShadow: "0 4px 20px 0 rgba(114, 46, 209, 0.1)",
  },
  game: {
    background: "linear-gradient(135deg, #52C41A 0%, #13C2C2 100%)",
    boxShadow: "0 4px 20px 0 rgba(82, 196, 26, 0.1)",
  },
  pending: {
    background: "linear-gradient(135deg, #FA541C 0%, #FAAD14 100%)",
    boxShadow: "0 4px 20px 0 rgba(250, 84, 28, 0.1)",
  },
  balance: {
    background: "linear-gradient(135deg, #13ACF0 0%, #80FFFF 100%)",
    boxShadow: "0 4px 20px 0 rgba(255, 193, 7, 0.1)",
  },
};

// 获取今日数据
async function fetchTodayData() {
  const today = getBrazilTime();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate()).padStart(2, "0");
  const dateStr = `${year}-${month}-${day}`;

  try {
    const { data } = await getDailyReport({
      start_time: dateStr,
      end_time: dateStr,
    });
    // console.log(data)
    if (data) {
      console.log(data.data);
      overviewData.value = data.data;
    }
  } catch (error) {
    console.error("获取今日数据失败:", error);
  }
}

// 获取最近7天数据
async function fetchLast7DaysData() {
  const end = getBrazilTime();
  const start = getBrazilTime();
  start.setDate(start.getDate() - 6);

  const startStr = formatDate(start);
  const endStr = formatDate(end);

  try {
    const { data } = await getDailyTransferReport({
      start_time: startStr,
      end_time: endStr,
    });

    if (data) {
      last7DaysData.value = data.data;
      initCharts();
    }
  } catch (error) {
    console.error("获取最近7天数据失败:", error);
  }
}

// 格式化日期
function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}

// 格式化金额
function formatAmount(amount: number) {
  // console.log(amount)
  return (amount / 100).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

// 初始化图表
function initCharts() {
  // 用户数据图表
  const userChartDom = document.getElementById("userChart");
  if (userChartDom) {
    userChart = echarts.init(userChartDom);
    const userOption = {
      title: {
        text: "用户数据趋势",
        left: "center",
      },
      tooltip: {
        trigger: "axis",
      },
      legend: {
        data: ["注册人数", "充值人数", "提现人数"],
        bottom: 0,
      },
      xAxis: {
        type: "category",
        data: last7DaysData.value.map((item) => item.date),
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          name: "注册人数",
          type: "line",
          data: last7DaysData.value.map((item) => item.register_users),
        },
        {
          name: "充值人数",
          type: "line",
          data: last7DaysData.value.map((item) => item.recharge_users),
        },
        {
          name: "提现人数",
          type: "line",
          data: last7DaysData.value.map((item) => item.withdrawal_users),
        },
      ],
    };
    userChart.setOption(userOption);
  }

  // 金额数据图表
  const moneyChartDom = document.getElementById("moneyChart");
  if (moneyChartDom) {
    moneyChart = echarts.init(moneyChartDom);
    const moneyOption = {
      title: {
        text: "金额数据趋势",
        left: "center",
      },
      tooltip: {
        trigger: "axis",
        formatter: function (params: any) {
          let result = params[0].axisValue + "<br/>";
          params.forEach((param: any) => {
            result +=
              param.seriesName + ": R$" + formatAmount(param.value) + "<br/>";
          });
          return result;
        },
      },
      legend: {
        data: ["充值金额", "提现金额", "平台营收"],
        bottom: 0,
      },
      xAxis: {
        type: "category",
        data: last7DaysData.value.map((item) => item.date),
      },
      yAxis: {
        type: "value",
        axisLabel: {
          formatter: (value: number) => "R$" + formatAmount(value),
        },
      },
      series: [
        {
          name: "充值金额",
          type: "bar",
          data: last7DaysData.value.map((item) => item.recharge_amount),
        },
        {
          name: "提现金额",
          type: "bar",
          data: last7DaysData.value.map((item) => item.withdrawal_amount),
        },
        {
          name: "平台营收",
          type: "line",
          data: last7DaysData.value.map((item) => item.platform_revenue),
        },
      ],
    };
    moneyChart.setOption(moneyOption);
  }

  // 游戏数据图表
  const gameChartDom = document.getElementById("gameChart");
  if (gameChartDom) {
    gameChart = echarts.init(gameChartDom);
    const gameOption = {
      title: {
        text: "游戏数据趋势",
        left: "center",
      },
      tooltip: {
        trigger: "axis",
        formatter: function (params: any) {
          let result = params[0].axisValue + "<br/>";
          params.forEach((param: any) => {
            result +=
              param.seriesName + ": R$" + formatAmount(param.value) + "<br/>";
          });
          return result;
        },
      },
      legend: {
        data: ["下注金额", "中奖金额", "游戏差值"],
        bottom: 0,
      },
      xAxis: {
        type: "category",
        data: last7DaysData.value.map((item) => item.date),
      },
      yAxis: {
        type: "value",
        axisLabel: {
          formatter: (value: number) => "R$" + formatAmount(value),
        },
      },
      series: [
        {
          name: "下注金额",
          type: "bar",
          data: last7DaysData.value.map((item) => item.down_bet_amount),
        },
        {
          name: "中奖金额",
          type: "bar",
          data: last7DaysData.value.map((item) => item.middle_win_amount),
        },
        {
          name: "游戏差值",
          type: "line",
          data: last7DaysData.value.map((item) => item.game_diff),
        },
      ],
    };
    gameChart.setOption(gameOption);
  }
}

// 监听窗口大小变化
function handleResize() {
  userChart?.resize();
  moneyChart?.resize();
  gameChart?.resize();
}

onMounted(() => {
  updateTime();
  timer = window.setInterval(updateTime, 1000);
  fetchTodayData();
  fetchLast7DaysData();
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
  window.removeEventListener("resize", handleResize);
  userChart?.dispose();
  moneyChart?.dispose();
  gameChart?.dispose();
});
</script>

<template>
  <div class="dashboard-container">
    <!-- 顶部欢迎区域 -->
    <div class="welcome-section">
      <h2>数据概览</h2>
      <div class="time-display">{{ currentTime }}</div>
    </div>

    <!-- 数据概览卡片 -->
    <div class="data-overview">
      <div class="data-card" :style="cardColors.users">
        <!-- <div class="card-title"></div> -->
        <div class="main-value user-main-value">
          <!-- <div class="user-value">
            <div class="user-label">用户数据</div>
            <div>{{ overviewData.registered_users }}</div>
          </div>
          <div class="user-value">
            <div class="user-label">在线</div>
            <div>{{ overviewData.online_users }}</div>
          </div> -->
          <div>
            <div class="card-title">用户数据</div>
            <div class="main-value">
              {{ overviewData.registered_users }}
            </div>
          </div>
          <div>
            <div class="card-title">在线</div>
            <div class="main-value">
              {{ overviewData.online_users }}
            </div>
          </div>
        </div>

        <div class="card-details">
          <div class="sub-value">
            今日注册:<br />{{ overviewData.registered_users }}
          </div>
          <div class="sub-value">
            邀请注册:<br />{{ overviewData.invited_users }}
          </div>
        </div>
      </div>
      <div class="data-card" :style="cardColors.money">
        <div class="card-title">充值</div>
        <div class="main-value">
          R${{ formatAmount(overviewData.total_recharge_amount) }}
        </div>
        <div class="card-details">
          <div class="sub-value">
            提现:<br />R$
            {{ formatAmount(overviewData.total_withdrawal_amount) }}
          </div>
          <div class="sub-value">
            首充:<br />{{ overviewData.first_recharge_users }}人
          </div>
        </div>
      </div>
      <div class="data-card" :style="cardColors.game">
        <div class="card-title">游戏数据</div>
        <div class="main-value">
          R${{ formatAmount(overviewData.game_betting) }}
        </div>
        <div class="card-details">
          <div class="sub-value">
            派奖:<br />R$ {{ formatAmount(overviewData.game_winning) }}
          </div>
          <div class="sub-value">
            盈亏:<br />R$
            {{ formatAmount(overviewData.game_betting_winning_diff) }}
          </div>
        </div>
      </div>
      <div class="data-card" :style="cardColors.pending">
        <div class="card-title">待处理事项</div>
        <div class="main-value">
          {{ overviewData.messages_pending_reply }}
        </div>
        <div class="card-details">
          <div class="sub-value">
            待回复消息:<br />{{ overviewData.messages_pending_reply }}
          </div>
          <div class="sub-value">
            待审核提现:<br />{{ overviewData.withdrawals_pending }}
          </div>
        </div>
      </div>
      <div class="data-card" :style="cardColors.balance">
        <div class="card-title">全平台现金总余额</div>
        <div class="main-value">
          R${{ overviewData.total_cash_withdrawable || 0 }}
        </div>
        <div class="card-details">
          <div class="sub-value">
            赠金总余额:<br />R$ {{ overviewData.total_digital_balance || 0 }}
          </div>
          <div class="sub-value">
            打码提现总余额:<br />R$ {{ overviewData.total_cash_balance || 0 }}
          </div>
        </div>
      </div>
    </div>

    <!-- 最近7天数据图表 -->
    <ElRow :gutter="20" class="chart-section">
      <ElCol :span="24">
        <ElCard shadow="hover" class="chart-card">
          <div id="userChart" class="chart"></div>
        </ElCard>
      </ElCol>
      <ElCol :span="24">
        <ElCard shadow="hover" class="chart-card">
          <div id="moneyChart" class="chart"></div>
        </ElCard>
      </ElCol>
      <ElCol :span="24">
        <ElCard shadow="hover" class="chart-card">
          <div id="gameChart" class="chart"></div>
        </ElCard>
      </ElCol>
    </ElRow>

    <!-- 数据详情卡片 -->
    <ElRow :gutter="20" class="data-details">
      <ElCol :xs="24" :sm="12" :md="8">
        <ElCard shadow="hover" class="detail-card">
          <template #header>
            <div class="card-header">
              <span>平台数据</span>
            </div>
          </template>
          <div class="detail-list">
            <div class="detail-item">
              <span class="label">平台余额：</span>
              <span class="value"
                >R$ {{ formatAmount(overviewData.platform_balance) }}</span
              >
            </div>
            <div class="detail-item">
              <span class="label">平台税收：</span>
              <span class="value"
                >R$ {{ formatAmount(overviewData.platform_tax) }}</span
              >
            </div>
            <div class="detail-item">
              <span class="label">平台收入：</span>
              <span class="value"
                >R$ {{ formatAmount(overviewData.platform_revenue) }}</span
              >
            </div>
          </div>
        </ElCard>
      </ElCol>
      <ElCol :xs="24" :sm="12" :md="8">
        <ElCard shadow="hover" class="detail-card">
          <template #header>
            <div class="card-header">
              <span>订单数据</span>
            </div>
          </template>
          <div class="detail-list">
            <div class="detail-item">
              <span class="label">充值订单：</span>
              <span class="value">{{ overviewData.recharge_orders }}笔</span>
            </div>
            <div class="detail-item">
              <span class="label">提现订单：</span>
              <span class="value">{{ overviewData.withdrawal_orders }}笔</span>
            </div>
            <div class="detail-item">
              <span class="label">打码量：</span>
              <span class="value"
                >R$ {{ formatAmount(overviewData.code_amount) }}</span
              >
            </div>
          </div>
        </ElCard>
      </ElCol>
      <ElCol :xs="24" :sm="12" :md="8">
        <ElCard shadow="hover" class="detail-card">
          <template #header>
            <div class="card-header">
              <span>奖励数据</span>
            </div>
          </template>
          <div class="detail-list">
            <div class="detail-item">
              <span class="label">邀请奖励：</span>
              <span class="value">R$ {{ overviewData.invitation_reward }}</span>
            </div>
            <div class="detail-item">
              <span class="label">赠送现金：</span>
              <span class="value">R$ {{ overviewData.gift_cash }}</span>
            </div>
            <div class="detail-item">
              <span class="label">赠送赠金：</span>
              <span class="value">R$ {{ overviewData.gift_bonus }}</span>
            </div>
          </div>
        </ElCard>
      </ElCol>
    </ElRow>
  </div>
</template>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: var(--el-bg-color-page);
  min-height: calc(100vh - 64px);
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  h2 {
    margin: 0;
    font-size: 24px;
    color: var(--el-text-color-primary);
  }

  .time-display {
    font-size: 16px;
    color: var(--el-text-color-secondary);
    font-family: "Courier New", monospace;
  }
}

.data-overview {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 24px;
  // overflow-x: auto;
  padding-bottom: 10px;

  &::-webkit-scrollbar {
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.data-card {
  flex: 1;
  // flex: 0 0 300px;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  text-align: center;

  &:hover {
    transform: translateY(-5px);
  }

  .card-title {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.85);
    margin-bottom: 8px;
    width: 100%;
    text-align: left;
  }

  .main-value {
    font-size: 28px;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
    margin-bottom: 16px;
    width: 100%;
    text-align: left;
  }

  .user-main-value {
    display: flex;
    justify-content: space-between;
    width: 100%;
    gap: 16px;
    .user-value {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      .user-label {
        font-size: 15px;
        font-weight: 400;
        // color: rgba(255,255,255,0.7);
        margin-bottom: 4px;
      }
      div:last-child {
        font-size: 28px;
        font-weight: bold;
        color: #fff;
      }
    }
  }

  .card-details {
    display: flex;
    justify-content: space-around;
    width: 100%;
    .sub-value {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.85);
      line-height: 1.5;
      text-align: center;
    }
  }
}

.chart-section {
  margin-bottom: 24px;
}

.chart-card {
  margin-bottom: 20px;

  .chart {
    height: 400px;
    width: 100%;
  }
}

.data-details {
  .detail-card {
    margin-bottom: 20px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      font-size: 16px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .detail-list {
      .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid var(--el-border-color-lighter);

        &:last-child {
          border-bottom: none;
        }

        .label {
          color: var(--el-text-color-regular);
          font-size: 14px;
        }

        .value {
          color: var(--el-text-color-primary);
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .data-overview {
    gap: 16px;
  }

  .data-card {
    flex: 0 0 260px;
  }

  .detail-card {
    margin-bottom: 16px;
  }

  .chart-card {
    margin-bottom: 16px;

    .chart {
      height: 300px;
    }
  }
}
</style>
