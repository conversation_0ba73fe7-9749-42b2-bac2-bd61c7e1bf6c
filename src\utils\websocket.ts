import { ref } from "vue";
import { getToken, getUserInfo } from "./auth";
import {
  getWebSocketConfig,
  shouldIgnoreCloseCode,
  isNormalCloseCode,
  isRetryableCloseCode,
  getCloseCodeStrategy,
  wsLog,
} from "./websocketConfig";
import websocketKeeper from "@/utils/websocketKeeper";
import store from "@/store";
import { md5Encrypt } from ".";
import { generateDeviceFingerprint } from "./device";

// WebSocket连接状态枚举
export enum WebSocketState {
  CONNECTING = 0,
  OPEN = 1,
  CLOSING = 2,
  CLOSED = 3,
}

// 消息类型定义
export interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

// 事件监听器类型
type MessageHandler = (data: any) => void;
type ConnectionHandler = () => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private lastHeartbeatResponse = 0; // 最后一次心跳响应时间
  private messageHandlers = new Map<string, Set<MessageHandler>>();
  private connectionHandlers = new Set<ConnectionHandler>();

  // 登录状态管理
  private isLoggedIn = false;
  private loginAttempts = 0;
  private lastLoginTime = 0;
  private lastLoginMessageId = "";
  private readonly LOGIN_COOLDOWN = 5000; // 登录消息冷却时间（毫秒）
  private readonly MAX_LOGIN_ATTEMPTS = 3;

  // 从配置获取设置
  private get config() {
    return getWebSocketConfig();
  }

  private get maxReconnectAttempts() {
    return this.config.connection.maxReconnectAttempts;
  }

  private get reconnectInterval() {
    return this.config.connection.reconnectInterval;
  }

  private get heartbeatInterval() {
    return this.config.heartbeat.interval;
  }

  private get heartbeatTimeout() {
    return this.config.heartbeat.timeout;
  }

  // 连接状态
  public readonly state = ref<WebSocketState>(WebSocketState.CLOSED);

  /**
   * 初始化WebSocket连接
   */
  public connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocketState.OPEN) {
        resolve();
        return;
      }

      try {
        let wsUrl;
        // if (window.location.host.includes("localhost")) {
        //   wsUrl = `${
        //     "https:" === "https:" ? "wss:" : "ws:"
        //   }//${"zsj.kk2133lz.com"}/front/ws/connect`;
        // } else {
        //   wsUrl = `${window.location.protocol === "https:" ? "wss:" : "ws:"}//${
        //     window.location.host
        //   }/front/ws/connect`;
        // }
        wsUrl = `${
        "ws:"
        }//${"************:9000"}/front/ws/connect`;
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = async () => {
          wsLog.info("WebSocket连接已建立");
          this.state.value = this.ws?.readyState || WebSocketState.OPEN;

          this.reconnectAttempts = 0;

          // 触发连接事件
          this.connectionHandlers.forEach((handler) => handler());

          // 自动发送登录信息
          try {
            await this.sendLogin();
          } catch (error) {
            console.error("自动发送登录信息失败:", error);
          }

          resolve();
        };

        this.ws.onmessage = (event: MessageEvent) => {
          try {
            const data = JSON.parse(event.data);
            console.log("收到WebSocket消息:", data);
            this.handleMessage(data);
          } catch (error) {
            console.error("解析WebSocket消息失败:", error);
          }
        };

        this.ws.onerror = (error: Event) => {
          wsLog.error("WebSocket连接错误:", error);

          reject(error);
        };

        this.ws.onclose = (event: CloseEvent) => {
          wsLog.info("WebSocket连接关闭", {
            code: event.code,
            reason: event.reason,
          });
          // 检查登录状态
          websocketKeeper.manualCheck();
          // const closeReasons = {
          //   1000: "正常关闭",
          //   1001: "离开",
          //   1002: "协议错误",
          //   1003: "不支持的数据类型",
          //   1004: "保留",
          //   1005: "没有状态码",
          //   1006: "异常关闭",
          //   1007: "数据类型不一致",
          //   1009: "消息太大",
          //   1010: "客户端需要扩展",
          //   1011: "服务器遇到意外情况",
          //   1015: "TLS握手失败",
          // };

          // const reason =
          //   closeReasons[event.code as keyof typeof closeReasons] || "未知原因";
          // const strategy = getCloseCodeStrategy(event.code);
          // if (event.code !== 1006) {
          //   // 停止心跳定时器
          //   this.stopHeartbeat();
          //   // 更新连接状态
          //   this.state.value = WebSocketState.CLOSED;
          //   this.isConnected.value = false;
          //   this.isConnecting = false;
          // }
          // // 根据配置策略处理关闭事件
          // switch (strategy) {
          //   case "ignore":
          //     wsLog.info(
          //       `WebSocket关闭代码 ${event.code} (${reason}) 被忽略，这通常是正常的网络切换或页面状态变化`
          //     );
          //     const heartbeatStatus = this.getHeartbeatStatus();
          //     const isConnected = this.getIsConnected();

          //     if (isConnected && !heartbeatStatus.isRunning) {
          //       console.log("WebSocket已连接但心跳未运行，尝试启动心跳");
          //       // 通过发送一次心跳来启动心跳机制
          //       this.startHeartbeatManually();
          //     }
          //     // 忽略的代码不触发重连，让连接保持器来处理
          //     return;

          //   case "normal":
          //     wsLog.info(
          //       `WebSocket正常关闭 - 代码: ${event.code}, 原因: ${reason}`
          //     );

          //     return;

          //   case "retry":
          //     wsLog.warn(
          //       `WebSocket异常关闭 - 代码: ${event.code}, 原因: ${reason}, 描述: ${event.reason}`
          //     );
          //     if (this.reconnectAttempts < this.maxReconnectAttempts) {
          //       wsLog.info(
          //         `连接异常关闭，将尝试重连 (当前重连次数: ${this.reconnectAttempts})`
          //       );
          //       this.scheduleReconnect();
          //     } else {
          //       wsLog.error(
          //         `已达到最大重连次数 (${this.maxReconnectAttempts})，停止重连`
          //       );
          //     }
          //     break;

          //   case "unknown":
          //   default:
          //     wsLog.warn(
          //       `未知的WebSocket关闭代码: ${event.code}, 原因: ${reason}`
          //     );
          //     // 对于未知代码，采用保守策略，尝试重连
          //     if (this.reconnectAttempts < this.maxReconnectAttempts) {
          //       wsLog.info(
          //         `未知关闭代码，尝试重连 (当前重连次数: ${this.reconnectAttempts})`
          //       );
          //       this.scheduleReconnect();
          //     } else {
          //       wsLog.error(
          //         `已达到最大重连次数 (${this.maxReconnectAttempts})，停止重连`
          //       );
          //     }
          //     break;
          // }
        };
      } catch (error) {
        console.error("创建WebSocket连接失败:", error);

        reject(error);
      }
    });
  }

  /**
   * 发送认证信息
   */
  private sendAuth(): void {
    const token = getToken();
    if (token) {
      this.send({
        type: "auth",
        token: token,
      });
    }
  }

  /**
   * 发送登录信息
   */
  public async sendLogin(forceResend = false): Promise<boolean> {
    const userInfo = getUserInfo()?.user;
    if (!userInfo || !userInfo.uuid) {
      console.log("用户信息不完整，跳过WebSocket登录");
      return false;
    }

    // 生成当前登录消息的唯一标识
    const currentMessageId = `${userInfo.uuid}_${md5Encrypt(
      userInfo.uuid + "box777studio"
    )}`;
    const currentTime = Date.now();

    // 检查是否需要发送登录消息
    if (!forceResend && this.shouldSkipLogin(currentMessageId, currentTime)) {
      return true; // 已经登录成功，无需重复发送
    }

    // 检查WebSocket连接状态
    if (this.ws?.readyState !== WebSocketState.OPEN) {
      console.log("WebSocket未连接，无法发送登录消息");
      return false;
    }

    try {
      // 生成设备指纹
      const { deviceId } = await generateDeviceFingerprint();
      const fingerprint = md5Encrypt(deviceId);
      console.log(localStorage.getItem("isLogin"));
      const loginMessage = {
        type: "login",
        data: {
          uuid: userInfo.uuid.toString(),
          token: md5Encrypt(userInfo.uuid + "box777studio"),
          in_game: false,
          md5: fingerprint,
          retry: localStorage.getItem("isLogin") === "false", // 根据localStorage中的isLogin状态判断
        },
      };

      // 更新登录状态
      this.lastLoginTime = currentTime;
      this.lastLoginMessageId = currentMessageId;
      this.loginAttempts++;

      const success = this.send(loginMessage);
      if (success) {
        console.log("WebSocket登录消息已发送:", loginMessage);
        return true;
      } else {
        console.error("WebSocket登录消息发送失败");
        return false;
      }
    } catch (error) {
      console.error("发送WebSocket登录消息失败:", error);
      return false;
    }
  }

  /**
   * 检查是否应该跳过登录
   */
  private shouldSkipLogin(
    currentMessageId: string,
    currentTime: number
  ): boolean {
    // 如果已经登录成功且消息ID相同，跳过
    if (this.isLoggedIn && this.lastLoginMessageId === currentMessageId) {
      console.log("WebSocket已登录且消息ID相同，跳过重复发送");
      return true;
    }

    // 检查冷却时间
    if (currentTime - this.lastLoginTime < this.LOGIN_COOLDOWN) {
      console.log(
        `登录消息发送过于频繁，冷却中... (剩余${
          this.LOGIN_COOLDOWN - (currentTime - this.lastLoginTime)
        }ms)`
      );
      return true;
    }

    // 检查最大尝试次数
    if (this.loginAttempts >= this.MAX_LOGIN_ATTEMPTS && this.isLoggedIn) {
      console.log("已达到最大登录尝试次数且已登录成功");
      return true;
    }

    return false;
  }

  /**
   * 重置登录状态
   */
  public resetLoginState(): void {
    this.isLoggedIn = false;
    this.loginAttempts = 0;
    this.lastLoginTime = 0;
    this.lastLoginMessageId = "";
    console.log("WebSocket登录状态已重置");
  }

  /**
   * 获取登录状态
   */
  public getLoginStatus(): {
    isLoggedIn: boolean;
    loginAttempts: number;
    lastLoginTime: number;
    canSendLogin: boolean;
  } {
    const currentTime = Date.now();
    const canSendLogin =
      currentTime - this.lastLoginTime >= this.LOGIN_COOLDOWN;

    return {
      isLoggedIn: this.isLoggedIn,
      loginAttempts: this.loginAttempts,
      lastLoginTime: this.lastLoginTime,
      canSendLogin,
    };
  }

  /**
   * 发送消息
   */
  public send(message: WebSocketMessage): boolean {
    if (this.ws?.readyState !== WebSocketState.OPEN) {
      console.warn("WebSocket未连接，无法发送消息");
      this.forceReconnect();
      return false;
    }

    try {
      this.ws?.send(JSON.stringify(message));
      return true;
    } catch (error) {
      console.error("发送WebSocket消息失败:", error);
      return false;
    }
  }

  /**
   * 发送聊天消息
   */
  public sendChat(message: string, roomId?: string): boolean {
    return this.send({
      type: "chat",
      message: message,
      roomId: roomId,
      timestamp: Date.now(),
    });
  }

  /**
   * 发送心跳包
   */
  public sendHeartbeat(): boolean {
    // // 检查心跳超时
    // if (this.lastHeartbeatResponse > 0 && checkTimeout) {
    //   const timeSinceLastResponse = Date.now() - this.lastHeartbeatResponse;
    //   if (timeSinceLastResponse > this.heartbeatTimeout) {
    //     console.warn(
    //       `心跳超时 (${timeSinceLastResponse}ms > ${this.heartbeatTimeout}ms)，可能需要重连`
    //     );
    //     // 心跳超时，尝试重连
    //     if (this.reconnectAttempts < this.maxReconnectAttempts) {
    //       this.scheduleReconnect();
    //     }
    //     return false;
    //   }
    // }

    return this.send({
      type: "heartbeat",
      data: {
        timestamp: Date.now(),
      },
    });
  }

  /**
   * 开始心跳定时器
   */
  private startHeartbeat(): void {
    // 清除现有的心跳定时器
    this.stopHeartbeat();

    // 立即发送一次心跳
    this.sendHeartbeat();

    // 设置定时器，定期发送心跳
    this.heartbeatTimer = setInterval(() => {
      wsLog.debug(`心跳检查 - WebSocket状态: ${this.ws?.readyState}`);

      if (this.ws?.readyState === WebSocketState.OPEN) {
        wsLog.debug("发送心跳包");
        const success = this.sendHeartbeat();
        if (!success) {
          wsLog.warn("心跳发送失败，可能需要重连");
        }
      } else {
        wsLog.info("连接状态异常，停止心跳", {
          wsReadyState: this.ws?.readyState,
        });
        this.stopHeartbeat();
      }
    }, this.heartbeatInterval);

    wsLog.info(`心跳定时器已启动，间隔: ${this.heartbeatInterval}ms`);
  }

  /**
   * 公开的启动心跳方法（用于外部调用）
   */
  public startHeartbeatManually(): boolean {
    console.log("手动启动心跳");
    this.startHeartbeat();
    return true;
  }

  /**
   * 停止心跳定时器
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
      console.log("心跳定时器已停止");
    }
  }

  /**
   * 手动停止心跳（公共方法）
   */
  public stopHeartbeatManually(): void {
    this.stopHeartbeat();
  }

  /**
   * 处理token过期
   */
  private handleTokenExpired(message: string): void {
    // 停止心跳
    this.stopHeartbeat();

    // 重置登录状态
    this.resetLoginState();

    // 清除本地存储的token和用户信息
    store.dispatch("auth/logout", true);

    // 触发token过期事件，让应用层处理
    this.emitTokenExpiredEvent(message);

    // 关闭WebSocket连接
    this.disconnect();
  }

  /**
   * 触发token过期事件
   */
  private emitTokenExpiredEvent(message: string): void {
    // 创建自定义事件
    const tokenExpiredEvent = new CustomEvent("websocket:token_expired", {
      detail: {
        message: message,
        timestamp: Date.now(),
      },
    });

    // 触发事件
    window.dispatchEvent(tokenExpiredEvent);
  }

  /**
   * 监听token过期事件
   */
  public onTokenExpired(handler: (event: CustomEvent) => void): () => void {
    const eventHandler = (event: Event) => {
      handler(event as CustomEvent);
    };

    window.addEventListener("websocket:token_expired", eventHandler);

    // 返回取消监听的函数
    return () => {
      window.removeEventListener("websocket:token_expired", eventHandler);
    };
  }

  /**
   * 加入房间
   */
  public joinRoom(roomId: string): boolean {
    return this.send({
      type: "join_room",
      roomId: roomId,
    });
  }

  /**
   * 离开房间
   */
  public leaveRoom(roomId: string): boolean {
    return this.send({
      type: "leave_room",
      roomId: roomId,
    });
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(data: any): void {
    const { type, success } = data;

    console.log(`处理WebSocket消息 - 类型: ${type}, 成功: ${success}`);

    // 处理登录成功消息，自动发起心跳
    if (type === "login" && success === true) {
      console.log("收到登录成功消息，开始发送心跳");
      this.isLoggedIn = true;
      // 登录成功后设置localStorage状态
      localStorage.setItem("isLogin", "false");
      this.startHeartbeat();
    } else if (type === "login" && success === false) {
      console.log("登录失败:", data.message);
      this.isLoggedIn = false;
    }

    // 处理心跳响应消息
    if (type === "heartbeat" && success === true) {
      console.log(
        "收到心跳响应:",
        data.message,
        "时间戳:",
        data.data?.timestamp
      );
      this.lastHeartbeatResponse = Date.now();
    }

    // 处理token过期消息
    if (type === "token_expired" && success === true) {
      console.log("收到token过期消息:", data.message);
      this.handleTokenExpired(data.message);
    }

    // 处理游戏相关消息，确保不会影响心跳
    if (type === "game_status") {
      console.log("收到游戏消息:", data);
      this.startHeartbeat();
      // 游戏消息不应该影响心跳状态
    }

    // 处理余额更新消息
    if (
      type === "broadcast_message" &&
      success === true &&
      data.message === "update_total_balance"
    ) {
      console.log(data);
      // 更新 Vuex store
      store.commit("auth/UPDATE_USER_WALLET_BALANCE", data?.data?.data);
    } else if (
      type === "broadcast_message" &&
      success === true &&
      !data.message
    ) {
      console.log(data?.data?.data?.data);
      store.commit("auth/UPDATE_USER_WALLET_BALANCE", data?.data?.data?.data);
    }

    // 触发对应类型的消息处理器
    if (this.messageHandlers.has(type)) {
      this.messageHandlers.get(type)?.forEach((handler) => {
        try {
          handler(data);
        } catch (error) {
          console.error(`处理消息类型 ${type} 时出错:`, error);
        }
      });
    }
  }

  /**
   * 监听特定类型的消息
   */
  public onMessage(type: string, handler: MessageHandler): () => void {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, new Set());
    }

    this.messageHandlers.get(type)!.add(handler);

    // 返回取消监听的函数
    return () => {
      this.messageHandlers.get(type)?.delete(handler);
    };
  }

  /**
   * 监听连接事件
   */
  public onConnect(handler: ConnectionHandler): () => void {
    this.connectionHandlers.add(handler);

    // 返回取消监听的函数
    return () => {
      this.connectionHandlers.delete(handler);
    };
  }

  /**
   * 安排重连
   */
  // private scheduleReconnect(): void {
  //   if (this.reconnectTimer) {
  //     clearTimeout(this.reconnectTimer);
  //   }

  //   this.reconnectAttempts++;
  //   const delay =
  //     this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);

  //   console.log(
  //     `WebSocket将在 ${delay}ms 后尝试重连 (第 ${this.reconnectAttempts} 次)`
  //   );

  //   this.reconnectTimer = setTimeout(() => {
  //     this.connect().catch((error) => {
  //       console.error("WebSocket重连失败:", error);
  //     });
  //   }, delay);
  // }

  /**
   * 关闭连接
   */
  public disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    // 停止心跳定时器
    this.stopHeartbeat();

    if (this.ws?.readyState === WebSocketState.OPEN) {
      this.ws.close(1000, "正常关闭");
    }

    this.state.value = this.ws?.readyState || WebSocketState.CLOSED;

    // 重置登录状态
    this.resetLoginState();
  }

  /**
   * 获取连接状态
   */
  public getState(): WebSocketState {
    return this.ws?.readyState || WebSocketState.CLOSED;
  }

  /**
   * 检查是否已连接
   */
  public getIsConnected(): boolean {
    return this.ws?.readyState === WebSocketState.OPEN;
  }

  /**
   * 重置重连计数器
   */
  public resetReconnectAttempts(): void {
    this.reconnectAttempts = 0;
    console.log("WebSocket重连计数器已重置");
  }

  /**
   * 强制重连（用于外部调用）
   */
  public readonly isRetry = ref(false);
  public async forceReconnect(): Promise<void> {
    // 检查WebSocket状态
    console.log(this.isRetry.value);
    console.log(this.ws?.readyState);
    if (this.isRetry.value || this.ws?.readyState === WebSocketState.OPEN)
      return;
    this.isRetry.value = true;
    setTimeout(() => {
      this.isRetry.value = false;
    }, 1000);
    try {
      console.log("强制重连WebSocket");
      this.resetReconnectAttempts();
      this.disconnect();
      await new Promise((resolve) => setTimeout(resolve, 1000)); // 等待1秒
      await this.connect();

      // 连接成功后会自动发送登录信息，这里只需要等待连接稳定
      // setTimeout(() => {
      //   this.isRetry.value = false;
      // }, 1000);

      console.log("WebSocket保持器: 强制重连成功");
    } catch (error) {
      console.error("WebSocket保持器: 强制重连失败", error);
    }
  }

  /**
   * 检查心跳状态
   */
  public getHeartbeatStatus(): {
    isRunning: boolean;
    interval: number;
    lastSent?: number;
    lastResponse?: number;
    isHealthy: boolean;
  } {
    const isHealthy =
      this.lastHeartbeatResponse === 0 ||
      Date.now() - this.lastHeartbeatResponse <= this.heartbeatTimeout;

    return {
      isRunning: this.heartbeatTimer !== null,
      interval: this.heartbeatInterval,
      lastSent: this.heartbeatTimer ? Date.now() : undefined,
      lastResponse: this.lastHeartbeatResponse || undefined,
      isHealthy,
    };
  }
}

// 创建单例实例
const websocketService = new WebSocketService();

// 导出单例和类型
export default websocketService;
export { WebSocketService };
