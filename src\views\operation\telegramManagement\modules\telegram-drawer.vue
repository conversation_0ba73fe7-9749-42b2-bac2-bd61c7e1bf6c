<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { $t } from '@/locales'
import { fetchAddSubscription, fetchUpdateSubscription } from '@/service/api/telegram'

interface Props {
  operateType: 'add' | 'edit'
  rowData?: any
}

interface Emits {
  (e: 'submitted'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = defineModel<boolean>('visible', {
  default: false
})

const title = computed(() => {
  const operateTypeMap = {
    add: '添加Telegram',
    edit: '编辑Telegram'
  }
  return operateTypeMap[props.operateType]
})

const formRef = ref()
const loading = ref(false)

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
]

const form = ref({
  chat_id: '',
  // bot_token: '',
  status: 1
})

function closeDrawer() {
  visible.value = false
}

function resetForm() {
  form.value = {
    chat_id: '',
    // bot_token: '',
    status: 1
  }
}

function setForm() {
  if (props.operateType === 'edit' && props.rowData) {
    form.value = {
      chat_id: props.rowData.chat_id || '',
      // bot_token: props.rowData.bot_token || '',
      status: props.rowData.status ?? 1
    }
  }
}

async function handleSubmit() {
  await formRef.value?.validate()
  loading.value = true
  try {
    const submitData = { ...form.value }

    if (props.operateType === 'add') {
      const { error } = await fetchAddSubscription(submitData)
      if (!error) {
        ElMessage.success('添加成功')
        closeDrawer()
        emit('submitted')
      }
    } else {
      const { error } = await fetchUpdateSubscription({
        ...submitData,
        subscription_id: props.rowData?.id
      })
      if (!error) {
        ElMessage.success('更新成功')
        closeDrawer()
        emit('submitted')
      }
    }
  } catch (error) {
    console.error('操作失败:', error)
  } finally {
    loading.value = false
  }
}

watch(visible, val => {
  if (val) {
    setForm()
  } else {
    resetForm()
  }
})
</script>

<template>
  <ElDrawer
    v-model="visible"
    :title="title"
    :size="400"
    destroy-on-close
    @close="closeDrawer"
  >
    <ElForm
      ref="formRef"
      :model="form"
      label-width="100px"
      label-position="top"
    >


      <ElFormItem
        label="频道链接"
        prop="chat_id"
        :rules="[{ required: true, message: '请输入频道链接' }]"
      >
        <ElInput v-model="form.chat_id" placeholder="请输入频道链接">
        </ElInput>
      </ElFormItem>

      <!-- <ElFormItem
        :rules="[{ required: true, message: '请输入机器人token' }]"
        label="机器人token"
        prop="bot_token"
      >
        <ElInput
          v-model="form.bot_token"
          type="textarea"
          :rows="4"
          placeholder="机器人token"
          maxlength="500"
          show-word-limit
        />
      </ElFormItem> -->

      <ElFormItem
        label="状态"
        prop="status"
        :rules="[{ required: true, message: '请选择状态' }]"
      >
        <ElRadioGroup v-model="form.status">
          <ElRadio
            v-for="item in statusOptions"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </ElRadio>
        </ElRadioGroup>
      </ElFormItem>
    </ElForm>

    <template #footer>
      <ElSpace class="w-full justify-end">
        <ElButton @click="closeDrawer">取消</ElButton>
        <ElButton type="primary" :loading="loading" @click="handleSubmit">
          保存
        </ElButton>
      </ElSpace>
    </template>
  </ElDrawer>
</template>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
