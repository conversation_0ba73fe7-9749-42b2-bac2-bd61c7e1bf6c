// import { dateEnUS, dateZhCN, enUS, zhCN } from 'naive-ui';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
import en from 'element-plus/dist/locale/en.mjs';
import ptBr from 'element-plus/dist/locale/pt-br.mjs';

export const UILocales: any = {
  'zh-CN': zhCn,
  'en-US': en,
  'pt-BR': ptBr
};

// export const naiveDateLocales: Record<App.I18n.LangType, NDateLocale> = {
//   'zh-CN': dateZhCN,
//   'en-US': dateEnUS
// };
