import { request } from '../request';

export interface WalletListParams {
  current?: number;
  size?: number;
  user_id?: number;
  wallet_id?: number;
  sort?: string;
}

export interface WalletData {
  id: number;
  user_id: number;
  total_balance: number;
  wallet_id: number;
  cash_balance: number;
  digital_balance: number;
  wallet_status: string;
  total_deposit: number;
  total_withdrawal: number;
  reward_cash: number;
  invite_reward_digital: number;
  invite_count: number;
  turnover: number;
  temp_turnover: number;
  bet_times: number;
  total_bet_amount: number;
  win_times: number;
  total_win_amount: number;
  frozen_amount: number;
  is_recharge: number;
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
  first_level_invite_count: number;
  first_level_first_recharge_count: number;
  first_level_first_recharge_amount: number;
  first_level_total_recharge: number;
  first_level_total_withdrawal: number;
  first_level_revenue: number;
  second_level_invite_count: number;
  second_level_first_recharge_count: number;
  second_level_first_recharge_amount: number;
  second_level_total_recharge: number;
  second_level_total_withdrawal: number;
  second_level_revenue: number;
  third_level_invite_count: number;
  third_level_first_recharge_count: number;
  third_level_first_recharge_amount: number;
  third_level_total_recharge: number;
  third_level_total_withdrawal: number;
  third_level_revenue: number;
  total_invite_count: number;
  total_first_recharge_count: number;
  total_first_recharge_amount: number;
  total_total_recharge: number;
  total_total_withdrawal: number;
  total_revenue: number;
}

export interface WalletListResponse {
  status_code: number;
  data: {
    list: WalletData[];
    total: number;
    size: number;
    current: number;
  };
}

export function getWalletList(params: WalletListParams) {
  return request<WalletListResponse>({
    url: '/backend/wallet/list',
    method: 'get',
    params
  });
}

export interface WalletReportParams {
  user_id?: string | number;
  action_type?: number | string;
  balance_type?: number | string;
  start_time?: number;
  end_time?: number;
  min_amount?: number;
  max_amount?: number;
  transaction_id?: string;
  related_id?: string;
  admin_id?: string | number;
  page?: number;
  size?: number;
}

export interface WalletReportRecord {
  id: number;
  user_id: number;
  user_name: string;
  action_type: number;
  action_type_name: string;
  admin_id: number;
  admin_name: string;
  amount: number;
  balance_type: number;
  balance_type_name: string;
  before_cash_balance: number;
  after_cash_balance: number;
  before_digital_balance: number;
  after_digital_balance: number;
  before_frozen_amount: number;
  after_frozen_amount: number;
  before_total_balance: number;
  after_total_balance: number;
  created_at: number;
  created_at_str: string;
  updated_at: number;
  device: string;
  ip: string;
  platform: string;
  related_id: string;
  remark: string;
  status: number;
  transaction_id: string;
  wallet_id: number;
}

export function getWalletReport(params: WalletReportParams) {
  return request<{ data: WalletReportRecord[]; count: number }>({
    url: '/backend/wallet/report',
    method: 'get',
    params,
  });
}

export interface DepositWithdrawalRankingParams {
  page?: number;
  size?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface DepositWithdrawalRankingRecord {
  user_id: number;
  deposit_amount: number;
  withdrawal_amount: number;
  diff_amount: number;
  deposit_count: number;
  withdrawal_count: number;
}

export function getDepositWithdrawalRanking(params: DepositWithdrawalRankingParams) {
  return request<{ data: DepositWithdrawalRankingRecord[]; count: number }>({
    url: '/backend/wallet/deposit-withdrawal-ranking',
    method: 'get',
    params,
  });
}

export interface FirstDepositBonusConfig {
  amount: number;
  bonus_type: string;
  status: number;
}

export function getFirstDepositBonusConfig() {
  return request<{ data: FirstDepositBonusConfig }>({
    url: '/backend/wallet/firstDepositBonus/config',
    method: 'get'
  });
}

export function setFirstDepositBonusConfig(params: FirstDepositBonusConfig) {
  return request({
    url: '/backend/wallet/firstDepositBonus/config',
    method: 'post',
    data: params
  });
}


export function getDepositPaymentMethods(params: any) {
  return request({
    url: '/backend/deposits/paymentMethods',
    method: 'get',
    params
  });
}

