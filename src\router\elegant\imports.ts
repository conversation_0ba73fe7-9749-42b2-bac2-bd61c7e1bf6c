/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  account_accountlist: () => import("@/views/account/accountlist/index.vue"),
  account_banklist: () => import("@/views/account/banklist/index.vue"),
  account_operationlog: () => import("@/views/account/operationLog/index.vue"),
  activities_acviteconfig: () => import("@/views/activities/acviteconfig/index.vue"),
  activities_checkin: () => import("@/views/activities/checkIn/index.vue"),
  activities_exchangecode: () => import("@/views/activities/exchangeCode/index.vue"),
  activities_invitation: () => import("@/views/activities/invitation/index.vue"),
  activities_pdd: () => import("@/views/activities/pdd/index.vue"),
  activities_poolofprizes: () => import("@/views/activities/poolOfPrizes/index.vue"),
  agent_agentdata: () => import("@/views/agent/agentData/index.vue"),
  agent_billmsg: () => import("@/views/agent/billmsg/index.vue"),
  agent_manage: () => import("@/views/agent/manage/index.vue"),
  agent_merchantmanagement: () => import("@/views/agent/merchantManagement/index.vue"),
  channel_channellist: () => import("@/views/channel/channellist/index.vue"),
  finance_giftmoney: () => import("@/views/finance/giftmoney/index.vue"),
  finance_paymentchannel: () => import("@/views/finance/paymentChannel/index.vue"),
  finance_rechargemsg: () => import("@/views/finance/rechargeMsg/index.vue"),
  finance_rechargepackage: () => import("@/views/finance/rechargePackage/index.vue"),
  finance_withdrawalmsg: () => import("@/views/finance/withdrawalMsg/index.vue"),
  finance_withdrawalapplylist: () => import("@/views/finance/withdrawalapplylist/index.vue"),
  finance_withdrawalmanagement: () => import("@/views/finance/withdrawalmanagement/index.vue"),
  game_manufacturer: () => import("@/views/game/manufacturer/index.vue"),
  game_order: () => import("@/views/game/order/index.vue"),
  game_product: () => import("@/views/game/product/index.vue"),
  home: () => import("@/views/home/<USER>"),
  manage_faq: () => import("@/views/manage/faq/index.vue"),
  manage_msg: () => import("@/views/manage/msg/index.vue"),
  manage_notice: () => import("@/views/manage/notice/index.vue"),
  manage_role: () => import("@/views/manage/role/index.vue"),
  manage_shortlink: () => import("@/views/manage/shortlink/index.vue"),
  manage_source: () => import("@/views/manage/source/index.vue"),
  "manage_user-detail": () => import("@/views/manage/user-detail/[id].vue"),
  manage_user: () => import("@/views/manage/user/index.vue"),
  operation_activityalertmanagement: () => import("@/views/operation/activityAlertManagement/index.vue"),
  operation_customerfaqmanagement: () => import("@/views/operation/customerFaqmanagement/index.vue"),
  operation_policymanagement: () => import("@/views/operation/policyManagement/index.vue"),
  operation_telegrammanagement: () => import("@/views/operation/telegramManagement/index.vue"),
  operation_viplevelmanagement: () => import("@/views/operation/viplevelManagement/index.vue"),
  report_commissionstatisticsforcoding: () => import("@/views/report/commissionStatisticsForCoding/index.vue"),
  report_gamelist: () => import("@/views/report/gameList/index.vue"),
  report_invitationrewards: () => import("@/views/report/invitationRewards/index.vue"),
  report_realtimedatacomparison: () => import("@/views/report/realTimeDataComparison/index.vue"),
  report_rechargeandwithdrawallist: () => import("@/views/report/rechargeAndWithdrawalList/index.vue"),
  report_sublevelusers: () => import("@/views/report/subLevelUsers/index.vue"),
  report_useraccountdynamics: () => import("@/views/report/userAccountDynamics/index.vue"),
  report_userinvitationstatistics: () => import("@/views/report/userInvitationStatistics/index.vue"),
};
