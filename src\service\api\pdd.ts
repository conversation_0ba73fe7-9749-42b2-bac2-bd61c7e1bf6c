/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-03 17:17:26
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-07-21 13:35:52
 * @FilePath: \betdoce-admin\src\service\api\pdd.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from "../request";

// 获取拼多多活动列表
export function getPddUserActivityList(params: any) {
  return request({
    url: "/backend/activity_config/list",
    method: "get",
    params,
  });
}

//
export function getPddActivityConfig(id: number) {
  return request<{
    id: number;
    name: string;
    content: string;
    status: number;
    start_time: number;
    end_time: number;
    created_at: number;
    created_by: string;
    updated_at: number;
    updated_by: string;
  }>({
    url: "/backend/activity/show",
    method: "get",
    params: { id },
  });
}

export interface UpdatePddActivityParams {
  id: number;
  name: string;
  content: string;
  status: number;
  start_time: number;
  end_time: number;
}
// 更新拼多多活动配置
export const updatePddActivityConfig = (params: UpdatePddActivityParams) => {
  return request({
    url: "/backend/activity_config/update",
    method: "post",
    data: params,
  });
};

/**
 * 获取拼多多用户活动详情
 * @param activityId 活动 ID
 * @returns 活动详情数据
 */
export const getPddUserActivityDetail = (activityId: number) => {
  // TODO: 在此处实现获取活动详情的 API 调用
  return request({
    url: "/backend/activity_config/details",
    method: "get",
    params: { activity_id: activityId },
  });
};

/**
 * 获取拼多多用户活动配置ID
 *
 * @returns 活动ID
 */
export const getPddId = (params: any) => {
  // TODO: 在此处实现获取拼多多用户活动配置ID 的API 调用
  return request({
    url: "/backend/activity_config/get_id",
    method: "get",
    params,
  });
};

/**
 * 获取活动总览数据
 *
 * @returns 活动总览数据
 */
export const getParticipateDetails = (params: any) => {
  // TODO: 在此处实现获取活动总览数据 API 调用
  return request({
    url: "/backend/activity_config/participate_details",
    method: "get",
    params,
  });
};

/**
 * 获取活动参与详情
 *
 * @returns 活动参与详情
 */
export const getTaskData = (params: any) => {
  if (!params.activity_id) return;
  // TODO: 在此处实现获取活动总览数据 API 调用
  return request({
    url: "/backend/activity_config/search",
    method: "get",
    params,
  });
};

/**
 * 新增拼多多用户活动
 * @param data 活动数据
 * @returns 响应数据
 */
export const addPddUserActivity = (data: any) => {
  // TODO: 在此处实现新增活动的 API 调用
  console.log("新增活动", data);
  return request({
    url: "/backend/activity_config/create",
    method: "post",
    data,
  }); // 示例返回值，请替换为实际 API 调用结果
};

/**
 * 更新拼多多用户活动
 * @param activityId 活动 ID
 * @param data 活动数据
 * @returns 响应数据
 */
export const updatePddUserActivity = (activityId: number, data: any) => {
  // TODO: 在此处实现更新活动的 API 调用
  console.log("更新活动", activityId, data);
  return request({
    url: "/backend/activity_config/update",
    method: "post",
    data,
  });
};

/**
 * 獲取拼多多活動參與者列表
 * @param params 查詢參數
 * @returns 參與者列表數據
 */
export const getPddParticipantsList = (activityId: number, params: any) => {
  return request({
    url: "/backend/activity_config/participants/" + activityId,
    method: "get",
    params,
  });
};
/**
 * 獲取拼多多活動充值者列表
 * @param params 查詢參數
 * @returns 參與者列表數據
 */
export const getPddRechargeList = (activityId: number, params: any) => {
  return request({
    url: "/backend/activity_config/recharge/" + activityId, // 請替換為實際的 API 端點
    method: "get",
    params,
  });
};

/**
 * 獲取拼多多活動成功人數列表
 * @param params 查詢參數
 * @returns 成功人數列表數據
 */
export const getPddActivitySuccessList = (activityId: number, params: any) => {
  return request({
    url: "/backend/activity_config/success/" + activityId, // 請替換為實際的 API 端點
    method: "get",
    params,
  });
};
/**
 * 獲取拼多多活動注册人數列表
 * @param params 查詢參數
 * @returns 成功人數列表數據
 */
export const getPddRegisterList = (activityId: number, params: any) => {
  return request({
    url: "/backend/activity_config/register/" + activityId, // 請替換為實際的 API 端點
    method: "get",
    params,
  });
};

// /**
//  *设置用户完成
//  *
//  *
//  */
export const getPddActivitySetDonet = (id: number, params?: any) => {
  return request({
    url: `/backend/activity_config/set_done/${id}`, // 請替換為實際的 API 端點
    method: "get",
    params,
  });
};

/**
 * 删除拼多多用户活动
 * @param activityId 活动 ID
 * @param data 活动数据
 * @returns 响应数据
 */
export const deletePddUserActivity = (data: any) => {
  // TODO: 在此处实现删除活动的 API 调用
  return request({
    url: "/backend/activity_config/delete",
    method: "post",
    data,
  });
};

/**
 * 更新拼多多用户活动状态
 * @param activityId 活动 ID
 * @param data 活动数据
 * @returns 响应数据
 */
export const updatePddUserActivityStatus = (data: any) => {
  // TODO: 在此处实现更新活动状态的 API 调用
  console.log("更新活动状态", data);
  return request({
    url: "/backend/activity_config/status",
    method: "post",
    data,
  });
};
