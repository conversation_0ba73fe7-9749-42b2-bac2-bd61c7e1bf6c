<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue';
import { $t } from '@/locales';
import { loginModuleRecord } from '@/constants/app';
import { useRouterPush } from '@/hooks/common/router';
import { useForm, useFormRules } from '@/hooks/common/form';
import { useAuthStore } from '@/store/modules/auth';
import { fetchGetGoogleSecret, fetchBindGoogleSecret } from '@/service/api';
import { localStg } from '@/utils/storage';
import {
  fetchGetAgentUserRole,
} from "@/service/api/agent";
import { ElDialog, ElMessage } from 'element-plus';
import QRCodeVue from 'qrcode.vue';  // 导入 QRCode 组件

defineOptions({ name: 'PwdLogin' });

const authStore = useAuthStore();
const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useForm();

const showGoogleAuthDialog = ref(false);
const qrcode = ref<InstanceType<typeof QRCodeVue> | null>(null);

const googleAuthData = ref({
  qrcode: '',
  secret: ''
});

const googleCode = ref('');

interface FormModel {
  userName: string;
  password: string;
  google_code: string;
}

const model = ref<FormModel>({
  userName:  '',
  password: '',
  google_code: ''
});

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  // 在计算属性中创建本地化引用，如果不使用 i18n，可以不使用计算属性定义
  const { formRules } = useFormRules();
  return {
    userName: formRules.userName,
    password: formRules.pwd,
    //google_code: formRules.google_code
  };
});

async function handleSubmit() {
  await validate();
  const res = await authStore.login(model.value.userName, model.value.password, model.value.google_code);
  console.log(res)

  // 获取谷歌验证码
  if (res) {
    const { data: responseData, error, response } = res;
    if (response?.data?.error_code === "Unbound_Goggle") {
      const res = await fetchGetGoogleSecret({
        user_name: model.value.userName
      });
      if (res.data) {
        console.log('res', res);
        googleCode.value = ""
        googleAuthData.value = res.data.data;
        showGoogleAuthDialog.value = true;
      }
    }
  }
}

// 保存二维码
function saveQrcode() {
  if (!qrcode.value) {
    console.error('无法找到二维码元素');
    return;
  }
  // 获取SVG元素
  const originalSvg = qrcode.value.$el;
  if (!originalSvg) {
    console.error('无法找到二维码SVG元素');
    return;
  }
  // 创建新的SVG元素用于保存
  const svgClone = originalSvg.cloneNode(true) as SVGElement;
  // 将SVG转换为字符串
  const svgData = new XMLSerializer().serializeToString(svgClone);
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const img = new Image();
  img.onload = () => {
    canvas.width = img.width;
    canvas.height = img.height;
    ctx.drawImage(img, 0, 0);
    const pngData = canvas.toDataURL('image/png');
    const link = document.createElement('a');
    link.href = pngData;
    link.download = 'qrcode.png';
    link.click();
  };
  img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
}


async function handleBindGoogleAuth() {
  if (!googleCode.value) {
    window.$message?.error('请输入谷歌验证码');
    return;
  }
  try {
    const res = await fetchBindGoogleSecret({
      user_name: model.value.userName,
      secret: googleAuthData.value.secret,
      google_code: googleCode.value
    });

    if (res.data) {
      window.$message?.success('绑定成功');
      showGoogleAuthDialog.value = false;
    }
  } catch (error) {
    window.$message?.error('绑定失败，请检查验证码是否正确');
  }
}

type AccountKey = 'super' | 'admin' | 'user';

interface Account {
  key: AccountKey;
  label: string;
  userName: string;
  password: string;
}

</script>

<template>
  <div style="width: 100%;">
    <ElForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keyup.enter="handleSubmit">
      <ElFormItem prop="userName">
        <ElInput v-model="model.userName" :placeholder="$t('page.login.common.userNamePlaceholder')" clearable>
          <template #prefix>
            <img src="@/assets/imgs/login-user.png" class="input-icon" alt="user" />
          </template>
        </ElInput>
      </ElFormItem>
      <ElFormItem prop="password">
        <ElInput v-model="model.password" type="password" :placeholder="$t('page.login.common.passwordPlaceholder')" clearable show-password>
          <template #prefix>
            <img src="@/assets/imgs/login-pwd.png" class="input-icon" style="width: 15px; height: 22px;" alt="pwd" />
          </template>
        </ElInput>
      </ElFormItem>
      <ElFormItem prop="google_code">
        <ElInput v-model="model.google_code" placeholder="请输入谷歌验证码" clearable>
          <template #prefix>
            <img src="@/assets/imgs/login-google.png" style="width: 19px; height: 19px;" class="input-icon" alt="google" />
          </template>
        </ElInput>
      </ElFormItem>
      <ElSpace direction="vertical" :size="24" class="w-full" fill>
        <!-- <div class="flex-y-center justify-between">
          <ElCheckbox>{{ $t('page.login.pwdLogin.rememberMe') }}</ElCheckbox>
          <ElButton text @click="toggleLoginModule('reset-pwd')">
            {{ $t('page.login.pwdLogin.forgetPassword') }}
          </ElButton>
        </div> -->
        <ElButton class="login-btn" type="primary" size="large" round block :loading="authStore.loginLoading" @click="handleSubmit">登  录</ElButton>
      </ElSpace>
    </ElForm>
    <!-- 谷歌验证码 -->
    <ElDialog v-model="showGoogleAuthDialog"
      :modal="true"
      title="Google 验证器绑定" width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      append-to-body
      >
      <div class="flex flex-col items-center p-4">
        <div class="mb-4">
          <div class="qrcode-container">
            <QRCodeVue :value="googleAuthData.qrcode" :size="200" level="H" render-as="svg"
              ref="qrcode"
            />
          </div>
        </div>
        <div class="text-center">
          <span class="text-gray-600 mb-2">密钥：</span>
          <span class="text-lg font-mono">{{ googleAuthData.secret }}</span>
        </div>
        <div class="mt-4 w-full">
          <ElInput v-model="googleCode" placeholder="请输入谷歌验证码" class="mb-4" />
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-2">
          <ElButton @click="showGoogleAuthDialog = false">关闭</ElButton>
          <ElButton type="primary" @click="saveQrcode">保存</ElButton>
          <ElButton type="primary" @click="handleBindGoogleAuth">绑定</ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>

/* 容器宽度由外部控制，表单内元素100% */
.el-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.el-form-item {
  margin-bottom: 0;
}

::v-deep .el-input__wrapper {
  background: transparent;
  min-height: 45px;
  border-radius: 0px;
  height: 45px;
  padding-left: 12px;
  border-color: #7493E2;
  box-shadow: 0 0 0 1px #7493E2;
}

.el-input__wrapper:focus-within,
.el-input__wrapper.is-focus {
  border-color: #212D6D;
}

.input-icon {
  width: 19px;
  height: 21px;
  margin-right: 12px;
  vertical-align: middle;
}

::v-deep .el-input__inner {
  /* color: white; */
  font-size: 16px;
  height: 45px;
  line-height: 45px;
}

::v-deep .el-input::placeholder,
::v-deep .el-input__inner::placeholder {
  color: #7493E2;
  opacity: 1;
  font-size: 16px;
}

.el-button.login-btn,
.el-button[type='primary'] {
  width: 100%;
  height: 48px;
  font-size: 16px;
  border-radius: 0px;
  background: #4eaaff;
  border: none;
}

.el-button.login-btn:active,
.el-button.login-btn:focus {
  background: #4eaaff;
}

@media (max-width: 600px) {
  .el-form {
    gap: 20px;
  }
  .el-button.login-btn,
  .el-button[type='primary'] {
    font-size: 18px;
    height: 44px;
  }
}

::v-deep .el-input .el-input__clear,
::v-deep .el-input .el-input__password {
  color: white;
  cursor: pointer;
  font-size: 18px;
}

.qrcode-container {
  width: 200px;
  height: 200px;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.qrcode-canvas {
  width: 100%;
  height: 100%;
}
</style>
