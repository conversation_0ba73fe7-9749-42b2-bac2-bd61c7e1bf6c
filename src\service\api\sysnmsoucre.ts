import { request } from '../request';


// 获取轮播图列表
export function fetchGetCarouselList(params: any) {
  return request({
    url: '/backend/carousel/list',
    method: 'get',
    params
  });
}

// 创建轮播图
export function fetchAddCarousel(data: any) {
  return request({
    url: '/backend/carousel/create',
    method: 'post',
    data
  });
}

// 更新轮播图
export function fetchUpdateCarousel(data: any) {
  return request({
    url: '/backend/carousel/update',
    method: 'post',
    data
  });
}

// 获取轮播图详情
export function fetchGetCarouselDetail(data: any) {
  return request({
    url: '/backend/carousel/show',
    method: 'post',
    data
  });
}

// 更新轮播图状态
export function fetchUpdateCarouselState(data: any) {
  return request({
    url: '/backend/carousel/state',
    method: 'post',
    data
  });
}