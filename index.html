<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 14:39:01
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-07-04 16:05:01
 * @FilePath: \betdoce-web\index.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/favicon" href="/favicon.ico" />
    <title>%VITE_APP_TITLE%</title>
    <!-- 预加载关键资源 -->
    <!-- <link
      rel="preload"
      href="<%= VITE_APP_URL %>fonts/important.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    /> -->

    <!-- 预取非关键资源 -->
    <!-- <link rel="prefetch" href="<%= VITE_APP_URL %>images/large-image.jpg" /> -->
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#ffffff" />
    <meta name="description" content="%VITE_APP_DESCRIPTION%" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <link rel="mask-icon" href="/masked-icon.svg" color="#FFFFFF" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <meta name="apple-mobile-web-app-title" content="%VITE_APP_TITLE%" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="%VITE_APP_URL%" />
    <meta property="og:title" content="%VITE_APP_TITLE%" />
    <meta property="og:description" content="%VITE_APP_DESCRIPTION%" />
    <meta property="og:image" content="%VITE_APP_IMAGE%" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:alt" content="%VITE_APP_TITLE%" />
    <meta property="og:site_name" content="%VITE_APP_TITLE%" />

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:url" content="%VITE_APP_URL%" />
    <meta name="twitter:title" content="%VITE_APP_TITLE%" />
    <meta name="twitter:description" content="%VITE_APP_DESCRIPTION%" />
    <meta name="twitter:image" content="%VITE_APP_IMAGE%" />
    <meta name="twitter:image:alt" content="%VITE_APP_TITLE%" />

    <!-- Keywords -->
    <meta name="keywords" content="%VITE_APP_KEYWORDS%" />

    <!-- Application Name -->
    <meta name="application-name" content="%VITE_APP_TITLE%" />

    <!-- Mobile and Browser Compatibility -->
    <meta
      name="viewport"
      content="width=device-width,user-scalable=no,initial-scale=1,minimum-scale=1,maximum-scale=1"
    />
    <meta name="format-detection" content="telephone=no" />
    <meta name="renderer" content="webkit" />
    <meta name="force-rendering" content="webkit" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="full-screen" content="yes" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="360-fullscreen" content="true" />
    <meta name="screen-orientation" content="portrait" />
    <meta name="x5-orientation" content="portrait" />

    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/@mdi/font@latest/css/materialdesignicons.min.css"
      rel="stylesheet"
    />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
        touch-action: manipulation;
      }
    </style>
  </head>

  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
