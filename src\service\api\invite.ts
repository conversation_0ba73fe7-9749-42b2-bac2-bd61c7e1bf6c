import { request } from '../request';

// 获取邀请配置
export function fetchInviteConfig(params?: any) {
  return request({
    url: '/backend/invite/config',
    method: 'get',
    params
  });
}

// 保存邀请配置
export function fetchInviteConfigSave(data?: any) {
  return request({
    url: '/backend/invite/config',
    method: 'post',
    data
  });
}

// 获取邀请排行榜
export function fetchInviteRankList(params?: any) {
  return request({
    url: '/backend/invite/rank',
    method: 'get',
    params
  });
}

// 获取邀请列表
export function fetchInviteList(params?: any) {
  return request({
    url: '/backend/invite/list',
    method: 'get',
    params
  });
}

