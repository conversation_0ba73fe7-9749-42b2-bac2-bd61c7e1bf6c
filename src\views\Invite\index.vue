<template>
  <v-container class="invite-container" max-width="940">
    <Invite v-if="toggle === 'Convite'"></Invite>
    <invite-details v-if="toggle === 'Detalhes'"></invite-details>
    <my-team v-if="toggle === 'Minha'"></my-team>
    <div class="bottom-toggle">
      <v-btn-toggle
        class="bottom-toggle-btn"
        v-model="toggle"
        rounded="xl"
        mandatory
      >
        <v-btn value="Convite">Convite</v-btn>
        <v-btn value="Detalhes">Detalhes</v-btn>
        <v-btn value="Minha">Minha Equipe</v-btn>
      </v-btn-toggle>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import Invite from "./invite/index.vue";
import InviteDetails from "./inviteDetails/index.vue";
import MyTeam from "./team/index.vue";
import { ref, onMounted } from "vue";
import { showSuccess } from "@/utils/toast";
import { useStore } from "vuex";

const toggle = ref("Convite");

onMounted(() => {});

// 加载更多排行数据
const loadMore = () => {
  // TODO: 实现加载更多逻辑
};
</script>

<style lang="scss" scoped>
:deep(.v-input__details) {
  display: none;
}
:deep(.v-textarea .v-field--active textarea) {
  font-size: 14px;
}
.invite-container {
  position: relative;
  padding: 16px;
  min-height: 100vh;

  display: flex;
  flex-direction: column;

  .section-header {
    color: white;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    padding: 16px;
    background: linear-gradient(87deg, #e5d51f, #3b922a, #e5d51f);
    border-radius: 8px 8px 0 0;

    .sub-header {
      font-size: 14px;
      margin-top: 4px;
      font-weight: normal;
    }
  }

  .invite-section {
    background: #2c5b40;
    border-radius: 8px;
    margin-bottom: 16px;

    .link-area {
      padding: 16px;

      .link-tip {
        color: #d1b146;
        font-size: 14px;
        margin-bottom: 12px;
      }

      .link-box {
        border-radius: 8px;
        margin-bottom: 12px;

        .link-text {
          flex: 1;
          color: white;
          margin-right: 12px;
          background: #253922;
          height: 40px;
          line-height: 40px;
          padding: 0 14px;
          border-radius: 8px;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .copy-btn {
          background: linear-gradient(180deg, #a1fa78, #24c404);
          color: white;
          border-radius: 20px;
          height: 40px;
          min-width: 120px;
        }
      }

      .link-desc {
        color: rgba(255, 255, 255, 0.6);
        font-size: 14px !important;
        line-height: 1.4;
        background: #253922;
        padding: 8px 14px;
        border-radius: 8px;
      }
    }
  }

  .rewards-section {
    background: #2c5b40;
    border-radius: 8px;
    margin-bottom: 16px;

    .rewards-list {
      padding: 16px;

      .reward-item {
        background: #2b324d;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .reward-title {
          color: white;
          margin-bottom: 8px;
        }

        .reward-content {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .reward-amount {
            color: #ffdf00;
            font-size: 18px;
            font-weight: 500;
          }

          .reward-count {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
          }
        }
      }
    }
  }

  .ranking-section {
    background: #2c5b40;
    border-radius: 8px;
    margin-bottom: 16px;

    .ranking-table {
      padding: 0 4px;
      overflow-x: auto;

      .table-header {
        display: grid;
        grid-template-columns: 80px 1fr 1fr 1fr;
        gap: 10px;
        padding: 12px 8px;
        color: white;
        font-size: 14px;
        font-weight: 500;
        background: #1d2654;
        text-align: center;
      }

      .table-row {
        display: grid;
        grid-template-columns: 80px 1fr 1fr 1fr;
        gap: 10px;
        padding: 12px 8px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 14px;
        text-align: center;

        .col {
          display: flex;
          align-items: center;
          justify-content: center;

          &.bonus {
            color: #ffdf00;
          }
        }
      }
    }

    .ranking-tip {
      padding: 12px 16px;
      color: rgba(255, 255, 255, 0.6);
      font-size: 14px;
      line-height: 1.4;
      background: #253922;
      margin: 12px;
      border-radius: 8px;
    }
  }

  .rules-section {
    background: #2c5b40;
    border-radius: 8px;
    margin-bottom: 16px;

    .rules-img {
      max-width: 100%;
      height: auto;
      margin: 16px;
    }

    .rules-content {
      padding: 16px;

      .rules-text {
        background: #253922;
        padding: 12px;
        border-radius: 8px;

        .rules-title {
          color: #ffdf00;
          margin-bottom: 12px;
          font-weight: 500;
        }

        .rules-list {
          color: rgba(255, 255, 255, 0.8);
          font-size: 14px;
          line-height: 1.5;

          .rule-item {
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }

  .bottom-toggle {
    position: sticky;
    width: 100%;
    bottom: 0px;

    display: flex;
    margin-top: 16px;
    box-sizing: border-box;
    // background: #2c5b40; /* Dark background for toggle container */
    padding: 4px; /* Adjust padding slightly if needed */
    border-radius: 8px; /* Outer container roundness */
    .bottom-toggle-btn {
      width: 100%;
    }
    .v-btn {
      flex: 1;
      padding: 0 8px;
      height: 44px; /* Maintain height */
      /* Removed explicit border-radius here to let v-btn-toggle handle it */
      color: rgba(
        255,
        255,
        255,
        0.8
      ); /* Default text color, slightly lighter */
      background-color: #2c5b40; /* Dark background for inactive buttons */
      box-sizing: border-box;
      font-size: 14px;
      /* Removed default border and box-shadow from v-btn if necessary */
      &::before {
        opacity: 0 !important;
      }

      &.v-btn--active {
        color: white; /* Active text color */
        background: linear-gradient(
          180deg,
          #a1fa78,
          #24c404
        ); /* Apply linear gradient background for active state */
      }
    }
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .invite-container {
    padding: 6px;

    .section-header {
      font-size: 14px;
      padding: 12px;

      .sub-header {
        font-size: 12px;
      }
    }

    .invite-section {
      .link-area {
        padding: 12px;

        .link-tip {
          font-size: 12px;
        }

        .link-box {
          .link-text {
            font-size: 12px;
            height: 36px;
            line-height: 36px;
          }

          .copy-btn {
            height: 36px;
            min-width: 100px;
            font-size: 12px;
          }
        }

        .link-desc {
          font-size: 12px;
        }
      }
    }

    .rewards-section {
      .rewards-list {
        padding: 12px;

        .reward-item {
          padding: 10px;

          .reward-title {
            font-size: 14px;
          }

          .reward-content {
            .reward-amount {
              font-size: 16px;
            }

            .reward-count {
              font-size: 12px;
            }
          }
        }
      }
    }

    .ranking-section {
      .ranking-table {
        .table-header {
          grid-template-columns: 60px 1fr 1fr 1fr;
          gap: 8px;
          padding: 8px;
          font-size: 12px;
        }

        .table-row {
          grid-template-columns: 60px 1fr 1fr 1fr;
          gap: 8px;
          padding: 8px;
          font-size: 12px;

          .col {
            img {
              width: 36px;
            }
          }
        }
      }

      .ranking-tip {
        padding: 10px 12px;
        font-size: 12px;
        margin: 8px;
      }
    }

    .rules-section {
      .rules-img {
        margin: 12px;
      }

      .rules-content {
        padding: 12px;

        .rules-text {
          padding: 10px;

          .rules-title {
            font-size: 14px;
          }

          .rules-list {
            font-size: 12px;
          }
        }
      }
    }

    .bottom-toggle {
      margin-top: 12px;
      height: 80px;
      background: #00551d;
      padding: 4px 4px 14px 4px; /* Adjust padding slightly if needed */
      .v-btn {
        height: 40px;
        font-size: 13px;
        padding: 0 6px;
      }
    }
  }
}

// 小屏幕手机适配
@media screen and (max-width: 375px) {
  .invite-container {
    padding: 8px;

    .ranking-section {
      .ranking-table {
        .table-header {
          grid-template-columns: 50px 1fr 1fr 1fr;
          font-size: 11px;
        }

        .table-row {
          grid-template-columns: 50px 1fr 1fr 1fr;
          font-size: 11px;

          .col {
            img {
              width: 32px;
            }
          }
        }
      }
    }

    .bottom-toggle {
      padding: 4px;
      margin-top: 8px;
      height: 80px;
      padding: 4px 4px 14px 4px; /* Adjust padding slightly if needed */
      .v-btn {
        height: 36px;
        font-size: 12px;
        padding: 0 4px;
      }
    }
  }
}
</style>
