<script setup lang="ts">
import { useTable } from "@/hooks/common/table";
import { fetchCheckInRecordList } from "@/service/api/checkIn";
import type { CheckInRecordItem } from "@/service/api/checkIn";
import Search from "./modules/search.vue";
import TableHeaderOperation from "@/components/advanced/table-header-operation.vue";
import { useRouter } from "vue-router";
import moment from "moment";
import { getBrazilDate } from "@/utils/format";

const router = useRouter();

defineOptions({ name: "CheckInRecord" });

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  columnChecks,
} = useTable({
  apiFn: fetchCheckInRecordList,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 20,
    sort: "id",
    user_id: undefined,
    phone: undefined,
    level: undefined,
    streak: undefined,
    // create_at:undefined
    start_time: undefined,
    end_time: undefined,
  },
  columns: () => [
    { type: "index", label: "序号", width: 64, prop: "index" },
    { prop: "uuid", label: "用户ID" },
    { prop: "phone", label: "手机号" },
    { prop: "nickname", label: "昵称" },
    { prop: "level", label: "VIP等级" },
    { prop: "streak", label: "连签天数" },
    {
      prop: "reward",
      label: "奖励金额",
      formatter: (row: CheckInRecordItem) => (row.reward / 100).toFixed(2),
    },
    {
      prop: "checkin_date",
      label: "签到日期",
      formatter: (row: CheckInRecordItem) =>
        row.checkin_date
          ? moment(row.checkin_date*1000).format("YYYY-MM-DD")
          : "-",
    },
    {
      prop: "created_at",
      label: "创建时间",
      formatter: (row: CheckInRecordItem) =>
        row.created_at
          ? moment(row.created_at).format("YYYY-MM-DD HH:mm:ss")
          : "-",
    },
  ],
});

function handleToConfig() {
  router.push("/operation/viplevelmanagement");
}
</script>

<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <Search
      v-model:model="searchParams"
      @reset="
        () => {
          resetSearchParams();
        }
      "
      @search="getDataByPage"
    >
      <template #table-operation>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          @refresh="getData"
        >
          <template #default>
            <ElButton type="primary" plain size="small" @click="handleToConfig">
              <template #icon>
                <icon-ant-design-setting-outlined />
              </template>
              活动配置
            </ElButton>
          </template>
        </TableHeaderOperation>
      </template>
    </Search>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-60px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="checkedRowKeys = $event.map((row) => row.id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
