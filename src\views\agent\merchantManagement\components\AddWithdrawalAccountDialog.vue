<template>
  <ElDialog
    v-model="visible"
    title="新增提现账号"
    width="500px"
    @close="handleCancel"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <ElFormItem label="账号类型" prop="account_type">
        <ElSelect
          v-model="formData.account_type"
          placeholder="请选择账号类型"
          style="width: 100%"
        >
          <ElOption
            v-for="type in props.accountTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="账号/卡号" prop="account_number">
        <ElInput
          v-model="formData.account_number"
          placeholder="请输入提现账号/卡号"
          maxlength="50"
        />
      </ElFormItem>
      <ElFormItem label="姓名" prop="account_name">
        <ElInput
          v-model="formData.account_name"
          placeholder="请输入姓名"
          maxlength="20"
        />
      </ElFormItem>
      <ElFormItem label="手机号" prop="phone">
        <ElInput
          v-model="formData.phone"
          placeholder="请输入手机号"
          maxlength="20"
        />
      </ElFormItem>
      <ElFormItem label="验证码" prop="verify_code">
        <div class="flex gap-16px">
          <ElInput
            disabled
            v-model="formData.verify_code"
            placeholder="请输入验证码"
            maxlength="10"
            style="flex-grow: 1"
          />
          <ElButton type="primary" @click="handleValidate">验证</ElButton>
        </div>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton type="primary" @click="handleSave">保存</ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, reactive, defineExpose, defineEmits } from "vue";
import { agentWithdrawalAccountsAdd, agentSendSms } from "@/service/api/agent";
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElMessage,
} from "element-plus";
import type { FormInstance, FormRules } from "element-plus";

interface Props {
  accountTypes: { value: string; label: string }[];
}

const props = defineProps<Props>();

const visible = ref(false);
const formRef = ref<FormInstance>();

const formData = reactive({
  account_type: "", // 账号类型
  account_number: "", // 账号/卡号
  account_name: "", // 姓名
  phone: "", // 手机号
  verify_code: "", // 验证码
});

const formRules = reactive<FormRules>({
  account_type: [
    { required: true, message: "请选择账号类型", trigger: "change" },
  ],
  account_number: [
    { required: true, message: "请输入账号/卡号", trigger: "blur" },
  ],
  account_name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
  phone: [{ required: true, message: "请输入手机号", trigger: "blur" }],
  verify_code: [{ required: true, message: "请输入验证码", trigger: "blur" }],
});

const emit = defineEmits(["refresh"]);

// 打开弹窗
function open() {
  visible.value = true;
  // 重置表单
  formRef.value?.resetFields();
  Object.assign(formData, {
    account_type: "",
    account_number: "",
    account_name: "",
    phone: "",
    verify_code: "",
  });
}

// 关闭弹窗
function close() {
  visible.value = false;
}

// 处理取消
function handleCancel() {
  close();
}

// 处理验证码验证
async function handleValidate() {
  if (!formData.phone) {
    ElMessage.warning("请先输入手机号");
    return;
  }
  try {
    const res = await agentSendSms({
      mobile: formData.phone,
      sms_channel: "indiahm_sms",
    });
    ElMessage.success("验证码已发送");
    console.log(res);
    formData.verify_code = res.data?.data?.content;
  } catch (error) {
    console.error("发送验证码失败:", error);
    ElMessage.error("发送验证码失败");
  }
}

// 处理保存
async function handleSave() {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    const params = {
      account_type: formData.account_type,
      account_number: formData.account_number,
      account_name: formData.account_name,
      phone: formData.phone,
      verify_code: formData.verify_code,
    };

    await agentWithdrawalAccountsAdd(params);
    ElMessage.success("新增提现账号成功");
    close();
    emit("refresh");
  } catch (error) {
    console.error("保存失败:", error);
    ElMessage.error("保存失败，请检查输入信息");
  }
}

defineExpose({
  open,
  close,
});
</script>

<style scoped>
.gap-16px {
  gap: 16px;
}
.flex {
  display: flex;
}
</style>
