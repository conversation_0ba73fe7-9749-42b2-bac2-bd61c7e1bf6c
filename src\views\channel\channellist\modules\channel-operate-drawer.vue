<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';
import { fetchCreateChannel, fetchUpdateChannel } from '@/service/api';

interface Props {
  operateType: 'add' | 'edit';
  rowData?: any | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', { default: false });

const title = computed(() => {
  return props.operateType === 'edit' ? '编辑渠道' : '新增渠道';
});

const formRef = ref();
const formData = reactive({
  channel_name: '',
  responsible_by: '',
  contact_info: '',
  channel_code: '',
  domain: '',
  status: 1
});

const rules = {
  channel_name: [{ required: true, message: '请输入渠道名称', trigger: 'blur' }],
  responsible_by: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
  contact_info: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],
  channel_code: [{ required: true, message: '请输入渠道代码', trigger: 'blur' }],
  domain: [{ required: true, message: '请输入域名', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
};

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  if (!formRef.value) return;
  await formRef.value.validate();
  if (props.operateType === 'edit') {
    await fetchUpdateChannel({ ...formData, id: props.rowData.id });
  } else {
    await fetchCreateChannel(formData);
  }
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    if (props.operateType === 'edit' && props.rowData) {
      Object.assign(formData, props.rowData);
    } else {
      Object.assign(formData, {
        channel_name: '',
        responsible_by: '',
        contact_info: '',
        channel_code: '',
        domain: '',
        status: 1
      });
    }
  }
});
</script>

<template>
  <ElDrawer :size="500" v-model="visible" :title="title">
    <ElForm ref="formRef" :model="formData" :rules="rules" label-position="top">
      <ElFormItem label="渠道名称" prop="channel_name">
        <ElInput v-model="formData.channel_name" placeholder="请输入渠道名称" />
      </ElFormItem>
      <ElFormItem label="负责人" prop="responsible_by">
        <ElInput v-model="formData.responsible_by" placeholder="请输入负责人" />
      </ElFormItem>
      <ElFormItem label="联系方式" prop="contact_info">
        <ElInput v-model="formData.contact_info" placeholder="请输入联系方式" />
      </ElFormItem>
      <ElFormItem label="渠道代码" prop="channel_code">
        <ElInput v-model="formData.channel_code" placeholder="请输入渠道代码" />
      </ElFormItem>
      <ElFormItem label="域名" prop="domain">
        <ElInput v-model="formData.domain" placeholder="请输入域名" />
      </ElFormItem>
      <ElFormItem label="状态" prop="status">
        <ElRadioGroup v-model="formData.status">
          <ElRadio :label="1">启用</ElRadio>
          <ElRadio :label="0">禁用</ElRadio>
        </ElRadioGroup>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="flex justify-end gap-2">
        <ElButton @click="visible = false">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit">确定</ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<style scoped>
</style>
