stages:
  - build

# 开发环境自动构建
box-777-admin-dev:
  stage: build
  variables:
    APP_NAME: box-777-admin-test
    ENVIRONMENT: "开发环境"
  script:
    - echo "🚀 开始构建 ${APP_NAME} (${ENVIRONMENT})"
    - bash ./build.sh
  after_script:
    - |
      # 飞书通知脚本
      if [ -z "${FEISHU_WEBHOOK}" ]; then
        echo "⚠️ FEISHU_WEBHOOK 未设置，跳过通知"
        exit 0
      fi
      
      # 清理URL
      CLEAN_WEBHOOK=$(echo "${FEISHU_WEBHOOK}" | tr -d '\n' | tr -d '\r' | sed 's/ //g')
      
      # 获取作业状态
      if [ "$CI_JOB_STATUS" == "success" ]; then
        STATUS="成功 ✅"
        COLOR="green"
        EMOJI="🟢"
      else
        STATUS="失败 ❌"
        COLOR="red"
        EMOJI="🔴"
      fi
      
      # 构建消息内容
      CONTENT="**项目**: ${CI_PROJECT_NAME}
      **任务**: ${CI_JOB_NAME}
      **环境**: ${ENVIRONMENT}
      **分支**: ${CI_COMMIT_REF_NAME}
      **状态**: <font color=${COLOR}>${STATUS}</font>
      **执行者**: ${GITLAB_USER_NAME:-"未知"}
      **持续时间**: ${CI_JOB_DURATION:-0}秒
      [查看流水线](${CI_PIPELINE_URL})"
      
      # 使用 jq 构建 JSON
      JSON_PAYLOAD=$(
        jq -n \
          --arg msg_type "interactive" \
          --arg content "$CONTENT" \
          --arg title "${EMOJI} GitLab CI 构建通知" \
          '{
            "msg_type": $msg_type,
            "card": {
              "header": {
                "title": {
                  "content": $title,
                  "tag": "plain_text"
                }
              },
              "elements": [{
                "tag": "div",
                "text": {
                  "content": $content,
                  "tag": "lark_md"
                }
              }]
            }
          }'
      )
      
      echo "发送通知到飞书..."
      curl -sS -X POST -H "Content-Type: application/json" \
           --data "${JSON_PAYLOAD}" \
           "${CLEAN_WEBHOOK}"
  tags:
    - build
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop"
      when: on_success

# 生产环境手动构建
box-777-admin-prod:
  stage: build
  variables:
    APP_NAME: box-777-admin-prod
    ENVIRONMENT: "生产环境"
  script:
    - echo "🚨 手动触发生产构建 ${APP_NAME} (${ENVIRONMENT})"
    - bash ./build-prod.sh  # 建议使用不同的生产环境构建脚本
  after_script:
    - |
      # 飞书通知脚本
      if [ -z "${FEISHU_WEBHOOK}" ]; then
        echo "⚠️ FEISHU_WEBHOOK 未设置，跳过通知"
        exit 0
      fi
      
      # 清理URL
      CLEAN_WEBHOOK=$(echo "${FEISHU_WEBHOOK}" | tr -d '\n' | tr -d '\r' | sed 's/ //g')
      
      # 构建消息内容
      CONTENT="**项目**: ${CI_PROJECT_NAME}
      **任务**: ${CI_JOB_NAME}
      **环境**: ${ENVIRONMENT}
      **分支**: ${CI_COMMIT_REF_NAME}
      **状态**: <font color="blue">手动部署完成</font>
      **执行者**: ${GITLAB_USER_NAME:-"未知"}
      **持续时间**: ${CI_JOB_DURATION:-0}秒
      [查看流水线](${CI_PIPELINE_URL})"
      
      # 使用 jq 构建 JSON
      JSON_PAYLOAD=$(
        jq -n \
          --arg msg_type "interactive" \
          --arg content "$CONTENT" \
          --arg title "🔵 GitLab CI 生产部署通知" \
          '{
            "msg_type": $msg_type,
            "card": {
              "header": {
                "title": {
                  "content": $title,
                  "tag": "plain_text"
                }
              },
              "elements": [{
                "tag": "div",
                "text": {
                  "content": $content,
                  "tag": "lark_md"
                }
              }]
            }
          }'
      )
      
      echo "发送生产部署通知到飞书..."
      curl -sS -X POST -H "Content-Type: application/json" \
           --data "${JSON_PAYLOAD}" \
           "${CLEAN_WEBHOOK}"
  tags:
    - build
  rules:
    - if: $CI_COMMIT_REF_NAME == "main"
      when: manual  # 手动触发