import { request } from '../request';

// 活动弹窗相关接口

/**
 * 获取活动弹窗列表
 */
export function fetchGetActivityAlertList(params: any) {
  return request({
    url: '/backend/popup/list',
    method: 'get',
    params
  });
}

/**
 * 获取活动弹窗详情
 */
export function fetchGetActivityAlertById(data: { id: number }) {
  return request({
    url: '/backend/popup/show',
    method: 'post',
    data
  });
}

/**
 * 创建活动弹窗
 */
export function fetchCreateActivityAlert(data: {
  title: string;
  content: string;
  image_url: string;
  hyperlink: string;
  weight: number;
  status: number;
  show_type: number;
}) {
  return request({
    url: '/backend/popup/create',
    method: 'post',
    data
  });
}

/**
 * 更新活动弹窗
 */
export function fetchUpdateActivityAlert(data: {
  id: number;
  title: string;
  content: string;
  image_url: string;
  hyperlink: string;
  weight: number;
  status: number;
  show_type: number;
}) {
  return request({
    url: '/backend/popup/update',
    method: 'post',
    data
  });
}

/**
 * 更新活动弹窗状态
 */
export function fetchUpdateActivityAlertStatus(data: {
  id: number;
  status: number;
}) {
  return request({
    url: '/backend/popup/updateStatus',
    method: 'post',
    data
  });
}
