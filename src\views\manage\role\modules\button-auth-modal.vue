<script setup lang="ts">
import { computed, ref, shallowRef, watch } from "vue";
import { $t } from "@/locales";

defineOptions({ name: "ButtonAuthModal" });

interface Props {
  /** the roleId */
  buttonIds: any[];
}

interface Emits {
  (e: "buttonIds", buttonIds: number[]): void;
}

const emit = defineEmits<Emits>();

const props = defineProps<Props>();

const visible = defineModel<boolean>("visible", {
  default: false,
});

function closeModal() {
  visible.value = false;
}

const title = computed(
  () => $t("common.edit") + $t("page.manage.role.buttonAuth"),
);

type ButtonConfig = {
  id: number;
  label: string;
  code: string;
};

const tree = shallowRef<ButtonConfig[]>([]);
const checks = ref([]);
async function getAllButtons() {
  // request
  tree.value = [
    { id: 1, label: "新增按钮", code: "code1" },
    { id: 2, label: "删除按钮", code: "code2" },
    { id: 3, label: "修改按钮", code: "code3" },
    { id: 4, label: "查询按钮", code: "code4" },
    { id: 5, label: "导出按钮", code: "code5" },
  ];

  checks.value = JSON.parse(JSON.stringify(props.buttonIds));
}

function checkChange(val: ButtonConfig) {
  const idx = checks.value?.indexOf(val.id);
  if (idx === -1) {
    checks.value?.push(val.id);
  } else {
    checks.value?.splice(idx, 1);
  }
}

function handleSubmit() {
  console.log("button_ids:", checks.value);
  emit("buttonIds", checks.value);
  closeModal();
}

function init() {
  getAllButtons();
}

watch(visible, () => {
  if (visible.value) {
    // 清空之前的选中状态
    checks.value = [];
    init();
  } else {
    checks.value = [];
  }
});
</script>

<template>
  <ElDialog v-model="visible" :title="title" preset="card" class="w-480px">
    <ElTree
      v-model:checked-keys="checks"
      :data="tree"
      node-key="id"
      show-checkbox
      class="h-280px"
      :default-checked-keys="checks"
      @check-change="checkChange"
    />
    <template #footer>
      <ElSpace class="w-full justify-end">
        <ElButton size="small" class="mt-16px" @click="closeModal">{{
          $t("common.cancel")
        }}</ElButton>
        <ElButton
          type="primary"
          size="small"
          class="mt-16px"
          @click="handleSubmit"
        >
          {{ $t("common.confirm") }}
        </ElButton>
      </ElSpace>
    </template>
  </ElDialog>
</template>

<style scoped></style>
