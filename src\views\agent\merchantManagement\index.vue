<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-06 10:38:18
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-07-09 02:57:53
 * @FilePath: \betdoce-admin\src\views\agent\merchantManagement\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <!-- 数据概览模块 -->
    <div class="data-overview">
      <div class="data-card" :style="cardColors.users">
        <div class="card-title">用户数据</div>
        <div class="main-value">{{ overviewData.registered_users }}</div>
        <div class="card-details">
          <div class="sub-value">
            今日注册:<br />{{ overviewData.registered_users }}
          </div>
          <div class="sub-value">
            邀请注册:<br />{{ overviewData.invited_users }}
          </div>
        </div>
      </div>
      <div class="data-card" :style="cardColors.money">
        <div class="card-title">充值</div>
        <div class="main-value">
          R${{ formatAmount(overviewData.total_recharge_amount) }}
        </div>
        <div class="card-details">
          <div class="sub-value">
            提现:<br />R$ {{ formatAmount(overviewData.total_withdrawal_amount) }}
          </div>
          <div class="sub-value">
            首充:<br />{{ overviewData.first_recharge_users }}人
          </div>
        </div>
      </div>
      <div class="data-card" :style="cardColors.game">
        <div class="card-title">游戏数据</div>
        <div class="main-value">
          R${{ formatAmount(overviewData.game_betting) }}
        </div>
        <div class="card-details">
          <div class="sub-value">
            派奖:<br />R$ {{ formatAmount(overviewData.game_winning) }}
          </div>
          <div class="sub-value">
            盈亏:<br />R$ {{ formatAmount(overviewData.game_betting_winning_diff) }}
          </div>
        </div>
      </div>
      <div class="data-card" :style="cardColors.pending">
        <div class="card-title">待处理事项</div>
        <div class="main-value">
          {{ overviewData.messages_pending_reply }}
        </div>
        <div class="card-details">
          <div class="sub-value">
            待回复消息:<br />{{ overviewData.messages_pending_reply }}
          </div>
          <div class="sub-value">
            待审核提现:<br />{{ overviewData.withdrawals_pending }}
          </div>
        </div>
      </div>
      <div class="data-card" :style="cardColors.balance">
        <div class="card-title">全平台现金总余额</div>
        <div class="main-value">
          R${{ overviewData.total_cash_withdrawable||0 }}
        </div>
        <div class="card-details">
          <div class="sub-value">
            赠金总余额:<br />R$ {{ overviewData.total_digital_balance||0 }}
          </div>
          <div class="sub-value">
            打码提现总余额:<br />R$ {{ overviewData.total_cash_balance||0 }}
          </div>
        </div>
      </div>
    </div>
    <!-- 基础信息 -->
    <ElCard class="box-card">
      <template #header>
        <div class="clearfix">
          <span>基础信息</span>
        </div>
      </template>
      <div class="text item">
        <p class="info-item">代理商ID: {{ agentBaseInfo.agent_id }}</p>
        <p class="info-item">代理商名称: {{ agentBaseInfo.agent_name }}</p>
        <p class="info-item">联系人: {{ agentBaseInfo.contact_person }}</p>
        <p class="info-item">联系方式: {{ agentBaseInfo.contact_phone }}</p>
        <p class="info-item">邮箱: {{ agentBaseInfo.contact_email }}</p>
        <p class="info-item">
          日提现限额: {{ agentBaseInfo.daily_withdrawal_limit / 100 }}
        </p>
        <p class="info-item">
          月提现限额: {{ agentBaseInfo.monthly_withdrawal_limit / 100 }}
        </p>
        <p class="info-item">
          创建时间:
          {{ moment(getBrazilDate(agentBaseInfo.created_at)).format("YYYY-MM-DD HH:mm:ss") }}
        </p>
        <p class="info-item">点击次数: {{ agentBaseInfo.click_count }}</p>
        <p class="info-item">注册次数: {{ agentBaseInfo.register_count }}</p>
        <div class="flex items-center">
          <span class="mr-8px">用户注册链接:</span>
          <ElInput
            v-model="userRegistrationLink"
            readonly
            class="registration-link-input"
          >
            <template #append>
              <ElButton type="primary" @click="copyRegistrationLink"
                >复制链接</ElButton
              >
            </template>
          </ElInput>
        </div>
      </div>
    </ElCard>

    <!-- 提现账号 -->
    <ElCard class="box-card mt-16px">
      <template #header>
        <div class="clearfix">
          <span>提现账号</span>
        </div>
      </template>
      <div>
        <!-- Search/Filter area -->
        <div class="mb-16px flex gap-16px">
          <ElInput
            placeholder="用户账号"
            style="width: 200px"
            v-model="accountSearchQuery"
          />
          <ElSelect
            placeholder="类型"
            style="width: 120px"
            v-model="accountTypeFilter"
          >
            <ElOption label="全部" value="全部" />
            <ElOption
              v-for="type in accountTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </ElSelect>
          <ElButton type="primary" @click="handleSearch">搜索</ElButton>
          <ElButton @click="handleReset">重置</ElButton>
          <ElButton type="primary" @click="handleAdd">新增</ElButton>
        </div>

        <!-- Table -->
        <ElTable :data="accountList" v-loading="loading" style="width: 100%">
          <ElTableColumn type="index" label="序号" width="60" />
          <ElTableColumn
            prop="account_type_name"
            label="账号类型"
            width="120"
          />
          <ElTableColumn
            prop="account_number"
            label="账号"
            min-width="180"
            show-overflow-tooltip
          />
          <!-- <ElTableColumn prop="account_type_name" label="账号名称" min-width="180" show-overflow-tooltip /> -->
          <ElTableColumn prop="account_name" label="姓名" width="120" />
          <ElTableColumn prop="phone" label="手机号码" width="120" />
          <ElTableColumn prop="created_at" label="创建时间" width="180">
            <template #default="{ row }">
              {{ moment(getBrazilDate(row.created_at)).format("YYYY-MM-DD HH:mm:ss") }}
            </template>
          </ElTableColumn>
        </ElTable>

        <ElPagination
          v-if="pagination.total > 0"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 30, 40]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          class="mt-16px"
        />
      </div>
    </ElCard>

    <!-- 新增提现账号弹窗 -->
    <AddWithdrawalAccountDialog
      ref="addDialogRef"
      :account-types="accountTypes"
      @refresh="fetchAccountList"
    />
  </div>
</template>

<script setup lang="tsx">
import {
  ElCard,
  ElButton,
  ElInput,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElTag,
} from "element-plus";
import { ref, onMounted } from "vue";
import {
  agentWithdrawalAccounts,
  agentInfo,
  agentWithdrawalAccountsTypes,
} from "@/service/api/agent";
import type {
  WithdrawalListParams,
  WithdrawalRecord as BaseWithdrawalRecord,
  WithdrawalAccount,
  WithdrawalAccountListParams,
} from "@/typings/withdrawal";
import AddWithdrawalAccountDialog from "./components/AddWithdrawalAccountDialog.vue";
import moment from "moment";
import { getDailyReport } from "@/service/api/report";
import { getBrazilTime,getBrazilDate } from "@/utils/format";

interface WithdrawalRecord extends BaseWithdrawalRecord {
  card_number?: string;
  name?: string;
  phone?: string;
}

const accountList = ref<WithdrawalAccount[]>([]);
const loading = ref(false);
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 代理商基础信息
const agentBaseInfo = ref({
  agent_id: 0,
  agent_name: "",
  contact_person: "",
  contact_phone: "",
  contact_email: "",
  daily_withdrawal_limit: 0,
  monthly_withdrawal_limit: 0,
  created_at: 0,
  invite_link: "",
  click_count: 0,
  register_count: 0,
});

const userRegistrationLink = ref(""); // 用户注册链接的值，从 API 获取

const addDialogRef = ref<InstanceType<typeof AddWithdrawalAccountDialog>>(); // 新增弹窗的引用

const accountSearchQuery = ref(""); // 用户账号搜索关键字
const accountTypeFilter = ref("全部"); // 账号类型筛选
const accountTypes = ref<{ value: string; label: string }[]>([]); // 支持的账号类型列表

const searchParams = ref<
  WithdrawalListParams & { account_num?: string; account_type?: string }
>({
  page: pagination.value.currentPage,
  size: pagination.value.pageSize,
});

// 数据概览相关
const overviewData = ref({
  messages_pending_reply: 0,
  withdrawals_pending: 0,
  code_amount: 0,
  platform_tax: 0,
  platform_balance: 0,
  online_users: 0,
  registered_users: 0,
  invited_users: 0,
  recharge_users: 0,
  first_recharge_users: 0,
  first_recharge_amount: 0,
  invitation_reward: 0,
  total_recharge_amount: 0,
  total_withdrawal_amount: 0,
  platform_revenue: 0,
  gift_cash: 0,
  gift_bonus: 0,
  withdrawal_users: 0,
  game_betting: 0,
  game_winning: 0,
  total_recharge_gift_cash: 0,
  game_betting_winning_diff: 0,
  recharge_orders: 0,
  withdrawal_orders: 0,
  invitation_first_recharge_users: 0,
  total_cash_withdrawable: 0,
  total_digital_balance: 0,
  total_cash_balance: 0,
});
const cardColors = {
  users: {
    background: "linear-gradient(135deg, #1890FF 0%, #36CBCB 100%)",
    boxShadow: "0 4px 20px 0 rgba(24, 144, 255, 0.1)",
  },
  money: {
    background: "linear-gradient(135deg, #722ED1 0%, #EB2F96 100%)",
    boxShadow: "0 4px 20px 0 rgba(114, 46, 209, 0.1)",
  },
  game: {
    background: "linear-gradient(135deg, #52C41A 0%, #13C2C2 100%)",
    boxShadow: "0 4px 20px 0 rgba(82, 196, 26, 0.1)",
  },
  pending: {
    background: "linear-gradient(135deg, #FA541C 0%, #FAAD14 100%)",
    boxShadow: "0 4px 20px 0 rgba(250, 84, 28, 0.1)",
  },
  balance: {
    background: "linear-gradient(135deg, #13ACF0 0%, #80FFFF 100%)",
    boxShadow: "0 4px 20px 0 rgba(255, 193, 7, 0.1)",
  },
};
function formatAmount(amount: number) {
  return (amount / 100).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}
// 获取数据的方法（请根据实际接口调整）
async function fetchTodayData() {
  const today = getBrazilTime();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate()).padStart(2, "0");
  const dateStr = `${year}-${month}-${day}`;

  try {
    const { data } = await getDailyReport({
      start_time: dateStr,
      end_time: dateStr,
    });
    // console.log(data)
    if (data) {
      console.log(data.data);
      overviewData.value = data.data;
    }
  } catch (error) {
    console.error("获取今日数据失败:", error);
  }
}

// 获取代理商基础信息
async function fetchAgentInfo() {
  try {
    const { data } = await agentInfo({});
    console.log(data);
    agentBaseInfo.value = data.data;
    userRegistrationLink.value = data.data.invite_link || "";
  } catch (error) {
    console.error("获取代理商基础信息失败:", error);
  }
}

// 获取支持的账号类型
async function fetchAccountTypes() {
  try {
    const { data } = await agentWithdrawalAccountsTypes({});
    accountTypes.value = data?.data?.map((type: any) => ({
      value: type.code.toString(),
      label: type.name,
    }));
  } catch (error) {
    console.error("获取账号类型失败:", error);
  }
}

async function fetchAccountList() {
  loading.value = true;
  try {
    const params: WithdrawalAccountListParams = {
      page: pagination.value.currentPage,
      size: pagination.value.pageSize,
      account_number:
        accountSearchQuery.value === "" ? undefined : accountSearchQuery.value,
      account_type:
        accountTypeFilter.value === "全部"
          ? undefined
          : accountTypeFilter.value,
    };

    const { data } = await agentWithdrawalAccounts(params);
    accountList.value = data.data;
    pagination.value.total = data.total;
  } catch (error) {
    console.error("获取提现账号列表失败:", error);
    accountList.value = [];
    pagination.value.total = 0;
  } finally {
    loading.value = false;
  }
}

function handleSearch() {
  pagination.value.currentPage = 1;
  fetchAccountList();
}

function handleReset() {
  accountSearchQuery.value = "";
  accountTypeFilter.value = "全部";
  pagination.value.currentPage = 1;
  fetchAccountList();
}

function handleAdd() {
  // 打开新增提现账号弹窗
  addDialogRef.value?.open();
}

// 复制注册链接到剪贴板
function copyRegistrationLink() {
  navigator.clipboard
    .writeText(userRegistrationLink.value)
    .then(() => {
      // TODO: Add a success message using ElMessage or similar
      console.log("复制成功!");
      window.$message?.success("复制成功！");
    })
    .catch((err) => {
      // TODO: Add an error message
      console.error("复制失败:", err);
    });
}

function handleCurrentChange(page: number) {
  pagination.value.currentPage = page;
  fetchAccountList();
}

function handleSizeChange(size: number) {
  pagination.value.pageSize = size;
  pagination.value.currentPage = 1;
  fetchAccountList();
}

// 添加編輯和刪除的處理函數
const handleEdit = (row: WithdrawalAccount) => {
  // TODO: 實現編輯功能
  console.log("編輯帳戶:", row);
};

const handleDelete = (row: WithdrawalAccount) => {
  // TODO: 實現刪除功能
  console.log("刪除帳戶:", row);
};

onMounted(() => {
  fetchAgentInfo();
  fetchAccountTypes();
  fetchAccountList();
  fetchTodayData(); // 调用获取今日数据的方法
});
</script>

<style scoped>
.box-card {
  width: 100%;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}
.text.item {
  font-size: 14px;
}
.mb-16px {
  margin-bottom: 16px;
}
.mt-16px {
  margin-top: 16px;
}
.flex {
  display: flex;
}
.gap-16px {
  gap: 16px;
}
.items-center {
  align-items: center;
}
.mr-8px {
  margin-right: 8px;
}

.registration-link-input {
  width: 400px; /* 设定固定宽度 */
}

/* 确保输入框内的文字不换行 */
.registration-link-input .el-input__inner {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info-item {
  margin-bottom: 10px; /* 设定底部边距 */
}

.data-overview {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 24px;
  padding-bottom: 10px;
}
.data-card {
  flex: 1;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  text-align: center;
}
.data-card:hover {
  transform: translateY(-5px);
}
.data-card .card-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 8px;
  width: 100%;
  text-align: left;
}
.data-card .main-value {
  font-size: 28px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  margin-bottom: 16px;
  width: 100%;
  text-align: left;
}
.data-card .card-details {
  display: flex;
  justify-content: space-around;
  width: 100%;
}
.data-card .card-details .sub-value {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.5;
  text-align: center;
}
</style>
