/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-16 13:31:05
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-22 10:01:55
 * @FilePath: \betdoce-admin\src\router\elegant\routes.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'account',
    path: '/account',
    component: 'layout.base',
    meta: {
      title: 'account',
      i18nKey: 'route.account'
    },
    children: [
      {
        name: 'account_accountlist',
        path: '/account/accountlist',
        component: 'view.account_accountlist',
        meta: {
          title: 'account_accountlist',
          i18nKey: 'route.account_accountlist'
        }
      },
      {
        name: 'account_banklist',
        path: '/account/banklist',
        component: 'view.account_banklist',
        meta: {
          title: 'account_banklist',
          i18nKey: 'route.account_banklist'
        }
      },
      {
        name: 'account_operationlog',
        path: '/account/operationlog',
        component: 'view.account_operationlog',
        meta: {
          title: 'account_operationlog',
          i18nKey: 'route.account_operationlog'
        }
      }
    ]
  },
  {
    name: 'activities',
    path: '/activities',
    component: 'layout.base',
    meta: {
      title: 'activities',
      i18nKey: 'route.activities'
    },
    children: [
      {
        name: 'activities_acviteconfig',
        path: '/activities/acviteconfig',
        component: 'view.activities_acviteconfig',
        meta: {
          title: 'activities_acviteconfig',
          i18nKey: 'route.activities_acviteconfig'
        }
      },
      {
        name: 'activities_checkin',
        path: '/activities/checkin',
        component: 'view.activities_checkin',
        meta: {
          title: 'activities_checkin',
          i18nKey: 'route.activities_checkin'
        }
      },
      {
        name: 'activities_exchangecode',
        path: '/activities/exchangecode',
        component: 'view.activities_exchangecode',
        meta: {
          title: 'activities_exchangecode',
          i18nKey: 'route.activities_exchangecode'
        }
      },
      {
        name: 'activities_invitation',
        path: '/activities/invitation',
        component: 'view.activities_invitation',
        meta: {
          title: 'activities_invitation',
          i18nKey: 'route.activities_invitation'
        }
      },
      {
        name: 'activities_pdd',
        path: '/activities/pdd',
        component: 'view.activities_pdd',
        meta: {
          title: 'activities_pdd',
          i18nKey: 'route.activities_pdd'
        }
      },
      {
        name: 'activities_poolofprizes',
        path: '/activities/poolofprizes',
        component: 'view.activities_poolofprizes',
        meta: {
          title: 'activities_poolofprizes',
          i18nKey: 'route.activities_poolofprizes'
        }
      }
    ]
  },
  {
    name: 'agent',
    path: '/agent',
    component: 'layout.base',
    meta: {
      title: 'agent',
      i18nKey: 'route.agent'
    },
    children: [
      {
        name: 'agent_agentdata',
        path: '/agent/agentdata',
        component: 'view.agent_agentdata',
        meta: {
          title: 'agent_agentdata',
          i18nKey: 'route.agent_agentdata'
        }
      },
      {
        name: 'agent_billmsg',
        path: '/agent/billmsg',
        component: 'view.agent_billmsg',
        meta: {
          title: 'agent_billmsg',
          i18nKey: 'route.agent_billmsg'
        }
      },
      {
        name: 'agent_manage',
        path: '/agent/manage',
        component: 'view.agent_manage',
        meta: {
          title: 'agent_manage',
          i18nKey: 'route.agent_manage'
        }
      },
      {
        name: 'agent_merchantmanagement',
        path: '/agent/merchantmanagement',
        component: 'view.agent_merchantmanagement',
        meta: {
          title: 'agent_merchantmanagement',
          i18nKey: 'route.agent_merchantmanagement'
        }
      }
    ]
  },
  {
    name: 'channel',
    path: '/channel',
    component: 'layout.base',
    meta: {
      title: 'channel',
      i18nKey: 'route.channel'
    },
    children: [
      {
        name: 'channel_channellist',
        path: '/channel/channellist',
        component: 'view.channel_channellist',
        meta: {
          title: 'channel_channellist',
          i18nKey: 'route.channel_channellist'
        }
      }
    ]
  },
  {
    name: 'finance',
    path: '/finance',
    component: 'layout.base',
    meta: {
      title: 'finance',
      i18nKey: 'route.finance'
    },
    children: [
      {
        name: 'finance_giftmoney',
        path: '/finance/giftmoney',
        component: 'view.finance_giftmoney',
        meta: {
          title: 'finance_giftmoney',
          i18nKey: 'route.finance_giftmoney'
        }
      },
      {
        name: 'finance_paymentchannel',
        path: '/finance/paymentchannel',
        component: 'view.finance_paymentchannel',
        meta: {
          title: 'finance_paymentchannel',
          i18nKey: 'route.finance_paymentchannel'
        }
      },
      {
        name: 'finance_rechargemsg',
        path: '/finance/rechargemsg',
        component: 'view.finance_rechargemsg',
        meta: {
          title: 'finance_rechargemsg',
          i18nKey: 'route.finance_rechargemsg'
        }
      },
      {
        name: 'finance_rechargepackage',
        path: '/finance/rechargepackage',
        component: 'view.finance_rechargepackage',
        meta: {
          title: 'finance_rechargepackage',
          i18nKey: 'route.finance_rechargepackage'
        }
      },
      {
        name: 'finance_withdrawalapplylist',
        path: '/finance/withdrawalapplylist',
        component: 'view.finance_withdrawalapplylist',
        meta: {
          title: 'finance_withdrawalapplylist',
          i18nKey: 'route.finance_withdrawalapplylist'
        }
      },
      {
        name: 'finance_withdrawalmanagement',
        path: '/finance/withdrawalmanagement',
        component: 'view.finance_withdrawalmanagement',
        meta: {
          title: 'finance_withdrawalmanagement',
          i18nKey: 'route.finance_withdrawalmanagement',
          hideInMenu: true
        }
      },
      {
        name: 'finance_withdrawalmsg',
        path: '/finance/withdrawalmsg',
        component: 'view.finance_withdrawalmsg',
        meta: {
          title: 'finance_withdrawalmsg',
          i18nKey: 'route.finance_withdrawalmsg'
        }
      }
    ]
  },
  {
    name: 'game',
    path: '/game',
    component: 'layout.base',
    meta: {
      title: 'game',
      i18nKey: 'route.game'
    },
    children: [
      {
        name: 'game_manufacturer',
        path: '/game/manufacturer',
        meta: {
          title: 'game_manufacturer',
          i18nKey: 'route.game_manufacturer'
        },
        children: [
          {
            name: 'game_manufacturer_shortlink',
            path: '/game/manufacturer/shortlink',
            component: 'view.game_manufacturer_shortlink',
            meta: {
              title: 'game_manufacturer_shortlink',
              i18nKey: 'route.game_manufacturer_shortlink'
            }
          }
        ]
      },
      {
        name: 'game_order',
        path: '/game/order',
        component: 'view.game_order',
        meta: {
          title: 'game_order',
          i18nKey: 'route.game_order'
        }
      },
      {
        name: 'game_product',
        path: '/game/product',
        component: 'view.game_product',
        meta: {
          title: 'game_product',
          i18nKey: 'route.game_product'
        }
      }
    ]
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      id: 1,
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'manage',
    path: '/manage',
    component: 'layout.base',
    meta: {
      title: 'manage',
      i18nKey: 'route.manage'
    },
    children: [
      {
        name: 'manage_faq',
        path: '/manage/faq',
        component: 'view.manage_faq',
        meta: {
          title: 'manage_faq',
          i18nKey: 'route.manage_faq'
        }
      },
      {
        name: 'manage_msg',
        path: '/manage/msg',
        component: 'view.manage_msg',
        meta: {
          title: 'manage_msg',
          i18nKey: 'route.manage_msg'
        }
      },
      {
        name: 'manage_notice',
        path: '/manage/notice',
        component: 'view.manage_notice',
        meta: {
          title: 'manage_notice',
          i18nKey: 'route.manage_notice'
        }
      },
      {
        name: 'manage_role',
        path: '/manage/role',
        component: 'view.manage_role',
        meta: {
          title: 'manage_role',
          i18nKey: 'route.manage_role'
        }
      },
      {
        name: 'manage_shortlink',
        path: '/manage/shortlink',
        component: 'view.manage_shortlink',
        meta: {
          title: 'manage_shortlink',
          i18nKey: 'route.manage_shortlink'
        }
      },
      {
        name: 'manage_source',
        path: '/manage/source',
        component: 'view.manage_source',
        meta: {
          title: 'manage_source',
          i18nKey: 'route.manage_source'
        }
      },
      {
        name: 'manage_user',
        path: '/manage/user',
        component: 'view.manage_user',
        meta: {
          title: 'manage_user',
          i18nKey: 'route.manage_user'
        }
      },
      {
        name: 'manage_user-detail',
        path: '/manage/user-detail/:id',
        component: 'view.manage_user-detail',
        meta: {
          title: 'manage_user-detail',
          i18nKey: 'route.manage_user-detail'
        }
      }
    ]
  },
  {
    name: 'operation',
    path: '/operation',
    component: 'layout.base',
    meta: {
      title: 'operation',
      i18nKey: 'route.operation'
    },
    children: [
      {
        name: 'operation_activityalertmanagement',
        path: '/operation/activityalertmanagement',
        component: 'view.operation_activityalertmanagement',
        meta: {
          title: 'operation_activityalertmanagement',
          i18nKey: 'route.operation_activityalertmanagement'
        }
      },
      {
        name: 'operation_customerfaqmanagement',
        path: '/operation/customerfaqmanagement',
        component: 'view.operation_customerfaqmanagement',
        meta: {
          title: 'operation_customerfaqmanagement',
          i18nKey: 'route.operation_customerfaqmanagement'
        }
      },
      {
        name: 'operation_policymanagement',
        path: '/operation/policymanagement',
        component: 'view.operation_policymanagement',
        meta: {
          title: 'operation_policymanagement',
          i18nKey: 'route.operation_policymanagement'
        }
      },
      {
        name: 'operation_telegrammanagement',
        path: '/operation/telegrammanagement',
        component: 'view.operation_telegrammanagement',
        meta: {
          title: 'operation_telegrammanagement',
          i18nKey: 'route.operation_telegrammanagement'
        }
      },
      {
        name: 'operation_viplevelmanagement',
        path: '/operation/viplevelmanagement',
        component: 'view.operation_viplevelmanagement',
        meta: {
          title: 'operation_viplevelmanagement',
          i18nKey: 'route.operation_viplevelmanagement'
        }
      }
    ]
  },
  {
    name: 'report',
    path: '/report',
    component: 'layout.base',
    meta: {
      title: 'report',
      i18nKey: 'route.report'
    },
    children: [
      {
        name: 'report_commissionstatisticsforcoding',
        path: '/report/commissionstatisticsforcoding',
        component: 'view.report_commissionstatisticsforcoding',
        meta: {
          title: 'report_commissionstatisticsforcoding',
          i18nKey: 'route.report_commissionstatisticsforcoding'
        }
      },
      {
        name: 'report_gamelist',
        path: '/report/gamelist',
        component: 'view.report_gamelist',
        meta: {
          title: 'report_gamelist',
          i18nKey: 'route.report_gamelist'
        }
      },
      {
        name: 'report_invitationrewards',
        path: '/report/invitationrewards',
        component: 'view.report_invitationrewards',
        meta: {
          title: 'report_invitationrewards',
          i18nKey: 'route.report_invitationrewards'
        }
      },
      {
        name: 'report_realtimedatacomparison',
        path: '/report/realtimedatacomparison',
        component: 'view.report_realtimedatacomparison',
        meta: {
          title: '实时数据对比',
          i18nKey: 'route.realtime_data_comparison',
          id: 3,
          order: 1,
          parentId: 6
        }
      },
      {
        name: 'report_rechargeandwithdrawallist',
        path: '/report/rechargeandwithdrawallist',
        component: 'view.report_rechargeandwithdrawallist',
        meta: {
          title: 'report_rechargeandwithdrawallist',
          i18nKey: 'route.report_rechargeandwithdrawallist'
        }
      },
      {
        name: 'report_sublevelusers',
        path: '/report/sublevelusers',
        component: 'view.report_sublevelusers',
        meta: {
          title: 'report_sublevelusers',
          i18nKey: 'route.report_sublevelusers',
          hideInMenu: true
        }
      },
      {
        name: 'report_useraccountdynamics',
        path: '/report/useraccountdynamics',
        component: 'view.report_useraccountdynamics',
        meta: {
          title: 'report_useraccountdynamics',
          i18nKey: 'route.report_useraccountdynamics'
        }
      },
      {
        name: 'report_userinvitationstatistics',
        path: '/report/userinvitationstatistics',
        component: 'view.report_userinvitationstatistics',
        meta: {
          title: 'report_userinvitationstatistics',
          i18nKey: 'route.report_userinvitationstatistics'
        }
      }
    ]
  }
];

//自己定义的
export const myRoutes: GeneratedRoute[] = [
  {
    name: 'game',
    path: '/game',
    component: 'layout.base',
    meta: {
      title: '游戏管理',
      i18nKey: '游戏管理',
      order: 2,
      id: 1
    },
    children: [
      {
        name: 'game_product',
        path: '/game/product',
        component: 'view.game_product',
        meta: {
          title: '产品管理',
          i18nKey: '产品管理',
          icon: 'ic:round-manage-accounts',
          order: 1,
          parentId: 1,
          id: 1
        }
      },
      {
        name: 'game_manufacturer',
        path: '/game/manufacturer',
        component: 'view.game_manufacturer',
        meta: {
          title: '厂家管理',
          i18nKey: '厂家管理',
          icon: 'ic:round-manage-accounts',
          order: 2,
          parentId: 1,
          id: 2
        }
      },
      {
        name: 'game_order',
        path: '/game/order',
        component: 'view.game_order',
        meta: {
          title: '订单管理',
          i18nKey: '订单管理',
          hideInMenu: true,
          order: 3,
          parentId: 1,
          id: 3
        }
      }
    ]
  },
  {
    name: 'manage',
    path: '/manage',
    component: 'layout.base',
    meta: {
      title: 'manage',
      i18nKey: 'route.manage',
      icon: 'carbon:cloud-service-management',
      order: 3,
      id: 2
    },
    children: [
      {
        name: 'manage_user',
        path: '/manage/user',
        component: 'view.manage_user',
        meta: {
          title: '管理员列表',
          i18nKey: '管理员列表',
          icon: 'ic:round-manage-accounts',
          order: 1,
          parentId: 2,
          id: 1
        }
      },
      {
        name: 'manage_role',
        path: '/manage/role',
        component: 'view.manage_role',
        meta: {
          title: '角色管理',
          i18nKey: '角色管理',
          icon: 'carbon:user-role',
          order: 2,
          parentId: 2,
          id: 2
        }
      },
      {
        name: 'manage_notice',
        path: '/manage/notice',
        component: 'view.manage_notice',
        meta: {
          title: '通知中心',
          i18nKey: '通知中心',
          icon: 'carbon:notification',
          hideAgentInMenu: true,
          order: 4,
          parentId: 2,
          id: 4
        }
      },
      {
        name: 'manage_source',
        path: '/manage/source',
        component: 'view.manage_source',
        meta: {
          title: '资源管理',
          i18nKey: '资源管理',
          icon: 'carbon:notification',
          hideAgentInMenu: true,
          order: 5,
          parentId: 2,
          id: 5
        }
      },
      {
        name: 'manage_shortlink',
        path: '/manage/shortlink',
        component: 'view.manage_shortlink',
        meta: {
          title: '短链服务',
          i18nKey: '短链服务',
          icon: 'carbon:notification',
          hideAgentInMenu: true,
          order: 6,
          parentId: 2,
          id: 6
        }
      },
      {
        name: 'manage_user-detail',
        path: '/manage/user-detail/:id',
        component: 'view.manage_user-detail',
        props: true,
        meta: {
          title: 'manage_user-detail',
          i18nKey: 'route.manage_user-detail',
          hideAgentInMenu: true,
          hideInMenu: true,
          activeMenu: 'manage_user'
        }
      },
      {
        name: 'manage_msg',
        path: '/manage/msg',
        component: 'view.manage_msg',
        meta: {
          title: '短信管理',
          i18nKey: '短信管理',
          icon: 'carbon:notification',
          hideAgentInMenu: true,
          order: 7,
          parentId: 2,
          id: 7
        }
      }
    ]
  },
  {
    name: 'account',
    path: '/account',
    component: 'layout.base',
    meta: {
      title: '账户管理',
      i18nKey: '账户管理',
      id: 3,
      order: 3
    },
    children: [
      {
        name: 'account_accountlist',
        path: '/account/accountlist',
        component: 'view.account_accountlist',
        meta: {
          order: 1,
          parentId: 3,
          id: 1,
          title: '用户管理',
          i18nKey: '用户管理'
        }
      },
      {
        name: 'account_operationlog',
        path: '/account/operationlog',
        component: 'view.account_operationlog',
        meta: {
          order: 2,
          parentId: 3,
          id: 2,
          title: '操作日志',
          i18nKey: '操作日志'
        }
      },
      {
        name: 'account_banklist',
        path: '/account/banklist',
        component: 'view.account_banklist',
        meta: {
          order: 2,
          parentId: 3,
          id: 3,
          title: '银行卡列表',
          i18nKey: '银行卡列表'
        }
      }
    ]
  },
  {
    name: 'agent',
    path: '/agent',
    component: 'layout.base',
    meta: {
      title: '代理管理',
      i18nKey: '代理管理',
      id: 5,
      order: 3
    },
    children: [
      {
        name: 'agent_manage',
        path: '/agent/manage',
        component: 'view.agent_manage',
        meta: {
          order: 1,
          parentId: 5,
          id: 1,
          title: '代理列表',
          i18nKey: '代理列表'
        }
      },
      {
        name: 'agent_billmsg',
        path: '/agent/billmsg',
        component: 'view.agent_billmsg',
        meta: {
          order: 2,
          parentId: 5,
          id: 2,
          hideInMenu: true,
          title: '账单列表',
          i18nKey: '账单列表'
        }
      },
      {
        name: 'agent_agentdata',
        path: '/agent/agentdata',
        component: 'view.agent_agentdata',
        meta: {
          order: 3,
          parentId: 5,
          id: 3,
          title: '代理数据',
          i18nKey: '代理数据'
        }
      },
      {
        name: 'agent_merchantmanagement',
        path: '/agent/merchantmanagement',
        component: 'view.agent_merchantmanagement',
        meta: {
          order: 4,
          parentId: 5,
          id: 4,
          hideManageInMenu: true,
          title: '商家管理',
          i18nKey: '商家管理'
        }
      }
    ]
  },
  {
    name: 'activities',
    path: '/activities',
    component: 'layout.base',
    meta: {
      title: 'activities',
      i18nKey: 'route.activities',
      id: 4,
      order: 3
    },
    children: [
      {
        name: 'activities_checkin',
        path: '/activities/checkin',
        component: 'view.activities_checkin',
        meta: {
          title: 'activities_checkin',
          i18nKey: 'route.activities_checkin',
          id: 1,
          parentId: 4
        }
      },
      {
        name: 'activities_exchangecode',
        path: '/activities/exchangecode',
        component: 'view.activities_exchangecode',
        meta: {
          title: 'activities_exchangecode',
          i18nKey: 'route.activities_exchangecode',
          id: 2,
          parentId: 4
        }
      },
      {
        name: 'activities_invitation',
        path: '/activities/invitation',
        component: 'view.activities_invitation',
        meta: {
          title: 'activities_invitation',
          i18nKey: 'route.activities_invitation',
          id: 3,
          parentId: 4
        }
      },
      {
        name: 'activities_pdd',
        path: '/activities/pdd',
        component: 'view.activities_pdd',
        meta: {
          title: 'activities_pdd',
          i18nKey: 'route.activities_pdd',
          id: 4,
          parentId: 4
        }
      },
      {
        name: 'activities_poolofprizes',
        path: '/activities/poolofprizes',
        component: 'view.activities_poolofprizes',
        meta: {
          title: 'activities_poolofprizes',
          i18nKey: 'route.activities_poolofprizes',
          id: 5,
          parentId: 4
        }
      },
      {
        name: 'activities_acviteconfig',
        path: '/activities/acviteconfig',
        component: 'view.activities_acviteconfig',
        meta: {
          hideInMenu: true,
          title: '活动配置',
          i18nKey: '活动配置',
          id: 6,
          parentId: 4
        }
      }
    ]
  },
  {
    name: 'report',
    path: '/report',
    component: 'layout.base',
    meta: {
      title: '报表',
      i18nKey: '报表',
      id: 6,
      order: 7
    },
    children: [
      // {
      //   name: 'report_commissionstatisticsforcoding',
      //   path: '/report/commissionstatisticsforcoding',
      //   component: 'view.report_commissionstatisticsforcoding',
      //   meta: {
      //     title: '打码佣金统计',
      //     i18nKey: '打码佣金统计',
      //     id: 1,
      //     order: 6,
      //     parentId: 6
      //   }
      // },
      {
        name: 'report_realtimedatacomparison',
        path: '/report/realtimedatacomparison',
        component: 'view.report_realtimedatacomparison',
        meta: {
          title: '实时数据对比',
          i18nKey: '实时数据对比',
          hideInMenu: true,
          id: 3,
          order: 1,
          parentId: 6
        }
      },
      {
        name: 'report_rechargeandwithdrawallist',
        path: '/report/rechargeandwithdrawallist',
        component: 'view.report_rechargeandwithdrawallist',
        meta: {
          title: '充值提现榜单',
          i18nKey: '充值提现榜单',
          id: 4,
          order: 2,
          parentId: 6
        }
      },
      {
        name: 'report_useraccountdynamics',
        path: '/report/useraccountdynamics',
        component: 'view.report_useraccountdynamics',
        meta: {
          title: '用户账户动态',
          i18nKey: '用户账户动态',
          hideInMenu: true,
          id: 5,
          order: 3,
          parentId: 6
        }
      },
      {
        name: 'report_userinvitationstatistics',
        path: '/report/userinvitationstatistics',
        component: 'view.report_userinvitationstatistics',
        meta: {
          title: '用户邀请统计',
          i18nKey: '用户邀请统计',
          id: 6,
          order: 4,
          parentId: 6
        }
      },
      {
        name: 'report_sublevelusers',
        path: '/report/subLevelUsers',
        component: 'view.report_sublevelusers',
        meta: {
          title: '邀请详情',
          i18nKey: '邀请详情',
          hideInMenu: true,
          activeMenu: 'report_userinvitationstatistics'
        }
      },
      {
        name: 'report_gamelist',
        path: '/report/gamelist',
        component: 'view.report_gamelist',
        meta: {
          title: '游戏榜单列表',
          i18nKey: '游戏榜单列表',
          id: 7,
          order: 5,
          parentId: 6
        }
      },
      {
        name: 'report_invitationrewards',
        path: '/report/invitationrewards',
        component: 'view.report_invitationrewards',
        meta: {
          title: '邀请奖励',
          i18nKey: '邀请奖励',
          id: 8,
          order: 6,
          parentId: 6
        }
      }
    ]
  },
  {
    name: 'finance',
    path: '/finance',
    component: 'layout.base',
    meta: {
      title: '财务管理',
      i18nKey: '财务管理',
      id: 7,
      order: 8
    },
    children: [
      {
        name: 'finance_paymentchannel',
        path: '/finance/paymentchannel',
        component: 'view.finance_paymentchannel',
        meta: {
          title: '支付通道管理',
          i18nKey: '支付通道管理',
          id: 1,
          order: 1,
          hideAgentInMenu: true,
          parentId: 7
        }
      },
      {
        name: 'finance_rechargepackage',
        path: '/finance/rechargepackage',
        component: 'view.finance_rechargepackage',
        meta: {
          title: '充值套餐管理',
          i18nKey: '充值套餐管理',
          id: 2,
          hideAgentInMenu: true,
          order: 2,
          parentId: 7
        }
      },
      {
        name: 'finance_withdrawalmanagement',
        path: '/finance/withdrawalmanagement',
        component: 'view.finance_withdrawalmanagement',
        meta: {
          title: '提现管理',
          i18nKey: '提现管理',
          id: 3,
          order: 3,
          parentId: 7,
          hideInMenu: true
        }
      },
      {
        name: 'finance_withdrawalapplylist',
        path: '/finance/withdrawalapplylist',
        component: 'view.finance_withdrawalapplylist',
        meta: {
          title: '提现申请列表',
          i18nKey: '提现申请列表',
          id: 4,
          order: 4,
          parentId: 7
        }
      },
      {
        name: 'finance_giftmoney',
        path: '/finance/giftmoney',
        component: 'view.finance_giftmoney',
        meta: {
          order: 5,
          parentId: 7,
          id: 5,
          title: '赠金管理',
          i18nKey: '赠金管理'
        }
      },
      {
        name: 'finance_rechargemsg',
        path: '/finance/rechargemsg',
        component: 'view.finance_rechargemsg',
        meta: {
          order: 5,
          parentId: 7,
          id: 6,
          title: '充值管理',
          i18nKey: '充值管理'
        }
      },
      {
        name: 'finance_withdrawalmsg',
        path: '/finance/withdrawalmsg',
        component: 'view.finance_withdrawalmsg',
        meta: {
          order: 6,
          parentId: 7,
          id: 7,
          title: '提现管理',
          i18nKey: '提现管理'
        }
      }
    ]
  },
  {
    name: 'operation',
    path: '/operation',
    component: 'layout.base',
    meta: {
      title: '运营管理',
      i18nKey: '运营管理',
      id: 8,
      order: 9
    },
    children: [
      {
        name: 'operation_activityalertmanagement',
        path: '/operation/activityalertmanagement',
        component: 'view.operation_activityalertmanagement',
        meta: {
          title: '活动弹框管理',
          i18nKey: '活动弹框管理',
          id: 1,
          order: 1,
          parentId: 8
        }
      },
      {
        name: 'operation_viplevelmanagement',
        path: '/operation/viplevelmanagement',
        component: 'view.operation_viplevelmanagement',
        meta: {
          title: 'VIP等级管理',
          i18nKey: 'VIP等级管理',
          id: 2,
          order: 2,
          parentId: 8
        }
      },
      {
        name: 'operation_customerfaqmanagement',
        path: '/operation/customerfaqmanagement',
        component: 'view.operation_customerfaqmanagement',
        meta: {
          title: '客服问题管理',
          i18nKey: '客服问题管理',
          id: 3,
          order: 3,
          parentId: 8
        }
      },
      {
        name: 'operation_telegrammanagement',
        path: '/operation/telegrammanagement',
        component: 'view.operation_telegrammanagement',
        meta: {
          title: '客服管理',
          i18nKey: '客服管理',
          id: 4,
          order: 4,
          parentId: 8
        }
      },
      {
        name: 'operation_policymanagement',
        path: '/operation/policymanagement',
        component: 'view.operation_policymanagement',
        meta: {
          title: '策略管理',
          i18nKey: '策略管理',
          id: 5,
          order: 4,
          parentId: 8
        }
      }
    ]
  },
  {
    name: 'channel',
    path: '/channel',
    component: 'layout.base',
    meta: {
      title: '渠道分享归因',
      i18nKey: '渠道分享归因',
      id: 9,
      order: 10
    },
    children: [
      {
        name: 'channel_channellist',
        path: '/channel/channellist',
        component: 'view.channel_channellist',
        meta: {
          title: '渠道列表',
          i18nKey: '渠道列表',
          id: 1,
          order: 1,
          parentId: 9
        }
      }
    ]
  }
];
