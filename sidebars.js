/**
 * Creating a sidebar enables you to:
 - create an ordered group of docs
 - render a sidebar for each doc of that group
 - provide next/previous navigation

 The sidebars can be generated from the filesystem, or explicitly defined here.

 Create as many sidebars as you want.
 */

// @ts-check

// /** @type {import('@docusaurus/plugin-content-docs').SidebarsConfig} */
// const sidebars = {
//   // By default, Docusaurus generates a sidebar from the docs folder structure
//   tutorialSidebar: [{type: 'autogenerated', dirName: '.'}],

//   // But you can create a sidebar manually
//   /*
//   tutorialSidebar: [
//     {
//       type: 'category',
//       label: 'Tutorial',
//       items: ['hello'],
//     },
//   ],
//    */
// };

// module.exports = sidebars;

module.exports = {
  // But you can create a sidebar manually

  docs: [
    {
      type: "category",
      collapsed: false,
      label: "UniPass Contract",
      items: [
        {
          type: "autogenerated",
          dirName: "contract",
        },
      ],
    },
    {
      type: "category",
      collapsed: false,
      label: "UniPass Wallet",
      items: [
        {
          type: "autogenerated",
          dirName: "wallet",
        },
      ],
    },
    // {
    //   type: "category",
    //   collapsed: false,
    //   label: "Introduction",
    //   items: [
    //     {
    //       type: "autogenerated",
    //       dirName: "introduction",
    //     },
    //   ],
    // },
    // {
    //   type: "category",
    //   collapsed: false,
    //   label: "Architecture",
    //   items: [
    //     {
    //       type: "autogenerated",
    //       dirName: "architecture",
    //     },
    //   ],
    // },
    "faq/faq",
    "audits/overview",
    "community/overview",
    {
      type: "category",
      collapsed: true,
      label: "Terms",
      items: [
        {
          type: "autogenerated",
          dirName: "terms",
        },
      ],
    },
  ],
  sdk: [
    {
      type: "category",
      collapsed: false,
      label: "Custom Auth SDK",
      items: [
        "custom-auth/introduction",
        {
          type: "category",
          collapsed: false,
          label: "Web SDK",
          items: [
            {
              type: "autogenerated",
              dirName: "custom-auth/web-sdk",
            },
          ],
        },
        {
          type: "category",
          collapsed: true,
          label: "Android SDK",
          items: [
            {
              type: "autogenerated",
              dirName: "custom-auth/android-sdk",
            },
          ],
        },
        {
          type: "category",
          collapsed: true,
          label: "iOS SDK",
          items: [
            {
              type: "autogenerated",
              dirName: "custom-auth/ios-sdk",
            },
          ],
        },
      ],
    },
    {
      type: "category",
      collapsed: true,
      label: "Plug & Play SDK",
      items: [
        "develop/introduction",
        {
          type: "category",
          collapsed: true,
          label: "Popup SDK",
          items: [
            {
              type: "autogenerated",
              dirName: "develop/popup-sdk",
            },
          ],
        },
        {
          type: "category",
          collapsed: true,
          label: "Android SDK",
          items: [
            {
              type: "autogenerated",
              dirName: "develop/android-sdk",
            },
          ],
        },
        {
          type: "category",
          collapsed: true,
          label: "iOS SDK",
          items: [
            {
              type: "autogenerated",
              dirName: "develop/ios-sdk",
            },
          ],
        },
        {
          type: "category",
          collapsed: true,
          label: "React Native SDK",
          items: [
            {
              type: "autogenerated",
              dirName: "develop/rn-sdk",
            },
          ],
        },
        {
          type: "category",
          collapsed: true,
          label: "Wallet Connectors",
          items: [
            {
              type: "autogenerated",
              dirName: "develop/wallet-connector",
            },
          ],
        },
        {
          type: "category",
          collapsed: true,
          label: "Flutter SDK (Legacy)",
          items: [
            {
              type: "autogenerated",
              dirName: "develop/flutter-sdk",
            },
          ],
        },
        {
          type: "category",
          collapsed: true,
          label: "Unity SDK (Legacy)",
          items: [
            {
              type: "autogenerated",
              dirName: "develop/unity-sdk",
            },
          ],
        },
        {
          type: "category",
          collapsed: true,
          label: "Unreal SDK (Legacy)",
          items: [
            {
              type: "autogenerated",
              dirName: "develop/unreal-sdk",
            },
          ],
        },
        {
          type: "category",
          collapsed: true,
          label: "Verify Message",
          items: [
            {
              type: "autogenerated",
              dirName: "develop/verifying-messages",
            },
          ],
        },
      ],
    },
  ],
};
