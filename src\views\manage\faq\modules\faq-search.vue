<script setup lang="ts">
import {ref} from 'vue';
import {$t} from '@/locales';

interface Props {
  model: Api.SystemManage.FAQSearchParams;
}

interface Emits {
  (e: 'search'): void;
  (e: 'reset'): void;
}

const activeName = ref(['faq-search']);
const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref(null);

function handleSearch() {
  emit('search');
}

function handleReset() {
  formRef.value?.resetFields();
  emit('reset');
}
</script>

<template>
  <div class="search-wrapper">
    <ElForm ref="formRef" :model="model">
      <ElRow :gutter="16">
        <ElCol :span="6">
          <ElFormItem label="提问" prop="keyword">
            <ElInput v-model="model.keyword" placeholder="请输入提问内容" clearable @keyup.enter="handleSearch"/>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>
