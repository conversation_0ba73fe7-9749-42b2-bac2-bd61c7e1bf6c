<template>
  <v-container class="my-bonus-container pa-4" max-width="940">
    <!-- 兑换码组件 -->
    <ExchangeCode class="mb-4" />

    <!-- 提示文本 -->
    <div class="ranking-tip mb-4">
      <img src="@/assets/images/home-msg-icon.png" class="tip-icon" alt="Tip" />
      <span
        >Lembrete: O saldo da conta sera alcancado dentro de 5 minutos apos o
        recebimento</span
      >
    </div>

    <!-- 加载状态 -->
    <v-progress-circular
      v-if="loading"
      indeterminate
      color="primary"
      class="d-flex justify-center w-100 my-4"
    ></v-progress-circular>

    <!-- 排行榜列表 -->
    <v-table v-else class="ranking-table">
      <thead>
        <tr>
          <th width="">Nome da atividade</th>
          <th>Hora do prêmio</th>
          <th>Valor do bônus</th>
        </tr>
      </thead>
      <tbody>
        <tr v-if="!redemptionRecords.length">
          <td colspan="4" class="text-center pa-4" style="color: grey">
            Nenhum registro encontrado
          </td>
        </tr>
        <tr v-for="item in redemptionRecords" :key="item.reward_time">
          <td>{{ item.activity_name }}</td>
          <td>{{ formatDateTime(getBrazilDate(item.reward_time)) }}</td>
          <td class="amount">{{ formatNumber(item.reward_amount / 100) }}</td>
        </tr>
      </tbody>
    </v-table>

    <div class="text-center mt-4" v-if="count > redemptionRecords.length">
      <v-btn
        class="view-more-btn mb-8"
        :loading="loading"
        @click="handleCollectAll"
      >
        Coleta com um clique
      </v-btn>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import { getRedemptionRecords } from "@/api/redemption";
import type { RedemptionRecordItem } from "@/api/redemption";
import { showError } from "@/utils/toast";
import { getBrazilDate, formatDateTime } from "@/utils/format";

const redemptionRecords = ref<RedemptionRecordItem[]>([]);
const loading = ref(false);
const count = ref(0);
// Function to map status code to text
const getStatusText = (status: number) => {
  switch (status) {
    case 0:
      return "Pendente"; // Assuming 0 is Pending
    case 1:
      return "Recebido"; // Assuming 1 is Collected
    default:
      return "Desconhecido";
  }
};
// 格式化数字
const formatNumber = (num: number) => {
  const parts = num.toFixed(2).split(".");
  const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  const result = integerPart + "." + parts[1];
  return result;
};
const state = reactive({
  query: {
    page: 1,
    size: 10,
  },
});
// Load redemption records
const loadRedemptionRecords = async () => {
  try {
    loading.value = true;
    const response = await getRedemptionRecords(state.query);
    console.log(response);
    // Ensure response.data is an array before assigning
    count.value = response?.count || 0;
    redemptionRecords.value = Array.isArray(response?.data)
      ? [...redemptionRecords.value, ...response.data]
      : [];
  } catch (error: any) {
    showError(error.message || "Falha ao carregar registros de resgate");
    redemptionRecords.value = []; // Clear on error
  } finally {
    loading.value = false;
  }
};

// Handle one-click collection (placeholder)
const handleCollectAll = () => {
  state.query.page++;
  loadRedemptionRecords();
  // showError("Função de coleta com um clique ainda não implementada");
  // TODO: Implement API call for collecting all bonuses
};

onMounted(() => {
  console.log(4123543532);
  loadRedemptionRecords();
});
</script>

<style lang="scss" scoped>
.my-bonus-container {
  height: auto;
  .ranking-tip {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px;
    background: #222b54;
    border-radius: 8px;
    .tip-icon {
      width: 20px;
      height: 20px;
    }

    span {
      font-size: 14px;
      color: #fff;
    }
  }
  .ranking-table {
    background: #1b193d !important;
    margin-bottom: 16px;
    border-radius: 0 !important;
    :deep(th) {
      color: rgba(255, 255, 255) !important;
      background: linear-gradient(180deg, #1b1f2d, #3c4155);
      font-size: 14px;
      font-weight: normal;
      text-transform: capitalize;
      text-align: left; // Ensure header text is aligned left
      padding: 12px 16px; // Add padding
    }
    tr:nth-child(even) {
      background: #2b324d;
    }
    :deep(td) {
      color: #fff !important;
      font-size: 14px;
      text-align: left;
      padding: 12px 16px; // Match header padding
    }

    .amount {
      color: #ffdf00 !important;
      font-weight: 500;
    }

    .bonus {
      color: #ffdf00 !important;
    }
  }
  .view-more-btn {
    background: linear-gradient(0deg, #a13351 0%, #f975a6 100%);
    border-radius: 22px;
    min-width: 320px;
    color: #fff !important;
    height: 44px;
    font-size: 18px;
    text-transform: none;
  }
}
</style>
