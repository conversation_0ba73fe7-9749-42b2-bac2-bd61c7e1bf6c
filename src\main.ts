/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-19 10:47:30
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-06-20 15:06:08
 * @FilePath: \betdoce-admind:\new_project\betdoce-web\src\main.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import vuetify from "./plugins/vuetify";
import DeviceUtil from "@/utils/device"
import Toast from "./components/common/Toast.vue";


// 创建 Vue 实例
const app = createApp(App);

// 错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error("Vue Error:", err);
  console.error("Error Info:", info);
};

// 警告处理
app.config.warnHandler = (msg, vm, trace) => {
  console.warn("Vue Warning:", msg);
  console.warn("Trace:", trace);
};

// 性能追踪
if (process.env.NODE_ENV === "development") {
  app.config.performance = true;
}

// 全局属性
app.config.globalProperties.$filters = {
  // 金额格式化
  currency(value: number): string {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  },
  // 日期格式化
  date(value: string | number | Date): string {
    return new Intl.DateTimeFormat("pt-BR").format(new Date(value));
  },
};
app.config.globalProperties.$DeviceUtil  = DeviceUtil
// 使用 provide/inject 机制注入 store
app.use(store);
app.use(router);
app.use(vuetify);
app.component("Toast", Toast);

// 挂载应用
app.mount("#app");

// 导出 app 实例（以便在其他地方使用）
export default app;

// 在 main.ts 或 App.vue 中添加
router.beforeEach((to, from, next) => {
  // 清理可能的内存泄漏
  if (from.name) {
    // 清理定时器
    const highestTimeoutId = window.setTimeout(() => {}, 0);
    for (let i = 0; i < highestTimeoutId; i++) {
      window.clearTimeout(i);
    }
  }
  next();
});
