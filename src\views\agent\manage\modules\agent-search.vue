<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-06-13 18:03:33
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\views\agent\manage\modules\agent-search.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <ElCard class="card-wrapper">
    <!-- <ElCollapse v-model="activeName">
      <ElCollapseItem :title="$t('common.search')" name="agent-search">
       
      </ElCollapseItem>
    </ElCollapse> -->
    <ElForm ref="formRef" :model="model" label-width="80px">
      <ElRow :gutter="20">
        <ElCol :span="8">
          <ElFormItem label="名称">
            <ElInput
              v-model="model.agent_name"
              placeholder="请输入代理商名称"
              clearable
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <ElFormItem label="状态">
            <ElSelect v-model="model.status" placeholder="请选择状态" clearable>
              <ElOption label="启用" :value="1" />
              <ElOption label="禁用" :value="2" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <ElFormItem>
            <div class="flex justify-end">
              <ElButton @click="handleReset">
                <template #icon>
                  <icon-ic-round-refresh class="text-icon" />
                </template>
                {{ $t("common.reset") }}
              </ElButton>
              <ElButton type="primary" plain @click="handleSearch">
                <template #icon>
                  <icon-ic-round-search class="text-icon" />
                </template>
                {{ $t("common.search") }}
              </ElButton>
            </div>
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
  </ElCard>
</template>

<script setup lang="ts">
import { ref } from "vue";
import {
  ElCard,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElRow,
  ElCol,
} from "element-plus";

interface Props {
  model: Record<string, any>;
}

interface Emits {
  (e: "search"): void;
  (e: "reset"): void;
}

const activeName = ref("agent-search");
const formRef = ref();

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

function handleSearch() {
  emit("search");
}

function handleReset() {
  formRef.value?.resetFields();
  emit("reset");
}
</script>

<style scoped lang="scss">
:deep(.el-form-item) {
  margin-bottom: 0;
}
</style>
