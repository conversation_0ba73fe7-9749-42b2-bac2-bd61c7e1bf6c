<script setup lang="ts">
import { ref,  computed, watch } from 'vue'
import { useStore } from 'vuex'
import { showSuccess, showError } from '@/utils/toast'
import { updateUserProfile } from '@/api/auth'
import AvatarDialog from '@/components/AvatarDialog.vue'
import { useRouter } from 'vue-router'
import EmailDialog from '@/components/EmailDialog.vue'

const store = useStore()
const router = useRouter()

// 从 store 获取用户信息
const userInfo = computed(() => store.state.auth?.user || {})

// 表单数据
const formData = ref({
  id: userInfo.value.id || 0,
  nickname: userInfo.value.nickname || '',
  email: userInfo.value.email || '',
  phone: userInfo.value.phone || '',
  avatar: userInfo.value.avatar || ''
})
// 加载状态
const loading = ref(false)
// 控制头像选择弹窗
const showAvatarDialog = ref(false)
// 控制邮箱编辑弹窗
const showEmailDialog = ref(false)

// 提交表单
const handleSubmit = async () => {
  try {
    loading.value = true
    console.log(formData.value,2134214)
    await updateUserProfile(formData.value)
    showSuccess('Informações atualizadas com sucesso')
    // 更新 store 中的用户信息
    await store.dispatch('auth/updateUserInfo', formData.value)
  } catch (error) {
    showError('Falha ao atualizar informações')
  } finally {
    loading.value = false
  }
}

// 处理编辑点击
const handleEdit = (field: string) => {
  if (field === 'email') {
    showEmailDialog.value = true
  }
}

// 处理邮箱更新
const handleEmailUpdate = async (email: string, code: string) => {
  try {
    loading.value = true
    formData.value.email = email
    await updateUserProfile(formData.value)
    showSuccess('E-mail atualizado com sucesso')
    showEmailDialog.value = false
    // 更新 store 中的用户信息
    await store.dispatch('auth/updateUserInfo', formData.value)
  } catch (error) {
    showError('Falha ao atualizar e-mail')
  } finally {
    loading.value = false
  }
}

// 处理隐私政策点击
const handlePrivacyPolicy = () => {
  router.push('/privacy-policy')
}

// 处理头像选择与昵称修改
const handleAvatarSelect = (data:{avatar:string,nickname:string})=>{
   formData.value.avatar = data.avatar
   formData.value.nickname = data.nickname
   showAvatarDialog.value = false
   handleSubmit()
}

// 监听 store 中的用户信息变化
watch(() => store.state.auth?.user, (newUser) => {
  if (newUser) {
    formData.value = {
      id:newUser.id || null,
      nickname: newUser.nickname || '',
      email: newUser.email || '',
      phone: newUser.phone || '',
      avatar: newUser.avatar || ''
    }
  }
}, { immediate: true })
</script>

<template>
  <div class="profile-container">
    <!-- 系统公告 -->
    <notice-list />
    
    <div class="avatar-section" >
      <v-avatar size="80" class="mb-2"  @click="showAvatarDialog = true">
        <v-img :src="formData.avatar || '/default-avatar.png'" />
      </v-avatar>
      <div class="edit-avatar"  @click="showAvatarDialog = true">
        <v-icon>mdi-pencil</v-icon>
        Editar Avatar
      </div>
    </div>

    <div class="info-list">
      <!-- 昵称 -->
      <div class="info-item" @click="showAvatarDialog = true">
        <div class="label">Apelido</div>
        <div class="value">
          <span>{{ formData.nickname || 'Não definido' }}</span>
          <v-icon>mdi-chevron-right</v-icon>
        </div>
      </div>

      <!-- 邮箱 -->
      <div class="info-item" @click="handleEdit('email')">
        <div class="label">E-mail</div>
        <div class="value">
          <span>{{ formData.email || 'Não definido' }}</span>
          <v-icon>mdi-chevron-right</v-icon>
        </div>
      </div>

      <!-- 电话 -->
      <div class="info-item" @click="handleEdit('phone')">
        <div class="label">Número de telefone</div>
        <div class="value">
          <span>{{ formData.phone || 'Não definido' }}</span>
          <v-icon>mdi-chevron-right</v-icon>
        </div>
      </div>

      <!-- 隐私政策 -->
      <div class="info-item" @click="handlePrivacyPolicy">
        <div class="label">Política de Privacidade</div>
        <div class="value">
          <v-icon>mdi-chevron-right</v-icon>
        </div>
      </div>

      <!-- 版本信息 -->
      <div class="info-item">
        <div class="label">Verifique actualizações</div>
        <div class="value">
          <span>V1.0.0</span>
        </div>
      </div>
    </div>

    <!-- 头像选择弹窗 -->
    <avatar-dialog
      :model-value="showAvatarDialog"
      @update="handleAvatarSelect"
      @update:modelValue="showAvatarDialog = $event"
      :current-avatar="formData.avatar"
      :current-nickname="formData.nickname"
      @select="handleAvatarSelect"
    />

    <!-- 邮箱编辑弹窗 -->
    <email-dialog
      v-if="showEmailDialog"
      :model-value="showEmailDialog"
      @update:model-value="showEmailDialog = $event"
      :current-email="formData.email"
      @save="handleEmailUpdate"
    />
  </div>
</template>

<style scoped lang="scss">
.profile-container {
  min-height: 100vh;
  padding: 20px;
  max-width: 940px;
  margin: 0 auto;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;

  .v-avatar {
    border: 2px solid #FFDF00;
    margin-bottom: 10px;
  }

  .edit-avatar {
    color: #fff;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;

    .v-icon {
      font-size: 16px;
    }
  }
}

.info-list {
  background: #202D60;
  border-radius: 12px;
  overflow: hidden;

  .info-item {
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;

    &:last-child {
      border-bottom: none;
    }

    .label {
      color: #fff;
      font-size: 14px;
    }

    .value {
      display: flex;
      align-items: center;
      gap: 5px;
      color: rgba(255, 255, 255, 0.6);
      font-size: 14px;

      .v-icon {
        font-size: 20px;
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }
}
</style> 