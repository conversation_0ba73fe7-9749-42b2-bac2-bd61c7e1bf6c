<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-26 14:22:15
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-07-21 10:45:22
 * @FilePath: \nb-h5-game-webd:\new_project\betdoce-web\src\components\RuleDialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <v-dialog
    v-model="visibleProxy"
    @click:outside="close"
    class="rule-dialog-mask"
  >
    <v-card class="rule-dialog-box">
      <div class="rule-dialog-header">
        <span class="rule-dialog-title">{{ title }}</span>
        <v-btn
          icon="mdi-close"
          variant="text"
          size="small"
          class="rule-dialog-close"
          @click="close"
        ></v-btn>
      </div>
      <div class="rule-dialog-content">
        <slot>
          <v-textarea
            :model-value="content"
            readonly
            variant="outlined"
            hide-details
            auto-grow
            rows="5"
            class="rule-textarea"
            :class="{ 'rule-textarea-readonly': true }"
          ></v-textarea>
        </slot>
      </div>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { computed } from "vue";

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    default: "规则说明",
  },
  content: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["update:visible"]);

// v-model 兼容
const visibleProxy = computed({
  get: () => props.visible,
  set: (v) => emit("update:visible", v),
});

function close() {
  emit("update:visible", false);
}
</script>

<style lang="scss" scoped>
:deep(.v-card) {
  border-radius: 20px !important;
}
:deep(.v-overlay__content) {
  min-width: 320px;
  max-width: 90vw;
}
.rule-dialog-mask {
  /* v-dialog 外层遮罩样式 */
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.rule-dialog-box {
  background: #fff7e0;
  border-radius: 40px;
  min-width: 320px;
  max-width: 90vw;
  padding: 24px 20px 20px 20px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
  position: relative;
}
.rule-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.rule-dialog-title {
  font-size: 20px;
  font-weight: bold;
  color: #e89a3c;
}
.rule-dialog-close {
  font-size: 20px;
  color: #e89a3c;
  cursor: pointer;
  user-select: none;
}
.rule-dialog-content {
  font-size: 16px;
  color: #e89a3c;
  line-height: 1.8;
  max-height: 50vh;
  overflow-y: auto;
  word-break: break-all;
}

.rule-textarea {
  font-size: 16px;
  color: #e89a3c;
  line-height: 1.8;
}

.rule-textarea-readonly {
  background: transparent !important;
}

.rule-textarea-readonly :deep(.v-field) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.rule-textarea-readonly :deep(.v-field__input) {
  /* padding: 0 !important; */
  min-height: auto !important;
  color: #e89a3c !important;
  font-size: 16px !important;
  line-height: 1.8 !important;
}

.rule-textarea-readonly :deep(.v-field__outline) {
  display: none !important;
}
</style>
