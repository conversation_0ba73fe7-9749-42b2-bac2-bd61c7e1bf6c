<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue";
import { enableStatusOptions } from "@/constants/business";
import {
  fetchAddRechargePackage,
  fetchUpdateRechargePackage,
  fetchGetRechargePackageDetail,
} from "@/service/api";

interface Props {
  /** the type of operation */
  operateType: UI.TableOperateType;
  /** the edit row data */
  rowData?: any | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: "submitted"): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>("visible", {
  default: false,
});

const title = computed(() => {
  const titles: Record<UI.TableOperateType, string> = {
    add: "新增充值套餐",
    edit: "编辑充值套餐",
  };
  return titles[props.operateType];
});

const amountType = ref(0);

const formRef = ref();
const formData = reactive({
  recharge_package_name: "",
  recharge_amount: "",
  bonus_amount: 0,
  cash_amount: 0,
  bet_cash_amount: 0,
  one_month: "",
  three_months: "",
  one_year: "",
  package_description: "",
  status: 1,
});

const rules = {
  recharge_package_name: [
    { required: true, message: "请输入套餐名称", trigger: "blur" },
  ],
  recharge_amount: [
    { required: true, message: "请输入充值金额", trigger: "blur" },
  ],
  cash_amount: [{ required: true, message: "请输入赠送金额", trigger: "blur" }],
  one_month: [
    { required: true, message: "购买次数限制 (一个月)", trigger: "blur" },
  ],
  three_months: [
    { required: true, message: "购买次数限制 (三个月)", trigger: "blur" },
  ],
  one_year: [
    { required: true, message: "购买次数限制 (一年)", trigger: "blur" },
  ],
  package_description: [
    { required: true, message: "请输入套餐描述", trigger: "blur" },
  ],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
};

async function fetchDetail() {
  if (!props.rowData?.id) return;
  try {
    const res = await fetchGetRechargePackageDetail({
      id: props?.rowData?.id,
    });
    if (res.data) {
      Object.assign(formData, res.data);
    }
  } catch (error) {
    window.$message?.error("获取详情失败");
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    if (!formData.cash_amount) {
      window.$message?.warning("套餐赠送优惠金额不能为0");
      return;
    }
    const queryParams = { ...formData };
    if (amountType.value === 1) {
      queryParams.cash_amount = queryParams.cash_amount * 100;
      queryParams.bonus_amount = 0;
      queryParams.bet_cash_amount = 0;
    } else if (amountType.value === 0) {
      queryParams.bonus_amount = queryParams.cash_amount * 100;
      queryParams.cash_amount = 0;
      queryParams.bet_cash_amount = 0;
    } else if (amountType.value === 2) {
      queryParams.bet_cash_amount = queryParams.cash_amount * 100;
      queryParams.bonus_amount = 0;
      queryParams.cash_amount = 0;
    }
    queryParams.recharge_amount = queryParams.recharge_amount * 100;
    if (props.operateType === "edit") {
      const { error } = await fetchUpdateRechargePackage({
        id: props?.rowData?.id,
        ...queryParams,
      });
      if (!error) {
        window.$message?.success("更新成功");
        closeDrawer();
        emit("submitted");
      }
    } else {
      const { error } = await fetchAddRechargePackage({ ...queryParams });
      if (!error) {
        window.$message?.success("创建成功");
        closeDrawer();
        emit("submitted");
      }
    }
  } catch (error) {
    window.$message?.error("失败");
  }
}
// 校验三个时间段内的次数限制问题
const three_month = ref(99999999999);
const one_month = ref(999999999999);
// 一月内购买次数限制
const changeThreeMounth = (e) => {
  if (e > 0) {
    one_month.value = e;
  } else {
    one_month.value = 999999999999;
  }
};
// 一月内购买次数限制
const changeOneYear = (e) => {
  if (e > 0) {
    three_month.value = e;
  } else {
    three_month.value = 999999999999;
  }
};
watch(visible, async () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
  }
});

/** 编辑时更新表单数据 */
function handleUpdateModelWhenEdit() {
  // 重置表单数据
  if (props.operateType === "edit" && props.rowData) {
    Object.assign(formData, props.rowData);
    three_month.value = props.rowData.one_year;
    one_month.value = props.rowData.three_months;
    formData.recharge_amount = props.rowData.recharge_amount / 100;
    formData.cash_amount = props.rowData.bonus_amount
      ? props.rowData.bonus_amount
      : props.rowData.cash_amount
        ? props.rowData.cash_amount
        : props.rowData.bet_cash_amount;

    formData.cash_amount = formData.cash_amount / 100;

    amountType.value = props.rowData.bonus_amount
      ? 0
      : props.rowData.cash_amount
        ? 1
        : 2;
  } else {
    Object.assign(formData, {
      recharge_package_name: "",
      recharge_amount: 0,
      bonus_amount: 0,
      cash_amount: 0,
      bet_cash_amount: 0,
      one_month: "",
      three_months: "",
      one_year: "",
      package_description: "",
      status: 1,
    });
    amountType.value = 0;
  }
}
</script>

<template>
  <ElDrawer :size="500" v-model="visible" :title="title">
    <ElForm ref="formRef" :model="formData" :rules="rules" label-position="top">
      <ElFormItem label="套餐名称" prop="recharge_package_name">
        <ElInput
          v-model="formData.recharge_package_name"
          placeholder="请输入套餐名称"
        />
      </ElFormItem>

      <ElFormItem label="充值金额" prop="recharge_amount">
        <ElInputNumber
          v-model="formData.recharge_amount"
          :min="0"
          :precision="0"
          style="width: 100%"
          placeholder="请输入充值金额"
        />
      </ElFormItem>

      <ElFormItem label="赠送类型与金额" prop="cash_amount">
        <div class="flex gap-2">
          <ElSelect
            v-model="amountType"
            style="width: 100px"
            placeholder="赠金"
          >
            <ElOption label="赠金" :value="0" />
            <ElOption label="现金" :value="1" />
            <ElOption label="打码提现" :value="2" />
          </ElSelect>
          <ElInputNumber
            v-model="formData.cash_amount"
            :min="0"
            :precision="0"
            style="flex: 1; width: 100%"
            placeholder="请输入金额"
          />
        </div>
      </ElFormItem>

      <ElFormItem required="true" label="购买次数限制" prop="three_months">
        <ElRow style="width: 100%" :gutter="20">
          <ElCol :span="8">
            <ElFormItem label="1个月内(次)" prop="one_month">
              <ElInputNumber
                v-model="formData.one_month"
                :min="0"
                :max="one_month"
                :precision="0"
                style="width: 100%"
                placeholder="请输入一个月内购买次数限制"
            /></ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="3个月内(次)" prop="three_months">
              <ElInputNumber
                v-model="formData.three_months"
                :min="formData.one_month"
                :precision="0"
                :max="three_month"
                style="width: 100%"
                @change="changeThreeMounth"
                placeholder="请输入三个月内购买次数限制"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="1年内(次)" prop="one_year">
              <ElInputNumber
                v-model="formData.one_year"
                :min="formData.three_months"
                :precision="0"
                style="width: 100%"
                @change="changeOneYear"
                placeholder="请输入一年内购买次数限制"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElFormItem>

      <ElFormItem label="套餐描述" prop="package_description">
        <ElInput
          v-model="formData.package_description"
          type="textarea"
          :rows="4"
          placeholder="请输入套餐描述"
        />
      </ElFormItem>

      <ElFormItem label="状态" prop="status">
        <ElRadioGroup v-model="formData.status">
          <ElRadio
            v-for="{ label, value } in enableStatusOptions"
            :key="value"
            :value="value"
            :label="label"
          />
        </ElRadioGroup>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="flex justify-end gap-2">
        <ElButton @click="visible = false">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit"> 确定 </ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<style scoped></style>
