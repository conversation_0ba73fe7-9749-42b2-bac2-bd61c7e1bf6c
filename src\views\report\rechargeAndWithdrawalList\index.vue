<script setup lang="ts">
import { ref, reactive } from "vue";
import {
  ElCard,
  ElForm,
  ElFormItem,
  ElButton,
  ElTable,
  ElTableColumn,
  ElDatePicker,
  ElInput,
  ElSelect,
  ElOption,
  ElPagination,
} from "element-plus";
import { getDepositWithdrawalRanking } from "@/service/api/report";
import { useTable } from "@/hooks/common/table";

interface SearchParams {
   start_time: string,
    end_time: string,
    user_id: string,
    sort_by: string,
    registration_source: string,
}

interface TransactionRecord {
  userId: string;
  rechargeAmount: number;
  withdrawalAmount: number;
  balanceDiff: number;
  rechargeCount: number;
  withdrawalCount: number;
}
const date_range = ref();
// 註冊來源選項
const registrationSources = [
  { label: "全部", value: 0 },
  { label: "平台", value: 1 },
  { label: "代理商", value: 2 },
  { label: "渠道", value: 3 },
];

// 排序類型選項
const sortTypes = [
  { label: "全部", value: "all" },
  { label: "充值金额", value: "deposit_amount" },
  { label: "提现金额", value: "withdrawal_amount" },
  { label: "充值差", value: "diff_amount" },
  { label: "充值单量", value: "deposit_count" },
  { label: "提现单量", value: "withdrawal_count" },
];

const {
  loading,
  data: tableData,
  searchParams,
  getData,
  updateSearchParams,
  resetSearchParams,
  columns,
  mobilePagination,
  getDataByPage,
} = useTable<any>({
  apiFn: getDepositWithdrawalRanking,
  apiParams: {
    page: 1,
    size: 10,
    start_time: undefined,
    end_time: undefined,
    user_id: undefined,
    sort_by: undefined,
    registration_source: undefined,
  },
  columns: () => [
    { prop: "user_id", label: "用户ID", minWidth: 120 },
    // { prop: "phone", label: "手机号", minWidth: 120 },
    // { prop: "username", label: "用户名", minWidth: 120 },
    { prop: "registration_source", label: "注册来源", minWidth: 120 },
    {
      prop: "deposit_amount",
      label: "充值金额",
      minWidth: 120,
      formatter: (row: any) =>`R$ ${(row?.deposit_amount / 100)?.toFixed(2)  || "0.00"}`,
    },
    {
      prop: "withdrawal_amount",
      label: "提现金额",
      minWidth: 120,
      formatter: (row: any) =>`R$ ${(row?.withdrawal_amount / 100)?.toFixed(2)  || "0.00"}`,
    },
    {
      prop: "diff_amount",
      label: "充值差",
      minWidth: 120,
      formatter: (row: any) =>`R$ ${(row?.diff_amount / 100)?.toFixed(2)  || "0.00"}`,
    },
    { prop: "deposit_count", label: "充值单量", minWidth: 120 },
    { prop: "withdrawal_count", label: "提现单量", minWidth: 120 },
  ],
});

// 更新搜索參數處理
const handleSearch = () => {
  getData();
};

// 重置
const handleReset = () => {
  date_range.value = undefined;
  resetSearchParams();
};

defineOptions({ name: "RechargeAndWithdrawalList" });
</script>

<template>
  <div
    class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto"
  >
    <div class="search-wrapper">
      <ElForm :model="searchParams" inline>
        <ElRow :gutter="16">
          <ElCol :span="6">
            <ElFormItem label="用户ID">
              <ElInput
                v-model="searchParams.user_id"
                placeholder="请输入用户ID"
                clearable
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem label="注册来源">
              <ElSelect
                v-model="searchParams.registration_source"
                placeholder="请选择注册来源"
                clearable
              >
                <ElOption
                  v-for="item in registrationSources"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem label="日期">
              <ElDatePicker
                v-model="date_range"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="x"
                @change="
                  (val: [string, string] | null) => {
                    if (val) {
                      searchParams.start_time = val[0];
                      searchParams.end_time = val[1];
                    } else {
                      searchParams.start_time = undefined;
                      searchParams.end_time = undefined;
                    }
                  }
                "
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem label="排序类型">
              <ElSelect v-model="searchParams.sort_by">
                <ElOption
                  v-for="item in sortTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem>
              <ElButton type="primary" @click="handleSearch">搜索</ElButton>
              <ElButton @click="handleReset">重置</ElButton>
              <ElButton @click="getData">刷新</ElButton>
            </ElFormItem>
          </ElCol>
        </ElRow>
      </ElForm>
    </div>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-50px)]">
        <ElTable v-loading="loading" height="100%" :data="tableData">
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>

        <div class="mt-20px flex justify-between items-center">
          <div class="flex gap-24px text-14px">
            <span
              >总记录数：<span class="text-primary">{{
                mobilePagination.total
              }}</span></span
            >
          </div>
          <ElPagination
            v-if="mobilePagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="mobilePagination"
            @current-change="mobilePagination['current-change']"
            @size-change="mobilePagination['size-change']"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}

.search-card {
  :deep(.el-form--inline) {
    .el-form-item {
      margin-right: 16px;
      margin-bottom: 16px;

      &:last-child {
        margin-right: 0;
      }

      .el-input,
      .el-select {
        width: 160px;
      }

      .el-date-editor {
        width: 260px;
      }
    }
  }
}

.flex-center {
  display: flex;
  align-items: center;
  gap: 8px;
}

.text-primary {
  color: var(--el-color-primary);
}
</style>
