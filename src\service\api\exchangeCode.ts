import { request } from '../request';

export interface ExchangeCodeItem {
  id: number;
  period_id: number;
  activity_period: string;
  redemption_pass: string;
  code_count: number;
  amount_type: number;
  bonus: number;
  random_odds: string;
  received_count: number;
  exchanged_count: number;
  deadline_time: string;
  create_at: string;
  create_by: number;
  update_at: string;
  update_by: number;
  is_delete: number;
  create_at_formatted: string;
  update_at_formatted: string;
}

export interface ExchangeCodeListParams extends Api.Common.CommonSearchParams {
  start_time?: string;
  end_time?: string;
}

export interface ExchangeCodeListResponse {
  list: ExchangeCodeItem[];
  total: number;
}

export interface CreateExchangeCodeParams {
  redemption_pass: string;
  code_count: number;
  amount_type: number;
  is_random_amount: number;
  random_bonus1?: number;
  random_percent1?: number;
  random_bonus2?: number;
  random_percent2?: number;
  random_bonus3?: number;
  random_percent3?: number;
  random_bonus4?: number;
  random_percent4?: number;
  random_bonus5?: number;
  random_percent5?: number;
  random_bonus6?: number;
  random_percent6?: number;
  deadline_time: number;
}

export interface UpdateExchangeCodeParams {
  redemption_code_id: number;
  redemption_pass: string;
  code_count: number;
  amount_type: number;
  is_random_amount: number;
  random_bonus1?: number;
  random_percent1?: number;
  random_bonus2?: number;
  random_percent2?: number;
  random_bonus3?: number;
  random_percent3?: number;
  random_bonus4?: number;
  random_percent4?: number;
  random_bonus5?: number;
  random_percent5?: number;
  random_bonus6?: number;
  random_percent6?: number;
  deadline_time: number;
}

/**
 * 获取兑换码列表
 */
export function fetchExchangeCodeList(params: ExchangeCodeListParams) {
  return request<ExchangeCodeListResponse>({
    url: '/backend/redemptioncode/list',
    method: 'get',
    params
  });
}

/**
 * 创建兑换码
 */
export function createExchangeCode(data: CreateExchangeCodeParams) {
  return request<any>({
    url: '/backend/redemptioncode/create',
    method: 'post',
    data
  });
}

/**
 * 更新兑换码
 */
export const updateExchangeCode = (params: UpdateExchangeCodeParams) => {
  return request<any>({
    url: '/backend/redemptioncode/update',
    method: 'post',
    data: params
  });
};

/**
 * 删除兑换码
 */
export const deleteExchangeCode = (redemption_code_id: number) => {
  return request<any>({
    url: '/backend/redemptioncode/delete',
    method: 'post',
    params: { redemption_code_id }
  });
};
