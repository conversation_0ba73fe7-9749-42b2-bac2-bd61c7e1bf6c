<template>
  <div v-if="show" class="loading-overlay">
    <div class="loading-content">
      <div ref="diceContainer" class="dice-container">
        <div ref="dice" class="dice">
          <div class="dice-face front">
            <span class="dot dot-center-middle"></span>
          </div>
          <div class="dice-face back">
            <span class="dot dot-top-left"></span>
            <span class="dot dot-top-right"></span>
            <span class="dot dot-center-middle"></span>
            <span class="dot dot-bottom-left"></span>
            <span class="dot dot-bottom-right"></span>
          </div>
          <div class="dice-face right">
            <span class="dot dot-top-left"></span>
            <span class="dot dot-top-right"></span>
            <span class="dot dot-center-middle"></span>
            <span class="dot dot-bottom-left"></span>
            <span class="dot dot-bottom-right"></span>
            <span class="dot dot-center-top"></span>
          </div>
          <div class="dice-face left">
            <span class="dot dot-top-left"></span>
            <span class="dot dot-top-right"></span>
            <span class="dot dot-bottom-left"></span>
            <span class="dot dot-bottom-right"></span>
          </div>
          <div class="dice-face top">
            <span class="dot dot-top-left"></span>
            <span class="dot dot-top-right"></span>
            <span class="dot dot-center-middle"></span>
          </div>
          <div class="dice-face bottom">
            <span class="dot dot-top-left"></span>
            <span class="dot dot-top-right"></span>
          </div>
        </div>
      </div>
      <div ref="logoContainer" class="logo-container">
        <img ref="logo" src="@/assets/images/h5/bottom-logo.png" alt="Betdoce" class="logo">
      </div>
      <div ref="progressBar" class="progress-bar">
        <div ref="progressFill" class="progress-fill"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import { onMounted, ref, onBeforeUnmount } from 'vue'
import gsap from 'gsap'

const diceContainer = ref(null)
const dice = ref(null)
const logoContainer = ref(null)
const logo = ref(null)
const progressBar = ref(null)
const progressFill = ref(null)

defineProps({
  show: {
    type: Boolean as PropType<boolean>,
    required: true
  }
})

let mainTimeline: gsap.core.Timeline | null = null

onMounted(() => {
  mainTimeline = gsap.timeline({
    repeat: -1,
    defaults: {
      ease: "power2.inOut"
    }
  })

  // 3D 骰子动画
  gsap.to(dice.value, {
    rotateX: 360,
    rotateY: 360,
    duration: 3,
    repeat: -1,
    ease: "none"
  })

  // Logo 发光效果
  gsap.to(logo.value, {
    filter: 'brightness(1.2) drop-shadow(0 0 10px rgba(255, 89, 137, 0.5))',
    duration: 1.5,
    repeat: -1,
    yoyo: true,
    ease: "sine.inOut"
  })

  // 进度条动画
  gsap.to(progressFill.value, {
    width: '100%',
    duration: 2,
    repeat: -1,
    ease: "power1.inOut",
    yoyo: true
  })
})

onBeforeUnmount(() => {
  if (mainTimeline) {
    mainTimeline.kill()
  }
})
</script>

<style scoped>
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
}

.dice-container {
  perspective: 1000px;
  width: 60px;
  height: 60px;
  margin-bottom: 20px;
}

.dice {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transform: rotateX(-30deg) rotateY(-45deg);
}

.dice-face {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-areas:
    "top-left    center-top    top-right"
    "center-left center-middle center-right"
    "bottom-left center-bottom bottom-right";
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  padding: 12%;
  background: linear-gradient(135deg, #198A51, #199a51);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  box-shadow:
    inset 0 0 15px rgba(0, 0, 0, 0.2),
    0 0 5px rgba(255, 89, 137, 0.3);
  backface-visibility: visible;
}

.front {
  transform: translateZ(30px);
}

.back {
  transform: rotateY(180deg) translateZ(30px);
}

.right {
  transform: rotateY(90deg) translateZ(30px);
}

.left {
  transform: rotateY(-90deg) translateZ(30px);
}

.top {
  transform: rotateX(90deg) translateZ(30px);
}

.bottom {
  transform: rotateX(-90deg) translateZ(30px);
}

.dice-face::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
      transparent 0%,
      rgba(255, 255, 255, 0.2) 50%,
      transparent 100%);
  border-radius: 12px;
  pointer-events: none;
}

.dot {
  width: 10px;
  height: 10px;
  background: white;
  border-radius: 50%;
  box-shadow:
    inset -1px -1px 3px rgba(0, 0, 0, 0.3),
    0 0 4px rgba(255, 255, 255, 0.5);
  margin: auto;
  position: relative;
  z-index: 1;
}

.dot-top-left {
  grid-area: top-left;
}

.dot-top-right {
  grid-area: top-right;
}

.dot-center-middle {
  grid-area: center-middle;
}

.dot-bottom-left {
  grid-area: bottom-left;
}

.dot-bottom-right {
  grid-area: bottom-right;
}

.dot-center-top {
  grid-area: center-top;
}

.dot-center-bottom {
  grid-area: center-bottom;
}

.dot-center-left {
  grid-area: center-left;
}

.dot-center-right {
  grid-area: center-right;
}

.logo-container {
  position: relative;
}

.logo {
  width: 160px;
  height: auto;
  filter: drop-shadow(0 0 5px rgba(14, 117, 59, 0.3));
}

.progress-bar {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  width: 0%;
  height: 100%;
  background: linear-gradient(90deg, #198A51, #199a51);
  border-radius: 2px;
  box-shadow: 0 0 10px RGBA(14, 117, 59, 0.3);
}
</style>