<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-16 17:48:32
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-19 17:33:15
 * @FilePath: \betdoce-admin\src\views\report\userInvitationStatistics\components\UserInvitationStatisticsSearch.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import {
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption,
} from "element-plus";
import { ref } from "vue";

interface Props {
  model: {
    user_id: string;
    phone: string;
    registration_source: string;
    sort_by: string;
  };
}

const props = defineProps<Props>();
const emit = defineEmits(["search", "reset"]);

// 注册来源选项
const registerSourceOptions = [
  { label: "全部", value: 0 },
  { label: "平台", value: 1 },
  { label: "代理商", value: 2 },
  { label: "渠道", value: 3 },
];

// 排序类型选项
const sortTypeOptions = [
  { label: "全部", value: "" },
  { label: "一级首充金额", value: "level1_first_charge_amount" },
  { label: "二级首充金额", value: "level2_first_charge_amount" },
  { label: "三级首充金额", value: "level3_first_charge_amount" },
  { label: "全部邀请人数", value: "total_invite_count" },
  { label: "全部首充人数", value: "total_first_charge_count" },
  { label: "全部首充金额", value: "total_first_charge_amount" },
  { label: "全部累计充值", value: "total_recharge_amount" },
  { label: "全部累计提现", value: "total_withdraw_amount" },
  { label: "全部累计营收", value: "total_profit_amount" },
];

const handleSearch = () => {
  emit("search");
};

const handleReset = () => {
  emit("reset");
};
</script>

<template>
  <ElForm :inline="true" :model="model">
    <ElFormItem label="用户ID">
      <ElInput v-model="model.user_id" placeholder="请输入用户ID" clearable />
    </ElFormItem>
    <ElFormItem label="手机号">
      <ElInput v-model="model.phone" placeholder="请输入手机号" clearable />
    </ElFormItem>
    <ElFormItem label="注册来源">
      <ElSelect
        v-model="model.registration_source"
        placeholder="请选择注册来源"
        clearable
      >
        <ElOption
          v-for="item in registerSourceOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </ElSelect>
    </ElFormItem>
    <ElFormItem label="排序类型">
      <ElSelect
        v-model="model.sort_by"
        placeholder="请选择排序类型"
        clearable
      >
        <ElOption
          v-for="item in sortTypeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </ElSelect>
    </ElFormItem>
    <ElFormItem>
      <ElButton type="primary" @click="handleSearch">搜索</ElButton>
      <ElButton @click="handleReset">重置</ElButton>
    </ElFormItem>
  </ElForm>
</template>
