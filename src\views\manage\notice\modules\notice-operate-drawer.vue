<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { enableStatusOptions } from '@/constants/business';
import { fetchAddNotice, fetchUpdateNotice, fetchGetNoticeDetail } from '@/service/api';

interface Props {
  /** the type of operation */
  operateType: UI.TableOperateType;
  /** the edit row data */
  rowData?: Api.SystemManage.Role | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const title = computed(() => {
  const titles: Record<UI.TableOperateType, string> = {
    add: "新增消息",
    edit: "编辑消息"
  };
  return titles[props.operateType];
});

const formRef = ref();
const formData = reactive({
  notice_type: 1,
  subject: '',
  content: '',
  status: 1
});

const noticeTypeOptions = [
  { value: 1, label: '消息中心' },
  { value: 2, label: '系统内公告' },
  { value: 3, label: '活动通知' }
];

const rules = {
  notice_type: [{ required: true, message: '请选择消息类型', trigger: 'change' }],
  subject: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入内容', trigger: 'blur' }]
};

async function fetchDetail() {
  if (!props.rowData?.id) return;
  try {
    const res = await fetchGetNoticeDetail({
      id: props?.rowData?.id
    });
    if (res.data) {
      Object.assign(formData, res.data);
    }
  } catch (error) {
    window.$message?.error('获取详情失败');
  }
}

function closeDrawer(){
  visible.value = false;
}

async function handleSubmit() {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    if (props.operateType === 'edit') {
     const {error} = await fetchUpdateNotice({
        id: props?.rowData?.id,
        ...formData
      });
      if (!error){
        window.$message?.success("更新成功");
        closeDrawer();
        emit('submitted');
      }
    } else {
      const {error} = await fetchAddNotice(formData);
      if (!error){
        window.$message?.success("创建成功");
        closeDrawer();
        emit('submitted');
      }
    }
  } catch (error) {
    window.$message?.error("失败");
  }
}

watch(visible,async () => {
  if (visible.value) {
    console.log(enableStatusOptions);
    handleUpdateModelWhenEdit();
  }
});

/** 编辑时更新表单数据 */
function handleUpdateModelWhenEdit() {
   // 重置表单数据
  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(formData, props.rowData);
  } else {
    Object.assign(formData, {
      notice_type: 1,
      subject: '',
      content: '',
      status: '1'
    });
  }
}
</script>

<template>
  <ElDrawer
    :size="360"
    v-model="visible"
    :title="title"
    
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
    >
      <ElFormItem label="消息类型" prop="notice_type">
        <ElSelect v-model="formData.notice_type" placeholder="请选择消息类型">
          <ElOption
            v-for="item in noticeTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem>

      <ElFormItem label="标题" prop="subject">
        <ElInput v-model="formData.subject" placeholder="请输入标题" />
      </ElFormItem>

      <ElFormItem label="内容" prop="content">
        <ElInput
          v-model="formData.content"
          type="textarea"
          :rows="15"
          placeholder="请输入内容"
        />
      </ElFormItem>
      <ElFormItem label="状态" prop="status">
        <ElRadioGroup v-model="formData.status">
          <ElRadio v-for="{ label, value } in enableStatusOptions" :key="value" :value="value" :label="label" />
        </ElRadioGroup>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="flex justify-end gap-2">
        <ElButton @click="visible = false">取消</ElButton>
        <ElButton
          type="primary"
          @click="handleSubmit"
        >
          确定
        </ElButton>
      </div>
    </template>
  </ElDrawer>
</template>



<style scoped>

</style> 