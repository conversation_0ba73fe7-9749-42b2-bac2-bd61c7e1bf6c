// 桌面端样式
.game-tabs {
  height: 40px;

  :deep() {
    .v-slide-group__content {
      gap: 8px;
    }

    .v-tabs-bar {
      height: 40px !important;
    }

    .v-tab {
      height: 40px !important;
      min-width: 108px !important;
      // padding: 0 !important;
      margin: 0 !important;
      border-radius: 0;
      opacity: 1;
      position: relative;
      gap: 8px !important;

      .item-icon {
        width: 30px;
      }

      &--selected {
        border-radius: 6px !important;
        overflow: hidden;
        border: 2px solid #01963A;

        .v-tab__slider {
          display: none;
        }

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 3px;
          background: #FFA600;
        }

        .tab-text {
          font-family: Inter, Inter;
          font-weight: 400 !important;
          font-size: 12px;
          color: #FFFFFF;
          line-height: 16px;
          text-align: left;
        }
      }
    }
  }
}

.tab-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  padding: 8px 0;

  .tab-icon {
    transition: all 0.3s ease;
  }

  .tab-text {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
  }
}

.custom-tab {
  text-transform: none !important;
  font-weight: normal !important;
  letter-spacing: 0 !important;
  border-radius: 6px !important;
  background: #1C1D2F;

  .tab-text {
    font-family: Inter, Inter;
    font-weight: 400;
    font-size: 12px;
    color: #FFFFFF;
    line-height: 16px;
    text-align: left;
    font-style: normal;
    text-transform: none;
  }

  &:hover {
    .item-icon {
      transform: scale(1.05);
    }

    .tab-text {
      transform: scale(1.05);
    }
  }
}

.tab-active {
  .tab-icon {
    transform: scale(1.1);
    opacity: 1;
  }

  .tab-text {
    color: #FFD93D;
  }
}

.search-field {
  margin-left: 20px;
  background: #000;
  border-radius: 20px;
}
.game-list {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 6px;

  .game-card {
    width: 125px;
    cursor: pointer;
    position: relative;

    .star-icon {
      position: absolute;
      background: rgba(0, 0, 0, 0.5);
      z-index: 9;
      top: 0;
      right: 0;
      border-radius: 0 8px 0 8px;
    }

    .game-image {
      // width: 125px;
      // height: 148px;
      width: 100%;
      border-radius: 8px;
    }

    .game-item-label {
      font-size: 14px;
      font-weight: 400;
    }

    .game-item-provider {
      color: #CE6A22;
      font-size: 14px;
    }
  }
}

.game-list-wrapper {
  height: auto;
  width: auto;
  max-width: 1140px;
  margin: 0 auto;
  border-radius: 10px;
  position: relative;

  .game-title {
    width: 100%;
    font-size: 20px;
    color: #FFFFFF;
    line-height: 30px;
    height: 30px;

    .title-more {
      background: #303142;
      border-radius: 4px;
      line-height: 11px;
      font-size: 11px;
      color: #9298B6;
    }
  }
}
