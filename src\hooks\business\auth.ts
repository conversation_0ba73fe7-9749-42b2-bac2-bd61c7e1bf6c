/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-19 10:46:25
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-03 10:24:25
 * @FilePath: \betdoce-admin\src\hooks\business\auth.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useAuthStore } from '@/store/modules/auth';

export function useAuth() {
  const authStore = useAuthStore();

  function hasAuth(codes: string | string[] | number | number[]) {
     
    if (!authStore.isLogin) {
      return false;
    }

    if (typeof codes === 'string') {
      return authStore.userInfo.buttons?.includes(codes);
    }

    if (typeof codes === 'number') {
      console.log(authStore.userInfo.buttons?.includes(codes))
      return authStore.userInfo.buttons?.includes(codes);
    }

    return codes.some(code => authStore.userInfo.buttons?.includes(code));
  }
 
  return {
    hasAuth
  };
}
