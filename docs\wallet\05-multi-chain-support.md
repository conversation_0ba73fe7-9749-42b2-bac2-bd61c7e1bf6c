---
sidebar_position: 5
---

# Multi-chain Support

UniPass Wallet supports all EVM-compatible chains and gives users the same address across all chains using [**EIP-2470**](https://eips.ethereum.org/EIPS/eip-2470). Currently supported chains are:

## MainNet

- Ethereum
- BNB Chain
- Polygon
- Rangers Protocol
- Arbitrum
- Avalanche (C chain)
- KCC
- PlatON
- OKC

## TestNet

- Goerli (Ethereum testnet)
- BSC testnet
- Mumbai (Polygon testnet)
- Robin (Rangers testnet)
- Arbitrum Goerli
- Avalanche Fuji testnet
- KCC testnet
- OKC testnet
- PlatON testnet
- Scroll

## Chains to support in the future

- Optimism
- opBNB
- Godwoken
- Fantom
