---
sidebar_position: 3
---

# Connect UniPass Wallet

create a `Login` function in the Widget Blueprint, and then complete its parameters.

- `Return email`: If `Return email` is checked, user’s email address will be returned.
- `Onlogin Event`: When user login succeed, user information will be returned as event data，including user wallet `address`, user `email` address(If `Return email` is checked), `newBorn`(user is new registered or not)

![connect to UniPass](./img/unreal-connect-unipass.png)
