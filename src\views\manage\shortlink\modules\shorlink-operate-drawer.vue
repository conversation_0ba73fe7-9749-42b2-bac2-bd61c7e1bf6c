<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { enableStatusOptions } from '@/constants/business';
import { statusOptions,positionOptions } from '@/constants/common';
import {
  fetchAddShortLink,
  fetchUpdateShortLink,
  fetchGetShortLinkDetail
} from '@/service/api';

interface Props {
  /** the type of operation */
  operateType: UI.TableOperateType;
  /** the edit row data */
  rowData?: any | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const title = computed(() => {
  const titles: Record<UI.TableOperateType, string> = {
    add: "新增短链接",
    edit: "编辑短链接"
  };
  return titles[props.operateType];
});

const formRef = ref();
const formData = reactive({
  origin_link: '',
  short_link: '',
  status: 1,
});

const rules = {
  origin_link: [{ required: true, message: '请输入原始链接', trigger: 'blur' }],
  short_link: [{ required: true, message: '请输入短链接', trigger: 'blur' }],
  key: [{ required: true, message: '请输入key', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'blur' }]
};

async function fetchDetail() {
  if (!props.rowData?.id) return;
  try {
    const res = await fetchGetShortLinkDetail({
      id: props?.rowData?.id
    });
    if (res.data) {
      Object.assign(formData, res.data);
    }
  } catch (error) {
    window.$message?.error('获取详情失败');
  }
}

function closeDrawer(){
  visible.value = false;
}

async function handleSubmit() {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    if (props.operateType === 'edit') {
      const { error } = await fetchUpdateShortLink({
        id: props?.rowData?.id,
        ...formData
      });
      if (!error) {
        window.$message?.success("更新成功");
        closeDrawer();
        emit('submitted');
      }
    } else {
      const { error } = await fetchAddShortLink(formData);
      if (!error) {
        window.$message?.success("创建成功");
        closeDrawer();
        emit('submitted');
      }
    }
  } catch (error) {
    window.$message?.error("操作失败");
  }
}

watch(visible, async () => {
  if (visible.value) {
    if (props.operateType === 'edit') {
      // await fetchDetail();
     Object.assign(formData, props.rowData);
    } else {
      Object.assign(formData, {
        origin_link: '',
        short_link: '',
        key:'',
        status: 1
      });
    }
  }
});
</script>

<template>
  <ElDrawer
    :size="360"
    v-model="visible"
    :title="title"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
    >
      <ElFormItem label="key" prop="key">
        <ElInput v-model="formData.key" placeholder="请输入key" />
      </ElFormItem>
      <ElFormItem label="原始链接" prop="origin_link">
        <ElInput v-model="formData.origin_link" placeholder="请输入原始链接" />
      </ElFormItem>
      <ElFormItem label="短链接" prop="short_link">
        <ElInput v-model="formData.short_link" placeholder="请输入短链接" />
      </ElFormItem>
      <ElFormItem label="状态" prop="status">
        <ElRadioGroup v-model="formData.status">
          <ElRadio v-for="{ label, value } in statusOptions" :key="value" :value="value" :label="label" />
        </ElRadioGroup>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="flex justify-end gap-2">
        <ElButton @click="closeDrawer">取消</ElButton>
        <ElButton
          type="primary"
          @click="handleSubmit"
        >
          确定
        </ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<style scoped>
</style> 