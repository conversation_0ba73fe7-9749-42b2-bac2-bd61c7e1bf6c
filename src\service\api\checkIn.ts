import { request } from '../request';

export interface CheckInRecordItem {
  user_id: number;
  checkin_date: number;
  streak: number;
  reward: number;
  reward_claimed: boolean;
  created_at: number;
  updated_at: number;
  uuid: number;
  nickname: string;
  username: string;
  phone: string;
  email: string;
  CPF: string;
  pix: string;
  accountType: number;
  customerName: string;
  merchantUserId: string;
  avatar: string;
  gender: number;
  reg_ip: string;
  level: number;
  invite_code: string;
  source: number;
  father_id: number;
  is_recharge: number;
  is_black: number;
  is_login: number;
  remake: string;
  created_by: string;
  updated_by: string;
}

export interface CheckInRecordListParams extends Api.Common.CommonSearchParams {
  user_id?: number;
  phone?: string;
  level?: number;
  streak?: number;
  sort?: string;
}

export interface CheckInRecordListResponse {
  list: CheckInRecordItem[];
  total: number;
}

/**
 * 获取签到记录列表
 */
export function fetchCheckInRecordList(params: CheckInRecordListParams) {
  return request<CheckInRecordListResponse>({
    url: '/backend/checkin/list',
    method: 'get',
    params: {
      ...params,
      sort: params.sort || 'id'
    }
  });
}
