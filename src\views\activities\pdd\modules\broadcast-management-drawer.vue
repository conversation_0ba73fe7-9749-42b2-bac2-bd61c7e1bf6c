<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  activityId: {
    type: Number,
    default: undefined
  }
});

const emit = defineEmits(['update:visible', 'success']);

const dialogVisible = ref(props.visible);
const virtualWinnerCount = ref<number | undefined>(undefined);
const virtualWinnerCopy = ref<string>('');

watch(() => props.visible, (val) => {
  dialogVisible.value = val;
  if (val) {
    // 當彈窗打開時，可以根據 activityId 加載現有數據
    // 例如：fetchBroadcastData(props.activityId);
    // 這裡暫時清空數據
    virtualWinnerCount.value = undefined;
    virtualWinnerCopy.value = '';
  }
});

watch(dialogVisible, (val) => {
  emit('update:visible', val);
});

const handleClose = () => {
  dialogVisible.value = false;
};

const handleSave = () => {
  // TODO: 實現保存邏輯，調用API
  console.log('保存广播管理数据', {
    activityId: props.activityId,
    virtualWinnerCount: virtualWinnerCount.value,
    virtualWinnerCopy: virtualWinnerCopy.value
  });
  ElMessage.success('广播设置已保存');
  emit('success');
  handleClose();
};
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="广播管理"
    width="500px"
    align-center
    @close="handleClose"
  >
    <ElForm label-width="120px">
      <ElFormItem label="虚拟中奖人数">
        <ElInput v-model.number="virtualWinnerCount" placeholder="请输入" type="number" />
      </ElFormItem>
      <ElFormItem label="虚拟中奖文案">
        <ElInput v-model="virtualWinnerCopy" type="textarea" :rows="4" placeholder="请输入" />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton type="primary" @click="handleSave">保存</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
