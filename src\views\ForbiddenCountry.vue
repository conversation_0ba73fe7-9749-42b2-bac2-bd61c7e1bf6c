<template>
  <div class="forbidden-country">
    <div class="icon-block">
      <svg width="48" height="48" viewBox="0 0 48 48" fill="none"><circle cx="24" cy="24" r="24" fill="#181B2A"/><path d="M34.8284 13.1716C37.3905 15.7337 37.3905 19.7663 34.8284 22.3284L22.3284 34.8284C19.7663 37.3905 15.7337 37.3905 13.1716 34.8284C10.6095 32.2663 10.6095 28.2337 13.1716 25.6716L25.6716 13.1716C28.2337 10.6095 32.2663 10.6095 34.8284 13.1716Z" fill="#FF3B5B"/><path d="M16.2222 31.7778L31.7778 16.2222" stroke="#fff" stroke-width="2.5" stroke-linecap="round"/></svg>
    </div>
    <div class="title">This game is not available in your country</div>
    <div class="desc">Due to licensing regulation laws, we cannot offer our services to you.</div>
    <div class="icon-block globe">
      <svg width="32" height="32" viewBox="0 0 32 32" fill="none"><circle cx="16" cy="16" r="16" fill="#181B2A"/><path d="M16 6C21.5228 6 26 10.4772 26 16C26 21.5228 21.5228 26 16 26C10.4772 26 6 21.5228 6 16C6 10.4772 10.4772 6 16 6Z" stroke="#fff" stroke-width="2"/><path d="M16 6V26" stroke="#fff" stroke-width="2"/><path d="M6 16H26" stroke="#fff" stroke-width="2"/></svg>
    </div>
    <div class="desc">Not in a restricted region?<br>If you're using a proxy service or VPN to access, try turning it off and reload the page.</div>
    <button class="support-btn" @click="contactSupport">Contact Support</button>
  </div>
</template>

<script setup lang="ts">
function contactSupport() {
  window.open('mailto:<EMAIL>');
}
</script>

<style scoped>
.forbidden-country {
  min-height: 100vh;
  background: #181b2a;
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
}
.icon-block {
  margin-bottom: 24px;
}
.icon-block.globe {
  margin-top: 24px;
  margin-bottom: 8px;
}
.title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
  text-align: center;
}
.desc {
  color: #b0b3c7;
  font-size: 15px;
  margin-bottom: 8px;
  text-align: center;
}
.support-btn {
  margin-top: 32px;
  background: #ff3b5b;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s;
}
.support-btn:hover {
  background: #e62e4a;
}
</style> 