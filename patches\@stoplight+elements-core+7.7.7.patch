diff --git a/node_modules/@stoplight/elements-core/index.esm.js b/node_modules/@stoplight/elements-core/index.esm.js
index 0a24c58..3faa884 100644
--- a/node_modules/@stoplight/elements-core/index.esm.js
+++ b/node_modules/@stoplight/elements-core/index.esm.js
@@ -793,11 +793,11 @@ const BasicAuth = ({ onChange, value }) => {
         React.createElement("div", null, "Username"),
         React.createElement(Text, { mx: 3 }, ":"),
         React.createElement(Flex, { flex: 1 },
-            React.createElement(Input, { style: { paddingLeft: 15 }, "aria-label": "Username", appearance: "minimal", flex: 1, placeholder: "username", value: username, type: "text", required: true, onChange: e => onCredentialsChange(e.currentTarget.value, password) })),
+            React.createElement(Input, { style: { paddingLeft: 15 }, "aria-label": "Username", appearance: "minimal", flex: 1, placeholder: "<ORG_ID>", value: username, type: "text", required: true, onChange: e => onCredentialsChange(e.currentTarget.value, password) })),
         React.createElement("div", null, "Password"),
         React.createElement(Text, { mx: 3 }, ":"),
         React.createElement(Flex, { flex: 1 },
-            React.createElement(Input, { style: { paddingLeft: 15 }, "aria-label": "Password", appearance: "minimal", flex: 1, placeholder: "password", value: password, type: "password", required: true, onChange: e => onCredentialsChange(username, e.currentTarget.value) }))));
+            React.createElement(Input, { style: { paddingLeft: 15 }, "aria-label": "Password", appearance: "minimal", flex: 1, placeholder: "<API_KEY>", value: password, type: "password", required: true, onChange: e => onCredentialsChange(username, e.currentTarget.value) }))));
 };
 function encode(value) {
     return btoa(value);
@@ -2063,7 +2063,7 @@ function getApiKeyDescription(inProperty, name) {
 function getBasicAuthDescription() {
     return `Basic authentication is a simple authentication scheme built into the HTTP protocol.
   To use it, send your HTTP requests with an Authorization header that contains the word Basic
-  followed by a space and a base64-encoded string \`username:password\`.
+  followed by a space and a base64-encoded string \`<ORG_ID>:<API_KEY>\`.
 
   Example: \`Authorization: Basic ZGVtbzpwQDU1dzByZA==\``;
 }
diff --git a/node_modules/@stoplight/elements-core/index.js b/node_modules/@stoplight/elements-core/index.js
index 66c682a..72a3459 100644
--- a/node_modules/@stoplight/elements-core/index.js
+++ b/node_modules/@stoplight/elements-core/index.js
@@ -847,11 +847,11 @@ const BasicAuth = ({ onChange, value }) => {
         React__namespace.createElement("div", null, "Username"),
         React__namespace.createElement(mosaic.Text, { mx: 3 }, ":"),
         React__namespace.createElement(mosaic.Flex, { flex: 1 },
-            React__namespace.createElement(mosaic.Input, { style: { paddingLeft: 15 }, "aria-label": "Username", appearance: "minimal", flex: 1, placeholder: "username", value: username, type: "text", required: true, onChange: e => onCredentialsChange(e.currentTarget.value, password) })),
+            React__namespace.createElement(mosaic.Input, { style: { paddingLeft: 15 }, "aria-label": "Username", appearance: "minimal", flex: 1, placeholder: "<ORG_ID>", value: username, type: "text", required: true, onChange: e => onCredentialsChange(e.currentTarget.value, password) })),
         React__namespace.createElement("div", null, "Password"),
         React__namespace.createElement(mosaic.Text, { mx: 3 }, ":"),
         React__namespace.createElement(mosaic.Flex, { flex: 1 },
-            React__namespace.createElement(mosaic.Input, { style: { paddingLeft: 15 }, "aria-label": "Password", appearance: "minimal", flex: 1, placeholder: "password", value: password, type: "password", required: true, onChange: e => onCredentialsChange(username, e.currentTarget.value) }))));
+            React__namespace.createElement(mosaic.Input, { style: { paddingLeft: 15 }, "aria-label": "Password", appearance: "minimal", flex: 1, placeholder: "<API_KEY>", value: password, type: "password", required: true, onChange: e => onCredentialsChange(username, e.currentTarget.value) }))));
 };
 function encode(value) {
     return btoa(value);
@@ -2117,7 +2117,7 @@ function getApiKeyDescription(inProperty, name) {
 function getBasicAuthDescription() {
     return `Basic authentication is a simple authentication scheme built into the HTTP protocol.
   To use it, send your HTTP requests with an Authorization header that contains the word Basic
-  followed by a space and a base64-encoded string \`username:password\`.
+  followed by a space and a base64-encoded string \`<ORG_ID>:<API_KEY>\`.
 
   Example: \`Authorization: Basic ZGVtbzpwQDU1dzByZA==\``;
 }
diff --git a/node_modules/@stoplight/elements-core/index.mjs b/node_modules/@stoplight/elements-core/index.mjs
index 0a24c58..3faa884 100644
--- a/node_modules/@stoplight/elements-core/index.mjs
+++ b/node_modules/@stoplight/elements-core/index.mjs
@@ -793,11 +793,11 @@ const BasicAuth = ({ onChange, value }) => {
         React.createElement("div", null, "Username"),
         React.createElement(Text, { mx: 3 }, ":"),
         React.createElement(Flex, { flex: 1 },
-            React.createElement(Input, { style: { paddingLeft: 15 }, "aria-label": "Username", appearance: "minimal", flex: 1, placeholder: "username", value: username, type: "text", required: true, onChange: e => onCredentialsChange(e.currentTarget.value, password) })),
+            React.createElement(Input, { style: { paddingLeft: 15 }, "aria-label": "Username", appearance: "minimal", flex: 1, placeholder: "<ORG_ID>", value: username, type: "text", required: true, onChange: e => onCredentialsChange(e.currentTarget.value, password) })),
         React.createElement("div", null, "Password"),
         React.createElement(Text, { mx: 3 }, ":"),
         React.createElement(Flex, { flex: 1 },
-            React.createElement(Input, { style: { paddingLeft: 15 }, "aria-label": "Password", appearance: "minimal", flex: 1, placeholder: "password", value: password, type: "password", required: true, onChange: e => onCredentialsChange(username, e.currentTarget.value) }))));
+            React.createElement(Input, { style: { paddingLeft: 15 }, "aria-label": "Password", appearance: "minimal", flex: 1, placeholder: "<API_KEY>", value: password, type: "password", required: true, onChange: e => onCredentialsChange(username, e.currentTarget.value) }))));
 };
 function encode(value) {
     return btoa(value);
@@ -2063,7 +2063,7 @@ function getApiKeyDescription(inProperty, name) {
 function getBasicAuthDescription() {
     return `Basic authentication is a simple authentication scheme built into the HTTP protocol.
   To use it, send your HTTP requests with an Authorization header that contains the word Basic
-  followed by a space and a base64-encoded string \`username:password\`.
+  followed by a space and a base64-encoded string \`<ORG_ID>:<API_KEY>\`.
 
   Example: \`Authorization: Basic ZGVtbzpwQDU1dzByZA==\``;
 }
