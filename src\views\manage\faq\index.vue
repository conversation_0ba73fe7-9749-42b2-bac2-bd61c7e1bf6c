<script setup lang="tsx">
import { ref, reactive, onMounted } from 'vue';
import { ElButton, ElPopconfirm, ElMessage } from 'element-plus';
import { fetchGetAllKeywords, fetchAddKeyword, fetchUpdateKeyword, fetchDeleteKeyword } from '@/service/api';
import { $t } from '@/locales';
import { useTable, useTableOperate } from '@/hooks/common/table';
import FAQOperateDrawer from './modules/faq-operate-drawer.vue';
import FAQSearch from './modules/faq-search.vue';
import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';

defineOptions({ name: 'FAQManage' });

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  columnChecks
} = useTable<Api.SystemManage.FAQItem>({
  apiFn: fetchGetAllKeywords,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 10,
    current: 1,
    keyword: undefined
  },
  columns: () => [
    { prop: 'index', label: $t('common.index'), width: 64 },
    { prop: 'keyword', label: '提问', minWidth: 200 },
    { prop: 'response', label: '回答', minWidth: 300 },
    {
      prop: 'operate',
      label: $t('common.operate'),
      align: 'center',
      formatter: (row: Api.SystemManage.FAQItem) => (
        <div class="flex-center">
          <ElButton
            type="primary"
            plain
            size="small"
            onClick={() => edit(row.id)}
          >
            {$t('common.edit')}
          </ElButton>
          <ElPopconfirm
            title={$t('common.confirmDelete')}
            onConfirm={() => handleDelete(row.id)}
          >
            {{
              reference: () => (
                <ElButton type="danger" plain size="small">
                  {$t('common.delete')}
                </ElButton>
              )
            }}
          </ElPopconfirm>
        </div>
      )
    }
  ]
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  onDeleted,
  checkedRowKeys
} = useTableOperate<Api.SystemManage.FAQItem>(data, getData, 'id');

async function handleDelete(id: number) {
  const { error } = await fetchDeleteKeyword(id);
  if (!error) {
    onDeleted();
  }
}

async function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.warning('请选择要删除的用户');
    return;
  }
  // const { error } = await fetchBatchDeleteKeyword({
  //   ids: checkedRowKeys.value
  // });
  // if (!error) {
  //   onBatchDeleted();
  // }
}

function edit(id: number) {
  handleEdit(id);
}

function handleSearch() {
  getDataByPage();
}

function handleReset() {
  resetSearchParams();
  handleSearch();
}

onMounted(getData);
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto">
    <FAQSearch
      v-model:model="searchParams"
      @reset="handleReset"
      @search="handleSearch"
    >
    <template #table-operation>
        <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading"
            @add="handleAdd"
            @delete="handleBatchDelete"
            @refresh="getData"
          />
      </template>
  </FAQSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="checkedRowKeys = $event.map(row => row.id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
      <FAQOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}

.sm\:flex-1-hidden {
  flex: 1;
  min-height: 0;
}

.card-wrapper {
  min-height: 400px;
}

.main-table {
  border-radius: 4px;
  font-size: 15px;
}

:deep(.el-table th) {
  font-weight: 600;
  background: #fafbfc;
}

:deep(.el-table .el-table__row) {
  transition: background 0.2s;
}
:deep(.el-table .el-table__row:hover) {
  background: #f5f7fa;
}

:deep(.el-pagination) {
  margin-top: 8px;
}
</style>
