<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  fetchGetActiveChannels,
  fetchDeleteSubscription,
  fetchGetCustomerServiceGroups,
  fetchDeleteCustomerServiceGroup
} from '@/service/api/telegram'
import TelegramDrawer from './modules/telegram-drawer.vue'
import CustomerServiceDrawer from './modules/customer-service-drawer.vue'

defineOptions({ name: 'TelegramManagement' })

const activeTab = ref('telegram')

// Telegram数据
const telegramData = ref([])
const telegramLoading = ref(false)
const telegramDrawerVisible = ref(false)
const telegramOperateType = ref<'add' | 'edit'>('add')
const telegramEditingData = ref(null)

// 客服群数据
const customerServiceData = ref([])
const customerServiceLoading = ref(false)
const customerServiceDrawerVisible = ref(false)
const customerServiceOperateType = ref<'add' | 'edit'>('add')
const customerServiceEditingData = ref(null)

// 获取Telegram数据
async function getTelegramData() {
  telegramLoading.value = true
  try {
    const { data, error } = await fetchGetActiveChannels()
    if (!error) {
      telegramData.value = data || []
    }
  } catch (error) {
    console.error('获取Telegram数据失败:', error)
  } finally {
    telegramLoading.value = false
  }
}

// 获取客服群数据
async function getCustomerServiceData() {
  customerServiceLoading.value = true
  try {
    const { data, error } = await fetchGetCustomerServiceGroups()
    if (!error) {
      customerServiceData.value = data || []
    }
  } catch (error) {
    console.error('获取客服群数据失败:', error)
  } finally {
    customerServiceLoading.value = false
  }
}

// Telegram操作
function handleAddTelegram() {
  telegramOperateType.value = 'add'
  telegramEditingData.value = null
  telegramDrawerVisible.value = true
}

function handleEditTelegram(row: any) {
  telegramOperateType.value = 'edit'
  telegramEditingData.value = row
  telegramDrawerVisible.value = true
}

async function handleDeleteTelegram(id: number) {
  try {
    const { error } = await fetchDeleteSubscription(id)
    if (!error) {
      ElMessage.success('删除成功')
      getTelegramData()
    }
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

// 客服群操作
function handleAddCustomerService() {
  customerServiceOperateType.value = 'add'
  customerServiceEditingData.value = null
  customerServiceDrawerVisible.value = true
}

function handleEditCustomerService(row: any) {
  customerServiceOperateType.value = 'edit'
  customerServiceEditingData.value = row
  customerServiceDrawerVisible.value = true
}

async function handleDeleteCustomerService(id: number) {
  try {
    const { error } = await fetchDeleteCustomerServiceGroup(id)
    if (!error) {
      ElMessage.success('删除成功')
      getCustomerServiceData()
    }
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('删除失败')
  }
}

// 标签页切换
function handleTabChange(tabName: string) {
  if (tabName === 'telegram') {
    getTelegramData()
  } else {
    getCustomerServiceData()
  }
}

// 初始化
onMounted(() => {
  getTelegramData()
})
</script>

<template>
  <div class="telegram-management">
    <ElTabs v-model="activeTab" @tab-change="handleTabChange">
      <!-- Telegram列表 -->
      <ElTabPane label="Telegram列表" name="telegram">
        <div class="mb-4 flex justify-end">
          <ElButton type="success" @click="handleAddTelegram">
            <template #icon>
              <icon-ic-round-plus class="text-icon" />
            </template>
            添加Telegram
          </ElButton>
        </div>

        <ElCard>
          <ElTable
            v-loading="telegramLoading"
            :data="telegramData"
            row-key="id"
            style="width: 100%"
          >
            <ElTableColumn prop="id" label="ID" width="80" />
            <ElTableColumn prop="name" label="名称" min-width="150" />
            <ElTableColumn label="用户名" min-width="150">
              <template #default="{ row }">
                @{{ row.username }}
              </template>
            </ElTableColumn>
            <ElTableColumn label="类型" width="120">
              <template #default="{ row }">
                <ElTag :type="row.type === 'public' ? 'success' : 'warning'">
                  {{ row.type === 'public' ? '公开频道' : '私有频道' }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn label="订阅人数" width="120" align="center">
              <template #default="{ row }">
                {{ row.subscriber_count?.toLocaleString() || '0' }}
              </template>
            </ElTableColumn>
            <ElTableColumn label="状态" width="100">
              <template #default="{ row }">
                <ElTag :type="row.status === 1 ? 'success' : 'danger'">
                  {{ row.status === 1 ? '启用' : '禁用' }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn label="操作" width="160" align="center">
              <template #default="{ row }">
                <ElButton
                  type="primary"
                  plain
                  size="small"
                  @click="handleEditTelegram(row)"
                >
                  <ElIcon><EditPen /></ElIcon>
                  编辑
                </ElButton>
                <ElPopconfirm
                  title="确定要删除这个Telegram吗？"
                  @confirm="handleDeleteTelegram(row.id)"
                >
                  <template #reference>
                    <ElButton type="danger" plain size="small">
                      <ElIcon><Delete /></ElIcon>
                      删除
                    </ElButton>
                  </template>
                </ElPopconfirm>
              </template>
            </ElTableColumn>
          </ElTable>
        </ElCard>
      </ElTabPane>

      <!-- 客服群列表 -->
      <ElTabPane label="客服群列表" name="customerService">
        <div class="mb-4 flex justify-end">
          <ElButton type="success" @click="handleAddCustomerService">
            <template #icon>
              <icon-ic-round-plus class="text-icon" />
            </template>
            添加客服群
          </ElButton>
        </div>

        <ElCard>
          <ElTable
            v-loading="customerServiceLoading"
            :data="customerServiceData"
            row-key="id"
            style="width: 100%"
          >
            <ElTableColumn prop="id" label="ID" width="80" />
            <ElTableColumn prop="group_name" label="群组名称" min-width="150" />
            <ElTableColumn prop="group_id" label="群组ID" min-width="150" />
            <ElTableColumn prop="customer_service_count" label="客服数量" width="100" align="center" />
            <ElTableColumn prop="operating_time_value" label="运行时间" width="120" >
              <template #default="{ row }">
                {{ row.operating_time_value || '24小时' }}
              </template>
            </ElTableColumn>
            <ElTableColumn label="在线人数" width="100" align="center">
              <template #default="{ row }">
                {{ row.online_count || 0 }}
              </template>
            </ElTableColumn>
            <ElTableColumn label="状态" width="100">
              <template #default="{ row }">
                <ElTag :type="row.status === 1 ? 'success' : 'danger'">
                  {{ row.status === 1 ? '启用' : '禁用' }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn label="操作" width="160" align="center">
              <template #default="{ row }">
                <ElButton
                  type="primary"
                  plain
                  size="small"
                  @click="handleEditCustomerService(row)"
                >
                  <ElIcon><EditPen /></ElIcon>
                  编辑
                </ElButton>
                <ElPopconfirm
                  title="确定要删除这个客服群吗？"
                  @confirm="handleDeleteCustomerService(row.id)"
                >
                  <template #reference>
                    <ElButton type="danger" plain size="small">
                      <ElIcon><Delete /></ElIcon>
                      删除
                    </ElButton>
                  </template>
                </ElPopconfirm>
              </template>
            </ElTableColumn>
          </ElTable>
        </ElCard>
      </ElTabPane>
    </ElTabs>

    <!-- Telegram抽屉 -->
    <TelegramDrawer
      v-model:visible="telegramDrawerVisible"
      :operate-type="telegramOperateType"
      :row-data="telegramEditingData"
      @submitted="getTelegramData"
    />

    <!-- 客服群抽屉 -->
    <CustomerServiceDrawer
      v-model:visible="customerServiceDrawerVisible"
      :operate-type="customerServiceOperateType"
      :row-data="customerServiceEditingData"
      @submitted="getCustomerServiceData"
    />
  </div>
</template>

<style lang="scss" scoped>
.telegram-management {
  padding: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.flex {
  display: flex;
}

.justify-end {
  justify-content: flex-end;
}

:deep(.el-card) {
  border-radius: 4px;
}

:deep(.el-tabs__content) {
  padding-top: 16px;
}
</style>
