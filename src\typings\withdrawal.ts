export interface WithdrawalRecord {
  id: number;
  user_id: number;
  wallet_id: number;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed';
  transaction_no: string;
  order_no: string;
  platform_id: string;
  account_type: number;
  account_num: string;
  fail_reason: string;
  end_to_end_id: string;
  fee: number;
  withdrawal_time: number;
  completed_time: number;
  description: string;
  admin_id: number;
  created_at: number;
  updated_at: number;
  deleted_at: number;
}

export interface WithdrawalListParams {
  page: number;
  size: number;
}

export interface WithdrawalListResponse {
  list: WithdrawalRecord[];
  total: number;
}

export interface WithdrawalConfigResponse {
    id: number; // 配置表主键ID
    WhitelistDailyCountLimit: number; // 白名单用户每日提现次数限制（单位：次）
    WhitelistDailyAmountLimit: number; // 白名单用户每日提现总金额限制（单位：BRL，精度为分）
    WhitelistSingleAmountLimit: number; // 白名单用户单笔提现最大金额限制（单位：BRL，精度为分）
    WhitelistMinAmountLimit: number; // 白名单用户单笔最小提现金额限制（单位：BRL，精度为分）例如：100000 表示 1,000.00 BRL
    NonWhitelistDailyCountLimit: number; // 非白名单用户每日提现次数限制（单位：次）
    NonWhitelistDailyAmountLimit: number; // 非白名单用户每日提现总金额限制（单位：BRL，精度为分）
    NonWhitelistSingleAmountLimit: number; // 非白名单用户单笔提现最大金额限制（单位：BRL，精度为分）
    NonWhitelistMinAmountLimit: number; // 非白名单用户单笔最小提现金额限制（单位：BRL，精度为分）
    FastTrackAmountThreshold: number; // 免审提现模式阈值，低于该金额当日总提现可免审核（单位：BRL，精度为分）
    ManualReviewAmountThreshold: number; // 人工审核模式阈值，超过该金额当日总提现需审核（单位：BRL，精度为分）
    ManualReviewCountThreshold: number; // 人工审核模式阈值，当日提现次数超过该值需审核（单位：次）
    ManualReviewSingleAmount: number; // 人工审核模式阈值，单笔提现金额大于该值需审核（单位：BRL，精度为分）
    created_at: number; // 创建时间（时间戳，单位毫秒）
    created_by: string; // 创建人标识
    updated_at: number; // 更新时间（时间戳，单位毫秒）
    updated_by: string; // 更新人标识
}
// 更新非白名单用户提现配置参数
export interface WithdrawalConfigParams {
  non_whitelist_daily_count_limit: number; // 非白名单用户每日最多可提现次数
  non_whitelist_daily_amount_limit: number; // 非白名单用户每日提现金额上限（BRL 分单位）
  non_whitelist_single_amount_limit: number; // 非白名单用户单笔提现金额上限（BRL 分单位）
  non_whitelist_min_amount_limit: number; // 非白名单用户最小提现金额限制（BRL 分单位）
}


// 更新白名单用户提现配置参数
export interface WhitelistWithdrawalConfigParams {
  whitelist_daily_count_limit: number; // 白名单用户每日最多可提现次数
  whitelist_daily_amount_limit: number; // 白名单用户每日提现金额上限（BRL 分单位）
  whitelist_single_amount_limit: number; // 白名单用户单笔提现金额上限（BRL 分单位）
  whitelist_min_amount_limit: number; // 白名单用户最小提现金额限制（BRL 分单位）
}


export interface WithdrawalModeConfigParams {
  fast_track_amount_threshold: number; // 免审提现模式阈值，低于该金额当日总提现可免审核（单位：BRL，精度为分）
  manual_review_amount_threshold: number; // 人工审核模式阈值，超过该金额当日总提现需审核（单位：BRL，精度为分）
  manual_review_count_threshold: number; // 人工审核模式阈值，当日提现次数超过该值需审核（单位：次）
  manual_review_single_amount: number; // 人工审核模式阈值，单笔提现金额大于该值需审核（单位：BRL，精度为分）
}

export interface WithdrawalAccount {
  id: number;
  account_name: string;
  account_number: string;
  account_type: string;
  account_type_name: string;
  bank_name: string;
  created_at: number;
  is_default: number;
  phone: string;
  status: number;
  verification_status: number;
}

export interface WithdrawalAccountListParams {
  page: number;
  size: number;
  account_number?: string;
  account_type?: string;
}

export interface WithdrawalAccountListResponse {
  data: WithdrawalAccount[];
  total: number;
}

// 白名单记录
export interface WhitelistRecord {
  id: number;
  user_id?: string;
  agent_id?: string;
  type: 'user' | 'agent';
  user_phone?: string;
  agent_phone?: string;
  // 提现限制可以是一个复杂对象，这里简化为字符串或根据后端实际返回调整
  withdrawal_limit: string;
  add_time: string;
}

// 新增白名单参数
export interface AddWhitelistParams {
  user_type: 'user' | 'agent';
  target_ids: string[];
  withdrawal_limits: {
    daily_count_limit: number;
    daily_amount_limit: number;
    single_amount_limit: number;
    min_amount_limit: number;
  };
}

// 白名单搜索参数
export interface WhitelistSearchParams {
  user_id?: string;
  type?: 'all' | 'user' | 'agent';
  start_time?: string;
  end_time?: string;
  page: number;
  size: number;
}

// 白名单列表响应
export interface WhitelistListResponse {
  list: WhitelistRecord[];
  total: number;
}
