<template>
  <div class="search-wrapper">
    <ElForm :model="props.model">
      <ElRow :gutter="16">
        <ElCol :span="6">
          <ElFormItem label="游戏名称">
            <ElInput v-model="props.model.game_name" placeholder="请输入游戏名称" clearable @keyup.enter="handleSearch" />
          </ElFormItem>
        </ElCol>

        <ElCol :span="6">
          <ElFormItem label="游戏类型">
            <ElSelect v-model="props.model.game_type" placeholder="请选择游戏类型" clearable>
              <ElOption v-for="item in gameTypeOptions" :key="item.value" :label="item.label" :value="item.value">
                <span style="float: left">{{ item.label }}</span>
                <span style="
                    float: right;
                    color: var(--el-text-color-secondary);
                    font-size: 13px;
                  ">
                  {{ item.labelValue }}
                </span>
              </ElOption>
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol :span="6">
          <ElFormItem label="厂商">
            <ElSelect v-model="props.model.manufacturer_id_list" placeholder="请选择厂商" multiple filterable collapse-tags
              collapse-tags-tooltip clearable>
              <ElOption v-for="item in manufacturerList" :key="item.id" :label="item.name" :value="item.id" />
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol :span="6">
          <ElFormItem label="平台">
            <ElSelect v-model="props.model.platforms_list" placeholder="请选择平台" multiple collapse-tags
              collapse-tags-tooltip clearable>
              <ElOption label="H5" value="H5" />
              <ElOption label="PC" value="PC" />
              <ElOption label="H5+PC" value="H5+PC" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="16">
        <ElCol :span="6">
          <ElFormItem label="状态">
            <ElSelect v-model="props.model.status" placeholder="请选择状态" clearable>
              <ElOption label="已上架" :value="1" />
              <ElOption label="已下架" :value="0" />
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol :span="4">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>

        <ElCol :span="14">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElForm, ElFormItem, ElInput, ElSelect, ElButton, ElOption, ElRow, ElCol } from 'element-plus';
import { fetchGetManufacturerDict } from '@/service/api/manufacturer';

interface SearchModel {
  game_name?: string;
  game_type?: string;
  game_uid?: string;
  manufacturer_id_list?: number[];
  platforms_list?: string[];
  status?: number;
}

interface SearchParams extends Omit<SearchModel, 'platforms'> {
  [key: string]: any;
}

interface Props {
  model: SearchModel;
}

interface ManufacturerItem {
  id: number;
  name: string;
}

// 游戏类型选项
const gameTypeOptions = [
  { label: "赠金游戏", value: "BonusSlots", labelValue: "BonusSlots" },
  { label: "热门游戏", value: "hot", labelValue: "hot" },
  { label: "老虎机", value: "slots", labelValue: "slots" },
  { label: "真人", value: "livecasino", labelValue: "livecasino" },
  { label: "卡牌", value: "Cartoes", labelValue: "Pôquer" },
  { label: "捕鱼", value: "Pescaria", labelValue: "Pescaria" },
  { label: "电玩", value: "Coluna", labelValue: "Coluna" },
];

const props = defineProps<Props>();
const emit = defineEmits(['update:model', 'search', 'reset']);

// 厂商列表
const manufacturerList = ref<ManufacturerItem[]>([]);

// 获取厂商列表
const getManufacturerList = async () => {
  try {
    const res = await fetchGetManufacturerDict({});
    if (res.data) {
      manufacturerList.value = res.data.data;
    }
  } catch (error) {
    console.error('获取厂商列表失败:', error);
  }
};

// 初始化获取厂商列表
onMounted(() => {
  getManufacturerList();
});

// 处理搜索参数

// 搜索
function handleSearch() {
  emit('search');
}

// 重置
function handleReset() {
  emit('reset');
}
</script>

<style lang="scss" scoped>
.search-wrapper {
  background-color: #fff;
  padding: 16px;
  border-radius: 4px 4px 0 0;
  --un-shadow: var(--un-shadow-inset) 0 1px 2px 0 var(--un-shadow-color, rgb(0 0 0 / 0.05));
  box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);

  .header-operation {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  :deep(.el-form-item) {
    margin-bottom: 16px;
    width: 100%;

    .el-form-item__content {
      width: 100%;
    }

    .el-select,
    .el-input {
      width: 100%;
    }
  }

  :deep(.el-button + .el-button) {
    margin-left: 8px;
  }
}
</style>
