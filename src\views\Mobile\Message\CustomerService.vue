<template>
  <div class="customer-service">
    <!-- 聊天区域 -->
    <div class="chat-container" ref="chatContainer">
      <!-- 常见问题区域 -->
      <div class="faq-section">
        <div class="section-title">Acho que você quer perguntar</div>
        <div class="questions-list">
          <div
            v-for="(question, index) in commonQuestions"
            :key="index"
            class="question-item"
            @click="selectQuestion(question)"
          >
            {{ question.keyword }}
          </div>
        </div>
      </div>

      <!-- 消息列表 -->
      <div class="messages-list">
        <div
          v-for="(message, index) in messages"
          :key="index"
          :class="['message-item', message.type]"
        >
          <template v-if="message.type === 'received'">
            <div class="message-content">
              {{ message.content }}
            </div>
          </template>
          <template v-else>
            <div class="message-content">
              {{ message.content }}
            </div>
          </template>
        </div>
        <div class="service-tag" @click="goToHumanService">
          <img src="@/assets/images/cliente-icon.png" class="service-icon" />
          <span>Serviço ao cliente humano</span>
        </div>
      </div>
    </div>

    <!-- 底部输入区域 -->
    <div class="input-area">
      <input
        v-model="inputMessage"
        type="text"
        class="message-input"
        placeholder="Por favor insira"
        @keyup.enter="sendMessage"
      />
      <button
        class="send-btn"
        :disabled="!inputMessage.trim()"
        @click="sendMessage"
      >
        Enviar
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import {
  getCustomerQuestions,
  customerServiceGroups,
  sendChatMessage,
} from "@/api/customer-service";
import type { CustomerQuestion } from "@/types/customer-service";

const chatContainer = ref<HTMLElement | null>(null);
const inputMessage = ref("");
const showQuestions = ref(true);
const commonQuestions = ref<CustomerQuestion[]>([]);
const loading = ref(false);
const linkLoading = ref(false);

// 获取常见问题列表
const fetchQuestions = async () => {
  try {
    loading.value = true;
    const response = await getCustomerQuestions();
    console.log(response)
    if (response) {
      commonQuestions.value = response.data;
    }
  } catch (error) {
    console.error("获取问题列表失败:", error);
  } finally {
    loading.value = false;
  }
};

// 获取Telegram群组链接
const fetchLink = async () => {
  try {
    linkLoading.value = true;
    const response = await customerServiceGroups();
    if (response) {
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      if (isIOS) {
        // iOS需要用户交互触发window.open
        setTimeout(() => {
          window.location.href = response[0].invitation_link;
        }, 100);
      } else {
        window.open(response[0].invitation_link,'_self');
      }
    }
  } catch (error) {
    console.error("获取人工客服链接失败:", error);
  } finally {
    linkLoading.value = false;
  }
};

const messages = ref([
  {
    type: "received",
    content: "Olá! Como posso ajudar você hoje?",
    time: "",
  },
]);

const scrollToBottom = async () => {
  await nextTick();
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
  }
};

const selectQuestion = (question: CustomerQuestion) => {
  showQuestions.value = false;

  // 添加用户发送的问题
  messages.value.push({
    content: question.keyword,
    type: "sent",
    time: new Date().toLocaleTimeString("pt-BR", {
      hour: "2-digit",
      minute: "2-digit",
    }),
  });

  // 添加客服回复的答案
  setTimeout(() => {
    messages.value.push({
      content: question.response,
      type: "received",
      time: new Date().toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit",
      }),
    });
    scrollToBottom();
  }, 500);
};

const sendMessage = async () => {
  if (!inputMessage.value.trim()) return;

  messages.value.push({
    type: "sent",
    content: inputMessage.value,
    time: new Date().toLocaleTimeString("pt-BR", {
      hour: "2-digit",
      minute: "2-digit",
    }),
  });

  inputMessage.value = "";
  showQuestions.value = false;

  try {
    // 发送消息到服务器
    const response = await sendChatMessage({ message: inputMessage.value });
    if (response) {
      // 添加客服回复
      messages.value.push({
        content: response.reply,
        type: "received",
        time: response.timestamp,
      });
    }
  } catch (error) {
    //添加错误提示
    messages.value.push({
      content:
        "Desculpe, ocorreu um erro ao processar sua mensagem. Por favor, tente novamente.",
      type: "received",
      time: new Date().toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit",
      }),
    });
  }

  scrollToBottom();
};

// 跳转到人工客服
const goToHumanService = async () => {
  try {
    await fetchLink();
  } catch (error) {
    console.error("获取人工客服链接失败:", error);
  }
};

onMounted(() => {
  fetchQuestions();
  scrollToBottom();
});
</script>

<style lang="scss" scoped>
.customer-service {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.cs-header {
  height: 56px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  background: #1e2237;
  position: relative;

  .back-btn {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 24px;
      height: 24px;
    }
  }

  .title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    font-size: 18px;
    font-weight: 500;
  }
}

.chat-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  -webkit-overflow-scrolling: touch;

  .faq-section {
    background: #724860;
    border-radius: 12px;
    margin-bottom: 20px;

    .section-title {
      color: #fff;
      font-size: 16px;
      padding: 8px 16px;
      opacity: 0.8;
    }
  }

  .questions-list {
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    background: #fff;
    .question-item {
      padding: 4px 16px;
      border-radius: 8px;
      color: #333;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      gap: 8px;

      &:active {
        opacity: 0.8;
      }
      &::before {
        content: "";
        display: block;
        width: 100%;
        width: 2px;
        height: 2px;
        background: #dbdbdb;
      }
    }
  }
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 60px;
  .service-tag {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #ff206e;
    padding: 6px 12px;
    border-radius: 20px;

    .service-icon {
      width: 16px;
      height: 16px;
    }

    span {
      color: #fff;
      font-size: 12px;
    }
  }

  .message-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-width: 85%;

    &.received {
      align-self: flex-start;
      .message-content {
        background: #fff;
        color: #333;
        border-radius: 0 12px 12px 12px;
      }
    }

    &.sent {
      align-self: flex-end;

      .message-content {
        background: #ff206e;
        color: #fff;
        border-radius: 12px 0 12px 12px;
      }
    }

    .message-content {
      padding: 12px 16px;
      font-size: 14px;
      line-height: 1.4;
    }
  }
}

.input-area {
  position: fixed;
  bottom: 56px;
  width: 100%;
  padding: 12px 16px;
  background: #1e2237;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 12px;

  .message-input {
    flex: 1;
    height: 40px;
    background: #fff;
    border: none;
    border-radius: 20px;
    padding: 0 16px;
    font-size: 14px;
    outline: none;
    color: #333;

    &::placeholder {
      color: #999;
    }
  }

  .send-btn {
    height: 40px;
    padding: 0 20px;
    border: none;
    border-radius: 20px;
    background: #ff206e;
    color: #fff;
    font-size: 14px;
    font-weight: 500;

    &:disabled {
      opacity: 0.5;
    }

    &:active {
      opacity: 0.8;
    }
  }
}
</style>
