import { ref } from "vue";
// import { useStore } from 'vuex';
import websocketService from "./websocket";
import { getToken, getUserInfo } from "./auth";
import { generateDeviceFingerprint } from "./device";
import { md5Encrypt } from ".";

/**
 * WebSocket连接保持器
 * 确保在token有效且用户信息存在时保持WebSocket连接和心跳
 */
class WebSocketKeeper {
  private keeperTimer: NodeJS.Timeout | null = null;
  private checkInterval = 30000; // 30秒检查一次
  private isActive = false;
  private lastTokenCheck = "";
  private lastUserUuid = "";

  // 响应式状态
  public readonly isKeeping = ref(false);
  public readonly lastCheckTime = ref(0);

  /**
   * 启动WebSocket连接保持
   */
  public start(): void {
    if (this.isActive) {
      console.log("WebSocket连接保持器已在运行");
      return;
    }

    this.isActive = true;
    this.isKeeping.value = true;
    console.log("启动WebSocket连接保持器");

    // 立即执行一次检查
    this.checkAndMaintainConnection();

    // 启动定时检查
    this.keeperTimer = setInterval(() => {
      this.checkAndMaintainConnection();
    }, this.checkInterval);

    console.log(`WebSocket连接保持器已启动，检查间隔: ${this.checkInterval}ms`);
  }

  /**
   * 停止WebSocket连接保持
   */
  public stop(): void {
    if (!this.isActive) {
      return;
    }

    this.isActive = false;
    this.isKeeping.value = false;
    console.log("停止WebSocket连接保持器");

    if (this.keeperTimer) {
      clearInterval(this.keeperTimer);
      this.keeperTimer = null;
    }

    // 重置状态
    this.lastTokenCheck = "";
    this.lastUserUuid = "";
  }

  /**
   * 检查并维护WebSocket连接
   */
  private async checkAndMaintainConnection(): Promise<void> {
    this.lastCheckTime.value = Date.now();

    try {
      // 检查基本条件
      const token = getToken();
      let userInfo;

      try {
        // const store = useStore();
        userInfo = getUserInfo()?.user;
      } catch (error) {
        console.warn("WebSocket保持器: 无法获取store，跳过检查");
        return;
      }

      // 检查token和用户信息是否有效
      if (!token || !userInfo?.uuid) {
        console.log("WebSocket保持器: Token无效或用户信息不完整，跳过维护");
        return;
      }
      // 检查WebSocket状态
      const isConnected = websocketService.getIsConnected();
      const wsState = websocketService.getState();
      const heartbeatStatus = websocketService.getHeartbeatStatus();
      // 检查是否有变化
      const currentTokenCheck = token.substring(0, 10); // 只检查token的前10位
      const currentUserUuid = userInfo.uuid;

      const hasTokenChanged = this.lastTokenCheck !== currentTokenCheck;
      const hasUserChanged = this.lastUserUuid !== currentUserUuid;

      if (
        (hasTokenChanged || hasUserChanged) &&
        !isConnected &&
        !websocketService.isRetry.value
      ) {
        console.log("WebSocket保持器: 检测到token或用户变化，重新建立连接");
        this.lastTokenCheck = currentTokenCheck;
        this.lastUserUuid = currentUserUuid;

        // 强制重连
        await websocketService.forceReconnect();
        return;
      }

      console.log("WebSocket保持器状态检查:", {
        isConnected,
        wsState,
        heartbeatRunning: heartbeatStatus.isRunning,
        heartbeatHealthy: heartbeatStatus.isHealthy,
        lastHeartbeatResponse: heartbeatStatus.lastResponse,
      });

      // 更智能的连接状态判断，忽略1006等临时状态
      const isReallyDisconnected = !isConnected && wsState === 3; // 只在真正关闭时认为断开
      const isConnecting = wsState === 0; // 连接中状态
      const needsHeartbeatFix =
        isConnected &&
        wsState === 1 &&
        (!heartbeatStatus.isRunning || !heartbeatStatus.isHealthy);

      if (isReallyDisconnected) {
        console.log("WebSocket保持器: 连接真正断开，尝试重连");
        await this.reconnectWebSocket();
      } else if (isConnecting) {
        console.log("WebSocket保持器: 连接建立中，等待完成");
        // 连接中状态，等待连接完成
      } else if (needsHeartbeatFix) {
        console.log("WebSocket保持器: 心跳异常，尝试修复");
        await this.fixHeartbeat();
      } else {
        console.log("WebSocket保持器: 连接和心跳正常");
      }
    } catch (error) {
      console.error("WebSocket保持器检查失败:", error);
    }
  }

  /**
   * 重连WebSocket
   */
  private async reconnectWebSocket(): Promise<void> {
    try {
      console.log("WebSocket保持器: 开始重连");
      websocketService.resetReconnectAttempts();
      await websocketService.forceReconnect();

      // 等待连接稳定后检查心跳
      setTimeout(() => {
        this.ensureHeartbeat();
      }, 3000);

      console.log("WebSocket保持器: 重连成功");
    } catch (error) {
      console.error("WebSocket保持器: 重连失败", error);
    }
  }

  /**
   * 修复心跳
   */
  private async fixHeartbeat(): Promise<void> {
    try {
      console.log("WebSocket保持器: 修复心跳");

      // 停止当前心跳
      websocketService.stopHeartbeatManually();

      // 等待一下再重新启动
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 发送一次心跳来重新启动心跳机制
      const success = websocketService.startHeartbeatManually();
      if (success) {
        console.log("WebSocket保持器: 心跳修复成功");
      } else {
        console.log("WebSocket保持器: 心跳修复失败，尝试重连");
        await this.reconnectWebSocket();
      }
    } catch (error) {
      console.error("WebSocket保持器: 心跳修复失败", error);
    }
  }

  /**
   * 确保心跳运行
   */
  private ensureHeartbeat(): void {
    const heartbeatStatus = websocketService.getHeartbeatStatus();
    const isConnected = websocketService.getIsConnected();

    if (isConnected && !heartbeatStatus.isRunning) {
      console.log("WebSocket保持器: 启动心跳");
      websocketService.startHeartbeatManually();
    }
  }

  /**
   * 手动触发检查
   */
  public async manualCheck(): Promise<void> {
    console.log("WebSocket保持器: 手动触发检查");
    await this.checkAndMaintainConnection();
  }

  /**
   * 获取状态
   */
  public getStatus(): {
    isActive: boolean;
    isKeeping: boolean;
    lastCheckTime: number;
    checkInterval: number;
  } {
    return {
      isActive: this.isActive,
      isKeeping: this.isKeeping.value,
      lastCheckTime: this.lastCheckTime.value,
      checkInterval: this.checkInterval,
    };
  }

  /**
   * 设置检查间隔
   */
  public setCheckInterval(interval: number): void {
    if (interval < 10000) {
      console.warn("WebSocket保持器: 检查间隔不能小于10秒");
      return;
    }

    this.checkInterval = interval;
    console.log(`WebSocket保持器: 检查间隔已设置为 ${interval}ms`);

    // 如果正在运行，重启定时器
    if (this.isActive) {
      this.stop();
      this.start();
    }
  }
}

// 创建单例实例
const websocketKeeper = new WebSocketKeeper();

export default websocketKeeper;
export { WebSocketKeeper };
