<script setup lang="ts">
import { ref } from "vue";
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElSelect,
  ElOption,
  ElDatePicker,
} from "element-plus";

interface Props {
  model: {
    user_id?: string | number;
    activity_id?: string | number;
    phone?: string;
    username?: string;
    nickname?: string;
    bonus_type?: string;
    status?: string;
  };
}

interface Emits {
  (e: "update:model", value: Props["model"]): void;
  (e: "search"): void;
  (e: "reset"): void;
}
const dateRange = ref();
const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const bonusTypeOptions = [
  { label: "全部", value: "" },
  { label: "现金", value: "1" },
  { label: "赠金", value: "2" },
  { label: "打码提现", value: "3" },
];

const statusOptions = [
  { label: "全部", value: "" },
  { label: "已完成", value: "1" },
  { label: "未完成", value: "2" },
  { label: "进行中", value: "3" },
];

const handleSearch = () => {
  emit("search");
};

const handleReset = () => {
  dateRange.value = undefined;
  emit("reset");
};
</script>

<template>
  <div class="search-wrapper">
    <ElForm inline>
      <ElRow :gutter="16">
        <ElCol :span="6">
          <ElFormItem label="用户ID">
            <ElInput
              v-model="model.user_id"
              placeholder="请输入用户ID"
              clearable
              @keyup.enter="handleSearch"
            />
          </ElFormItem>
        </ElCol>
        <!-- <ElCol :span="6">
          <ElFormItem label="活动ID">
            <ElInput v-model="model.activity_id" placeholder="请输入活动ID" clearable @keyup.enter="handleSearch" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="手机号">
            <ElInput v-model="model.phone" placeholder="请输入手机号" clearable @keyup.enter="handleSearch" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="用户名">
            <ElInput v-model="model.username" placeholder="请输入用户名" clearable @keyup.enter="handleSearch" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="昵称">
            <ElInput v-model="model.nickname" placeholder="请输入昵称" clearable @keyup.enter="handleSearch" />
          </ElFormItem>
        </ElCol> -->
        <ElCol :span="6">
          <ElFormItem label="奖金类型">
            <ElSelect
              v-model="model.bonus_type"
              placeholder="请选择奖金类型"
              clearable
            >
              <ElOption
                v-for="item in bonusTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="状态">
            <ElSelect v-model="model.status" placeholder="请选择状态" clearable>
              <ElOption
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="时间">
            <ElDatePicker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="x"
              @change="
                (val: [string, string] | null) => {
                  if (val) {
                    model.start_time = val[0];
                    model.end_time = val[1];
                  } else {
                    model.start_time = undefined;
                    model.end_time = undefined;
                  }
                }
              "
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<style lang="scss" scoped>
.search-card {
  :deep(.el-form--inline) {
    .el-form-item {
      margin-right: 16px;
      margin-bottom: 16px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
