<template>
  <v-container fluid class="game-list-container px-2 py-4" max-width="940">
    <div class="content-wrapper d-flex ga-4">
      <div class="game-list-class" v-if="gameClass !== '1'">
        <div
          v-for="item in manufacturers"
          :key="item.id"
          class="filter-chip"
          :class="
            item.id == selectedManufacturer ? `filter-active-${gameClass}` : ''
          "
          @click="handleManufacturerSelect(item.id)"
        >
          <template v-if="item.icon">
            <img :src="item.icon" width="108" height="100%" loading="lazy" />
          </template>
          <template v-else>
            {{ item.name }}
          </template>
        </div>
      </div>
      <!-- Header Section -->
      <div class="content-section">
        <div
          class="d-flex justify-space-between align-center content-section-title"
          :class="`content-title-${gameClass}`"
        >
          <div>{{ gameClassLabel[gameClass] || "" }}</div>
          <!-- Search and Filter Bar -->
          <div class="filter-section">
            <v-text-field
              v-model="searchQuery"
              placeholder="Digite o name do jogo"
              variant="outlined"
              density="comfortable"
              hide-details
              class="search-input"
              @input="debounceSearch"
            >
              <template #prepend-inner>
                <img src="@/assets/images/query-icon.png" class="search-icon" />
              </template>
            </v-text-field>
          </div>
        </div>
        <div class="game-grid mt-4">
          <div
            v-for="game in games"
            :key="game.id"
            cols="6"
            sm="4"
            md="3"
            lg="3"
            class="game-column"
          >
            <v-hover v-slot="{ isHovering, props }">
              <v-card
                v-bind="props"
                :elevation="isHovering ? 8 : 2"
                class="game-card"
                :class="{ 'no-class': ['1'].includes(gameClass) }"
                @click="handleGameClick(game)"
              >
                <div class="game-image-wrapper">
                  <v-img
                    :src="game.icon"
                    class="game-image"
                    :class="{ 'image-hover': isHovering }"
                    loading="lazy"
                    cover
                    aspect-ratio="0.84"
                    lazy-src="https://via.placeholder.com/40x40?text=..."
                    :alt="game.game_name"
                  >
                    <!-- <template v-slot:placeholder>
                      <v-row
                        class="fill-height ma-0"
                        align="center"
                        justify="center"
                      >
                        <v-progress-circular
                          indeterminate
                          color="primary"
                        ></v-progress-circular>
                      </v-row>
                    </template> -->
                  </v-img>
                  <div v-if="isHovering" class="play-overlay">
                    <v-btn
                      icon="mdi-play-circle"
                      variant="text"
                      size="x-large"
                      color="white"
                    ></v-btn>
                  </div>
                </div>

                <v-card-text class="pa-3">
                  <div class="game-title">{{ game.game_name }}</div>
                  <div class="game-provider">
                    {{ game.manufacturer }}
                  </div>
                </v-card-text>
              </v-card>
            </v-hover>
          </div>
        </div>

        <!-- 加载更多 -->
        <div v-if="hasMore" class="text-center pa-4">
          <v-btn :loading="loading" @click="loadMore" class="more-btn">
            Carregar mais
          </v-btn>
        </div>
      </div>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick, onUnmounted } from "vue";
import { getGameList, getManufacturer } from "@/api/game";
import type { GameItem, ApiResponse, PaginationData } from "@/types/game";
import { useRoute, useRouter } from "vue-router";

// 厂家类型定义
interface Manufacturer {
  id: number;
  name: string;
  icon?: string;
}

const route = useRoute();
const router = useRouter();

// 状态变量
const searchQuery = ref("");
const games = ref<GameItem[]>([]);
const pageNo = ref(1);
const pageSize = ref(30);
const total = ref(0);
const loading = ref(false);
const isInitialized = ref(false);
const gameClass = ref("");
const manufacturers = ref<Manufacturer[]>([]);
const selectedManufacturer = ref<number>(0);

// 缓存机制
const gameCache = new Map<
  string,
  { data: GameItem[]; total: number; timestamp: number }
>();
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

// 图片预加载缓存
const imageCache = new Set<string>();

// 监听路由变化
watch(
  route,
  async (newVal) => {
    if (newVal.query?.id) {
      gameClass.value = newVal.query.id as string;
      if (isInitialized.value) {
        await loadManufacturers();
        fetchGames();
      }
    }
  },
  { immediate: true }
);

// 计算是否有更多数据
const hasMore = computed(() => games.value?.length < total.value);

const gameClassLabel: Record<string, string> = {
  "1": "Hot",
  "2": "Slots",
  "3": "Bônus Slots",
  "4": "Cassino ao Vivo",
  "7": "Cartões",
  "8": "Pescaria",
  "9": "Coluna",
};

// 生成缓存键
const getCacheKey = (params: any) => {
  return `${params.game_type}_${params.manufacturer_id}_${params.search_key}_${params.page_no}`;
};

// 检查缓存
const getFromCache = (cacheKey: string) => {
  const cached = gameCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached;
  }
  return null;
};

// 设置缓存
const setCache = (cacheKey: string, data: GameItem[], total: number) => {
  gameCache.set(cacheKey, {
    data,
    total,
    timestamp: Date.now(),
  });
};

// 获取游戏列表
const fetchGames = async (isLoadMore = false) => {
  if (loading.value) return;

  const startTime = performance.now();

  const params = {
    page_no: pageNo.value,
    page_size: pageSize.value,
    game_type: gameClass.value,
    manufacturer_id: selectedManufacturer.value.toString(),
    search_key: searchQuery.value,
  };
  if (gameClass.value == "1") {
    delete params.manufacturer_id;
  }
  // 检查缓存（仅在非搜索和非加载更多时使用缓存）
  if (!isLoadMore && !searchQuery.value) {
    const cacheKey = getCacheKey(params);
    const cached = getFromCache(cacheKey);
    if (cached) {
      games.value = cached.data;
      total.value = cached.total;
      return;
    }
  }

  loading.value = true;
  try {
    const res = await getGameList(params);
    console.log(res);
    if (res.data) {
      const gameData = res.data;
      const gameTotal = res.total;

      if (isLoadMore) {
        games.value.push(...gameData);
      } else {
        games.value = gameData;
      }
      total.value = gameTotal;
      // 预加载图片
      if (gameData && gameData.length > 0) {
        preloadImages(gameData);
      }

      // 设置缓存
      if (!isLoadMore && !searchQuery.value) {
        const cacheKey = getCacheKey(params);
        setCache(cacheKey, gameData, gameTotal);
      }
    }
  } catch (error) {
    console.error("Falha ao buscar jogos:", error);
  } finally {
    loading.value = false;
    const endTime = performance.now();
    console.log(`游戏列表加载耗时: ${endTime - startTime}ms`);
  }
};

// 加载更多
const loadMore = () => {
  if (loading.value || !hasMore.value) return;
  pageNo.value++;
  fetchGames(true);
};

// 优化的防抖函数
const debounceSearch = (() => {
  let timeoutId: ReturnType<typeof setTimeout>;
  let lastSearchQuery = "";

  return () => {
    clearTimeout(timeoutId);

    // 如果搜索内容没有变化，不执行搜索
    if (lastSearchQuery === searchQuery.value) {
      return;
    }

    lastSearchQuery = searchQuery.value;

    timeoutId = setTimeout(() => {
      pageNo.value = 1;
      fetchGames();
    }, 500); // 减少防抖时间从1000ms到500ms
  };
})();

// 游戏点击处理
const handleGameClick = (game: GameItem) => {
  router.push(`/game/${game.game_uid}?uuid=` + game.id);
};

// 加载厂家列表
const loadManufacturers = async () => {
  try {
    const response = await getManufacturer({
      manufacturer_type: gameClass.value,
    });
    if (response && Array.isArray(response)) {
      manufacturers.value = response;
      if (manufacturers.value.length > 0) {
        selectedManufacturer.value = manufacturers.value[0].id;
      }
    }
  } catch (error) {
    console.error("Failed to load manufacturers:", error);
  }
};

// 选择厂家
const handleManufacturerSelect = (manufacturer: number) => {
  selectedManufacturer.value = manufacturer;
  pageNo.value = 1; // 重置页码
  // 重新加载游戏列表
  fetchGames();
};

// 预加载图片
const preloadImage = (src: string) => {
  if (imageCache.has(src)) return;

  const img = new Image();
  img.onload = () => {
    imageCache.add(src);
  };
  img.src = src;
};

// 批量预加载图片
const preloadImages = (games: GameItem[]) => {
  games.forEach((game) => {
    if (game.icon) {
      preloadImage(game.icon);
    }
  });
};

// 清理缓存
const clearCache = () => {
  gameCache.clear();
  imageCache.clear();
};

onMounted(async () => {
  if (route.query.id) {
    gameClass.value = route.query.id as string;
  } else {
    return;
  }
  await loadManufacturers();
  await nextTick();
  fetchGames();
  isInitialized.value = true;
});

// 组件卸载时清理缓存
onUnmounted(() => {
  clearCache();
});
</script>

<style lang="scss" scoped>
.game-list-container {
  .more-btn {
    background: linear-gradient(0deg, #c9b737, #2abb27);
    border-radius: 15px;
  }
  min-height: calc(100vh - 64px);
  .content-wrapper {
    .game-list-class {
      width: 108px;
      height: auto;
      overflow: hidden;
      .filter-chip {
        width: 108px;
        height: 60px;
        line-height: 20px;
        padding: 0 6px;
        text-align: center;
        border-radius: 8px;
        background: #303141;
        margin-bottom: 8px;
        cursor: pointer;
        border: 1px solid #303141;
        word-wrap: break-word;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .filter-active-1 {
        background: linear-gradient(35deg, #7c3f5b, #db5b96, #7c3f5b);
        border-color: #b00080;
      }
      .filter-active-2 {
        background: linear-gradient(35deg, #e6870b, #e4ba74, #e6870b);
        border-color: #b74e00;
      }
      .filter-active-3 {
        background: linear-gradient(35deg, #1768cd, #3f82cc, #1768cd);
        border-color: #003e9c;
      }
      .filter-active-4 {
        background: linear-gradient(35deg, #17407e, #2063a3, #17407e);
        border-color: #0049a3;
      }
      .filter-active-7 {
        background: linear-gradient(35deg, #06a57c, #0cc393, #06a57c);
        border-color: #00690f;
      }
      .filter-active-9 {
        background: linear-gradient(35deg, #732448, #d13680, #732448);
        border-color: #d13680;
      }
      .filter-active-8 {
        background: linear-gradient(35deg, #732448, #d13680, #732448);
        border-color: #d13680;
      }
    }
    .content-section {
      .game-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 16px;
      }
      flex: 1;
      .content-section-title {
        height: 44px;
        line-height: 44px;
        padding-left: 25px;
        font-weight: 400;
        font-size: 20px;
        color: #ffffff;
        border-radius: 8px;
        .search-input {
          width: 318px;
          height: 36px;
          background: #1c1d2f;
          margin-right: 4px;
          border-radius: 8px;
          :deep() {
            .v-field__field {
              height: 36px;
            }
            input {
              padding: 0 8px;
              height: 36px !important;
              line-height: 36px !important;
              min-height: 36px;
            }
            .search-icon {
              width: 19px;
              height: auto;
            }
            .v-field__outline {
              display: none;
            }
          }
        }
      }
      .content-title-1 {
        background: linear-gradient(-87deg, #3613b8, #87365b);
      }
      .content-title-2 {
        background: linear-gradient(-87deg, #e6870b, #e4bc78);
      }
      .content-title-3 {
        background: linear-gradient(-87deg, #1684ff, #4ba7fe);
      }
      .content-title-4 {
        background: linear-gradient(-87deg, #2063a3, #17407e);
      }
      .content-title-7 {
        background: linear-gradient(-87deg, #09ae83, #0cc393);
      }
      .content-title-9 {
        background: linear-gradient(-87deg, #3b1661, #87365b);
      }
      .content-title-8 {
        background: linear-gradient(-87deg, #3b1661, #87365b);
      }
    }
  }
}

.game-card {
  width: auto;
  max-width: 146px;
  position: relative;
  background: rgba(31, 41, 55, 0.7);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  will-change: transform;
  transform: translateZ(0); // 启用硬件加速
  &:hover {
    transform: translateY(-8px) translateZ(0);
  }
}
.no-class {
  max-width: 170px;
}

.game-image-wrapper {
  position: relative;
  overflow: hidden;

  .game-image {
    transition: transform 0.3s ease;
    will-change: transform;
    transform: translateZ(0); // 启用硬件加速
  }

  .image-hover {
    transform: scale(1.05) translateZ(0);
  }
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;

  .v-btn {
    transform: scale(0.8);
    transition: transform 0.3s ease;
  }

  &:hover {
    opacity: 1;

    .v-btn {
      transform: scale(1);
    }
  }
}

.game-title {
  color: white;
  font-size: 0.95rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;
}

.game-provider {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 4px;
}

@media (max-width: 600px) {
  .game-column {
    padding: 8px;
  }

  .game-card {
    margin-bottom: 0;
  }

  .game-title {
    font-size: 0.85rem;
  }

  .game-provider {
    font-size: 0.75rem;
  }
}
</style>
