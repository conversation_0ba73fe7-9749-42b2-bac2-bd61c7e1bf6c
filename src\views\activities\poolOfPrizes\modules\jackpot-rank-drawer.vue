<template>
  <ElDrawer
    :model-value="visible"
    title="获奖者列表"
    size="540px"
    @close="handleClose"
  >
    <div v-loading="loading">
      <div class="jackpot-info">
        <div class="jackpot-title">{{ config?.jackpot_name }}</div>
        <div class="jackpot-meta">
          <div>开始时间：{{ formatDate(config?.start_time) }}</div>
          <div>结束时间：{{ formatDate(config?.end_time) }}</div>
          <div>
            奖池总额：<span class="jackpot-amount"
              >R$ {{ formatAmount((leaderboardBonusCount || 0) / 100) }}</span
            >
          </div>
        </div>
      </div>
      <div class="winner-list-title">获奖者名单</div>
      <div class="winner-list">
        <div
          v-for="item in leaderboard"
          :key="item.rank"
          class="winner-item"
          :class="{
            top1: item.rank === 1,
            top2: item.rank === 2,
            top3: item.rank === 3,
          }"
        >
          <div class="rank-badge" :class="'rank-' + item.rank">
            <span v-if="item.rank <= 3">{{ item.rank }}</span>
            <span v-else>{{ item.rank }}</span>
          </div>
          <!-- <div class="user-avatar">
          <img src="/default-avatar.png" alt="头像" />
        </div> -->
          <div class="user-info">
            <div class="user-name">
              用户{{ item.user_name }}
              <span class="user-id">(ID: {{ item.uuid }})</span>
            </div>
            <div class="user-points">积分: {{ item.points }}</div>
          </div>
          <div class="user-bonus">R$ {{ formatAmount(item.bonus / 100) }}</div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="footer-btns">
        <ElButton @click="handleClose">关闭</ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { fetchJackpotRankList } from "@/service/api/jackpot";
import moment from "moment";
import { getBrazilDate } from "@/utils/format";

interface Config {
  jackpot_name: string;
  start_time: number;
  end_time: number;
  fixed_bonus: number;
}
interface LeaderboardItem {
  rank: number;
  uuid: number;
  user_name: string;
  points: number;
  bonus: number;
}

const props = defineProps<{ visible: boolean; jackpotId: number | string }>();
const emit = defineEmits(["update:visible"]);

const config = ref<Config>({
  jackpot_name: "",
  start_time: 0,
  end_time: 0,
  fixed_bonus: 0,
});
const leaderboard = ref<LeaderboardItem[]>([]);
const loading = ref(true);

watch(
  () => props.visible,
  (val) => {
    if (val && props.jackpotId) {
      fetchData();
    } else {
      loading.value = true;
      leaderboard.value = [];
      config.value = {
        jackpot_name: "",
        start_time: 0,
        end_time: 0,
        fixed_bonus: 0,
      };
    }
  },
);
const leaderboardBonusCount = ref(); // 获奖总额
async function fetchData() {
  loading.value = true;
  try {
    const { response } = await fetchJackpotRankList({ id: props.jackpotId });
    if (response?.data?.data) {
      config.value = response.data?.data?.config;
      leaderboard.value = response.data?.data?.leaderboard || [];
      leaderboardBonusCount.value =
        response.data?.data?.leaderboard_bonus_count;
    } else {
      ElMessage.error("数据获取失败");
    }
  } finally {
    loading.value = false;
  }
}

function handleClose() {
  emit("update:visible", false);
}

function formatDate(ts: number) {
  if (!ts) return "";
  return moment(getBrazilDate(ts)).format("YYYY-MM-DD");
}
function formatAmount(val: number) {
  return Number(val);
}
</script>

<style scoped>
.jackpot-info {
  margin-bottom: 18px;
}

.jackpot-title {
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 6px;
}

.jackpot-meta {
  color: #666;
  font-size: 14px;
  line-height: 22px;
}

.jackpot-amount {
  color: #ff9800;
  font-weight: bold;
  font-size: 16px;
}

.winner-list-title {
  font-weight: bold;
  font-size: 16px;
  margin: 18px 0 10px 0;
}

.winner-list {
  margin-bottom: 20px;
}

.winner-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 15px;
}

.rank-badge {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e0e0e0;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  margin-right: 16px;
}

.rank-1 {
  background: #ffd700;
  color: #fff;
}

.rank-2 {
  background: #bdbdbd;
  color: #fff;
}

.rank-3 {
  background: #cd7f32;
  color: #fff;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 500;
  color: #333;
}

.user-id {
  color: #888;
  font-size: 13px;
  margin-left: 4px;
}

.user-points {
  color: #888;
  font-size: 13px;
}

.user-bonus {
  color: #ff9800;
  font-weight: bold;
  font-size: 16px;
  min-width: 90px;
  text-align: right;
}

.footer-btns {
  display: flex;
  justify-content: flex-end;
  padding: 8px 0 0 0;
}
</style>
