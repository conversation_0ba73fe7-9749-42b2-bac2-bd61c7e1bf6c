<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-07 14:00:23
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-06-12 16:03:44
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\views\activities\pdd\modules\recharge-list-drawer.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import { ref, watch } from "vue";
import { useI18n } from "vue-i18n";
import { getPddRechargeList } from "@/service/api/pdd";
import { getBrazilDate } from "@/utils/format";
import moment from "moment";

const { t } = useI18n();

const props = defineProps<{
  visible: boolean;
  activityId?: number;
}>();

const emit = defineEmits<{
  (e: "update:visible", visible: boolean): void;
}>();

const internalVisible = ref(props.visible);
const loading = ref(false);
const data = ref([]);
const pagination = ref({
  total: 0,
  currentPage: 1,
  pageSize: 5,
  "current-change": (page: number) => {
    pagination.value.currentPage = page;
    getData();
  },
  "size-change": (size: number) => {
    pagination.value.pageSize = size;
    pagination.value.currentPage = 1;
    getData();
  },
});

const columns = [
  { type: "index", label: t("common.index"), minWidth: 60 },
  { prop: "user_id", label: "用户ID", minWidth: 100 },
  { prop: "created_at", label: "创建时间", minWidth: 160,
    formatter: (row: any) => {
      return row.created_at
        ? moment(getBrazilDate(row.created_at)).format("YYYY-MM-DD HH:mm:ss")
        : "-";
    },
  },
  { prop: "order_id", label: "订单号", minWidth: 120 },
  { prop: "pay_method", label: "支付方式", minWidth: 120 },
  { prop: "payment_account", label: "支付账号", minWidth: 120 },
  { prop: "recharge_amount", label: "充值金额 (R$)", minWidth: 120 },
  { prop: "recharge_count", label: "充值次数", minWidth: 100 },
];

const getData = async () => {
  if (!props.activityId) return;

  loading.value = true;
  try {
    const { data: response } = await getPddRechargeList(props.activityId, {
      page: pagination.value.currentPage,
      size: pagination.value.pageSize,
    });

    if (response?.data) {
      data.value = response.data.result || [];
      pagination.value.total = pagination.value.total ? pagination.value.total:response.data.count || 0;
    }
  } catch (error) {
    console.error("Failed to fetch recharge list:", error);
  } finally {
    loading.value = false;
  }
};

watch(
  () => props.visible,
  (newVal) => {
    internalVisible.value = newVal;
    if (newVal && props.activityId) {
      getData();
    } else {
      data.value = [];
      pagination.value.currentPage = 1;
      pagination.value.total = 0;
    }
  },
);

watch(internalVisible, (newVal) => {
  emit("update:visible", newVal);
});

const handleClose = () => {
  internalVisible.value = false;
};
</script>

<template>
  <ElDialog
    v-model="internalVisible"
    title="充值人员列表"
    width="80%"
    @close="handleClose"
  >
    <div class="h-[500px]">
      <ElTable v-loading="loading" height="100%" :data="data">
        <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
      </ElTable>
    </div>
    <div class="mt-20px flex justify-start">
      <ElPagination
        v-if="pagination.total"
        layout="total,prev,pager,next,sizes"
        :total="pagination.total"
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        @current-change="pagination['current-change']"
        @size-change="pagination['size-change']"
      />
    </div>
  </ElDialog>
</template>

<style scoped>
/* 可以根據需要添加樣式 */
</style>
