<template>
  <ElDialog
    v-model="drawerVisibleLocal"
    :title="drawerTitle"
    width="500px"
    @close="handleClose"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      label-position="right"
    >
      <ElFormItem label="WhatsApp账号" prop="account_id">
        <ElInput
          v-model="formData.account_id"
          placeholder="请输入WhatsApp账号"
        />
      </ElFormItem>
      <ElFormItem label="手机号" prop="phone_number">
        <ElInput
          v-model="formData.phone_number"
          placeholder="请输入手机号"
        />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <span class="dialog-footer">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit">保存</ElButton>
      </span>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
import { computed, ref, watch, reactive } from "vue";
import { ElMessage, ElForm, ElInput, ElButton, ElDialog, ElFormItem } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { type TableOperateType } from "@/hooks/common/table";

interface Props {
  visible: boolean;
  operateType: TableOperateType;
  rowData?: ApiTelegram.WhatsAppAccount | null;
}

interface Emits {
  (e: "update:visible", visible: boolean): void;
  (e: "submitted", data: ApiTelegram.WhatsAppAccountForm, operateType: TableOperateType): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const formData = reactive<ApiTelegram.WhatsAppAccountForm>({
  account_id: "",
  phone_number: "",
});

const formRules: FormRules = {
  account_id: [{ required: true, message: "请输入WhatsApp账号", trigger: "blur" }],
  phone_number: [{ required: true, message: "请输入手机号", trigger: "blur" }],
};

const drawerTitle = computed(() => {
  if (props.operateType === "add") {
    return "添加WhatsApp客服";
  } else if (props.operateType === "edit") {
    return "编辑WhatsApp客服";
  }
  return "";
});

const drawerVisibleLocal = computed({
  get() {
    return props.visible;
  },
  set(value: boolean) {
    emit("update:visible", value);
  },
});

watch(
  () => props.visible,
  (val) => {
    if (val) {
      resetForm();
      if (props.operateType === "edit" && props.rowData) {
        Object.assign(formData, props.rowData);
      }
    }
  }
);

async function handleSubmit() {
  const isValid = await formRef.value?.validate();
  if (isValid) {
    try {
      // 这里模拟保存数据的逻辑
      // 实际开发中，这里会调用后端 API
      // 为了演示，我们直接在父组件的 mock 数据中添加/更新
      ElMessage.success("保存成功");
      emit("submitted", formData, props.operateType);
      handleClose();
    } catch (error) {
      console.error("保存失败:", error);
      ElMessage.error("保存失败");
    }
  }
}

function handleClose() {
  emit("update:visible", false);
}

function resetForm() {
  formRef.value?.resetFields();
  Object.assign(formData, {
    account_id: "",
    phone_number: "",
  });
}

// 定义 API 类型，以避免 TypeScript 错误
declare namespace ApiTelegram {
  interface WhatsAppAccount {
    id?: number;
    account_id: string;
    account_name: string;
    phone_number: string;
    added_time: string;
    status: number; // 1: 正常, 0: 已禁用
  }

  interface WhatsAppAccountForm {
    account_id: string;
    phone_number: string;
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 