import { Module, ActionContext } from "vuex";
import { RootState, AuthState } from "@/types/store";
import {
  login,
  LoginParams,
  getUserInfo,
  sendVerificationCode,
  logoutPost,
} from "@/api/auth";
import {
  setToken,
  removeToken,
  setUserInfo,
  removeUserInfo,
  removePddActivityData,
} from "@/utils/auth";
import { showError } from "@/utils/toast";
import websocketService from "@/utils/websocket";

interface LoginResponse {
  token: string;
  refresh_token: string;
  is_new: boolean;
  user?: Record<string, any>;
}

type AuthActionContext = ActionContext<AuthState, RootState>;

const auth: Module<AuthState, RootState> = {
  namespaced: true,

  state: (): AuthState => {
    // Initialize state from localStorage if available
    const token = localStorage.getItem("token");
    const userInfo = localStorage.getItem("userInfo");
    const user = userInfo ? JSON.parse(userInfo) : null;

    return {
      token,
      user: user?.user || null,
      userWallet: user?.userWallet || null,
      unreadNum: user?.unreadNum || 0,
    };
  },

  mutations: {
    SET_TOKEN(state: AuthState, token: string) {
      state.token = token;
      setToken(token);
    },
    SET_USER_DATA(state: AuthState, data: Record<string, any>) {
      state.user = data?.user || null;
      state.userWallet = data?.userWallet || null;
      state.unreadNum = data?.unreadNum || 0;
      // Save user data to localStorage
      if (data) {
        setUserInfo(data);
      }
    },
    CLEAR_AUTH(state: AuthState) {
      state.token = null;
      state.user = null;
      state.userWallet = null;
      state.unreadNum = 0;
      removeToken();
      removeUserInfo();
      removePddActivityData();
      websocketService.disconnect();
    },
    UPDATE_USER_WALLET_BALANCE(state: AuthState, newBalance: number) {
      if (state.userWallet.total_balance !== newBalance) {
        state.userWallet.total_balance = newBalance;
      }
    },
  },

  actions: {
    // 发送验证码
    async sendVerificationCode(
      {}: AuthActionContext,
      params: { phone: string; type: string }
    ) {
      try {
        const res = await sendVerificationCode(params.type, params.phone);
        if (res) {
          return res;
        }
      } catch (error) {
        console.error("Send verification code failed:", error);
        throw error;
      }
    },

    // 登录
    async login({ commit, dispatch }: AuthActionContext, params: LoginParams) {
      try {
        const res = await login(params);
        console.log(res, "res");
        if (res) {
          const { token, refresh_token, is_new, ...userData } =
            res as LoginResponse;
          // 保存 token
          // setToken(token);
          commit("SET_TOKEN", token);
          localStorage.setItem("refresh_token", refresh_token);
          localStorage.setItem("isNew", is_new.toString());
          // 获取完整的用户信息
          await dispatch("fetchUserInfo");

          return true;
        }
        return false;
      } catch (error) {
        console.error("Login failed:", error);
        showError(error);
        return false;
      }
    },

    // 获取用户信息
    async fetchUserInfo({ commit, state }) {
      // 如果没有 token，不获取用户信息
      if (!localStorage.getItem("token")) {
        return null;
      }
      try {
        const response = await getUserInfo();
        console.log(response, "response");
        if (response) {
          // 确保用户信息包含必要的字段
          const userData = {
            ...response,
            user: response.user,
            userWallet: response.user_wallet,
            unreadNum: response.unread_num,
          };

          commit("SET_USER_DATA", userData);
          return userData;
        }
        return null;
      } catch (error) {
        console.error("Failed to fetch user info:", error);
        throw error;
      }
    },

    // 退出登录
    async logout({ commit }, type) {
      if (type) {
        commit("CLEAR_AUTH");
      } else {
        await logoutPost().then(() => {
          commit("CLEAR_AUTH");
        });
      }
    },

    // 添加清理方法
    clearAuthData({ commit }) {
      commit("CLEAR_AUTH");
    },
  },

  getters: {
    isLoggedIn: (state: AuthState) => !!state.token && !!state.user,
    userInfo: (state: AuthState) => state.user,
    userWallet: (state: AuthState) => state.userWallet,
    unreadNum: (state: AuthState) => state.unreadNum,
  },
};

export default auth;
