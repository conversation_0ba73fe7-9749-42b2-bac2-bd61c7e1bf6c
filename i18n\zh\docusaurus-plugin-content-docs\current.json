{"version.label": {"message": "Next", "description": "The label for version current"}, "sidebar.tutorialSidebar.category.Introduction": {"message": "介绍", "description": "The label for category Introduction in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Introduction.link.generated-index.description": {"message": "快速了解 UniPass Wallet 及其核心特点。", "description": "The generated-index page description for category Tutorial - Basics in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Develop": {"message": "开发指南", "description": "The label for category Develop in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Popup": {"message": "Popup SDK", "description": "The label for category Popup SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Android": {"message": "Android SDK", "description": "The label for category Android SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.iOS": {"message": "iOS SDK", "description": "The label for category iOS SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.RN": {"message": "ReactNative SDK", "description": "The label for category ReactNative SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Flutter": {"message": "Flutter SDK (遗留版本)", "description": "The label for category Flutter SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Unity": {"message": "Unity SDK (遗留版本)", "description": "The label for category Unity SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Unreal": {"message": "Unreal SDK (遗留版本)", "description": "The label for category Unreal SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Experimental": {"message": "试验性功能 (遗留版本)", "description": "The label for category Unreal SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Architecture": {"message": "核心架构", "description": "The label for category Architecture in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Terms": {"message": "条款", "description": "The label for category Terms in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.FAQ": {"message": "FAQ", "description": "The label for category FAQ in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Audits": {"message": "合约审计", "description": "The label for category Contract Audits in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Community": {"message": "社区 & 技术支持", "description": "The label for category Community in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Architecture.link.generated-index.description": {"message": "深度解析 UniPass Wallet 核心架构。", "description": "The generated-index page description for category Tutorial - Basics in sidebar tutorialSidebar"}, "sidebar.docs.category.Introduction": {"message": "Introduction", "description": "The label for category Introduction in sidebar docs"}, "sidebar.docs.category.Architecture": {"message": "Architecture", "description": "The label for category Architecture in sidebar docs"}, "sidebar.docs.category.Terms": {"message": "Terms", "description": "The label for category Terms in sidebar docs"}, "sidebar.sdk.category.Custom Auth SDK": {"message": "Custom Auth SDK", "description": "The label for category Custom Auth SDK in sidebar sdk"}, "sidebar.sdk.category.Web SDK": {"message": "Web SDK", "description": "The label for category Web SDK in sidebar sdk"}, "sidebar.sdk.category.Android SDK": {"message": "Android SDK", "description": "The label for category Android SDK in sidebar sdk"}, "sidebar.sdk.category.Plug & Play SDK": {"message": "Plug & Play SDK", "description": "The label for category Plug & Play SDK in sidebar sdk"}, "sidebar.sdk.category.Popup SDK": {"message": "Popup SDK", "description": "The label for category Popup SDK in sidebar sdk"}, "sidebar.sdk.category.iOS SDK": {"message": "iOS SDK", "description": "The label for category iOS SDK in sidebar sdk"}, "sidebar.sdk.category.Flutter SDK": {"message": "Flutter SDK", "description": "The label for category Flutter SDK in sidebar sdk"}, "sidebar.sdk.category.React Native SDK": {"message": "React Native SDK", "description": "The label for category React Native SDK in sidebar sdk"}, "sidebar.sdk.category.Unity SDK": {"message": "Unity SDK", "description": "The label for category Unity SDK in sidebar sdk"}, "sidebar.sdk.category.Unreal SDK": {"message": "Unreal SDK", "description": "The label for category Unreal SDK in sidebar sdk"}, "sidebar.sdk.category.Wallet Connectors": {"message": "Wallet Connectors", "description": "The label for category Wallet Connectors in sidebar sdk"}, "sidebar.sdk.category.Verify Message": {"message": "验签", "description": "The label for category Verify Message in sidebar sdk"}, "sidebar.sdk.category.Flutter SDK (Legacy)": {"message": "Flutter SDK (暂停维护)", "description": "The label for category Flutter SDK (Legacy) in sidebar sdk"}, "sidebar.sdk.category.Unity SDK (Legacy)": {"message": "Unity SDK (暂停维护)", "description": "The label for category Unity SDK (Legacy) in sidebar sdk"}, "sidebar.sdk.category.Unreal SDK (Legacy)": {"message": "Unreal SDK (暂停维护)", "description": "The label for category Unreal SDK (Legacy) in sidebar sdk"}, "sidebar.sdk.category.Init Master Key": {"message": "设置 Master Key", "description": "The label for category Init Master Key in sidebar sdk"}, "sidebar.docs.category.UniPass Wallet": {"message": "UniPass Wallet", "description": "The label for category UniPass Wallet in sidebar docs"}, "sidebar.docs.category.UniPass Contract": {"message": "UniPass 合约", "description": "The label for category UniPass Contract in sidebar docs"}}