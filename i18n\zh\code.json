{"theme.ErrorPageContent.title": {"message": "页面已崩溃。", "description": "The title of the fallback page when the page crashed"}, "theme.ErrorPageContent.tryAgain": {"message": "重试", "description": "The label of the button to try again when the page crashed"}, "theme.NotFound.title": {"message": "找不到页面", "description": "The title of the 404 page"}, "theme.NotFound.p1": {"message": "我们找不到您要找的页面。", "description": "The first paragraph of the 404 page"}, "theme.NotFound.p2": {"message": "请联系原始链接来源网站的所有者，并告知他们链接已损坏。", "description": "The 2nd paragraph of the 404 page"}, "theme.admonition.note": {"message": "备注", "description": "The default label used for the Note admonition (:::note)"}, "theme.admonition.tip": {"message": "提示", "description": "The default label used for the Tip admonition (:::tip)"}, "theme.admonition.danger": {"message": "危险", "description": "The default label used for the Danger admonition (:::danger)"}, "theme.admonition.info": {"message": "信息", "description": "The default label used for the Info admonition (:::info)"}, "theme.admonition.caution": {"message": "警告", "description": "The default label used for the Caution admonition (:::caution)"}, "theme.AnnouncementBar.closeButtonAriaLabel": {"message": "关闭", "description": "The ARIA label for close button of announcement bar"}, "theme.BackToTopButton.buttonAriaLabel": {"message": "回到顶部", "description": "The ARIA label for the back to top button"}, "theme.blog.archive.title": {"message": "历史博文", "description": "The page & hero title of the blog archive page"}, "theme.blog.archive.description": {"message": "历史博文", "description": "The page & hero description of the blog archive page"}, "theme.blog.paginator.navAriaLabel": {"message": "博文列表分页导航", "description": "The ARIA label for the blog pagination"}, "theme.blog.paginator.newerEntries": {"message": "较新的博文", "description": "The label used to navigate to the newer blog posts page (previous page)"}, "theme.blog.paginator.olderEntries": {"message": "较旧的博文", "description": "The label used to navigate to the older blog posts page (next page)"}, "theme.blog.post.paginator.navAriaLabel": {"message": "博文分页导航", "description": "The ARIA label for the blog posts pagination"}, "theme.blog.post.paginator.newerPost": {"message": "较新一篇", "description": "The blog post button label to navigate to the newer/previous post"}, "theme.blog.post.paginator.olderPost": {"message": "较旧一篇", "description": "The blog post button label to navigate to the older/next post"}, "theme.blog.post.plurals": {"message": "{count} 篇博文", "description": "Pluralized label for \"{count} posts\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.blog.tagTitle": {"message": "{nPosts} 含有标签「{tagName}」", "description": "The title of the page for a blog tag"}, "theme.tags.tagsPageLink": {"message": "查看所有标签", "description": "The label of the link targeting the tag list page"}, "theme.colorToggle.ariaLabel": {"message": "切换浅色/暗黑模式（当前为{mode}）", "description": "The ARIA label for the navbar color mode toggle"}, "theme.colorToggle.ariaLabel.mode.dark": {"message": "暗黑模式", "description": "The name for the dark color mode"}, "theme.colorToggle.ariaLabel.mode.light": {"message": "浅色模式", "description": "The name for the light color mode"}, "theme.docs.breadcrumbs.home": {"message": "主页面", "description": "The ARIA label for the home page in the breadcrumbs"}, "theme.docs.breadcrumbs.navAriaLabel": {"message": "页面路径", "description": "The ARIA label for the breadcrumbs"}, "theme.docs.DocCard.categoryDescription": {"message": "{count} 个项目", "description": "The default description for a category card in the generated index about how many items this category includes"}, "theme.docs.paginator.navAriaLabel": {"message": "文档分页导航", "description": "The ARIA label for the docs pagination"}, "theme.docs.paginator.previous": {"message": "上一页", "description": "The label used to navigate to the previous doc"}, "theme.docs.paginator.next": {"message": "下一页", "description": "The label used to navigate to the next doc"}, "theme.docs.tagDocListPageTitle.nDocsTagged": {"message": "{count} 篇文档带有标签", "description": "Pluralized label for \"{count} docs tagged\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.tagDocListPageTitle": {"message": "{nDocsTagged}「{tagName}」", "description": "The title of the page for a docs tag"}, "theme.docs.versionBadge.label": {"message": "版本：{versionLabel}"}, "theme.docs.versions.unreleasedVersionLabel": {"message": "此为 {siteTitle} {versionLabel} 版尚未发行的文档。", "description": "The label used to tell the user that he's browsing an unreleased doc version"}, "theme.docs.versions.unmaintainedVersionLabel": {"message": "此为 {siteTitle} {versionLabel} 版的文档，现已不再积极维护。", "description": "The label used to tell the user that he's browsing an unmaintained doc version"}, "theme.docs.versions.latestVersionSuggestionLabel": {"message": "最新的文档请参阅 {latestVersionLink} ({versionLabel})。", "description": "The label used to tell the user to check the latest version"}, "theme.docs.versions.latestVersionLinkLabel": {"message": "最新版本", "description": "The label used for the latest version suggestion link label"}, "theme.common.editThisPage": {"message": "编辑此页", "description": "The link label to edit the current page"}, "theme.common.headingLinkTitle": {"message": "标题的直接链接", "description": "Title for link to heading"}, "theme.lastUpdated.atDate": {"message": "于 {date} ", "description": "The words used to describe on which date a page has been last updated"}, "theme.lastUpdated.byUser": {"message": "由 {user} ", "description": "The words used to describe by who the page has been last updated"}, "theme.lastUpdated.lastUpdatedAtBy": {"message": "最后{byUser}{atDate}更新", "description": "The sentence used to display when a page has been last updated, and by who"}, "theme.navbar.mobileVersionsDropdown.label": {"message": "选择版本", "description": "The label for the navbar versions dropdown on mobile view"}, "theme.common.skipToMainContent": {"message": "跳到主要内容", "description": "The skip to content label used for accessibility, allowing to rapidly navigate to main content with keyboard tab/enter navigation"}, "theme.tags.tagsListLabel": {"message": "标签：", "description": "The label alongside a tag list"}, "theme.blog.sidebar.navAriaLabel": {"message": "最近博文导航", "description": "The ARIA label for recent posts in the blog sidebar"}, "theme.CodeBlock.copied": {"message": "复制成功", "description": "The copied button label on code blocks"}, "theme.CodeBlock.copyButtonAriaLabel": {"message": "复制代码到剪贴板", "description": "The ARIA label for copy code blocks button"}, "theme.CodeBlock.copy": {"message": "复制", "description": "The copy button label on code blocks"}, "theme.CodeBlock.wordWrapToggle": {"message": "切换自动换行", "description": "The title attribute for toggle word wrapping button of code block lines"}, "theme.DocSidebarItem.toggleCollapsedCategoryAriaLabel": {"message": "打开/收起侧边栏菜单「{label}」", "description": "The ARIA label to toggle the collapsible sidebar category"}, "theme.navbar.mobileLanguageDropdown.label": {"message": "选择语言", "description": "The label for the mobile language switcher dropdown"}, "theme.TOCCollapsible.toggleButtonLabel": {"message": "本页总览", "description": "The label used by the button on the collapsible TOC component"}, "theme.blog.post.readMore": {"message": "阅读更多", "description": "The label used in blog post item excerpts to link to full blog posts"}, "theme.blog.post.readMoreLabel": {"message": "阅读 {title} 的全文", "description": "The ARIA label for the link to full blog posts from excerpts"}, "theme.blog.post.readingTime.plurals": {"message": "阅读需 {readingTime} 分钟", "description": "Pluralized label for \"{readingTime} min read\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.docs.sidebar.collapseButtonTitle": {"message": "收起侧边栏", "description": "The title attribute for collapse button of doc sidebar"}, "theme.docs.sidebar.collapseButtonAriaLabel": {"message": "收起侧边栏", "description": "The title attribute for collapse button of doc sidebar"}, "theme.navbar.mobileSidebarSecondaryMenu.backButtonLabel": {"message": "← 回到主菜单", "description": "The label of the back button to return to main menu, inside the mobile navbar sidebar secondary menu (notably used to display the docs sidebar)"}, "theme.docs.sidebar.expandButtonTitle": {"message": "展开侧边栏", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.docs.sidebar.expandButtonAriaLabel": {"message": "展开侧边栏", "description": "The ARIA label and title attribute for expand button of doc sidebar"}, "theme.SearchBar.noResultsText": {"message": "没有找到任何文档"}, "theme.SearchBar.seeAll": {"message": "查看全部结果"}, "theme.SearchBar.label": {"message": "搜索", "description": "The ARIA label and placeholder for search button"}, "theme.SearchPage.existingResultsTitle": {"message": "“{query}” 的搜索结果", "description": "The search page title for non-empty query"}, "theme.SearchPage.emptyResultsTitle": {"message": "搜索文档", "description": "The search page title for empty query"}, "theme.SearchPage.documentsFound.plurals": {"message": "共找到 {count} 篇文档", "description": "Pluralized label for \"{count} documents found\". Use as much plural forms (separated by \"|\") as your language support (see https://www.unicode.org/cldr/cldr-aux/charts/34/supplemental/language_plural_rules.html)"}, "theme.SearchPage.noResultsText": {"message": "没有找到任何文档", "description": "The paragraph for empty search result"}, "theme.IdealImageMessage.loading": {"message": "加载中……", "description": "When the full-scale image is loading"}, "theme.IdealImageMessage.load": {"message": "点击加载图片{sizeMessage}", "description": "To prompt users to load the full image. sizeMessage is a parenthesized size figure."}, "theme.IdealImageMessage.offline": {"message": "你的浏览器处于离线状态。图片未加载", "description": "When the user is viewing an offline document"}, "theme.IdealImageMessage.404error": {"message": "未找到图片", "description": "When the image is not found"}, "theme.IdealImageMessage.error": {"message": "出现错误，点击重试", "description": "When the image fails to load for unknown error"}, "sdksSection.support": {"message": "放心交给我们"}, "sdksSection.customIntro": {"message": "Non-UI SDK，支持自定义账户管理和用户界面。"}, "sdksSection.customFeature1": {"message": "白标 & 可自定义 UI"}, "sdksSection.customFeature2": {"message": "可自定义登录方式"}, "sdksSection.customFeature3": {"message": "静默签名"}, "sdksSection.customFeature4": {"message": "Gasless 服务"}, "sdksSection.knowMore": {"message": "了解更多"}, "sdksSection.plugIntro": {"message": "去中心化钱包 SDK，提供钱包界面和统一的非托管账户管理。"}, "sdksSection.plugFeature1": {"message": "用户友好 & 非托管账户"}, "sdksSection.plugFeature2": {"message": "内含钱包 UI"}, "sdksSection.plugFeature3": {"message": "安全的签名界面"}, "sdksSection.plugFeature4": {"message": "Gasless 服务"}, "sdksSection.integrate": {"message": "集成"}, "sdksSection.buildWithWC": {"message": "基于 wallet connectors 构建"}, "sdksSection.comingSoon": {"message": "即将推出"}, "faqSection.faq": {"message": "常见问题"}, "faqSection.faqIntro": {"message": "我们收集并整理了一些关于 UniPass SDK 的常见问题"}, "faqSection.faqLink": {"message": "有问题？先来这里找找"}, "heroSection.title": {"message": "使用 UniPass SDK 构建"}, "heroSection.subTitle": {"message": "UniPass SDK 基于智能合约钱包提供 seedless 和 gasless 的用户体验。"}, "heroSection.start": {"message": "开始构建"}}