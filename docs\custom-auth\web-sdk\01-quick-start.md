---
sidebar_position: 2
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';


# Quick Start

## Install SDK

<Tabs>
<TabItem value="npm">

```bash
npm install @unipasswallet/smart-account@0.1.1
```
</TabItem>

<TabItem value="yarn">

```bash
yarn add @unipasswallet/smart-account@0.1.1
```
</TabItem>


<TabItem value="pnpm">

```bash
pnpm add @unipasswallet/smart-account@0.1.1
```
</TabItem>
</Tabs>

### Local Testing Url

Please use `localhost` as testing url, instead of `127.0.0.1`.

## Source Code
* [Online Demo](https://up-smart-account-demo.vercel.app/)
* [Demo code](https://github.com/UniPassID/smart-account-sdk-demo)