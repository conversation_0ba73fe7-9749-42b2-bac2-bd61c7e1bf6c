<template>
  <v-dialog v-model="show" max-width="340">
    <v-card class="pdd-modal-new">
      <div class="modal-header">
        R<span style="font-size: 30px">$</span>
        <span class="amount">{{ rules?.bonus_amount }}</span>
        <v-icon
          class="icon-tip"
          icon="mdi-alert-circle-outline"
          @click="() => (showRule = true)"
        ></v-icon>
      </div>
      <div class="modal-content">
        <div class="modal-content-body">
          <div class="icon-row">
            <img class="step-icon" :src="step1" />
            <span class="icon-arrow">...</span>
            <img class="step-icon" :src="step2" />
            <span class="icon-arrow">...</span>
            <img class="step-icon" :src="step3" />
          </div>
          <div class="steps-new">
            <div class="step-new">
              1 Convide novos jogadores para se registrar e jogar, ou convide
              pessoas para recarregar
            </div>
            <div class="step-new">2 Alcançar a meta especificada</div>
            <div class="step-new">
              3 Confirme as informações da conta de retirada e retire o dinheiro
              imediatamente
            </div>
          </div>
          <button class="invite-btn-new" @click="handleInvite">
            Convidar agora
          </button>
        </div>
      </div>
    </v-card>
    <RuleDialog
      :visible.sync="showRule"
      title="Regras de Atividade"
      :content="rules?.activity_rules"
      @update:visible="handleRuleDialogClose"
    />
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, defineEmits, watch } from "vue";
import step1 from "@/assets/images/h5/step1.png";
import step2 from "@/assets/images/h5/step2.png";
import step3 from "@/assets/images/h5/step3.png";
import RuleDialog from "@/components/RuleDialog.vue"
import { enterPddActivity, PddActivityContent } from "@/api/home";

const emit = defineEmits(["close", "invite"]);
const show = ref(false);

const handleClose = () => {
  show.value = false;
};
// 规则设置
const showRule = ref(false);
function handleRuleDialogClose(val) {
  showRule.value = val;
}
const rules = ref();
async function getRules() {
  const response = (await enterPddActivity()) as PddActivityContent;
  console.log("response", response);
  rules.value = response;
}
// 監聽對話框狀態變化
watch(show, (newValue) => {
  if (newValue) {
    getRules();
  }
});
const handleInvite = () => {
  show.value = false;
  emit("invite");
};
/**
 * 顯示對話框
 */
const showDialog = (): void => {
  show.value = true;
};
// 暴露方法給父組件
defineExpose({
  showDialog,
  handleClose,
});
</script>

<style scoped lang="scss">
.pdd-modal-new {
  background: none;
  border-radius: 28px !important;
  box-shadow: none !important;
  // padding: 0 0 0px 0;
  position: relative;
  overflow: visible;
  min-width: 320px;
  align-items: center !important;
}

.modal-header {
  width: 90%;
  background: linear-gradient(180deg, #e97b3a 0%, #f7b15a 100%);
  border-radius: 24px 24px 0 0;
  box-shadow: 0 4px 16px rgba(255, 153, 0, 0.18);
  padding: 32px 24px 40px 24px;
  display: flex;
  justify-content: center;
  // flex-direction: column;
  align-items: center;
  position: relative;
  border: 10px solid #f2aa62;
  border-bottom: none;
  margin-bottom: -60px;
  position: relative;
  color: #fff;
  font-family: RockoFLF, RockoFLF;
  font-weight: bold;
  font-size: 50px;
  color: #ffffff;
  // line-height: 120px;
  text-shadow: 2px 4px 4px rgba(0, 0, 0, 0.25);
  .amount {
    margin-left: 6px;
  }
  .icon-tip {
    color: #fff;
    position: absolute;
    top: 16px;
    right: 16px;
    font-size: 24px;
    cursor: pointer;
  }
}

.modal-content {
  width: 100%;
  background: #ffe9b0;
  // z-index: 1;
  border-radius: 24px;
  margin-top: -40px;
  box-shadow: 0 8px 32px -8px rgba(0, 0, 0, 0.4);
  .modal-content-body {
    margin-top: 40px;
    width: 100%;
    border-radius: 0 0 24px 24px;
    position: relative;
    // overflow: hidden;
    background-image: url("@/assets/images/bottom-bg2.png");
    background-size: 100% 100%;

    padding: 60px 24px 24px 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
  }
}

.icon-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 18px 0 10px 0;
  .step-icon {
    width: 48px;
    height: 48px;
    // border-radius: 50%;
    // background: #fff8e1;
    box-shadow: 0 2px 8px #facc2e44;
    object-fit: contain;
  }
  .icon-arrow {
    color: #cd7131;
    font-size: 28px;
    margin: 0 8px;
    font-weight: bold;
    letter-spacing: 2px;
    font-family: Arial, sans-serif;
  }
}

.steps-new {
  margin: 0 0 18px 0;
  width: 100%;
  .step-new {
    font-family: RockoFLF, RockoFLF;
    font-weight: bold;
    font-size: 14px;
    color: #e0574d;
    line-height: 1.5;
    text-shadow: 2px 4px 4px rgba(0, 0, 0, 0.25);
    text-align: left;
    font-style: normal;
    text-transform: none;
  }
}

.invite-btn-new {
  display: block;
  // margin: 0 auto 24px auto;
  width: 220px;
  height: 56px;
  background: linear-gradient(180deg, #f1a585 0%, #db4224 60%);
  box-shadow: inset -8px -8px 30px 0px rgba(0, 0, 0, 0.15),
    0px 4px 9px 0px rgba(0, 0, 0, 0.25);
  color: #fff;
  font-size: 20px;
  font-weight: 800;
  border-radius: 28px;
  border: none;
  cursor: pointer;
  // margin-top: 10px;
  letter-spacing: 1px;
  text-shadow: 0 2px 8px rgba(255, 153, 0, 0.1);
  transition: background 0.2s;
}
.invite-btn-new:active {
  background: linear-gradient(90deg, #ff3a3a 0%, #ff7c3a 100%);
}
</style>
