<template>
  <v-container class="commission-container" max-width="940">
    <!-- 佣金总览 -->
    <div class="commission-overview">
      <div class="section-header">
        Comissao Total: <span class="total-amount">{{ formatAmount(overview.totalAmount) }}</span>
      </div>

      <!-- 佣金详情表格 -->
      <div class="commission-detail">
        <div class="detail-header">Relatório de detalhes do convite</div>
        <div class="detail-table">
          <div class="table-row table-header">
            <div class="col">nome</div>
            <div class="col">hoje</div>
            <div class="col">total</div>
          </div>
          <div class="table-row">
            <div class="col">Cadastre-se</div>
            <div class="col value">{{ overview.registerCount.today }}</div>
            <div class="col value">{{ overview.registerCount.total }}</div>
          </div>
          <div class="table-row">
            <div class="col">Novo membro</div>
            <div class="col value">{{ overview.newMemberCount.today }}</div>
            <div class="col value">{{ overview.newMemberCount.total }}</div>
          </div>
          <div class="table-row">
            <div class="col">Convidar</div>
            <div class="col value">{{ formatAmount(overview.inviteBonus.today) }}</div>
            <div class="col value">{{ formatAmount(overview.inviteBonus.total) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 团队级别详情 -->
    <div class="team-level-detail">
      <div class="section-header">Detalhes da equipe de nivel</div>
      <div class="level-table">
        <div class="table-header level-header">
          <div class="col">nivel</div>
          <div class="col">Valor total de apostas pontos</div>
          <div class="col">Taxa de desconto de apostas pontos</div>
          <div class="col">Valor de aposta pontos de ontem</div>
          <div class="col">Comissão Total de apostas pontos</div>
          <div class="col">Comissão de apostas pontos de ontem</div>
        </div>

        <div v-for="(level,index) in teamLevels" :key="level.level" class="table-row level-row">
          <div class="col level-icon">
            <template v-if="index === 0">
              <img src="@/assets/images/d1-icon.png" width="45" />
            </template>
            <template v-else-if="index === 1">
              <img src="@/assets/images/d2-icon.png" width="45" />
            </template>
            <template v-else-if="index === 2">
              <img src="@/assets/images/d3-icon.png" width="45" />
            </template>
            <template v-else="index === 0">
              <span style="display: block;width: 45px;text-align: center">{{ level.level }}</span>
            </template>
          </div>
          <div class="col">{{ level.totalBetAmount.toFixed(2) }}</div>
          <div class="col percent">{{ formatPercent(level.betRate) }}</div>
          <div class="col">{{ level.yesterdayBetAmount.toFixed(2) }}</div>
          <div class="col">{{ formatAmount(level.totalCommission) }}</div>
          <div class="col">{{ formatAmount(level.yesterdayCommission) }}</div>
        </div>
      </div>
    </div>

    <!-- 佣金规则说明 -->
    <div class="bonus-description">
      <div class="section-header">Descricao do bonus</div>
      <div class="bonus-content">
        <div class="rule-item">1.Seu lucro consiste em duas partes,a saber [Bônus de Convite]+[Comissão de Apostas]</div>
        
        <div class="rule-section">
          <div class="section-title">[Bônus de Convite]:</div>
          <div class="section-text">O usuário que você convidar para depositar pela primeira vez,você receberá um bonus em dinheiro de R$10</div>
        </div>
        
        <div class="rule-section">
          <div class="section-title">[Comissão de Apostas]:</div>
          <div class="section-text">Esta será sua renda principal e você receberá uma percentagem diferente de cada aposta que vocêconvidar os jogadores a fazer de comissão</div>
        </div>
        
        <div class="rule-item">2.Você receberá uma comissão por cada aposta que você convida os usuários a fazereganhrar ou perder.</div>
      </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav">
      <v-btn class="nav-btn text-none text-subtitle-1" to="/invite">
        <template #prepend>
          <img src="@/assets/images/invite-btn-icon-01.png" width="22" />
        </template>
        Convidar
      </v-btn>
      <v-btn class="nav-btn active text-none text-subtitle-1">
        <template #prepend>
          <img src="@/assets/images/invite-btn-icon-02.png" width="22" />
        </template>
        Comissao
      </v-btn>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 定义数据接口
interface CommissionOverview {
  totalAmount: number
  registerCount: {
    today: number
    total: number
  }
  newMemberCount: {
    today: number
    total: number
  }
  inviteBonus: {
    today: number
    total: number
  }
}

interface TeamLevel {
  level: number
  totalBetAmount: number
  betRate: number
  yesterdayBetAmount: number
  totalCommission: number
  yesterdayCommission: number
}

// 响应式状态
const overview = ref<CommissionOverview>({
  totalAmount: 19918.43,
  registerCount: {
    today: 0,
    total: 2200
  },
  newMemberCount: {
    today: 0,
    total: 2200
  },
  inviteBonus: {
    today: 0.21,
    total: 19770.00
  }
})

const teamLevels = ref<TeamLevel[]>([
  {
    level: 1,
    totalBetAmount: 10068524.99,
    betRate: 0.1,
    yesterdayBetAmount: 312.68,
    totalCommission: 10068.52,
    yesterdayCommission: 0.65
  },
  {
    level: 2,
    totalBetAmount: 6389156.14,
    betRate: 0.1,
    yesterdayBetAmount: 498.70,
    totalCommission: 6389.16,
    yesterdayCommission: 0.58
  },
  {
    level: 3,
    totalBetAmount: 3300516.08,
    betRate: 0.2,
    yesterdayBetAmount: 213.44,
    totalCommission: 3300.51,
    yesterdayCommission: 0.27
  }
])

// 加载数据方法
const loadCommissionData = async () => {
  try {
    // TODO: 实际项目中这里会调用API
    // const response = await api.getCommissionData()
    // overview.value = response.overview
    // teamLevels.value = response.teamLevels
    console.log('加载佣金数据')
  } catch (error) {
    console.error('加载佣金数据失败:', error)
  }
}

// 格式化金额
const formatAmount = (amount: number) => {
  return `R$ ${amount.toFixed(2)}`
}

// 格式化百分比
const formatPercent = (rate: number) => {
  return `${rate}%`
}

onMounted(() => {
  loadCommissionData()
})
</script>

<style lang="scss" scoped>
.commission-container {
  position: relative;
  padding: 16px;
  min-height: 100vh;

  display: flex;
  flex-direction: column;

  .section-header {
    color: white;
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    padding: 16px;
    background: linear-gradient(87deg, #E5D51F, #3B922A, #E5D51F);
    border-radius: 8px 8px 0 0;

    .total-amount {
      color: #FF9F43;
      font-size: 18px;
      font-weight: bold;
    }
  }

  .commission-overview, .team-level-detail, .bonus-description {
    background: #222B54;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
  }

  .detail-header, .level-header {
    background: #314081;
    color: white;
    padding: 12px;
    font-weight: 500;
    text-align: center;
  }

  .detail-table {
    .table-row {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      padding: 10px 12px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      &.table-header {
        font-weight: 500;
        color: white;
      }

      .col {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;

        &.value {
          color: #FF4366;
        }
      }
    }
  }

  .level-table {
    padding: 0 4px;
    overflow-x: auto;

    .table-header {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 10px;
      padding: 12px 8px;
      color: white;
      font-size: 14px;
      font-weight: 500;
      background: #1D2654;
      text-align: center;
    }

    .table-row {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 10px;
      padding: 12px 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      color: white;
      font-size: 14px;
      text-align: center;

      .level-icon {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .percent {
        color: #32CCBC;
      }
    }
  }

  .bonus-content {
    padding: 16px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;

    .rule-item {
      margin-bottom: 16px;
    }

    .rule-section {
      background: #1B1F2D;
      padding: 12px;
      border-radius: 8px;
      margin-bottom: 16px;

      .section-title {
        color: #FF4366;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .section-text {
        color: rgba(255, 255, 255, 0.7);
      }
    }
  }

  .bottom-nav {
    position: sticky;
    width: 100%;
    bottom: 0;
    display: flex;
    justify-content: space-around;
    padding: 8px 16px;
    background: #1B1A4B;
    border-radius: 8px;
    margin-top: 16px;

    .nav-btn {
      flex: 1;
      margin: 0 8px;
      height: 44px;
      border-radius: 22px;
      color: rgba(255,255,255,0.6);
      background: transparent;

      &.active {
        color: white;
        background: linear-gradient(0deg, #A13351 0%, #F975A6 100%);
      }
    }
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .commission-container {
    padding: 12px;

    .section-header {
      font-size: 14px;
      padding: 12px;

      .total-amount {
        font-size: 16px;
      }
    }

    .detail-table {
      .table-row {
        padding: 8px;
        font-size: 12px;
      }
    }

    .level-table {
      .table-header {
        grid-template-columns: 60px repeat(5, minmax(100px, 1fr));
        padding: 8px;
        font-size: 12px;
      }

      .table-row {
        grid-template-columns: 60px repeat(5, minmax(100px, 1fr));
        padding: 8px;
        font-size: 12px;

        .level-icon {
          img {
            width: 36px;
          }
        }
      }
    }

    .bonus-content {
      padding: 12px;
      font-size: 12px;

      .rule-section {
        padding: 10px;
      }
    }

    .bottom-nav {
      padding: 6px 12px;

      .nav-btn {
        height: 40px;
        font-size: 12px;

        img {
          width: 18px;
        }
      }
    }
  }
}

// 小屏幕手机适配
@media screen and (max-width: 375px) {
  .commission-container {
    padding: 8px;

    .level-table {
      .table-header {
        grid-template-columns: 50px repeat(5, minmax(80px, 1fr));
        font-size: 11px;
      }

      .table-row {
        grid-template-columns: 50px repeat(5, minmax(80px, 1fr));
        font-size: 11px;

        .level-icon {
          img {
            width: 32px;
          }
        }
      }
    }
  }
}
</style> 