import type { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

export interface BaseResponseData<T = any> {
  status_code: number;
  error_code: string;
  data: T;
  count?: number;
}

export interface ResponseData<T = any> extends BaseResponseData<T> {}

export interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  requestId?: string;
}

export interface RequestOption<ResponseData = any> {
  /**
   * @param config Axios config
   */
  onRequest: (config: InternalAxiosRequestConfig) => InternalAxiosRequestConfig | Promise<InternalAxiosRequestConfig>;
  /**
   * @param response Axios response
   */
  isBackendSuccess: (response: AxiosResponse<ResponseData>) => boolean;
  /**
   * @param response Axios response
   * @param instance Axios instance
   */
  onBackendFail: (
    response: AxiosResponse<ResponseData>,
    instance: AxiosInstance
  ) => Promise<AxiosResponse<ResponseData> | null>;
  /**
   * @param response Axios response
   */
  transformBackendResponse: (response: AxiosResponse<ResponseData>) => Promise<any>;
  /**
   * @param error Axios error
   */
  onError: (error: AxiosError<ResponseData>) => Promise<void>;
}

export type ResponseType = 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream';

export type MappedType<R extends ResponseType, T> = R extends 'json' ? T : R extends 'blob' ? Blob : R extends 'text' ? string : any;

export interface RequestInstance<State = Record<string, unknown>> {
  <T = any, R extends ResponseType = 'json'>(config: CustomAxiosRequestConfig): Promise<T>;
  cancelRequest: (requestId: string) => void;
  cancelAllRequest: () => void;
  state: State;
}

export interface FlatRequestInstance<State = Record<string, unknown>, ResponseData = any> {
  <T = any, R extends ResponseType = 'json'>(
    config: CustomAxiosRequestConfig
  ): Promise<{
    data: T | null;
    error: AxiosError<ResponseData> | null;
    response: AxiosResponse<ResponseData> | null;
  }>;
  cancelRequest: (requestId: string) => void;
  cancelAllRequest: () => void;
  state: State;
}
