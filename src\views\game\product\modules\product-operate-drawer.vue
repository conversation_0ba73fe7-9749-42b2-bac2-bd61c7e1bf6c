<script setup lang="ts">
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElInputNumber,
  ElSwitch,
  ElUpload,
  ElMessage,
  ElCheckboxGroup,
  ElCheckbox,
  ElOption,
} from "element-plus";
import { ref, computed, watch, onMounted } from "vue";
import {
  fetchCreateGame,
  fetchUpdateGame,
  type GameItem,
} from "@/service/api/gameprodect";
import { fetchGetManufacturerDict } from "@/service/api/manufacturer";
import ImageUpload from "@/components/upload/ImageUpload.vue";
import { $t } from "@/locales";

interface Props {
  visible: boolean;
  operateType: "add" | "edit";
  rowData?: GameItem;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  (e: "update:visible", visible: boolean): void;
  (e: "submitted"): void;
}>();

const formRef = ref();
const loading = ref(false);

const formData = ref({
  game_name: "",
  game_type: [] as string[],
  game_uid: "",
  manufacturer: "",
  manufacturer_id: 1,
  icon: "",
  description: "",
  status: 1,
  sort_order: 100,
  platforms: "H5+PC" as string,
  tags: "",
  access_restriction: "",
  coin_type: "both",
});

// 平台选项
const platformOptions = [
  { label: "H5", value: "H5" },
  { label: "PC", value: "PC" },
  { label: "H5+PC", value: "H5+PC" },
];

// 游戏类型选项
const gameTypeOptions = [
  { label: "赠金游戏", value: "BonusSlots", labelValue: "BonusSlots" },
  { label: "热门游戏", value: "hot", labelValue: "hot" },
  { label: "老虎机", value: "slots", labelValue: "slots" },
  { label: "真人", value: "livecasino", labelValue: "livecasino" },
  { label: "卡牌", value: "Cartoes", labelValue: "Pôquer" },
  { label: "捕鱼", value: "Pescaria", labelValue: "Pescaria" },
  { label: "电玩", value: "Coluna", labelValue: "Coluna" },
];

// 厂商列表
const manufacturerList = ref<Array<{ id: number; name: string }>>([]);
const loadingManufacturer = ref(false);

// 金币类型选项
const coinTypeOptions = [
  { label: "现金", value: "cash" },
  { label: "赠金", value: "bonus" },
  { label: "现金、赠金", value: "both" },
];

// 访问限制选项
const accessRestrictionOptions = [
  { label: "无限制", value: "" },
  { label: "VIP1", value: "vip1" },
  { label: "VIP2", value: "vip2" },
  { label: "VIP3", value: "vip3" },
  { label: "VIP4", value: "vip4" },
  { label: "VIP5", value: "vip5" },
  { label: "VIP6", value: "vip6" },
  { label: "VIP7", value: "vip7" },
  { label: "VIP8", value: "vip8" },
  { label: "VIP9", value: "vip9" },
  { label: "VIP10", value: "vip10" },
];

const rules = {
  game_name: [{ required: true, message: "请输入游戏名称", trigger: "blur" }],
  game_type: [{ required: true, message: "请选择游戏类型", trigger: "change" }],
  game_uid: [{ required: true, message: "请输入游戏UID", trigger: "blur" }],
  manufacturer: [{ required: true, message: "请输入制造商", trigger: "blur" }],
  manufacturer_id: [
    { required: true, message: "请输入制造商ID", trigger: "blur" },
  ],
  icon: [{ required: true, message: "请上传游戏图标", trigger: "change" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
  sort_order: [{ required: true, message: "请输入排序", trigger: "blur" }],
  platforms: [{ required: true, message: "请选择平台", trigger: "change" }],
  coin_type: [{ required: true, message: "请选择金币类型", trigger: "change" }],
};

const title = computed(() =>
  props.operateType === "add" ? "新增游戏" : "编辑游戏",
);

// 获取厂商列表
const getManufacturerList = async () => {
  try {
    loadingManufacturer.value = true;
    const res = await fetchGetManufacturerDict();
    if (res.data?.data) {
      manufacturerList.value = res.data.data;
    }
  } catch (error) {
    console.error("获取厂商列表失败:", error);
    ElMessage.error("获取厂商列表失败");
  } finally {
    loadingManufacturer.value = false;
  }
};

// 处理厂商选择变化
const handleManufacturerChange = (val: string) => {
  if (!manufacturerList.value) return;

  const selected = manufacturerList.value.find((item) => item.name === val);
  if (selected) {
    formData.value.manufacturer_id = selected.id;
  }
};

// 监听抽屉显示状态
watch(
  () => props.visible,
  (val) => {
    if (val) {
      getManufacturerList();
      if (props.operateType === "edit" && props.rowData) {
        formData.value = {
          ...props.rowData,
          game_type: props.rowData.game_type
            ? props.rowData.game_type.split(",")
            : [],
          platforms: props.rowData.platforms || "H5+PC",
        };
      } else if (props.operateType === "add") {
        formData.value = {
          game_name: "",
          game_type: [],
          game_uid: "",
          manufacturer: "",
          manufacturer_id: 1,
          icon: "",
          description: "",
          status: 1,
          sort_order: 100,
          platforms: "H5+PC",
          tags: "",
          access_restriction: "",
          coin_type: "both",
        };
      }
    }
  },
);

// 处理图片上传
const handleUploadSuccess = (response: any) => {
  if (response.status_code === 200) {
    formData.value.icon = response.data.url;
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;

    // 准备提交的数据，将数组转换为逗号分隔的字符串
    const submitData = {
      ...formData.value,
      game_type: formData.value.game_type.join(","),
      platforms: formData.value.platforms,
    };

    if (props.operateType === "add") {
      await fetchCreateGame(submitData);
      ElMessage.success("新增成功");
    } else {
      await fetchUpdateGame({ ...submitData, id: props.rowData?.id });
      ElMessage.success("更新成功");
    }

    emit("submitted");
    emit("update:visible", false);
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("提交失败");
  } finally {
    loading.value = false;
  }
};

// 关闭抽屉
const handleClose = () => {
  emit("update:visible", false);
};
</script>

<template>
  <ElDrawer
    :model-value="visible"
    :title="title"
    size="50%"
    @update:model-value="(val) => emit('update:visible', val)"
    @close="handleClose"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="p-20px"
    >
      <ElFormItem label="游戏名称" prop="game_name">
        <ElInput v-model="formData.game_name" placeholder="请输入游戏名称" />
      </ElFormItem>

      <ElFormItem label="游戏类型" prop="game_type">
        <ElSelect
          v-model="formData.game_type"
          placeholder="请选择游戏类型"
          multiple
          collapse-tags
          collapse-tags-tooltip
        >
          <ElOption
            v-for="item in gameTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <span style="float: left">{{ item.label }}</span>
            <span
              style="
                float: right;
                color: var(--el-text-color-secondary);
                font-size: 13px;
              "
            >
              {{ item.labelValue }}
            </span>
          </ElOption>
        </ElSelect>
      </ElFormItem>

      <ElFormItem label="游戏UID" prop="game_uid">
        <ElInput v-model="formData.game_uid" placeholder="请输入游戏UID" />
      </ElFormItem>

      <ElFormItem label="厂商" prop="manufacturer">
        <ElSelect
          v-model="formData.manufacturer"
          placeholder="请选择厂商"
          :loading="loadingManufacturer"
          @change="handleManufacturerChange"
        >
          <ElOption
            v-for="item in manufacturerList"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          />
        </ElSelect>
      </ElFormItem>

      <ElFormItem label="游戏图标" prop="icon">
        <ElInput v-model="formData.icon" placeholder="请输入图标链接" />
        <div class="mt-10px">
          <ImageUpload
            v-model="formData.icon"
            :show-tip="true"
            tip-text="支持 jpg、png 格式图片，大小不超过 2MB"
          />
        </div>
      </ElFormItem>

      <ElFormItem label="游戏描述" prop="description">
        <ElInput
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入游戏描述"
        />
      </ElFormItem>

      <ElFormItem label="状态" prop="status">
        <ElSwitch
          v-model="formData.status"
          :active-value="1"
          :inactive-value="0"
          active-text="上架"
          inactive-text="下架"
        />
      </ElFormItem>

      <ElFormItem label="排序" prop="sort_order">
        <ElInputNumber v-model="formData.sort_order" :min="0" />
      </ElFormItem>

      <ElFormItem label="平台" prop="platforms">
        <ElSelect v-model="formData.platforms" placeholder="请选择平台">
          <ElOption
            v-for="item in platformOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem>

      <ElFormItem label="标签" prop="tags">
        <ElInput v-model="formData.tags" placeholder="请输入标签" />
      </ElFormItem>

      <ElFormItem label="访问限制" prop="access_restriction">
        <ElSelect
          v-model="formData.access_restriction"
          placeholder="请选择访问限制"
        >
          <ElOption
            v-for="item in accessRestrictionOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem>

      <ElFormItem label="金币类型" prop="coin_type">
        <ElSelect v-model="formData.coin_type" placeholder="请选择金币类型">
          <ElOption
            v-for="item in coinTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem>
    </ElForm>

    <template #footer>
      <div class="flex justify-end gap-10px">
        <ElButton @click="handleClose">取消</ElButton>
        <ElButton type="primary" :loading="loading" @click="handleSubmit">
          确定
        </ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<style lang="scss" scoped>
:deep(.el-drawer__body) {
  padding: 0;
}
</style>
