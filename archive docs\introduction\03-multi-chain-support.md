---
sidebar_position: 3
---

# Multi-chain Support

UniPass Wallet supports all EVM-compatible chains and gives users the same address across all chains using [**EIP-2470**](https://eips.ethereum.org/EIPS/eip-2470). Currently supported chains are:

## MainNet

- Ethereum
- BNB Chain
- Polygon
- Rangers Protocol
- Arbitrum
- Avalanche (C chain)
- KCC

## TestNet

- Goerli (Ethereum testnet)
- BSC testnet
- Mumbai (Polygon testnet)
- <PERSON> (Rangers testnet)
- Arbitrum Goerli
- Avalanche Fuji testnet
- KCC testnet

## Chains to support in the future

- Optimism
- PlatON
- OKC
- Godwoken
- Fantom
