import { request } from '../request';

export interface walletPayChannelParams {
  id?:number
  channel_name: string
  channel_type: string
  system_fees: number
  platform_fees: number
  min_recharge_amount: number
  max_recharge_amount: number
  status: number
  config_params:string
}

// 添加支付渠道
export function fetchAddPaymentChannel(params:walletPayChannelParams){
  return request({
    url: '/backend/walletPayChannel/insert',
    method: 'post',
    data: params
  });
}
// 列表荐类型定义
export interface RechargePackage {
   id: number;
  channel_name: string;
  channel_type: string;
  system_fees: number;
  platform_fees: number;
  min_recharge_amount: number;
  max_recharge_amount: number;
  status: number;
  config_params: string; // 原始的 JSON 字符串
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
}

// 修改支付渠道

export function fetchUpdatePaymentChannel(params:walletPayChannelParams){
  return request({
    url: '/backend/walletPayChannel/update',
    method: 'post',
    data: params
  });
}

// 获取支付渠道列表
export function fetchPaymentChannelList(params:any){
  return request<{ data: RechargePackage[]; count: number }>({
    url: '/backend/walletPayChannel/list',
    method: 'get',
    params,
  });
}

// 修改支付渠道状态
export function fetchUpdatePaymentChannelStatus(params:{id:number,status:number}){
  return request({
    url: '/backend/walletPayChannel/updateStatus',
    method: 'post',
    data: params
  });
}