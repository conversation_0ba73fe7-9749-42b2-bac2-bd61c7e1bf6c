<script setup lang="ts">
import { localStg } from "@/utils/storage";
interface Props {
  item: App.Global.Menu;
}

const { item } = defineProps<Props>();
const isManage = localStg.get("isManage");
const hasChildren = item.children && item.children.length > 0;
</script>

<template>
  <template v-if="isManage !== 'true'">
    <template v-if="hasChildren">
      <MenuItem
        v-for="child in item.children"
        :key="child.key"
        :item="child"
        :index="child.key"
      />
    </template>
    <ElMenuItem v-else-if="!['home', '/'].includes(item.key)" :index="item.key">
      <span class="ib-ellipsis">{{ item.label }}</span>
    </ElMenuItem>
  </template>
  <template v-else>
    <ElSubMenu v-if="hasChildren" :index="item.key">
      <template #title>
        <span class="ib-ellipsis">{{ item.label }}</span>
      </template>
      <MenuItem
        v-for="child in item.children"
        :key="child.key"
        :item="child"
        :index="child.key"
      ></MenuItem>
    </ElSubMenu>
    <ElMenuItem v-else :index="item.key">
      <span class="ib-ellipsis">{{ item.label }}</span>
    </ElMenuItem>
  </template>
</template>

<style scoped>
.ib-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
}
</style>
