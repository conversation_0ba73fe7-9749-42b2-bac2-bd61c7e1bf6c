import { Method } from 'alova'

// API 响应数据结构
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

// 分页响应数据结构
export interface PageResponse<T = any> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

// 分页请求参数
export interface PageParams {
  page: number
  pageSize: number
  [key: string]: any
}

// 请求方法类型
export type RequestMethod<T = any, R = any> = Method<ApiResponse<T>, R, unknown> 