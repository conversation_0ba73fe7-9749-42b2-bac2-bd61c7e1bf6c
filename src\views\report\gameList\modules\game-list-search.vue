<template>
  <div class="search-wrapper">
    <ElForm ref="formRef" :model="model">
      <ElRow :gutter="16">
        <ElCol :span="4">
          <ElFormItem label="厂商" prop="manufacturer_id">
            <ElSelect v-model="model.manufacturer_id" placeholder="请选择厂商" clearable filterable>
              <ElOption v-for="item in manufacturerOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem label="排序方式" prop="sort_by">
            <ElSelect v-model="model.sort_by" placeholder="请选择排序方式" clearable>
              <ElOption v-for="item in sortTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem label="游戏名称" prop="game_name">
            <ElInput v-model="model.game_name" placeholder="请输入游戏名称" clearable @keyup.enter="handleSearch" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <div class="header-operation">
                <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElForm } from 'element-plus';
import { fetchGameManufacturers } from '@/service/api/gameList';

interface Props {
  model: Record<string, any>;
}

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref();
const manufacturerOptions = ref<any[]>([]);
const sortTypeOptions = [
  { value: 'user_count', label: '按累计用户数' },
  { value: 'code_amount', label: '按累计打码量' },
  { value: 'profit', label: '按累计盈利' }
];

function handleReset() {
  formRef.value?.resetFields();
  emit('reset');
}

function handleSearch() {
  emit('search');
}

async function getManufacturers() {
  const { response } = await fetchGameManufacturers();
  manufacturerOptions.value = (response?.data?.data || []).map((item: any) => ({
    value: item.manufacturer_id,
    label: item.manufacturer_name
  }));
}

onMounted(getManufacturers);
</script>

<style lang="scss" scoped>
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 18px;
    margin-right: 18px;
  }
}
</style>
