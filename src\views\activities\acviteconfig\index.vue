<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import ImageUpload from "@/components/upload/ImageUpload.vue";
import {
  fetchInviteConfigSave,
  fetchInviteConfig,
  fetchInviteRankList,
} from "@/service/api/invite";
import {
  EditPen,
  UserFilled,
  Setting,
  ArrowLeft,
  CaretRight,
} from "@element-plus/icons-vue";
import axios from "axios";
import {formatNumber } from "@/utils/format"

// 数据结构
interface Config {
  invite_level: number;
  status: number;
  domain: string;
  invite_rules: string;
  share_image: string;
  share_text: string;
  bonus_type: number; // 1: 現金, 2: 贈金, 3: 打碼提現
}
interface Reward {
  level: number;
  register_reward: number;
  first_charge_reward: number;
  bet_commission: number;
}

const config = reactive<Config>({
  invite_level: 0,
  status: 1,
  domain: "",
  invite_rules: "",
  share_image: "",
  share_text: "",
  bonus_type: 1,
});

const statistics = reactive({
  total_first_recharge: 0,
  total_invites: 0,
  total_recharge_bonus: 0,
  total_register_bonus: 0,
});

const rewards = ref<Reward[]>([]);
const loading = ref(false);
const editDialogVisible = ref(false);
const editConfig = reactive<Config>({ ...config });
const editRewards = ref<Reward[]>([]);
const activeTab = ref("base");

// 查询接口
const fetchConfig = async () => {
  loading.value = true;
  try {
    const { response } = await fetchInviteConfig();
    if (response?.data?.data) {
      Object.assign(config, response.data.data.config);
      Object.assign(statistics, response.data.data.statistics);
      rewards.value = response.data.data.rewards || [];
    }
  } catch (e) {
    ElMessage.error("获取配置失败");
  } finally {
    loading.value = false;
  }
};

// 打开编辑弹窗
const openEdit = () => {
  Object.assign(editConfig, config);
  editRewards.value = rewards.value.map((r) => ({
    ...r,
    first_charge_reward: Number(r.first_charge_reward) / 100,
    register_reward: Number(r.register_reward) / 100,
  }));
  editDialogVisible.value = true;
};

// 保存接口
const saveConfig = async () => {
  try {
    const data =
      editConfig.invite_level === 3
        ? JSON.parse(JSON.stringify(editRewards.value.slice(0, 3)))
        : JSON.parse(JSON.stringify(editRewards.value));

    const rewardsToSave = data.map((item) => ({
      ...item,
      bet_commission: Number(item.bet_commission),
      first_charge_reward: Number(item.first_charge_reward) * 100,
      register_reward: Number(item.register_reward) * 100,
    }));
    const { response } = await fetchInviteConfigSave({
      config: editConfig,
      rewards: rewardsToSave,
    });
    if (response?.data.status_code === 200) {
      ElMessage.success("保存成功");
      editDialogVisible.value = false;
      fetchConfig();
      getInviteRankList();
    } else {
      ElMessage.error(response?.data.msg);
    }
  } catch (e) {
    ElMessage.error("保存失败");
  }
};

// 获取邀请排行榜
async function getInviteRankList() {
  const { response } = await fetchInviteRankList();
  if (response?.data.data.list) {
    inviteTop10.value = response?.data.data.list;
  } else {
    inviteTop10.value = [];
  }
}

// mock数据
const inviteTop10 = ref([]);

// 添加change事件处理函数
function changeInviteLevel(value: number) {
  // 如果层级为0（无限级）或大于等于4，editRewards补充到4级
  if (value === 0 || value >= 4) {
    while (editRewards.value.length < 4) {
      editRewards.value.push({
        level: editRewards.value.length + 1,
        register_reward: 0,
        first_charge_reward: 0,
        bet_commission: 0,
      });
    }
  }
}

onMounted(() => {
  fetchConfig();
  getInviteRankList();
});
</script>

<template>
  <div v-loading="loading">
    <div class="page-header">
      <div class="breadcrumb">
        <span class="back-link" @click="$router.back()">
          <ElIcon><ArrowLeft /></ElIcon>
          返回列表
        </span>
        <ElIcon><CaretRight class="divider-icon" /></ElIcon>
        <span class="current-page">活动管理配置</span>
      </div>
    </div>
    <div class="top-section">
      <ElCard class="card-wrapper" style="width: 100%">
        <template #header>
          <div
            style="
              display: flex;
              flex-direction: row;
              justify-content: space-between;
              align-items: center;
            "
          >
            <span>邀请规则</span>
            <ElButton @click="openEdit">
              <template #icon>
                <icon-ant-design-setting-outlined class="text-icon" />
              </template>
              修改规则
            </ElButton>
          </div>
        </template>
        <div style="display: flex; flex-direction: row; gap: 32px">
          <div class="big-card left-card">
            <div class="section-title">奖励规则</div>
            <ElDescriptions :column="1" border class="big-desc">
              <ElDescriptionsItem label="邀请层级" label-width="130px">
                <span v-if="config.invite_level === 0">无限级</span>
                <span v-else>{{ config.invite_level }}级</span>
              </ElDescriptionsItem>
              <ElDescriptionsItem label="状态">
                <ElTag
                  :type="config.status === 1 ? 'success' : 'info'"
                  size="large"
                >
                  {{ config.status === 1 ? "已启用" : "已停用" }}
                </ElTag>
              </ElDescriptionsItem>
              <ElDescriptionsItem label="活动域名">{{
                config.domain
              }}</ElDescriptionsItem>
              <ElDescriptionsItem label="分享图片">
                <ElImage
                  :src="config.share_image"
                  :preview-src-list="[config.share_image]"
                  style="width: 80px; height: 80px; border-radius: 8px"
                  fit="cover"
                />
              </ElDescriptionsItem>
              <ElDescriptionsItem label="分享文案">{{
                config.share_text
              }}</ElDescriptionsItem>
              <ElDescriptionsItem label="邀请规则文案">{{
                config.invite_rules
              }}</ElDescriptionsItem>
            </ElDescriptions>
          </div>
          <div class="big-card right-card">
            <div class="section-title">奖励规则</div>
            <ElTable :data="rewards" border class="big-table">
              <ElTableColumn prop="level" label="层级" width="120">
                <template #default="scope">
                  <span>{{
                    ["", "一级", "二级", "三级", "四级及以上"][scope.row.level]
                  }}</span>
                  <!-- <span v-if="scope.row.level === 1">一级</span>
                  <span v-else-if="scope.row.level === 2">二级</span>
                  <span v-else-if="scope.row.level === 3">三级</span>
                  <span v-else>{{ scope.row.level }}级及以上</span> -->
                </template>
              </ElTableColumn>
              <ElTableColumn
                prop="register_reward"
                label="注册奖励"
                width="160"
              >
                <template #default="scope">
                  <b>R${{ formatNumber(scope.row.register_reward / 100) }}</b>
                </template>
              </ElTableColumn>
              <ElTableColumn
                prop="first_charge_reward"
                label="首充奖励"
                width="160"
              >
                <template #default="scope">
                  <b>R${{ formatNumber(scope.row.first_charge_reward / 100) }}</b>
                </template>
              </ElTableColumn>
              <ElTableColumn
                prop="bet_commission"
                label="打码返水(‰)"
                width="140"
              >
                <template #default="scope"
                  ><b>{{ scope.row.bet_commission }}‰</b></template
                >
              </ElTableColumn>
            </ElTable>
          </div>
        </div>
      </ElCard>
    </div>

    <!-- 新增：邀请活动数据统计 -->
    <ElCard class="stat-section">
      <div class="stat-row">
        <div class="stat-card blue">
          <div class="stat-num">R${{ formatNumber(statistics.total_invites) }}</div>
          <div class="stat-label">累计邀请注册人数</div>
        </div>
        <div class="stat-card green">
          <div class="stat-num">R${{ formatNumber(statistics.total_first_recharge) }}</div>
          <div class="stat-label">累计邀请首充人数</div>
        </div>
        <div class="stat-card red">
          <div class="stat-num">R${{ formatNumber(statistics.total_register_bonus) }}</div>
          <div class="stat-label">累计发放注册奖励</div>
        </div>
        <div class="stat-card orange">
          <div class="stat-num">R${{ formatNumber(statistics.total_recharge_bonus) }}</div>
          <div class="stat-label">累计发放首充奖励</div>
        </div>
      </div>
    </ElCard>

    <!-- 邀请链接TOP10排行榜 -->
    <ElCard style="margin-top: 0">
      <div style="font-size: 14px; font-weight: bold; margin-bottom: 18px">
        邀请链接TOP10
      </div>
      <ElTable :data="inviteTop10" border style="width: 100%">
        <ElTableColumn prop="rank" label="排名" align="center" />
        <ElTableColumn prop="uuid" label="用户ID" align="center" />
        <ElTableColumn prop="nickname" label="用户昵称" align="center" />
        <ElTableColumn
          prop="direct_invites"
          label="直接邀请人数"
          align="center"
        />
        <ElTableColumn
          prop="indirect_invites"
          label="间接邀请人数"
          align="center"
        />
        <ElTableColumn
          prop="total_recharge_amount"
          label="邀请充值金额"
          align="center"
        >
          <template #default="scope">
            R${{ formatNumber(scope.row.total_recharge_amount) }}
          </template>
        </ElTableColumn>
        <ElTableColumn
          prop="first_charge_reward"
          label="获得奖励"
          align="center"
        >
          <template #default="scope">
            R${{ formatNumber(scope.row.first_charge_reward) }}
          </template>
        </ElTableColumn>
      </ElTable>
    </ElCard>

    <!-- 编辑弹窗（Tab结构） -->
    <ElDialog v-model="editDialogVisible" title="编辑邀请规则" width="600px">
      <ElTabs v-model="activeTab">
        <ElTabPane label="基本设置" name="base">
          <ElForm :model="editConfig" label-width="100px" class="mb-4">
            <ElFormItem label="邀请层级">
              <ElSelect
                v-model="editConfig.invite_level"
                placeholder="请选择邀请层级"
                @change="changeInviteLevel"
              >
                <ElOption :label="'无限级'" :value="0" />
                <ElOption :label="'三级'" :value="3" />
                <ElOption :label="'五级'" :value="5" />
                <ElOption :label="'九级'" :value="9" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="活动状态">
              <ElRadioGroup v-model="editConfig.status">
                <ElRadio :label="1">启用</ElRadio>
                <ElRadio :label="0">停用</ElRadio>
              </ElRadioGroup>
            </ElFormItem>
            <ElFormItem label="活动域名">
              <ElInput
                v-model="editConfig.domain"
                type="text"
                placeholder="请输入域名"
              />
            </ElFormItem>
            <ElFormItem label="邀请规则文案">
              <ElInput
                v-model="editConfig.invite_rules"
                type="textarea"
                :rows="5"
              />
              <div style="color: #4a90e2; font-size: 13px; margin-top: 4px">
                此文案将显示在邀请页面，向用户说明邀请规则
              </div>
            </ElFormItem>
          </ElForm>
        </ElTabPane>
        <ElTabPane label="奖励设置" name="reward">
          <ElForm :model="editConfig" label-width="100px" class="mb-4">
            <ElFormItem label="奖金类型">
              <ElSelect
                v-model="editConfig.bonus_type"
                placeholder="请选择奖金类型"
              >
                <ElOption :label="'现金'" :value="1" />
                <ElOption :label="'赠金'" :value="2" />
                <ElOption :label="'打码提现'" :value="3" />
              </ElSelect>
            </ElFormItem>
          </ElForm>
          <ElTable
            :data="
              editConfig.invite_level === 3
                ? editRewards.slice(0, 3)
                : editRewards
            "
            border
            style="width: 100%; margin-bottom: 10px"
          >
            <ElTableColumn prop="level" label="层级" width="120">
              <template #default="scope">
                <span v-if="scope.row.level === 1">一级</span>
                <span v-else-if="scope.row.level === 2">二级</span>
                <span v-else-if="scope.row.level === 3">三级</span>
                <span v-else>{{ scope.row.level }}级及以上</span>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="register_reward" label="注册奖励(元)">
              <template #default="scope">
                <ElInput v-model="scope.row.register_reward" type="number" />
              </template>
            </ElTableColumn>
            <ElTableColumn prop="first_charge_reward" label="首充奖励(元)">
              <template #default="scope">
                <ElInput
                  v-model="scope.row.first_charge_reward"
                  type="number"
                />
              </template>
            </ElTableColumn>
            <ElTableColumn prop="bet_commission" label="打码返水(‰)">
              <template #default="scope">
                <ElInput v-model="scope.row.bet_commission" type="number" />
              </template>
            </ElTableColumn>
          </ElTable>
        </ElTabPane>
        <ElTabPane label="分享设置" name="share">
          <ElForm :model="editConfig" label-width="100px">
            <ElFormItem label="分享图片">
              <ImageUpload
                v-model="editConfig.share_image"
                :max-size="5"
                :show-tip="true"
                tip-text="支持 jpg、png 格式图片，大小不超过 5MB"
              />
            </ElFormItem>
            <ElFormItem label="分享文案">
              <ElInput
                v-model="editConfig.share_text"
                type="textarea"
                rows="5"
              />
            </ElFormItem>
          </ElForm>
        </ElTabPane>
      </ElTabs>
      <template #footer>
        <ElButton @click="editDialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="saveConfig">保存</ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<style lang="scss" scoped>
.top-section {
  display: flex;
  flex-direction: row;
  gap: 10px;
  margin-bottom: 10px;
  position: relative;
}

.page-header {
  margin-bottom: 24px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.back-link {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: color 0.3s;
}

.back-link:hover {
  color: var(--el-color-primary);
}

.big-card {
  flex: 1;
}

.left-card,
.right-card {
  flex: 1;
  width: 50%;
  max-width: none;
  min-width: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 800;
  margin-bottom: 14px;
  color: #222;
}

.stat-section {
  margin-top: 0;
  padding: 0;
  background: transparent;
  box-shadow: none;
  border: none;
}
.stat-row {
  display: flex;
  gap: 20px;
  justify-content: space-between;
  flex-wrap: wrap;
}
.stat-card {
  flex: 1;
  min-width: 260px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 18px 0 rgba(0, 0, 0, 0.07);
  padding: 36px 0 28px 0;
  text-align: center;
  margin-bottom: 12px;
  transition: box-shadow 0.2s;
}
.stat-card .stat-num {
  font-size: 30px;
  font-weight: 800;
  margin-bottom: 8px;
}
.stat-card .stat-label {
  font-size: 14px;
  color: #444;
  font-weight: 500;
}
.stat-card.blue .stat-num {
  color: #2196f3;
}
.stat-card.green .stat-num {
  color: #22b96a;
}
.stat-card.red .stat-num {
  color: #f44336;
}
.stat-card.orange .stat-num {
  color: #ff9800;
}
</style>
