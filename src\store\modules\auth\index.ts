import { computed, reactive, ref } from "vue";
import { useRoute } from "vue-router";
import { defineStore } from "pinia";
import { useLoading } from "@sa/hooks";
import { SetupStoreId } from "@/enum";
import { useRouterPush } from "@/hooks/common/router";
import {
  fetchGetUserInfo,
  fetchLogin,
  fetchGetGoogleSecret,
  fetchGetAgentUserRole,
} from "@/service/api";
import { localStg } from "@/utils/storage";
import { $t } from "@/locales";
import { useRouteStore } from "../route";
import { useTabStore } from "../tab";
import { clearAuthStorage, getToken, getLocalUserInfo } from "./shared";

export const useAuthStore = defineStore(SetupStoreId.Auth, () => {
  const route = useRoute();
  const routeStore = useRouteStore();
  const tabStore = useTabStore();
  const { toLogin, redirectFromLogin } = useRouterPush(false);
  const { loading: loginLoading, startLoading, endLoading } = useLoading();

  const token = ref(getToken());

  const userInfo: Api.Auth.UserInfo = reactive({
    id: 0,
    user_id: 0,
    user_name: "",
    password: "",
    status: 0,
    role_id: 0,
    phone: "",
    token: "",
    gender: 0,
    is_delete: 0,
    parent_id: 0,
    invitation_code: "",
    created_at: 0,
    created_by: "",
    updated_at: 0,
    updated_by: "",
    buttons: [],
    routes: "",
    expires: "",
  });

  /** is super role in static route */
  const isStaticSuper = computed(() => {
    const { VITE_AUTH_ROUTE_MODE, VITE_STATIC_SUPER_ROLE } = import.meta.env;
    return VITE_AUTH_ROUTE_MODE === "static";
  });

  /** Is login */
  const isLogin = computed(() => Boolean(token.value));

  /** Reset auth store */
  async function resetStore() {
    const authStore = useAuthStore();
    clearAuthStorage();
    authStore.$reset();
    if (!route.meta.constant) {
      await toLogin();
    }
    tabStore.cacheTabs();
    routeStore.resetStore();
  }

  /**
   * Login
   * @param userName User name
   * @param password Password
   * @param [redirect=true] Whether to redirect after login. Default is `true`
   */
  async function login(
    userName: string,
    password: string,
    google_code: string,
    redirect = true,
  ) {
    startLoading();
    try {
      // 等待 fetchLogin 返回的 Promise 解析
      const res = await fetchLogin(userName, password, google_code);
      const { data: responseData, error, response } = res;
      console.log("response", response);
      console.log("responseData", responseData);

      // if(response.status === 401){
      //   window.$notification?.error({
      //     title: $t('page.login.common.loginFailed'),
      //     message: $t('page.login.common.loginFailed'),
      //     duration: 4500
      //   });
      // }

      if (!error && responseData) {
        console.log("loginToken", responseData);
        const pass = await loginByToken(responseData);
        if (pass) {
          const res = await fetchGetAgentUserRole({});
          const isManage = res.data.data.role_id === 1 ? "true" : "false";
          localStg.set("isManage", isManage);
          // 获取角色权限 平台/代理
          await redirectFromLogin(redirect);
          window.$notification?.success({
            title: $t("page.login.common.loginSuccess"),
            message: $t("page.login.common.welcomeBack", {
              userName: userInfo.user_name,
            }),
            duration: 4500,
          });
        }
      } else {
        resetStore();
        return res;
      }
    } catch (error) {
      console.error("Login error:", error);
      resetStore();
    } finally {
      endLoading();
    }
  }

  async function loginByToken(loginToken: Api.Auth.UserInfo) {
    // 1. stored in the localStorage, the later requests need it in headers
    console.log("loginToken", loginToken);
    localStg.set("token", loginToken.data.token);
    //将获取的信息复制给userInfo
    Object.assign(userInfo, loginToken.data);
    const pass = await getUserInfo();
    console.log("pass", pass);
    if (pass) {
      token.value = loginToken.data.token;
    
      // 将 userInfo 存储到 localStorage 中
      localStg.set("userInfo", JSON.stringify(userInfo));
      return true;
    }
    return false;
  }

  async function getUserInfo() {
    const { data: info, error } = await fetchGetUserInfo();
    if (!error) {
      console.log("info", info.data);
      userInfo.buttons = info.data.buttons?.map((item: any) => item.button_id);
      userInfo.routes = info.data.routes;
      return true;
    }
    return false;
  }

  async function initUserInfo() {
    const hasToken = getToken();
    if (hasToken) {
      const localUserInfo = getLocalUserInfo();
      if (localUserInfo) {
        Object.assign(userInfo, localUserInfo);
      }
      const pass = await getUserInfo();
      if (!pass) {
        resetStore();
      }
    }
  }

  return {
    token,
    userInfo,
    isStaticSuper,
    isLogin,
    loginLoading,
    resetStore,
    login,
    initUserInfo,
  };
});
