<template>
  <v-container class="ranking-container pt-4" max-width="940">
    <div class="ranking-card">
      <!-- 标题区域 -->
      <div class="ranking-header">
        <img src="@/assets/images/aposta-banner.png" />
        <div class="jackpot-balance">
          <span>JACKPOT</span>
          <div class="d-flex align-center">
            <img class="icon-coin mr-2" src="@/assets/images/coin.png" />
            <span>{{ formatNumber(currentBalance / 100) }}</span>
          </div>
        </div>
        <img
          class="icon-tip"
          src="@/assets/images/pontos.png"
          @click="() => (pontosRule = true)"
        />
      </div>

      <!-- 提示文本 -->
      <div class="ranking-tip">
        <span>Total de pontos: {{ jackpotInfo?.points || 0 }}</span>
        <span>No:{{ jackpotInfo?.rank ? jackpotInfo?.rank : "Não" }}</span>
      </div>
      <!-- 实时排行榜 -->
      <div class="ranking-section text-center">
        <div class="section-title">Lista de classificação</div>

        <!-- 前三名展示 -->
        <div class="top-three mt-4 mb-4" v-if="!isMobile">
          <div
            v-for="rank in topThree"
            :key="rank.rank"
            class="rank-item"
            :class="`rank-${rank.rank}`"
          >
            <div class="rank-badge">
              <div class="bonus d-flex flex-column" v-show="rank.bonus">
                <span> Bônus esperado: </span>
                <span> R${{ formatNumber((rank.bonus || 0) / 100) }}</span>
              </div>
              <div class="user-id" v-show="rank.uuid">ID:{{ rank.uuid }}</div>
              <div class="user-aposte" v-show="rank.points">
                Pontos: {{ rank.points }}
              </div>
            </div>
          </div>
        </div>
        <div class="top-three-mobile" v-else>
          <div
            v-for="(rank, index) in topThree"
            :key="rank.rank"
            :class="'rank-item' + index"
          >
            <div class="rank-badge">
              <div class="bonus" v-show="rank.bonus">
                Bônus esperado: R${{ formatNumber((rank.bonus || 0) / 100) }}
              </div>
              <div class="user-id" v-show="rank.uuid">ID:{{ rank.uuid }}</div>
              <div class="user-aposte" v-show="rank.points">
                Pontos: {{ rank.points }}
              </div>
            </div>
          </div>
        </div>

        <!-- 排行榜列表 -->
        <v-table class="ranking-table ma-4">
          <thead>
            <tr>
              <th>No.</th>
              <th>ID</th>
              <th>Pontos</th>
              <th>Bônus esperado</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in currentRankings" :key="item.rank">
              <td
                :class="{
                  amount: item.rank === jackpotInfo?.rank,
                  ['rank' + item.rank]: true,
                }"
              >
                {{ item.rank }}
              </td>
              <td :class="{ amount: item.rank === jackpotInfo?.rank }">
                {{ item.uuid }}
              </td>
              <td class="amount">{{ item.points }}</td>
              <td class="bonus">
                R$ {{ formatNumber((item.bonus || 0) / 100) }}
              </td>
            </tr>
          </tbody>
        </v-table>

        <v-btn
          class="view-more-btn mb-8"
          @click="loadMoreCurrentRankings"
          :loading="loadingMoreCurrent"
          :disabled="!hasMoreCurrent || loadingMoreCurrent"
        >
          {{ hasMoreCurrent ? "Ver mais" : "Não há mais dados" }}
        </v-btn>
      </div>
      <!-- 规则内容展示 -->
      <div class="activity-rule" v-if="jackpotInfo?.rule_management">
        <div
          class="rule-title"
          style="font-weight: bold; font-size: 18px; margin-bottom: 8px"
        >
          Regras da atividade
        </div>
        <v-textarea
          :model-value="jackpotInfo?.rule_management"
          readonly
          variant="outlined"
          hide-details
          auto-grow
          rows="5"
          class="rule-textarea"
          :class="{ 'rule-textarea-readonly': true }"
        ></v-textarea>
      </div>
    </div>
    <PontosDialog
      :visible="pontosRule"
      @update:visible="() => (pontosRule = false)"
    />
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from "vue";
import { useDevice } from "@/composables/useDevice";
import {
  getRealTimeLeaderboard,
  getUserPointsDetail,
  getUserDetails,
  LeaderboardItem,
  getJackpotBalance,
} from "@/api/jackpot";
import { showError, showWarning } from "@/utils/toast";
import PontosDialog from "@/components/pontosDialog.vue";
// 判断设备类型
const { currentDevice, isMobile, isTablet } = useDevice();

// 规则设置
const pontosRule = ref(false);
// 奖池金额
const currentBalance = ref();
async function getCurrentBalance() {
  const response = await getJackpotBalance();
  currentBalance.value = response.amount;
}
// 排行榜数据
const realTimeData = ref<LeaderboardItem[]>([]);
const yesterdayData = ref<LeaderboardItem[]>([]);
const currentPage = ref(1);

const pageSize = 10;

// 加载状态
const loadingMoreCurrent = ref(false);

// 是否还有更多数据
const hasMoreCurrent = ref(false);

// 总数据量
const totalCurrentCount = ref(0);

// 前三名数据
const topThree = computed(() =>{
  let arr = [];
  for (var i = 0; i < 3; i++) {
    arr[i] = realTimeData.value.slice(0, 3)[i]
      ? realTimeData.value.slice(0, 3)[i]
      : { user_name: "", rank: i + 1, points: 0, bonus: 0,uuid:0 };
  }

  return arr;
});

// 当前排行榜数据（第4名开始）
const currentRankings = computed(() => {
  return realTimeData.value;
});

// 昨日排行榜数据
// const yesterdayRankings = computed(() => {
//   return yesterdayData.value;
// });

// // 格式化数字
// const formatNumber = (num: number) => {
//   return num.toLocaleString("pt-BR", {
//     minimumFractionDigits: 2,
//     maximumFractionDigits: 2,
//   });
// };

// 加载实时排行榜数据
const loadRealTimeLeaderboard = async (page: number = 1) => {
  try {
    const response = await getRealTimeLeaderboard(page, pageSize);
    console.log(response);
    if (response?.data?.leaderboard) {
      // 保存总数据量
      if (page === 1 && response.count) {
        totalCurrentCount.value = response.count;
      }

      if (page === 1) {
        realTimeData.value = response?.data?.leaderboard.sort((a, b) => a.rank - b.rank);
      } else {
        realTimeData.value = [
          ...realTimeData.value,
          ...response?.data?.leaderboard,
        ];
      }

      // 根据已加载的数据量和总数据量判断是否还有更多数据
      hasMoreCurrent.value =
        realTimeData.value.length < totalCurrentCount.value;
    }
  } catch (error) {
    console.error("Failed to load real-time leaderboard:", error);
    showError("Falha ao carregar classificação em tempo real");
  }
};

// // 加载昨日排行榜数据
// const loadYesterdayLeaderboard = async (page: number = 1) => {
//   try {
//     const response = await getYesterdayLeaderboard(page, pageSize);
//     if (response?.leaderboard) {
//       // 保存总数据量
//       if (page === 1) {
//         totalYesterdayCount.value = response.total;
//       }

//       if (page === 1) {
//         yesterdayData.value = response.leaderboard;
//       } else {
//         yesterdayData.value = [...yesterdayData.value, ...response.leaderboard];
//       }

//       // 根据已加载的数据量和总数据量判断是否还有更多数据
//       hasMoreYesterday.value =
//         yesterdayData.value.length < totalYesterdayCount.value;
//     }
//   } catch (error) {
//     console.error("Failed to load yesterday leaderboard:", error);
//     showError("Falha ao carregar classificação de ontem");
//   }
// };

// 加载更多实时排行榜数据
const loadMoreCurrentRankings = async () => {
  if (loadingMoreCurrent.value || !hasMoreCurrent.value) return;

  loadingMoreCurrent.value = true;
  currentPage.value++;
  await loadRealTimeLeaderboard(currentPage.value);
  loadingMoreCurrent.value = false;
};

// // 加载更多昨日排行榜数据
// const loadMoreYesterdayRankings = async () => {
//   if (loadingMoreYesterday.value || !hasMoreYesterday.value) return;

//   loadingMoreYesterday.value = true;
//   yesterdayPage.value++;
//   await loadYesterdayLeaderboard(yesterdayPage.value);
//   loadingMoreYesterday.value = false;
// };

// 定时刷新数据
// let refreshTimer: number | null = null;

// 启动定时刷新
// const startRefreshTimer = () => {
//   // 每60秒刷新一次数据
//   refreshTimer = window.setInterval(() => {
//     loadRealTimeLeaderboard(1);
//   }, 60000);
// };

const jackpotInfo = ref();
// 加载用户数据
const userDetails = () => {
  getUserDetails({})
    .then((res) => {
      jackpotInfo.value = res;
    })
    .catch((error) => {
      console.log(error);
      showWarning(error);
    });
};
// 格式化數字，支援自定義配置
const formatNumber = (num: number, options: any = {}) => {
  // 檢查是否為 NaN
  if (isNaN(num)) {
    return "0.00";
  }

  const {
    decimals = 2,
    thousandsSeparator = ",",
    decimalSeparator = ".",
    locale = "en-US",
  } = options;

  // 使用 Intl.NumberFormat 進行格式化，啟用千分位分組，並指定 locale
  const formatter = new Intl.NumberFormat(locale, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
    useGrouping: true, // 啟用千分位分組
  });

  // 直接返回 Intl.NumberFormat 格式化後的結果
  return formatter.format(num);
};
// 组件挂载时加载数据
onMounted(() => {
  loadRealTimeLeaderboard();
  getCurrentBalance();
  userDetails();
  // loadYesterdayLeaderboard();
  // startRefreshTimer();
});
onUnmounted(() => {
  // if (refreshTimer !== null) {
  //   clearInterval(refreshTimer);
  //   refreshTimer = null;
  // }
});
</script>

<style lang="scss" scoped>
.top-three-mobile {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  // justify-content: center;
  .rank-item0 {
    width: 100%;
    height: 90px;
    background-image: url("@/assets/images/h5/level1.png");

    background-size: 100% 90px;
    background-position: center;
    background-repeat: no-repeat;
  }

  .rank-item1 {
    width: 90%;
    height: 80px;
    background-image: url("@/assets/images/h5/level2.png");

    background-size: 100% 80px;
    background-position: center;
    background-repeat: no-repeat;
  }

  .rank-item2 {
    width: 80%;
    height: 70px;
    background-image: url("@/assets/images/h5/level3.png");
    background-size: 100% 70px;

    background-position: center;
    background-repeat: no-repeat;
  }
}

.ranking-container {
  padding: 0;
  min-height: 100vh;

  .ranking-card {
    position: relative;
  }

  .total-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: linear-gradient(0deg, #c9b737, #2abb27);
    height: 70px;
    border-radius: 10px;

    .num {
      font-size: 20px;
      font-weight: 600;
    }

    .name {
      font-size: 14px;
    }
  }
}

.ranking-header {
  text-align: center;
  position: relative;
  margin-bottom: 12px;

  img {
    width: 100%;
    height: auto;
  }

  .icon-tip {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 40px;
    height: auto;
    color: gray;
  }

  .icon-coin {
    width: 70px;
    height: auto;
  }

  .jackpot-balance {
    position: absolute;
    right: 15%;
    top: 15%;
    display: flex;
    flex-direction: column;
    font-size: 60px;
    font-weight: bold;
  }
}

.ranking-tip {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
  padding: 16px;
  background: #222b54;
  border-radius: 8px;
  margin-bottom: 24px;

  .tip-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    margin-top: 3px;
  }

  span {
    font-size: 14px;
    color: #fff;
    line-height: 1.4;
  }
}

.ranking-section {
  // margin-bottom: 32px;
  border-radius: 0;

  .section-title {
    font-size: 18px;
    font-weight: 500;
    color: #fff;
    margin: 0;
    height: 53px;
    line-height: 53px;
    text-transform: capitalize;
    text-align: center;
    background: linear-gradient(87deg, #e5d51f, #3b922a, #e5d51f);
    width: 100%;
    border-radius: 20px;
  }
}

.top-three {
  display: flex;
  justify-content: center;
  gap: 20px;
  background-image: url("@/assets/images/pm-bg.png");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  height: 280px;
  margin: 0 -16px;
  padding: 20px 16px 0;
  position: relative;

  .rank-item {
    text-align: center;
    // width: ;
    width: 210px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .rank-badge {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 8px;
      width: 100%;
    }

    .user-id {
      font-size: 14px;
      color: #ffffff;
      margin-bottom: 8px;
      word-break: break-all;
    }

    .user-aposte {
      font-size: 12px;
      color: #eaeaea;
      margin-bottom: 8px;
      word-break: break-all;
    }

    .bonus {
      font-size: 16px;
      color: #fff;
      word-break: break-all;
    }

    &.rank-1 {
      margin-top: 65px;
      order: 2;
    }

    &.rank-2 {
      margin-top: 100px;
      order: 1;
    }

    &.rank-3 {
      margin-top: 100px;
      order: 3;
    }
  }
}

.ranking-table {
  margin: 0 !important;
  background: #1b193d !important;
  border-radius: 20px !important;
  width: 100%;
  border-collapse: collapse;

  .rank1 {
    color: #d0724f !important;
  }

  .rank2 {
    color: #cd566c !important;
  }

  .rank3 {
    color: #48aabe !important;
  }

  :deep(th) {
    color: rgba(255, 255, 255) !important;
    background: linear-gradient(180deg, #1b1f2d, #3c4155);
    font-size: 14px;
    font-weight: normal;
    text-transform: capitalize;
    white-space: nowrap;
    padding: 12px 8px !important;
    text-align: center !important;
  }

  tr:nth-child(even) {
    background: #2b324d;
  }

  :deep(td) {
    color: #fff !important;
    font-size: 14px;
    padding: 12px 8px !important;
    text-align: center;
    vertical-align: middle;

    &.rank-cell {
      padding: 8px 0 !important;

      .rank-number {
        display: inline-block;
        width: 45px;
        text-align: center;
        font-size: 14px;
      }

      img {
        display: block;
        margin: 0 auto;
      }
    }

    &.user-id {
      color: #fff !important;
      word-break: break-all;
    }

    &.amount {
      color: #ffdf00 !important;
    }

    &.bonus {
      color: #ffdf00 !important;
      font-weight: 500;
      word-break: break-all;
    }
  }
}

.view-more-btn {
  background: linear-gradient(0deg, #c9b737, #2abb27);
  border-radius: 22px;
  min-width: 160px;
  color: #fff !important;
  height: 44px;
  font-size: 18px;
  text-transform: none;
  margin-top: 16px;
}

.game-type-filter {
  margin: 0;
  background: #251d31;
  width: 100%;

  .game-type-buttons {
    width: 100%;
    background: transparent;
    border-radius: 0;
    display: flex;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .game-type-btn {
      flex: 1;
      min-width: 100px;
      height: 44px;
      padding: 0 20px;
      color: #fff;
      font-size: 14px;
      font-weight: 400;
      text-transform: none;
      border-radius: 0;
      border: none;
      position: relative;
      opacity: 0.8;

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 2px;
        background: #ffdf00;
        transition: width 0.3s ease;
      }

      &:hover {
        opacity: 1;
      }

      &.v-btn--active {
        background: transparent;
        opacity: 1;
        color: #ffdf00;

        &::after {
          width: 80%;
        }
      }
    }
  }
}

.v-btn.v-btn--icon {
  background: transparent;
  color: #fff;
  width: 40px;
  height: 40px;
  border-radius: 8px;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

.v-btn.v-btn--size-default {
  padding: 0 16px;
  min-width: auto;

  .v-icon {
    font-size: 20px;
  }
}

.rule-textarea {
  font-size: 16px;
  color: #fff;
  line-height: 1.8;
}

.rule-textarea-readonly {
  background: transparent !important;
}

.rule-textarea-readonly :deep(.v-field) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.rule-textarea-readonly :deep(.v-field__input) {
  /* padding: 0 !important; */
  min-height: auto !important;
  color: #fff !important;
  font-size: 16px !important;
  line-height: 1.8 !important;
}

.rule-textarea-readonly :deep(.v-field__outline) {
  display: none !important;
}

@media (max-width: 768px) {
  .ranking-container {
    padding: 16px;

    .ranking-card {
      border-radius: 12px;
      overflow: hidden;
    }
  }

  .ranking-header {
    text-align: center;
    position: relative;
    margin-bottom: 12px;

    img {
      width: 100%;
      height: auto;
    }

    .icon-tip {
      position: absolute;
      right: 10px;
      top: 10px;
      width: 20px;
      height: auto;
      color: gray;
    }

    .icon-coin {
      width: 30px;
      height: auto;
    }

    .jackpot-balance {
      position: absolute;
      right: 15%;
      top: 15%;
      display: flex;
      flex-direction: column;
      font-size: 25px;
      font-weight: bold;
    }
  }

  .ranking-tip {
    margin: 0 0 16px 0;
    padding: 12px;
  }

  .total-card {
    height: 60px;

    .num {
      font-size: 18px;
    }

    .name {
      font-size: 12px;
    }
  }

  .ranking-section {
    margin-bottom: 16px;

    .section-title {
      font-size: 16px;
      height: 44px;
      line-height: 44px;
      border-radius: 20px;
    }
  }

  .game-type-filter {
    .game-type-buttons {
      .game-type-btn {
        min-width: 80px;
        height: 40px;
        padding: 0 12px;
        font-size: 13px;
      }
    }
  }

  .ranking-table {
    margin: 8px 0 !important;

    :deep(th) {
      font-size: 12px;
      padding: 8px 4px !important;
    }

    :deep(td) {
      font-size: 12px;
      padding: 8px 4px !important;

      &.rank-cell {
        padding: 4px 0 !important;

        .rank-number {
          width: 30px;
          font-size: 12px;
        }

        img {
          width: 30px;
          height: 30px;
        }
      }
    }
  }

  .view-more-btn {
    margin: 12px auto;
    height: 40px;
    min-width: 140px;
    font-size: 16px;
  }

  .rank-badge {
    padding: 14px;

    .user-id {
      font-size: 12px;
      word-break: break-all;
      // margin-bottom: 4px;
    }

    .user-aposte {
      font-size: 11px;
      word-break: break-all;
      // margin-bottom: 4px;
    }

    .bonus {
      font-size: 0.7rem;
      word-break: break-all;
    }
  }

  .rank-item0 {
    .rank-badge {
      padding-top: 24px;
    }
  }

  .rank-item1 {
    .rank-badge {
      padding-top: 18px;
    }
  }

  // .top-three {
  //   height: 200px;
  //   margin: 0;
  //   gap: 14px;
  //   background-size: 100% auto;
  //   padding: 0;

  //   .rank-item0 {
  //     max-width: 100px;

  //     .user-id {
  //       font-size: 12px;
  //       margin-bottom: 4px;
  //     }

  //     .user-aposte {
  //       font-size: 11px;
  //       margin-bottom: 4px;
  //     }

  //     .bonus {
  //       font-size: 0.7rem;
  //     }

  //     &.rank-1 {
  //       margin-top: 60px;
  //     }

  //     &.rank-2 {
  //       margin-top: 80px;
  //     }

  //     &.rank-3 {
  //       margin-top: 80px;
  //     }
  //   }
  // }

  .v-btn.v-btn--icon {
    width: 36px;
    height: 36px;

    .v-icon {
      font-size: 18px;
    }
  }
}

.activity-rule {
  // background: #222b54;
  border-radius: 8px;
  color: #fff;
  // margin-top: 24px;
  font-size: 15px;
  line-height: 1.7;
}
</style>
