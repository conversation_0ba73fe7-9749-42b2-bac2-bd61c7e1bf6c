<script setup lang="tsx">
import { ref } from "vue";
import { ElButton, ElPopconfirm, ElMessage, ElTag, ElIcon } from "element-plus";
import { EditPen, Delete } from "@element-plus/icons-vue";
import {
  fetchGetAllCustomerKeywords,
  fetchDeleteCustomerKeyword,
} from "@/service/api/customerFaq";
import { $t } from "@/locales";
import { useTable, useTableOperate } from "@/hooks/common/table";
import CustomerFaqDrawer from "./modules/customer-faq-drawer.vue";
import CustomerFaqSearch from "./modules/customer-faq-search.vue";
import { useAuth } from "@/hooks/business/auth";

const { hasAuth } = useAuth();

defineOptions({ name: "CustomerFaqManagement" });

// 问题类型映射
const keywordTypeMap: Record<number, string> = {
  1: "欢迎语",
  2: "充值问题",
  3: "提现问题",
  4: "账户问题",
  5: "游戏问题",
  6: "活动优惠",
  7: "其他问题",
};

// 问题类型颜色映射
const keywordTypeColorMap: Record<
  number,
  "info" | "success" | "warning" | "danger" | "primary"
> = {
  1: "info",
  2: "success",
  3: "warning",
  4: "danger",
  5: "primary",
  6: "success",
  7: "info",
};

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  columnChecks,
} = useTable({
  apiFn: fetchGetAllCustomerKeywords,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 10,
    current: 1,
    keyword_type: undefined,
  },
  columns: () => [
    { prop: "index", label: "ID", width: 80 },
    {
      prop: "keyword_type",
      label: "问题类型",
      width: 120,
      formatter: (row: any) => (
        <ElTag type={keywordTypeColorMap[row.keyword_type] || "info"}>
          {keywordTypeMap[row.keyword_type] || "未知"}
        </ElTag>
      ),
    },
    {
      prop: "keyword",
      label: "问题",
      minWidth: 200,
      showOverflowTooltip: true,
    },
    {
      prop: "response",
      label: "答案",
      minWidth: 300,
      showOverflowTooltip: true,
    },
    {
      prop: "weight",
      label: "权重",
      width: 80,
      align: "center",
    },
    {
      prop: "status",
      label: "状态",
      width: 100,
      formatter: (row: any) => (
        <ElTag type={row.status === 1 ? "success" : "danger"}>
          {row.status === 1 ? "启用" : "禁用"}
        </ElTag>
      ),
    },
    {
      prop: "operate",
      label: "操作",
      width: 160,
      align: "center",
      formatter: (row: any) => (
        <div class="flex-center gap-2">
          {hasAuth(3) && (
            <ElButton
              type="primary"
              plain
              size="small"
              onClick={() => edit(row.id)}
            >
              <ElIcon>
                <EditPen />
              </ElIcon>
              编辑
            </ElButton>
          )}
          {hasAuth(2) && (
            <ElPopconfirm
              title="确定要删除这个常见问题吗？"
              onConfirm={() => handleDelete(row.id)}
            >
              {{
                reference: () => (
                  <ElButton type="danger" plain size="small">
                    <ElIcon>
                      <Delete />
                    </ElIcon>
                    删除
                  </ElButton>
                ),
              }}
            </ElPopconfirm>
          )}
        </div>
      ),
    },
  ],
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  onDeleted,
  checkedRowKeys,
} = useTableOperate<any>(data, getData, "id");

async function handleDelete(id: number) {
  try {
    const { error } = await fetchDeleteCustomerKeyword(id);
    if (!error) {
      ElMessage.success("删除成功");
      onDeleted();
    }
  } catch (error) {
    console.error("删除失败:", error);
    ElMessage.error("删除失败");
  }
}

async function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    ElMessage.warning("请选择要删除的问题");
    return;
  }
  // 批量删除功能可以后续实现
  ElMessage.info("批量删除功能开发中");
}

function edit(id: number) {
  handleEdit(id);
}

function handleSearch() {
  getDataByPage(1);
}

function handleReset() {
  resetSearchParams();
  getDataByPage(1);
}
</script>

<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <CustomerFaqSearch
      v-model:model="searchParams"
      @reset="handleReset"
      @search="handleSearch"
    >
      <template #table-operation>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        >
          <template #default>
            <ElButton v-if="hasAuth(1)" type="success" @click="handleAdd">
              <template #icon>
                <icon-ic-round-plus class="text-icon" />
              </template>
              添加常见问题
            </ElButton>
          </template>
        </TableHeaderOperation>
      </template>
    </CustomerFaqSearch>

    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="checkedRowKeys = $event.map((row) => row.id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
    </ElCard>

    <CustomerFaqDrawer
      v-model:visible="drawerVisible"
      :operate-type="operateType"
      :row-data="editingData"
      @submitted="getData"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
