import React, { ComponentProps } from "react";

export function DiscordIcon(props: ComponentProps<"svg">) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 15 15"
      fill="none"
      {...props}
    >
      <path
        fill="currentColor"
        fillRule="evenodd"
        d="M5.075 1.826a.48.48 0 00-.127-.003c-.841.091-2.121.545-2.877.955a.48.48 0 00-.132.106c-.314.359-.599.944-.822 1.498C.887 4.95.697 5.55.59 5.984.236 7.394.043 9.087.017 10.693a.48.48 0 00.056.23c.3.573.947 1.104 1.595 1.492.655.393 1.42.703 2.036.763a.48.48 0 00.399-.153c.154-.167.416-.557.614-.86.09-.138.175-.27.241-.375.662.12 1.492.19 2.542.19 1.048 0 1.878-.07 2.54-.19.066.106.15.237.24.374.198.304.46.694.615.861a.48.48 0 00.399.153c.616-.06 1.38-.37 2.035-.763.648-.388 1.295-.919 1.596-1.492a.48.48 0 00.055-.23c-.025-1.606-.219-3.3-.571-4.71a12.98 12.98 0 00-.529-1.601c-.223-.554-.508-1.14-.821-1.498a.48.48 0 00-.133-.106c-.755-.41-2.035-.864-2.877-.955a.48.48 0 00-.126.003 1.18 1.18 0 00-.515.238 2.905 2.905 0 00-.794.999A14.046 14.046 0 007.5 3.02c-.402 0-.774.015-1.117.042a2.905 2.905 0 00-.794-.998 1.18 1.18 0 00-.514-.238zm5.943 9.712a23.136 23.136 0 00.433.643c.396-.09.901-.3 1.385-.59.543-.325.974-.7 1.182-1.017-.033-1.506-.219-3.07-.54-4.358a12.046 12.046 0 00-.488-1.475c-.2-.498-.415-.92-.602-1.162-.65-.337-1.675-.693-2.343-.79a.603.603 0 00-.058.04 1.5 1.5 0 00-.226.22 2.52 2.52 0 00-.113.145c.305.056.577.123.818.197.684.21 1.177.5 1.418.821a.48.48 0 11-.768.576c-.059-.078-.316-.29-.932-.48-.595-.182-1.47-.328-2.684-.328-1.214 0-2.09.146-2.684.329-.616.19-.873.4-.932.479a.48.48 0 11-.768-.576c.241-.322.734-.61 1.418-.82.24-.075.512-.141.816-.197a2.213 2.213 0 00-.114-.146 1.5 1.5 0 00-.225-.22.604.604 0 00-.059-.04c-.667.097-1.692.453-2.342.79-.188.243-.402.664-.603 1.162-.213.53-.39 1.087-.487 1.475-.322 1.288-.508 2.852-.54 4.358.208.318.638.692 1.181 1.018.485.29.989.5 1.386.589a16.32 16.32 0 00.433-.643c-.785-.279-1.206-.662-1.48-1.072a.48.48 0 01.8-.532c.26.392.944 1.086 4.2 1.086 3.257 0 3.94-.694 4.2-1.086a.48.48 0 01.8.532c-.274.41-.696.794-1.482 1.072zM4.08 7.012c.244-.262.575-.41.92-.412.345.002.676.15.92.412.243.263.38.618.38.988s-.137.725-.38.988c-.244.262-.575.41-.92.412a1.263 1.263 0 01-.92-.412A1.453 1.453 0 013.7 8c0-.37.137-.725.38-.988zM10 6.6c-.345.002-.676.15-.92.412-.243.263-.38.618-.38.988s.137.725.38.988c.244.262.575.41.92.412.345-.002.676-.15.92-.412.243-.263.38-.618.38-.988s-.137-.725-.38-.988A1.263 1.263 0 0010 6.6z"
        clipRule="evenodd"
      />
    </svg>
  );
}

export function AndroidIcon(props: ComponentProps<"svg">) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_1_14)">
        <rect width="32" height="32" fill="black" />
        <path
          d="M12.829 18.3059C13.5993 18.3059 14.2239 18.9248 14.2239 19.6882V25.6178C14.2239 26.3812 13.5993 27 12.829 27C12.0587 27 11.4342 26.3812 11.4342 25.6178V19.6882C11.4342 18.9248 12.0587 18.3059 12.829 18.3059Z"
          fill="#A4C639"
        />
        <path
          d="M9.64467 11.1768C9.64007 11.2292 9.63602 11.2822 9.63602 11.3358V20.3197C9.63602 21.2859 10.4078 22.0642 11.3658 22.0642H20.585C21.5431 22.0642 22.3148 21.2859 22.3148 20.3197V11.3358C22.3148 11.2822 22.3128 11.2291 22.3083 11.1768H9.64467Z"
          fill="#A4C639"
        />
        <path
          d="M19.1219 18.3059C19.8923 18.3059 20.5168 18.9248 20.5168 19.6882V25.6178C20.5168 26.3812 19.8923 27 19.1219 27C18.3517 27 17.7271 26.3812 17.7271 25.6178V19.6882C17.7271 18.9248 18.3517 18.3059 19.1219 18.3059Z"
          fill="#A4C639"
        />
        <path
          d="M7.39489 11.9764C8.16515 11.9764 8.78968 12.5953 8.78968 13.3587V19.2883C8.78968 20.0517 8.16515 20.6706 7.39489 20.6706C6.62453 20.6706 6 20.0517 6 19.2883V13.3587C5.9999 12.5953 6.62443 11.9764 7.39489 11.9764Z"
          fill="#A4C639"
        />
        <path
          d="M24.5561 11.9764C25.3264 11.9764 25.9509 12.5953 25.9509 13.3587V19.2883C25.9509 20.0517 25.3264 20.6706 24.5561 20.6706C23.7858 20.6706 23.1613 20.0517 23.1613 19.2883V13.3587C23.1613 12.5953 23.7858 11.9764 24.5561 11.9764Z"
          fill="#A4C639"
        />
        <path
          d="M9.68536 10.5866C9.72415 7.82719 12.1546 5.56454 15.2744 5.26636H16.6763C19.7963 5.56463 22.2265 7.82729 22.2653 10.5866H9.68536Z"
          fill="#A4C639"
        />
        <path
          d="M10.1705 5L11.6419 7.5257"
          stroke="#A4C639"
          stroke-width="3"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M21.7805 5L20.3091 7.5257"
          stroke="#A4C639"
          stroke-width="3"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M14.1016 8.1136C14.1028 8.49933 13.7807 8.81293 13.382 8.81399C12.9836 8.81506 12.6595 8.50322 12.6584 8.11749C12.6584 8.11613 12.6584 8.11486 12.6584 8.1136C12.6573 7.72777 12.9794 7.41425 13.378 7.41318C13.7764 7.41211 14.1005 7.72388 14.1016 8.10971C14.1016 8.11097 14.1016 8.11234 14.1016 8.1136Z"
          fill="white"
        />
        <path
          d="M19.4884 8.1136C19.4896 8.49933 19.1674 8.81293 18.7688 8.81399C18.3703 8.81506 18.0463 8.50322 18.0451 8.11749C18.0451 8.11613 18.0451 8.11486 18.0451 8.1136C18.044 7.72777 18.3662 7.41425 18.7647 7.41318C19.1632 7.41211 19.4872 7.72388 19.4884 8.10971C19.4884 8.11097 19.4884 8.11234 19.4884 8.1136Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_1_14">
          <rect width="32" height="32" rx="4" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function iOSIcon(props: ComponentProps<"svg">) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_3_26)">
        <rect width="32" height="32" fill="black" />
        <path
          d="M22.3209 16.7504C22.3546 20.3824 25.507 21.5911 25.542 21.6065C25.5153 21.6916 25.0383 23.3288 23.8811 25.0199C22.8808 26.482 21.8426 27.9386 20.2071 27.9688C18.6001 27.9984 18.0834 27.0158 16.2462 27.0158C14.4095 27.0158 13.8353 27.9386 12.3141 27.9984C10.7355 28.0582 9.53334 26.4175 8.52476 24.9608C6.46376 21.9811 4.88873 16.5409 7.0036 12.8687C8.05422 11.0451 9.93176 9.8903 11.9697 9.86069C13.5198 9.83112 14.983 10.9036 15.9307 10.9036C16.8777 10.9036 18.6558 9.61385 20.525 9.80327C21.3075 9.83583 23.5041 10.1194 24.9145 12.1839C24.8009 12.2544 22.2936 13.714 22.3209 16.7504ZM19.3007 7.83196C20.1388 6.81747 20.7029 5.4052 20.549 4C19.3409 4.04855 17.8801 4.80502 17.0136 5.81896C16.237 6.71684 15.5569 8.15397 15.7404 9.53135C17.087 9.63553 18.4625 8.8471 19.3007 7.83196Z"
          fill="#BFBFBF"
        />
      </g>
      <defs>
        <clipPath id="clip0_3_26">
          <rect width="32" height="32" rx="4" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function WebIcon(props: ComponentProps<"svg">) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_6_2)">
        <rect width="32" height="32" rx="4" fill="black" />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M6.90039 15.8789H8.19922L8.66602 18.3496L9.34961 15.8789H10.6387L11.3281 18.3535L11.7988 15.8789H13.0918L12.1152 20.3086H10.7734L9.99609 17.5195L9.22461 20.3086H7.88477L6.90039 15.8789ZM5.625 7H26.375C27.2695 7 28 7.73047 28 8.625V23.1992C28 24.0918 27.2695 24.8242 26.375 24.8242H5.625C4.73047 24.8242 4 24.0918 4 23.1992V8.625C4 7.73047 4.73047 7 5.625 7ZM27.041 11.5488H5.0332V23.3574C5.0332 23.4824 5.08203 23.5918 5.16406 23.6758C5.24609 23.7578 5.35742 23.8066 5.48242 23.8066H26.584C26.709 23.8066 26.8184 23.7578 26.9023 23.6758C26.9863 23.5918 27.0332 23.4824 27.0332 23.3574V11.5488H27.041ZM24.8281 8.82617C25.2715 8.82617 25.6309 9.18555 25.6309 9.62891C25.6309 10.0723 25.2715 10.4316 24.8281 10.4316C24.3848 10.4316 24.0254 10.0723 24.0254 9.62891C24.0254 9.18555 24.3867 8.82617 24.8281 8.82617ZM19.3906 8.82617C19.834 8.82617 20.1934 9.18555 20.1934 9.62891C20.1934 10.0723 19.834 10.4316 19.3906 10.4316C18.9473 10.4316 18.5879 10.0723 18.5879 9.62891C18.5879 9.18555 18.9473 8.82617 19.3906 8.82617ZM22.1094 8.82617C22.5527 8.82617 22.9121 9.18555 22.9121 9.62891C22.9121 10.0723 22.5527 10.4316 22.1094 10.4316C21.666 10.4316 21.3066 10.0723 21.3066 9.62891C21.3066 9.18555 21.666 8.82617 22.1094 8.82617ZM19.293 15.8789H20.5918L21.0586 18.3496L21.7422 15.8789H23.0313L23.7207 18.3535L24.1914 15.8789H25.4844L24.5078 20.3086H23.166L22.3887 17.5195L21.6172 20.3086H20.2773L19.293 15.8789ZM13.0977 15.8789H14.3965L14.8633 18.3496L15.5469 15.8789H16.8359L17.5254 18.3535L17.9961 15.8789H19.2891L18.3125 20.3086H16.9707L16.1934 17.5195L15.4219 20.3086H14.082L13.0977 15.8789Z"
          fill="#1AF28B"
        />
      </g>
      <defs>
        <clipPath id="clip0_6_2">
          <rect width="32" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function FlutterIcon(props: ComponentProps<"svg">) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_3_28)">
        <rect width="32" height="32" rx="4" fill="black" />
        <path
          d="M9.69574 19.6948L6 15.999L18 4H25.3905M25.3905 15.0727H18L15.2316 17.8411L18.9273 21.5368"
          fill="#42A5F5"
          fill-opacity="0.8"
        />
        <path
          d="M15.2316 25.2316L18 28H25.3905L18.9273 21.5368"
          fill="#0D47A1"
        />
        <path
          d="M11.5446 21.5388L15.2345 17.8479L18.9244 21.5378L15.2345 25.2287L11.5446 21.5388Z"
          fill="#42A5F5"
        />
        <path
          d="M15.2345 25.2287L18.9244 21.5388L19.4395 22.0538L15.7496 25.7438L15.2345 25.2287Z"
          fill="url(#paint0_linear_3_28)"
        />
        <path
          d="M15.2316 25.2316L20.715 23.3372L18.9273 21.5359"
          fill="url(#paint1_linear_3_28)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_3_28"
          x1="17.0802"
          y1="23.383"
          x2="17.5953"
          y2="23.8981"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.2" stop-opacity="0.15" />
          <stop offset="0.85" stop-color="#616161" stop-opacity="0.01" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_3_28"
          x1="15.2329"
          y1="23.3838"
          x2="20.7163"
          y2="23.3838"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.2" stop-opacity="0.55" />
          <stop offset="0.85" stop-color="#616161" stop-opacity="0.01" />
        </linearGradient>
        <clipPath id="clip0_3_28">
          <rect width="32" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function ReactIcon(props: ComponentProps<"svg">) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_3_34)">
        <rect width="32" height="32" rx="4" fill="black" />
        <path
          d="M28 15.689C28 14.0991 26.009 12.5923 22.9564 11.658C23.6608 8.54668 23.3477 6.07134 21.9682 5.27884C21.6502 5.09295 21.2784 5.00489 20.8724 5.00489V6.0958C21.0974 6.0958 21.2784 6.13983 21.4301 6.22299C22.0954 6.60457 22.384 8.05748 22.159 9.92621C22.1052 10.3861 22.0171 10.8704 21.9095 11.3645C20.9507 11.1296 19.9038 10.9486 18.8031 10.8312C18.1427 9.92621 17.4578 9.10436 16.768 8.38524C18.3628 6.90298 19.8598 6.09091 20.8773 6.09091V5C19.532 5 17.7709 5.95883 15.9902 7.6221C14.2095 5.96861 12.4484 5.01957 11.1031 5.01957V6.11048C12.1158 6.11048 13.6176 6.91765 15.2124 8.39013C14.5275 9.10925 13.8426 9.92621 13.192 10.8312C12.0864 10.9486 11.0395 11.1296 10.0807 11.3693C9.9682 10.8801 9.88504 10.4056 9.82633 9.95067C9.59641 8.08194 9.88015 6.62903 10.5406 6.24256C10.6873 6.1545 10.8781 6.11537 11.1031 6.11537V5.02446C10.6922 5.02446 10.3204 5.11252 9.99755 5.29841C8.62291 6.09091 8.31472 8.56135 9.02405 11.6629C5.98125 12.6021 4 14.104 4 15.689C4 17.2788 5.99103 18.7856 9.04362 19.7199C8.33918 22.8312 8.65226 25.3066 10.0318 26.0991C10.3498 26.285 10.7216 26.373 11.1325 26.373C12.4778 26.373 14.2389 25.4142 16.0196 23.7509C17.8002 25.4044 19.5614 26.3534 20.9066 26.3534C21.3176 26.3534 21.6894 26.2654 22.0122 26.0795C23.3869 25.287 23.6951 22.8166 22.9857 19.715C26.0188 18.7807 28 17.274 28 15.689ZM21.6307 12.426C21.4497 13.0571 21.2246 13.7077 20.9702 14.3583C20.7697 13.967 20.5593 13.5756 20.3294 13.1843C20.1044 12.7929 19.8647 12.4113 19.625 12.0395C20.3196 12.1423 20.9898 12.2695 21.6307 12.426ZM19.3901 17.636C19.0086 18.2964 18.6172 18.9225 18.2112 19.5047C17.4823 19.5683 16.7436 19.6025 16 19.6025C15.2613 19.6025 14.5226 19.5683 13.7986 19.5096C13.3926 18.9274 12.9963 18.3062 12.6148 17.6506C12.243 17.0098 11.9054 16.3592 11.5972 15.7036C11.9005 15.0481 12.243 14.3926 12.6099 13.7517C12.9914 13.0913 13.3828 12.4651 13.7888 11.883C14.5177 11.8194 15.2564 11.7852 16 11.7852C16.7387 11.7852 17.4774 11.8194 18.2014 11.8781C18.6074 12.4603 19.0037 13.0815 19.3852 13.7371C19.757 14.3779 20.0946 15.0285 20.4028 15.6841C20.0946 16.3396 19.757 16.9951 19.3901 17.636ZM20.9702 17C21.2344 17.6555 21.4594 18.311 21.6453 18.947C21.0045 19.1035 20.3294 19.2356 19.6298 19.3384C19.8695 18.9617 20.1093 18.5752 20.3343 18.179C20.5593 17.7876 20.7697 17.3914 20.9702 17ZM16.0098 22.2197C15.5548 21.7501 15.0999 21.2267 14.6498 20.6543C15.0901 20.6739 15.5402 20.6885 15.9951 20.6885C16.455 20.6885 16.9099 20.6788 17.3551 20.6543C16.9148 21.2267 16.4598 21.7501 16.0098 22.2197ZM12.3702 19.3384C11.6755 19.2356 11.0053 19.1084 10.3645 18.9519C10.5455 18.3208 10.7705 17.6702 11.0249 17.0196C11.2254 17.4109 11.4358 17.8023 11.6657 18.1936C11.8956 18.585 12.1305 18.9666 12.3702 19.3384ZM15.9853 9.15817C16.4403 9.6278 16.8952 10.1512 17.3453 10.7236C16.905 10.704 16.455 10.6894 16 10.6894C15.5402 10.6894 15.0852 10.6991 14.64 10.7236C15.0803 10.1512 15.5353 9.6278 15.9853 9.15817ZM12.3653 12.0395C12.1256 12.4162 11.8859 12.8027 11.6608 13.1989C11.4358 13.5903 11.2254 13.9817 11.0249 14.373C10.7607 13.7175 10.5357 13.062 10.3498 12.426C10.9906 12.2744 11.6657 12.1423 12.3653 12.0395ZM7.93804 18.1643C6.20628 17.4256 5.08602 16.457 5.08602 15.689C5.08602 14.9209 6.20628 13.9474 7.93804 13.2136C8.35875 13.0326 8.81859 12.8712 9.29311 12.7195C9.57195 13.6784 9.93885 14.6763 10.3938 15.6987C9.94374 16.7163 9.58174 17.7093 9.30779 18.6633C8.82348 18.5116 8.36364 18.3453 7.93804 18.1643ZM10.5699 25.1549C9.90461 24.7733 9.61598 23.3204 9.84101 21.4517C9.89482 20.9918 9.98288 20.5075 10.0905 20.0135C11.0493 20.2483 12.0962 20.4293 13.1969 20.5467C13.8573 21.4517 14.5422 22.2735 15.232 22.9927C13.6372 24.4749 12.1402 25.287 11.1227 25.287C10.9026 25.2821 10.7167 25.2381 10.5699 25.1549ZM22.1737 21.4272C22.4036 23.296 22.1199 24.7489 21.4594 25.1353C21.3127 25.2234 21.1219 25.2625 20.8969 25.2625C19.8842 25.2625 18.3824 24.4554 16.7876 22.9829C17.4725 22.2638 18.1574 21.4468 18.808 20.5418C19.9136 20.4244 20.9605 20.2434 21.9193 20.0037C22.0318 20.4978 22.1199 20.9723 22.1737 21.4272ZM24.0571 18.1643C23.6364 18.3453 23.1765 18.5067 22.702 18.6584C22.4232 17.6996 22.0563 16.7016 21.6013 15.6792C22.0514 14.6616 22.4134 13.6686 22.6873 12.7146C23.1716 12.8663 23.6315 13.0326 24.062 13.2136C25.7937 13.9523 26.914 14.9209 26.914 15.689C26.9091 16.457 25.7888 17.4305 24.0571 18.1643Z"
          fill="#61DAFB"
        />
        <path
          d="M15.9951 17.9246C17.2298 17.9246 18.2308 16.9237 18.2308 15.689C18.2308 14.4543 17.2298 13.4533 15.9951 13.4533C14.7604 13.4533 13.7595 14.4543 13.7595 15.689C13.7595 16.9237 14.7604 17.9246 15.9951 17.9246Z"
          fill="#61DAFB"
        />
      </g>
      <defs>
        <clipPath id="clip0_3_34">
          <rect width="32" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function UnityIcon(props: ComponentProps<"svg">) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_3_42)">
        <rect width="32" height="32" rx="4" fill="black" />
        <path
          d="M19.2663 16.0007L23.4664 8.74826L25.4959 16.0007L23.4663 23.2515L19.2663 16.0007ZM17.2192 17.1785L21.4198 24.4298L14.1042 22.5556L8.81906 17.1785H17.2192ZM21.4189 7.56956L17.2192 14.822H8.81906L14.1042 9.44455L21.4189 7.56956ZM27.4148 13.5326L24.8525 4L15.2886 6.55472L13.8729 9.04375L11.0002 9.02317L4 16.0014L11.0002 22.9779H11.0004L13.872 22.9567L15.2897 25.4457L24.8525 28L27.4147 18.4693L25.9595 16.0008L27.4147 13.5327L27.4148 13.5326Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_3_42">
          <rect width="32" height="32" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function UnrealIcon(props: ComponentProps<"svg">) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_3_44)">
        <rect width="32" height="32" fill="black" />
        <path
          d="M16 4.57034C22.308 4.57034 27.4297 9.70342 27.4297 16C27.4297 22.2966 22.2966 27.4297 16 27.4297C9.70342 27.4297 4.57034 22.308 4.57034 16C4.57034 9.69201 9.70342 4.57034 16 4.57034ZM16 4C9.37262 4 4 9.37262 4 16C4 22.6274 9.37262 28 16 28C22.6274 28 28 22.6274 28 16C28 9.37262 22.6388 4 16 4Z"
          fill="white"
        />
        <path
          d="M15.1673 8.81366C15.1673 8.81366 12.4639 9.57792 10.0342 12.1559C7.60455 14.7338 7.29657 16.5703 7.29657 17.9391C7.83269 17.0266 11.1407 11.9962 11.9163 14.3916V20.1178C11.9163 20.1178 11.8707 20.8935 10.6844 20.5855C11.038 21.2471 12.8631 22.8783 16.1597 23.2091C16.9125 22.4562 17.8935 21.3726 17.8935 21.3726L19.5361 22.7642C19.5361 22.7642 22.4905 20.8479 23.654 18.0646C22.5703 18.7718 21.2585 20.4144 20.5741 19.2623V12.2927C20.5741 12.2927 22.3308 9.65777 22.6046 9.53229C21.9087 9.65777 19.4563 10.4677 18.1673 12.1331C17.8023 11.7338 16.7871 11.7224 16.7871 11.7224C16.7871 11.7224 17.5855 12.384 17.597 12.9886C17.6084 13.5931 17.597 18.635 17.597 19.2167C17.0494 19.7756 16.4677 20.0722 16.0912 20.0722C15.2129 20.0722 14.962 19.7642 14.7224 19.4562V12.1331C14.7224 12.1331 14.289 12.4981 13.9468 11.9049C13.6046 11.3118 13.5931 10.1597 15.1673 8.81366Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_3_44">
          <rect width="32" height="32" rx="4" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function WagmiIcon(props: ComponentProps<"svg">) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_27_41)">
        <rect width="32" height="32" fill="black" />
        <g clip-path="url(#clip1_27_41)">
          <path
            d="M30 13.5989C30 13.9297 29.7318 14.1979 29.4011 14.1979C29.0703 14.1979 28.8021 13.9297 28.8021 13.5989C28.8021 13.2682 29.0703 13 29.4011 13C29.7318 13 30 13.2682 30 13.5989Z"
            fill="white"
          />
          <path
            d="M22.6631 18.2406C22.9112 18.2406 23.1123 18.0396 23.1123 17.7914V15.9947C23.1123 15.7466 23.3134 15.5455 23.5615 15.5455H24.4599C24.708 15.5455 24.9091 15.7466 24.9091 15.9947V17.7914C24.9091 18.0396 25.1102 18.2406 25.3583 18.2406C25.6064 18.2406 25.8075 18.0396 25.8075 17.7914V15.9947C25.8075 15.7466 26.0086 15.5455 26.2567 15.5455H27.1551C27.4032 15.5455 27.6043 15.7466 27.6043 15.9947V17.7914C27.6043 18.0396 27.8054 18.2406 28.0535 18.2406H29.4011C29.6492 18.2406 29.8503 18.0396 29.8503 17.7914V15.0963C29.8503 14.8482 29.6492 14.6471 29.4011 14.6471C29.153 14.6471 28.9519 14.8482 28.9519 15.0963V17.1177C28.9519 17.2417 28.8513 17.3423 28.7273 17.3423C28.6032 17.3423 28.5027 17.2417 28.5027 17.1177V15.0963C28.5027 14.8482 28.3016 14.6471 28.0535 14.6471H22.6631C22.415 14.6471 22.2139 14.8482 22.2139 15.0963V17.7914C22.2139 18.0396 22.415 18.2406 22.6631 18.2406Z"
            fill="white"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M3.34759 17.3423C3.09951 17.3423 2.8984 17.1411 2.8984 16.8931V15.0963C2.8984 14.8482 2.69728 14.6471 2.4492 14.6471C2.20111 14.6471 2 14.8482 2 15.0963V17.7914C2 18.0396 2.20111 18.2406 2.4492 18.2406H7.83957C8.08768 18.2406 8.28877 18.0396 8.28877 17.7914V15.9947C8.28877 15.7466 8.48986 15.5455 8.73797 15.5455H13.9037C14.0278 15.5455 14.1283 15.646 14.1283 15.7701C14.1283 15.8941 14.0278 15.9947 13.9037 15.9947H9.18717C8.93906 15.9947 8.73797 16.1958 8.73797 16.4439V17.7914C8.73797 18.0396 8.93906 18.2406 9.18717 18.2406H14.5775C14.8256 18.2406 15.0267 18.0396 15.0267 17.7914V15.0963C15.0267 14.8482 14.8256 14.6471 14.5775 14.6471H7.83957C7.59147 14.6471 7.39037 14.8482 7.39037 15.0963V16.8931C7.39037 17.1411 7.18928 17.3423 6.94118 17.3423H6.04278C5.79469 17.3423 5.59358 17.1411 5.59358 16.8931V15.0963C5.59358 14.8482 5.39247 14.6471 5.14439 14.6471C4.8963 14.6471 4.69519 14.8482 4.69519 15.0963V16.8931C4.69519 17.1411 4.49408 17.3423 4.24599 17.3423H3.34759ZM14.1283 17.1177C14.1283 17.2417 14.0278 17.3423 13.9037 17.3423H9.86096C9.73693 17.3423 9.63636 17.2417 9.63636 17.1177C9.63636 16.9936 9.73693 16.8931 9.86096 16.8931H13.9037C14.0278 16.8931 14.1283 16.9936 14.1283 17.1177Z"
            fill="white"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M15.4759 17.7914C15.4759 18.0396 15.677 18.2406 15.9251 18.2406H20.6417C20.7657 18.2406 20.8663 18.3412 20.8663 18.4652C20.8663 18.5893 20.7657 18.6898 20.6417 18.6898H15.9251C15.677 18.6898 15.4759 18.8909 15.4759 19.139C15.4759 19.3871 15.677 19.5882 15.9251 19.5882H21.3155C21.5636 19.5882 21.7647 19.3871 21.7647 19.139V15.0963C21.7647 14.8482 21.5636 14.6471 21.3155 14.6471H15.9251C15.677 14.6471 15.4759 14.8482 15.4759 15.0963V17.7914ZM16.8235 15.5455C16.5754 15.5455 16.3743 15.7466 16.3743 15.9947V16.8931C16.3743 17.1411 16.5754 17.3423 16.8235 17.3423H20.4171C20.6652 17.3423 20.8663 17.1411 20.8663 16.8931V15.9947C20.8663 15.7466 20.6652 15.5455 20.4171 15.5455H16.8235Z"
            fill="white"
          />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_27_41">
          <rect width="32" height="32" rx="4" fill="white" />
        </clipPath>
        <clipPath id="clip1_27_41">
          <rect
            width="28"
            height="6.58824"
            fill="white"
            transform="translate(2 13)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export function RainbowkitIcon(props: ComponentProps<"svg">) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_27_2)">
        <rect width="32" height="32" fill="black" />
        <path
          d="M5 9.95H6.65C15.1552 9.95 22.05 16.8448 22.05 25.35V27H25.35C26.2613 27 27 26.2613 27 25.35C27 14.111 17.889 5 6.65 5C5.73873 5 5 5.73873 5 6.65V9.95Z"
          fill="url(#paint0_radial_27_2)"
        />
        <path
          d="M22.6 25.35H27C27 26.2613 26.2612 27 25.35 27H22.6V25.35Z"
          fill="url(#paint1_linear_27_2)"
        />
        <path
          d="M6.65 5L6.65 9.4H5L5 6.65C5 5.73873 5.73873 5 6.65 5Z"
          fill="url(#paint2_linear_27_2)"
        />
        <path
          d="M5 9.40002H6.65C15.4589 9.40002 22.6 16.5411 22.6 25.35V27H17.65V25.35C17.65 19.2749 12.7251 14.35 6.65 14.35H5V9.40002Z"
          fill="url(#paint3_radial_27_2)"
        />
        <path
          d="M18.2 25.35H22.6V27H18.2V25.35Z"
          fill="url(#paint4_linear_27_2)"
        />
        <path
          d="M5 13.8L5 9.40002L6.65 9.40002L6.65 13.8H5Z"
          fill="url(#paint5_linear_27_2)"
        />
        <path
          d="M5 16.55C5 17.4613 5.73873 18.2 6.65 18.2C10.5988 18.2 13.8 21.4012 13.8 25.35C13.8 26.2613 14.5387 27 15.45 27H18.2V25.35C18.2 18.9711 13.0289 13.8 6.65 13.8H5V16.55Z"
          fill="url(#paint6_radial_27_2)"
        />
        <path
          d="M13.8 25.35H18.2V27H15.45C14.5387 27 13.8 26.2613 13.8 25.35Z"
          fill="url(#paint7_radial_27_2)"
        />
        <path
          d="M6.65 18.2C5.73873 18.2 5 17.4613 5 16.55L5 13.8L6.65 13.8L6.65 18.2Z"
          fill="url(#paint8_radial_27_2)"
        />
      </g>
      <defs>
        <radialGradient
          id="paint0_radial_27_2"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(6.65 25.35) rotate(-90) scale(20.35)"
        >
          <stop offset="0.770277" stop-color="#FF4000" />
          <stop offset="1" stop-color="#8754C9" />
        </radialGradient>
        <linearGradient
          id="paint1_linear_27_2"
          x1="22.325"
          y1="26.175"
          x2="27"
          y2="26.175"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FF4000" />
          <stop offset="1" stop-color="#8754C9" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_27_2"
          x1="5.825"
          y1="5"
          x2="5.825"
          y2="9.675"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#8754C9" />
          <stop offset="1" stop-color="#FF4000" />
        </linearGradient>
        <radialGradient
          id="paint3_radial_27_2"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(6.65 25.35) rotate(-90) scale(15.95)"
        >
          <stop offset="0.723929" stop-color="#FFF700" />
          <stop offset="1" stop-color="#FF9901" />
        </radialGradient>
        <linearGradient
          id="paint4_linear_27_2"
          x1="18.2"
          y1="26.175"
          x2="22.6"
          y2="26.175"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FFF700" />
          <stop offset="1" stop-color="#FF9901" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_27_2"
          x1="5.825"
          y1="13.8"
          x2="5.825"
          y2="9.40002"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FFF700" />
          <stop offset="1" stop-color="#FF9901" />
        </linearGradient>
        <radialGradient
          id="paint6_radial_27_2"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(6.65 25.35) rotate(-90) scale(11.55)"
        >
          <stop offset="0.59513" stop-color="#00AAFF" />
          <stop offset="1" stop-color="#01DA40" />
        </radialGradient>
        <radialGradient
          id="paint7_radial_27_2"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(13.525 26.175) scale(4.675 12.4667)"
        >
          <stop stop-color="#00AAFF" />
          <stop offset="1" stop-color="#01DA40" />
        </radialGradient>
        <radialGradient
          id="paint8_radial_27_2"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(5.825 18.475) rotate(-90) scale(4.675 88.6518)"
        >
          <stop stop-color="#00AAFF" />
          <stop offset="1" stop-color="#01DA40" />
        </radialGradient>
        <clipPath id="clip0_27_2">
          <rect width="32" height="32" rx="4" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function OnboardIcon(props: ComponentProps<"svg">) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_27_54)">
        <rect width="32" height="32" fill="black" />
        <path
          d="M16.1851 21.4943L13 16.0114H19.3475L22.5326 21.4943H16.1851Z"
          fill="white"
        />
        <path
          d="M19.3475 16.0114H13L16.1851 10.5057H22.5326L19.3475 16.0114Z"
          fill="url(#paint0_linear_27_54)"
        />
        <path
          d="M20.6949 16.0114L17.5326 10.5057H11.1851L8 5H20.6949L27.0424 16.0114H20.6949Z"
          fill="white"
        />
        <path
          d="M20.6949 27H8L11.1851 21.4943H17.5326L20.6949 16.0114H27.0424L20.6949 27Z"
          fill="url(#paint1_linear_27_54)"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_27_54"
          x1="13.003"
          y1="13.2531"
          x2="22.5252"
          y2="13.2531"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#55CCFE" />
          <stop offset="1" stop-color="#5E93EF" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_27_54"
          x1="8.00298"
          y1="21.4994"
          x2="27.0475"
          y2="21.4994"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#55CCFE" />
          <stop offset="1" stop-color="#5E93EF" />
        </linearGradient>
        <clipPath id="clip0_27_54">
          <rect width="32" height="32" rx="4" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function ModalIcon(props: ComponentProps<"svg">) {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_27_15)">
        <rect width="32" height="32" fill="black" />
        <path
          d="M8.91617 11.8738C12.8289 8.04208 19.1711 8.04208 23.0889 11.8738L23.5596 12.3344C23.757 12.5267 23.757 12.8355 23.5596 13.0278L21.95 14.6071C21.8538 14.7033 21.6919 14.7033 21.5957 14.6071L20.9478 13.9744C18.2195 11.3018 13.7906 11.3018 11.0623 13.9744L10.3689 14.6526C10.2727 14.7488 10.1107 14.7488 10.0146 14.6526L8.39987 13.0734C8.20247 12.881 8.20247 12.5723 8.39987 12.3799L8.91617 11.8738ZM26.4195 15.1335L27.8519 16.5356C28.0494 16.7279 28.0494 17.0367 27.8519 17.229L21.3882 23.5612C21.1908 23.7536 20.8769 23.7536 20.6795 23.5612L16.0886 19.0664C16.038 19.0209 15.962 19.0209 15.9114 19.0664L11.3255 23.5612C11.1281 23.7536 10.8143 23.7536 10.6169 23.5612L4.14805 17.2341C3.95065 17.0418 3.95065 16.733 4.14805 16.5407L5.58051 15.1386C5.77792 14.9462 6.09174 14.9462 6.28915 15.1386L10.8801 19.6333C10.9307 19.6789 11.0066 19.6789 11.0573 19.6333L15.6431 15.1386C15.8406 14.9462 16.1544 14.9462 16.3518 15.1386L20.9427 19.6333C20.9934 19.6789 21.0693 19.6789 21.1199 19.6333L25.7108 15.1386C25.9032 14.9412 26.2221 14.9412 26.4195 15.1335Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_27_15">
          <rect width="32" height="32" rx="4" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
