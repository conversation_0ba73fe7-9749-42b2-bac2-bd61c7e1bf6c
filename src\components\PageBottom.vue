<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-04 16:56:35
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-07-16 13:32:15
 * @FilePath: \betdoce-web\src\components\pageBottom.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <v-footer color="green darken-3"  padless>
    <v-container class="text-center white--text py-6" v-if="!isMobile">
      <v-row justify="center">
        <!-- 左侧菜单 -->
        <v-col cols="3" class="text-left">
          <div class="mb-4 page-bottom-title">Ajuda</div>
          <div class="page-bottom-link-list d-flex flex-column">
            <router-link
              to="/privacy-policy"
              class="text-decoration-none white--text"
            >
              <span>Política de Privacidade</span>
            </router-link>
            <router-link
              to="/terms-of-service"
              class="text-decoration-none white--text"
            >
              <span>Termos de Serviço</span>
            </router-link>
            <router-link
              to="/responsible-gaming"
              class="text-decoration-none white--text"
            >
              <span>Jogo Responsável (18+)</span>
            </router-link>
            <router-link to="/vip" class="text-decoration-none white--text">
              <span>Descrição do Nível VIP</span>
            </router-link>
          </div>
        </v-col>
        <!-- 右侧logo、描述和社交按钮整体居中 -->
        <v-col cols="9">
          <v-row class="mb-4">
            <v-col cols="auto" class="d-flex flex-column align-start">
              <v-img
                :src="bottomTitleImg"
                min-width="80"
                class="mx-2"
                cover
                :alt="'Bottom Title'"
                @error="handleImageError"
              />
            </v-col>
            <v-col cols="auto" class="d-flex flex-column align-start">
              <v-img
                :src="bottomLogoImg"
                min-width="120"
                class="mx-2"
                cover
                :alt="'Bottom Logo'"
                @error="handleImageError"
              />
              <div class="mb-2">©2023 All rights reserved.</div>
            </v-col>
          </v-row>
          <div class="page-bottom-desc">
            Os jogos podem ser viciantes. Por favor, jogue com
            responsabilidade.<br />
            Esta plataforma só aceita clientes maiores de 18 anos. Esperamos que
            todos os jogadores nos dêem mais sugestões e atualizaremos o cassino
            de acordo com as suas preferências!
          </div>
          <div
            class="mb-4 d-flex flex-row flex-nowrap justify-start align-center"
          >
            <v-btn
              v-for="icon in icons"
              :key="icon.alt"
              :href="icon.link"
              icon
              target="_blank"
              class="mx-2"
            >
              <v-img
                :src="icon.src"
                :alt="icon.alt"
                min-width="40"
                cover
                @error="handleImageError"
              />
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </v-container>
    <v-container class="text-center white--text py-6" v-else>
      <v-row justify="start" class="mb-4">
        <v-col cols="col" class="d-flex flex-column align-center">
          <v-img
            :src="bottomTitleImg"
            min-width="80"
            class="mx-2"
            cover
            :alt="'Bottom Title'"
            @error="handleImageError"
          />
        </v-col>
        <v-col cols="col" class="d-flex flex-column align-start">
          <v-img
            :src="bottomLogoImg"
            min-width="120"
            class="mx-2"
            cover
            :alt="'Bottom Logo'"
            @error="handleImageError"
          />
          <div class="mb-2">©2023 All rights reserved.</div>
        </v-col>
      </v-row>
      <div class="mb-4 text-start">
        Os jogos podem ser viciantes. Por favor, jogue com responsabilidade.<br />
        Esta plataforma só aceita clientes maiores de 18 anos, éramos que todos
        os jogadores nos mais sugerimos e atualizaremos o cassino de acordo com
        suas preferências!
      </div>
      <div class="mb-4 d-flex align-center justify-center">
        <span class="me-2">Ajuda</span>
        <v-divider class="flex-grow-1" />
      </div>
      <div class="mb-4 d-flex flex-row flex-nowrap justify-center align-center">
        <v-btn
          v-for="icon in icons"
          :key="icon.alt"
          :href="icon.link"
          icon
          target="_blank"
          class="mx-2"
        >
          <v-img
            :src="icon.src"
            :alt="icon.alt"
            min-width="40"
            cover
            @error="handleImageError"
          />
        </v-btn>
      </div>
      <v-row justify="start">
        <v-col cols="6">
          <router-link
            to="/privacy-policy"
            class="text-decoration-none white--text"
          >
            <span class="font-color">Política de Privacidade</span>
          </router-link>
        </v-col>
        <v-col cols="6">
          <router-link
            to="/terms-of-service"
            class="text-decoration-none white--text"
          >
            <span class="font-color">Termos de Serviço</span>
          </router-link>
        </v-col>
        <v-col cols="6">
          <router-link
            to="/responsible-gaming"
            class="text-decoration-none white--text"
          >
            <span class="font-color">Jogo Responsável (18+)</span>
          </router-link>
        </v-col>
        <v-col cols="6">
          <router-link to="/vip" class="text-decoration-none white--text">
            <span class="font-color">Descrição do Nível VIP</span>
          </router-link>
        </v-col>
      </v-row>
    </v-container>
  </v-footer>
</template>

<script setup>
import bottomTitleImg from "@/assets/images/h5/bottom-title.png";
import bottomLogoImg from "@/assets/images/h5/bottom-logo.png";
import facebook from "@/assets/images/h5/facebook.png";
import googleplus from "@/assets/images/h5/googleplus.png";
import WhatsApp from "@/assets/images/WhatsApp.png";
import Telegram from "@/assets/images/Telegram.png";
import X from "@/assets/images/h5/X.png";
import tiktok from "@/assets/images/h5/tiktok.png";
import { useDevice } from "@/composables/useDevice";

// 判断设备类型
const { currentDevice, isMobile, isTablet } = useDevice();

const icons = [
  { src: facebook, alt: "Facebook", link: "#" },
  { src: googleplus, alt: "Google+", link: "#" },
  { src: WhatsApp, alt: "WhatsApp", link: "#" },
  { src: Telegram, alt: "Telegram", link: "#" },
  { src: X, alt: "X", link: "#" },
  { src: tiktok, alt: "TikTok", link: "#" },
];

// 图片加载错误处理
const handleImageError = (event) => {
  console.error("图片加载失败:", event.target.src);
};
</script>

<style scoped lang="scss">
.v-footer {
  background: #0d3b1e !important;
   border-radius: 20px 20px 0 0;
}
:deep(.v-btn--variant-elevated) {
  background: none !important;
  box-shadow: none !important;
}
.page-bottom-title {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 16px;
}
.page-bottom-link-list {
  display: flex;
  flex-direction: column;
  gap: 12px;

  span {
    color: #fff;
    font-size: 1rem;
    transition: color 0.2s;
    &:hover {
      color: #ffd700;
    }
  }
}
.page-bottom-desc {
  font-size: 0.95rem;
  color: #e0e0e0;
  margin: 16px 0;
  line-height: 1.6;
  text-align: left;
  // max-width: 400px;
}
.v-btn:hover {
  background: #0e753b !important;
}
@media (max-width: 768px) {
 

  // 移动端触摸优化
  * {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  .v-row {
    align-items: flex-start !important;
  }
  .v-row > .v-col {
    margin-bottom: 0 !important;
    text-align: left;
  }

  .font-color {
    color: #fff;
    transition: color 0.2s;

    // 桌面端hover效果
    &:hover {
      color: #ffd700;
    }

    // 移动端active效果（触摸反馈）
    &:active {
      color: #ffd700;
      transform: scale(0.98);
    }
  }
  .mb-2 {
    margin-bottom: 8px !important;
  }
  .v-btn {
    // background: #23305a !important;
    border-radius: 50%;
    margin: 0 6px;
    transition: background 0.2s;

    // 移动端hover效果
    &:hover {
      background: #0e753b !important;
    }

    // 移动端active效果（触摸反馈）
    &:active {
      background: #0e753b !important;
      transform: scale(0.95);
    }
  }

  .page-bottom-desc {
    max-width: none;
  }
}
</style>
