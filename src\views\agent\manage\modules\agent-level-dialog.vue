<template>
  <ElDialog
    v-model="visible"
    title="层级管理(无限级)"
    width="50%"
    destroy-on-close
  >
    <div class="agent-level-container">
      <!-- Search Area -->
      <div class="search-area">
        <ElInput placeholder="代理商名称" v-model="searchAgentName" />
        <ElButton type="primary" @click="handleSearch">搜索</ElButton>
        <ElButton @click="handleReset">重置</ElButton>
      </div>
      <div class="action-buttons">
        <ElButton type="danger" plain @click="handleRestrictWithdrawal"
          >限制当前及下级提现</ElButton
        >
        <ElButton type="danger" @click="handleDeleteSelected"
          >删除当前及下级</ElButton
        >
      </div>
      <!-- Tree Structure -->
      <div class="tree-area">
        <ElTree
          ref="treeRef"
          :data="treeData"
          show-checkbox
          node-key="agent_id"
          :props="defaultProps"
          :filter-node-method="filterNode"
          default-expand-all
          @check="handleCheck"
          v-loading="loading"
          element-loading-text="加载中..."
        />
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <ElButton type="danger" plain @click="handleRemoveRestriction"
          >解除限制</ElButton
        >
        <ElButton type="danger" @click="handleDeleteAll">删除全部</ElButton>
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton type="primary" @click="handleSave">保存</ElButton>
      </div>
    </div>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, watch, PropType, nextTick } from "vue";
import {
  ElDialog,
  ElInput,
  ElButton,
  ElTree,
  ElMessageBox,
} from "element-plus";
import type { TreeInstance } from "element-plus";
import {
  fetchGetAgentHierarchy,
  restrictWithdrawal,
  deleteSubAgents,
} from "@/service/api/agent";

const props = defineProps({
  visible: { type: Boolean, default: false },
  agentId: {
    type: Number as PropType<number | null | undefined>,
    required: false,
  },
});

const emit = defineEmits(["update:visible"]);

const visible = ref(props.visible);
const searchAgentName = ref("");
const treeData = ref([]);
const treeRef = ref<TreeInstance>();
const defaultProps = {
  children: "children",
  label: "agent_name",
};

const loading = ref(false);

// 监听搜索关键字变化
watch(searchAgentName, (val) => {
  treeRef.value?.filter(val);
});

// 过滤节点方法
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.agent_name.toLowerCase().includes(value.toLowerCase());
};

watch(
  () => props.visible,
  (newVal) => {
    visible.value = newVal;
    if (newVal && props.agentId) {
      fetchAgentLevelTree(props.agentId);
    }
  },
);

watch(visible, (newVal) => {
  emit("update:visible", newVal);
});

// 获取树形数据
async function fetchAgentLevelTree(agentId: number) {
  loading.value = true;
  try {
    const response = await fetchGetAgentHierarchy({ agent_id: agentId });
    console.log("API Response:", response);

    if (!response.data || !response.data.data) {
      console.error("Invalid API response structure");
      treeData.value = [];
      return;
    }

    // 確保數據是陣列
    const data = Array.isArray(response.data.data)
      ? response.data.data
      : [response.data.data];

    // 確保每個節點都有正確的結構
    const processedData = data.map((node: any) => ({
      ...node,
      children: Array.isArray(node.children) ? node.children : [],
    }));

    treeData.value = processedData;
  } catch (error) {
    console.error("获取代理商层级数据失败:", error);
    treeData.value = [];
  } finally {
    loading.value = false;
  }
}

function handleSearch() {
  treeRef.value?.filter(searchAgentName.value);
}

function handleReset() {
  searchAgentName.value = "";
  treeRef.value?.filter("");
}

// 处理节点勾选
async function handleCheck(data: any, checked: any) {
  const checkedNodes = treeRef.value?.getCheckedNodes();
  console.log("已勾选的节点:", checkedNodes);
}

// 处理限制提现
async function handleRestrictWithdrawal() {
  try {
    await ElMessageBox.confirm(
      "确定要限制所选代理商及其下级的提现功能吗？",
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      },
    );

    const checkedNodes = treeRef.value?.getCheckedNodes();
    // TODO: 调用限制提现 API
    console.log("限制提现的节点:", checkedNodes);
    await restrictWithdrawal({
      agent_ids: checkedNodes?.map((e) => e.agent_id),
      status: 1,
    });
    fetchAgentLevelTree(props.agentId);
  } catch (error) {
    console.log("取消操作");
  }
}

// 处理删除选中节点
async function handleDeleteSelected() {
  try {
    await ElMessageBox.confirm("确定要删除所选代理商及其下级吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const checkedNodes = treeRef.value?.getCheckedNodes();
    // TODO: 调用删除 API
    console.log("要删除的节点:", checkedNodes);
    await deleteSubAgents({ agent_ids: checkedNodes?.map((e) => e.agent_id) });
    fetchAgentLevelTree(props.agentId);
  } catch (error) {
    console.log("取消操作");
  }
}

// 处理删除全部
async function handleDeleteAll() {
  try {
    await ElMessageBox.confirm("确定要删除当前代理商的所有下级吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    // TODO: 调用删除全部 API
    console.log("删除所有下级");
  } catch (error) {
    console.log("取消操作");
  }
}

// 处理解除限制
async function handleRemoveRestriction() {
  try {
    await ElMessageBox.confirm("确定要解除所有提现限制吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    const checkedNodes = treeRef.value?.getCheckedNodes();
    await restrictWithdrawal({
      agent_ids: checkedNodes?.map((e) => e.agent_id),
      status: 0,
    });
    fetchAgentLevelTree(props.agentId);
    // TODO: 调用解除限制 API
    console.log("解除所有提现限制");
  } catch (error) {
    console.log("取消操作");
  }
}

function handleCancel() {
  visible.value = false;
}

function handleSave() {
  visible.value = false;
}
</script>

<style scoped>
.agent-level-container {
  padding: 0 20px 20px 20px;
}

.search-area {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.tree-area {
  margin-top: 20px;
  margin-bottom: 20px;
  min-height: 300px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
