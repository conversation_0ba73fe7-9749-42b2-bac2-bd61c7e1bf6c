/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-06-19 14:10:12
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\typings\report.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export interface DailyReportParams {
  start_time: string;
  end_time: string;
}

export interface DailyReportData {
  messages_pending_reply: number;
  withdrawals_pending: number;
  code_amount: number;
  platform_tax: number;
  platform_balance: number;
  online_users: number;
  registered_users: number;
  invited_users: number;
  recharge_users: number;
  first_recharge_users: number;
  first_recharge_amount: number;
  invitation_reward: number;
  total_recharge_amount: number;
  total_withdrawal_amount: number;
  platform_revenue: number;
  gift_cash: number;
  gift_bonus: number;
  withdrawal_users: number;
  game_betting: number;
  game_winning: number;
  total_recharge_gift_cash: number;
  game_betting_winning_diff: number;
  recharge_orders: number;
  withdrawal_orders: number;
  invitation_first_recharge_users: number;
  total_cash_withdrawable: number;
  total_digital_balance: number;
  total_cash_balance: number;
}

export interface DailyReportResponse {
  status_code: number;
  data: DailyReportData;
  count: number;
}
