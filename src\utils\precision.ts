/**
 * 处理 JavaScript 浮点数计算精度问题的工具函数
 */

/**
 * 将数字转换为指定精度的字符串
 * @param num 要处理的数字
 * @param precision 精度（小数位数）
 */
export const toPrecision = (num: number, precision: number = 2): string => {
  return Number(num).toFixed(precision)
}

/**
 * 加法运算
 */
export const add = (a: number, b: number): number => {
  const precision = Math.max(
    (a.toString().split('.')[1] || '').length,
    (b.toString().split('.')[1] || '').length
  )
  const multiplier = Math.pow(10, precision)
  return (a * multiplier + b * multiplier) / multiplier
}

/**
 * 减法运算
 */
export const subtract = (a: number, b: number): number => {
  const precision = Math.max(
    (a.toString().split('.')[1] || '').length,
    (b.toString().split('.')[1] || '').length
  )
  const multiplier = Math.pow(10, precision)
  return (a * multiplier - b * multiplier) / multiplier
}

/**
 * 乘法运算
 */
export const multiply = (a: number, b: number): number => {
  const precision = (a.toString().split('.')[1] || '').length +
                   (b.toString().split('.')[1] || '').length
  const multiplier = Math.pow(10, precision)
  return (a * multiplier * (b * multiplier)) / (multiplier * multiplier)
}

/**
 * 除法运算
 */
export const divide = (a: number, b: number): number => {
  const precision = Math.max(
    (a.toString().split('.')[1] || '').length,
    (b.toString().split('.')[1] || '').length
  )
  const multiplier = Math.pow(10, precision)
  return (a * multiplier) / (b * multiplier)
}

/**
 * 四舍五入到指定小数位
 */
export const round = (num: number, precision: number = 2): number => {
  const multiplier = Math.pow(10, precision)
  return Math.round(num * multiplier) / multiplier
}

/**
 * 格式化金额
 * @param amount 金额
 * @param currency 货币符号
 * @param precision 精度
 */
export const formatCurrency = (
  amount: number,
  currency: string = '$',
  precision: number = 2
): string => {
  return `${currency}${toPrecision(amount, precision)}`
}

/**
 * 解析金额字符串为数字
 * @param str 金额字符串
 */
export const parseAmount = (str: string): number => {
  const num = parseFloat(str.replace(/[^\d.-]/g, ''))
  return isNaN(num) ? 0 : num
} 