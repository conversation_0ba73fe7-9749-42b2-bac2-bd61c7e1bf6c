// 断点配置
$breakpoints: (
  'xs': 320px,
  'sm': 576px,
  'md': 768px,
  'lg': 992px,
  'xl': 1200px,
  'xxl': 1400px
);

// 响应式混合器
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "未知的断点: #{$breakpoint}";
  }
}

// 容器宽度
@mixin container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1rem;
  padding-left: 1rem;

  @include respond-to('sm') {
    max-width: 540px;
  }
  @include respond-to('md') {
    max-width: 720px;
  }
  @include respond-to('lg') {
    max-width: 960px;
  }
  @include respond-to('xl') {
    max-width: 1140px;
  }
  @include respond-to('xxl') {
    max-width: 1320px;
  }
}

// 网格系统
@mixin make-row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -0.5rem;
  margin-left: -0.5rem;
}

@mixin make-col($size, $columns: 12) {
  flex: 0 0 percentage($size / $columns);
  max-width: percentage($size / $columns);
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}

// 常用布局工具
@mixin flex-row {
  display: flex;
  flex-direction: row;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin grid($columns: 1) {
  display: grid;
  grid-template-columns: repeat($columns, 1fr);
  gap: 1rem;
} 