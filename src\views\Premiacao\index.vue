<template>
  <v-container class="ranking-container pt-4" max-width="940">
    <div class="ranking-card">
      <!-- 标题区域 -->
      <div class="ranking-header">
        <img src="@/assets/images/aposta-banner.png" />
      </div>

      <!-- 提示文本 -->
      <div class="ranking-tip">
        <img src="@/assets/images/home-msg-icon.png" class="tip-icon" alt="Tip" />
        <span>Os bônus são emitidos uma vez por semana ou irregularmente, Convide novos usuários para completar a primeira recarga para acumular 1 ponto, Ganhe 1 ponto a cada 100 reais apostados. O ranking e a premiação sãoatualizados em tempo real. O bônus do usuário multiplicado pelo percentual de pontos ganhos para o top 100 pontos totais.</span>
      </div>
      <div class="ranking-section text-center">
        <div class="section-title">Classificação em tempo real</div>
        
        <!-- 前三名展示 -->
        <div class="top-three mt-4 mb-4">
          <div v-for="rank in topThree" :key="rank.rank" class="rank-item" :class="`rank-${rank.rank}`">
            <div class="rank-badge">
              <div class="user-id">{{ rank.uuid }}</div>
              <div class="user-aposte">Aposte R${{ formatNumber(rank.digital_amount) }}</div>
              <div class="bonus">Bônus R${{ formatNumber(rank.bet_total_amount) }}</div>
            </div>
          </div>
        </div>

        <!-- 排行榜列表 -->
        <v-table class="ranking-table ma-4">
          <thead>
            <tr>
              <th>classificagao</th>
              <th>ID do usuário</th>
              <th>Apostas</th>
              <th>premio</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in currentRankings" :key="item.rank">
              <td>{{ item.rank }}</td>
              <td>{{ item.uuid }}</td>
              <td class="amount">R${{ formatNumber(item.digital_amount) }}</td>
              <td class="bonus">+{{ formatNumber(item.bet_total_amount) }}</td>
            </tr>
          </tbody>
        </v-table>
      </div>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { getInviteWeekRrank } from '@/api/auth'
import { showError } from '@/utils/toast'
import { useStore } from 'vuex'

// 排行榜数据
const realTimeData = ref([])
const currentPage = ref(1)
const pageSize = 10
const store = useStore()

// 加载状态
const loadingMoreCurrent = ref(false)

// 是否还有更多数据
const hasMoreCurrent = ref(true)

// 总数据量
const totalCurrentCount = ref(0)


// 前三名数据
const topThree = computed(() => {
  return realTimeData.value.slice(0, 3)
})

// 当前排行榜数据（第4名开始）
const currentRankings = computed(() => {
  return realTimeData.value.slice(3)
})


// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString('pt-BR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}


// 加载上周排行榜数据
const loadRealTimeLeaderboard = async (page: number = 1) => {
  try {
    const response = await getInviteWeekRrank({
      activity_id:3,
      user_id:store.state.auth.user?.id || 0
    })
    if (response) {
      const {rank} = response
      realTimeData.value = rank.map((item, index) => ({
        rank: index + 1,
        ...item,
      }))
    }
  } catch (error) {
    console.error('Failed to load real-time leaderboard:', error)
    showError('Falha ao carregar classificação em tempo real')
  }
}

// 加载更多实时排行榜数据
const loadMoreCurrentRankings = async () => {
  if (loadingMoreCurrent.value || !hasMoreCurrent.value) return
  
  loadingMoreCurrent.value = true
  currentPage.value++
  await loadRealTimeLeaderboard(currentPage.value)
  loadingMoreCurrent.value = false
}
// 定时刷新数据
let refreshTimer: number | null = null

// 启动定时刷新
const startRefreshTimer = () => {
  // 每30秒刷新一次数据
  refreshTimer = window.setInterval(() => {
    loadRealTimeLeaderboard(1)
  }, 30000)
}

// 组件挂载时加载数据
onMounted(() => {
  loadRealTimeLeaderboard()
  startRefreshTimer()
})
</script>

<style lang="scss" scoped>
.ranking-container {
  padding: 0;
  min-height: 100vh;
  
  .ranking-card {

    position: relative;
  }

  .total-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: linear-gradient(0deg, #C9B737, #2ABB27);
    height: 70px;
    border-radius: 10px;
    .num {
      font-size: 20px;
      font-weight: 600;
    }
    .name {
      font-size: 14px;
    }
  }
}

.ranking-header{
  text-align: center;
  position: relative;
  margin-bottom: 12px;
  img{
    width: 100%;
    height: auto;
  }
}

.ranking-tip {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 16px;
  background: #222B54;
  border-radius: 8px;
  margin-bottom: 24px;

  .tip-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
    margin-top: 3px;
  }

  span {
    font-size: 14px;
    color: #fff;
    line-height: 1.4;
  }
}

.ranking-section {
  margin-bottom: 32px;

  border-radius: 0;

  .section-title {
    font-size: 18px;
    font-weight: 500;
    color: #fff;
    margin: 0;
    height: 53px;
    line-height: 53px;
    text-transform: capitalize;
    text-align: center;
    background: linear-gradient(87deg, #E5D51F, #3B922A, #E5D51F);
    width: 100%;
  }
}

.top-three {
  display: flex;
  justify-content: center;
  gap: 105px;
  background-image: url("@/assets/images/pm-bg.png");
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  height: 280px;
  margin: 0 -16px;
  padding: 20px 16px 0;
  position: relative;

  .rank-item {
    text-align: center;
    width: 33.33%;
    max-width: 120px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    
    .rank-badge {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 8px;
      width: 100%;
    }

    .user-id {
      font-size: 14px;
      color: #FFFFFF;
      margin-bottom: 8px;
      word-break: break-all;
    }
    
    .user-aposte {
      font-size: 12px;
      color: #EAEAEA;
      margin-bottom: 8px;
    }
    
    .bonus {
      font-size: 16px;
      color: #fff;
    }

    &.rank-1 {
      margin-top: 75px;
      order: 2;
    }

    &.rank-2 {
      margin-top: 100px;
      order: 1;
    }

    &.rank-3 {
      margin-top: 100px;
      order: 3;
    }
  }
}

.ranking-table {
  margin: 0 !important;
  background: #1B193D !important;
  border-radius: 0 !important;
  width: 100%;
  border-collapse: collapse;
  
  :deep(th) {
    color: rgba(255, 255, 255) !important;
    background: linear-gradient(180deg, #1B1F2D, #3C4155);
    font-size: 14px;
    font-weight: normal;
    text-transform: capitalize;
    white-space: nowrap;
    padding: 12px 8px !important;
    text-align: center;
  }
  
  tr:nth-child(even) {
    background: #2B324D;
  }
  
  :deep(td) {
    color: #fff !important;
    font-size: 14px;
    padding: 12px 8px !important;
    text-align: center;
    vertical-align: middle;
    
    &.rank-cell {
      padding: 8px 0 !important;
      
      .rank-number {
        display: inline-block;
        width: 45px;
        text-align: center;
        font-size: 14px;
      }
      
      img {
        display: block;
        margin: 0 auto;
      }
    }
    
    &.user-id {
      color: #fff !important;
      word-break: break-all;
    }

    &.amount {
      color: #FFDF00 !important;
    }

    &.bonus {
      color: #FFDF00 !important;
      font-weight: 500;
    }
  }
}

.view-more-btn {
  background: linear-gradient(0deg, #C9B737, #2ABB27);
  border-radius: 22px;
  min-width: 160px;
  color: #fff !important;
  height: 44px;
  font-size: 18px;
  text-transform: none;
  margin-top: 16px;
}

.game-type-filter {
  margin: 0;
  background: #251D31;
  width: 100%;
  
  .game-type-buttons {
    width: 100%;
    background: transparent;
    border-radius: 0;
    display: flex;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    
    &::-webkit-scrollbar {
      display: none;
    }
    
    .game-type-btn {
      flex: 1;
      min-width: 100px;
      height: 44px;
      padding: 0 20px;
      color: #fff;
      font-size: 14px;
      font-weight: 400;
      text-transform: none;
      border-radius: 0;
      border: none;
      position: relative;
      opacity: 0.8;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 2px;
        background: #FFDF00;
        transition: width 0.3s ease;
      }
      
      &:hover {
        opacity: 1;
      }
      
      &.v-btn--active {
        background: transparent;
        opacity: 1;
        color: #FFDF00;
        
        &::after {
          width: 80%;
        }
      }
    }
  }
}

.v-btn.v-btn--icon {
  background: transparent;
  color: #fff;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

.v-btn.v-btn--size-default {
  padding: 0 16px;
  min-width: auto;
  
  .v-icon {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .ranking-container {
    padding: 16px;
    
    .ranking-card {
      border-radius: 12px;
      overflow: hidden;
    }
  }

  .ranking-header{
    text-align: center;
    position: relative;
    margin-bottom: 12px;
    img{
      width: 100%;
      height: auto;
    }
  }

  .ranking-tip {
    margin: 0 0 16px 0;
    padding: 12px;
  }

  .total-card {
    height: 60px;
    
    .num {
      font-size: 18px;
    }
    
    .name {
      font-size: 12px;
    }
  }

  .ranking-section {
    margin-bottom: 16px;

    .section-title {
      font-size: 16px;
      height: 44px;
      line-height: 44px;
    }
  }

  .game-type-filter {
    .game-type-buttons {
      .game-type-btn {
        min-width: 80px;
        height: 40px;
        padding: 0 12px;
        font-size: 13px;
      }
    }
  }

  .ranking-table {
    margin: 8px 0 !important;
    
    :deep(th) {
      font-size: 12px;
      padding: 8px 4px !important;
    }
    
    :deep(td) {
      font-size: 12px;
      padding: 8px 4px !important;
      
      &.rank-cell {
        padding: 4px 0 !important;
        
        .rank-number {
          width: 30px;
          font-size: 12px;
        }
        
        img {
          width: 30px;
          height: 30px;
        }
      }
    }
  }

  .view-more-btn {
    margin: 12px auto;
    height: 40px;
    min-width: 140px;
    font-size: 16px;
  }

  .top-three {
    height: 200px;
    margin: 0;
    gap: 14px;
    background-size: 100% auto;
    padding: 0;
    
    .rank-item {
      max-width: 100px;
      
      .rank-badge {
        padding: 4px;
      }
      
      .user-id {
        font-size: 12px;
        margin-bottom: 4px;
      }
      
      .user-aposte {
        font-size: 11px;
        margin-bottom: 4px;
      }
      
      .bonus {
        font-size: 0.7rem;
      }

      &.rank-1 {
        margin-top: 60px;
      }

      &.rank-2 {
        margin-top: 80px;
      }

      &.rank-3 {
        margin-top: 80px;
      }
    }
  }

  .v-btn.v-btn--icon {
    width: 36px;
    height: 36px;
    
    .v-icon {
      font-size: 18px;
    }
  }
}
</style> 