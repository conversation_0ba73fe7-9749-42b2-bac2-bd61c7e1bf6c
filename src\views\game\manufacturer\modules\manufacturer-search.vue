<template>
  <div class="search-wrapper">
    <ElForm :model="props.model">
      <ElRow :gutter="16">
        <ElCol :span="6">
          <ElFormItem label="厂商">
            <ElSelect
              v-model="props.model.name"
              placeholder="请选择厂商"
              filterable
              clearable
            >
              <ElOption
                v-for="(item, key) in manufacturerOptions"
                :key="key"
                :label="item.name"
                :value="item.name"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol :span="6">
          <ElFormItem label="渠道">
            <ElSelect
              v-model="props.model.channel"
              placeholder="请选择渠道"
              clearable
            >
              <ElOption
                v-for="(label, value) in channelOptions"
                :key="value"
                :label="label"
                :value="value"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol :span="6">
          <ElFormItem label="游戏类型">
            <ElSelect
              v-model="props.model.game_manufacturer_types"
              placeholder="请选择游戏类型"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
            >
              <ElOption
                v-for="item in gameTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left">{{ item.label }}</span>
                <span
                  style="
                    float: right;
                    color: var(--el-text-color-secondary);
                    font-size: 13px;
                  "
                >
                  {{ item.labelValue }}
                </span>
              </ElOption>
            </ElSelect>
          </ElFormItem>
        </ElCol>

        <ElCol :span="6">
          <ElFormItem label="状态">
            <ElSelect
              v-model="props.model.status"
              placeholder="请选择状态"
              clearable
            >
              <ElOption
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <ElRow :gutter="16">
        <ElCol :span="8">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="16">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { statusOptions } from "@/constants/common";
import {
  ElForm,
  ElFormItem,
  ElSelect,
  ElInput,
  ElButton,
  ElOption,
  ElRow,
  ElCol,
} from "element-plus";
import { useI18n } from "vue-i18n";
import { fetchGetManufacturerDict } from "@/service/api";

const { t } = useI18n();

interface ManufacturerItem {
  id?: number;
  name: string;
}

interface SearchModel {
  name?: string;
  channel?: string;
  status?: number;
  game_manufacturer_types?: string[];
}

interface Props {
  model: SearchModel;
}

const props = defineProps<Props>();
const emit = defineEmits(["update:model", "search", "reset"]);

const manufacturerOptions = ref<Record<string, ManufacturerItem>>({});

// 游戏类型选项
const gameTypeOptions = [
  { label: "赠金游戏", value: "BonusSlots", labelValue: "BonusSlots" },
  { label: "热门游戏", value: "hot", labelValue: "hot" },
  { label: "老虎机", value: "slots", labelValue: "slots" },
  { label: "真人", value: "livecasino", labelValue: "livecasino" },
  { label: "卡牌", value: "Cartoes", labelValue: "Pôquer" },
  { label: "捕鱼", value: "Pescaria", labelValue: "Pescaria" },
  { label: "电玩", value: "Coluna", labelValue: "Coluna" },
];

const channelOptions = {
  1: "灰度Gaming",
  2: "PG",
  3: "OMG总代",
};

async function getManufacturerDict() {
  try {
    const res = await fetchGetManufacturerDict();
    if (res.data.data) {
      manufacturerOptions.value = res.data.data;
    }
  } catch (error) {
    console.error("获取厂商列表失败:", error);
  }
}

// 搜索
function handleSearch() {
  emit("search");
}

// 重置
function handleReset() {
  emit("reset");
}

onMounted(() => {
  getManufacturerDict();
});
</script>

<style lang="scss" scoped>
.search-wrapper {
  background-color: #fff;
  padding: 16px;
  border-radius: 4px 4px 0 0;
  --un-shadow: var(--un-shadow-inset) 0 1px 2px 0
    var(--un-shadow-color, rgb(0 0 0 / 0.05));
  box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow),
    var(--un-shadow);

  .header-operation {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  :deep(.el-form-item) {
    margin-bottom: 16px;
    width: 100%;

    .el-form-item__content {
      width: 100%;
    }

    .el-select,
    .el-input {
      width: 100%;
    }
  }

  :deep(.el-button + .el-button) {
    margin-left: 8px;
  }
}
</style>
