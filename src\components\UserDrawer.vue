<template>
  <v-navigation-drawer
    :model-value="modelValue"
    :width="drawerWidth"
    @update:model-value="emit('update:modelValue', $event)"
    location="right"
    temporary
    class="user-drawer"
    style="z-index: 999; width: auto; min-width: 300px; max-width: 90vw"
  >
    <!-- 用户基本信息 -->
    <div class="user-info">
      <div style="overflow: hidden">
        <v-btn
          class="close-btn"
          icon="mdi-close"
          variant="text"
          @click="emit('update:modelValue', false)"
        ></v-btn>
      </div>
      <div class="top-info">
        <div class="d-flex align-center mb-4">
          <v-avatar
            size="60"
            :image="userInfo?.avatar || userTx"
            color="primary"
          ></v-avatar>
          <div class="ml-4">
            <div class="text-h6">{{ userInfo?.nickname || "usuário" }}</div>
            <div class="d-flex align-center">
              <span class="text-caption text-grey"
                >ID: {{ userInfo?.uuid }}</span
              >
              <img src="@/assets/images/user-copy-icon.png" @click="copyToClipboard(userInfo?.uuid)" class="cody-icon" />
            </div>
          </div>
        </div>
        <div class="user-edit-btn d-flex align-center" @click="handleEdit">
          <img src="@/assets/images/user-user-icon.png" class="user-icon" />
          <span>Editar</span>
        </div>
      </div>

      <!-- 账户余额 -->
      <v-card class="balance-card mb-4 ml-2 mr-2">
        <div
          class="d-flex justify-space-between align-center pl-2 pr-2 balance-title"
        >
          <span class="text-subtitle-1">Saldo atual da conta</span>
          <span class="text-h6 ml-2"
            >R$ {{ formatNumber(userWallet?.total_balance / 100) }}</span
          >
        </div>
        <div class="pa-3">
          <div class="d-flex justify-space-between mb-2">
            <div class="d-flex flex-column">
              <span class="text-caption">Dinheiro</span>
              <span class="text-caption balance-text"
                >R$ {{ formatNumber(userWallet?.cash_balance / 100) }}</span
              >
            </div>
            <div class="d-flex flex-column">
              <span class="text-caption">Bónus</span>
              <span class="text-caption balance-text"
                >R$ {{ formatNumber(userWallet?.digital_balance / 100) }}</span
              >
            </div>
          </div>
          <div class="text-caption mt-1 text-right">
            <span class="text-color"
              >R${{ formatNumber(withdraw?.daily_withdrawal_limit) }}</span
            >
          </div>
          <div class="d-flex align-center">
            <span class="mr-3">Hoje</span>
            <v-progress-linear
              :model-value="withdraw?.withdrawal_ratio"
              color="#FFDF00"
              height="9"
              rounded
              class="balance-progress"
            ></v-progress-linear>
          </div>

          <!-- <div class="balance-bottom d-flex align-center justify-lg-start mt-4">
            <img src="@/assets/images/purse-icon.png" />
            <span
              >Saldo sacado: R$ {{ withdraw?.total_withdraw_amount || 0 }}+{{
                withdraw?.daily_withdraw_amount || 0
              }}</span
            >
          </div> -->
        </div>
      </v-card>

      <!-- VIP等级 -->
      <v-card class="vip-card mb-4 ml-2 mr-2">
        <div
          class="d-flex justify-space-between align-center pl-2 pr-2 vip-title"
        >
          <div class="d-flex align-center">
            <img src="@/assets/images/user-title-icon.png" class="vip-icon" />
            <span class="text-subtitle-2">NIVEL VIP</span>
          </div>
          <div
            class="d-flex justify-end align-center"
            style="cursor: pointer"
            @click="handleVip"
          >
            <span>VIP {{ userInfo?.level }}</span>
            <img src="@/assets/images/user-right-icon.png" class="right-icon" />
          </div>
        </div>
        <v-divider></v-divider>
        <div class="pa-3">
          <div class="text-caption mb-1">
            Proximo nivel
            <span class="text-color"
              >{{ vipProgressObj?.deposit_progress }}%</span
            >
          </div>
          <v-progress-linear
            :model-value="vipProgressObj?.deposit_progress"
            color="#FFDF00"
            height="9"
            rounded
          ></v-progress-linear>
          <div class="text-caption mt-1">
            <div class="text-tips">
              Depositos totais nos ultimos 30 dfias: R$
              {{ vipProgressObj?.total_deposit_amount / 100 }}
            </div>
          </div>
          <div>
            <div class="text-caption mt-1">
              Proximo nivel
              <span class="text-color"
                >{{ vipProgressObj?.bet_progress }}%</span
              >
            </div>
            <v-progress-linear
              :model-value="vipProgressObj?.bet_progress"
              color="#FFDF00"
              height="9"
              rounded
            ></v-progress-linear>
          </div>
          <div class="text-tips mt-1">
            Pontos de apostas nos ltimos 30 dias:
            {{ vipProgressObj?.total_bet_amount / 100 }}
          </div>
        </div>
      </v-card>
    </div>

    <!-- 退出按钮 -->
    <v-btn color="error" class="ma-2 logout-btn" @click="handleLogout">
      Sair
    </v-btn>
  </v-navigation-drawer>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watchEffect } from "vue";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import { vipProgress } from "@/api/user";
import userTx from "@/assets/images/tx-icon.png";
import { formatNumber } from "@/utils/index";
import { showSuccess } from "@/utils/toast";

const store = useStore();
const router = useRouter();
const withdraw = computed(() => {
  const userInfoString = localStorage.getItem("userInfo");
  // console.log(JSON.parse(userInfoString))
  return userInfoString ? JSON.parse(userInfoString) : null;
});
const userInfo = computed(() => store.state.auth.user || {});
const userWallet = computed(() => store.state.auth.userWallet || {});
const unreadNum = computed(() => store.state.auth.unreadNum || 0);
const props = defineProps<{
  modelValue: boolean;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
  (e: "logout"): void;
}>();

// 处理退出登录
const handleLogout = () => {
  emit("logout");
};

// 处理编辑按钮点击
const handleEdit = () => {
  // 关闭抽屉
  emit("update:modelValue", false);
  // 跳转到用户信息编辑页面
  router.push("/user/profile");
};

// 处理VIP按钮点击
const handleVip = () => {
  // 关闭抽屉
  emit("update:modelValue", false);
  // 跳转到VIP页面
  router.push("/vip");
};
// 获取vip进度数据
const vipProgressObj = ref();
const getVipProgress = () => {
  vipProgress().then((res) => {
    vipProgressObj.value = res;
  });
};
watchEffect(() => {
  if (props.modelValue) {
    getVipProgress();
  }
});

const drawerWidth = ref(400); // 默认宽度

// 你可以根据内容或窗口大小动态设置宽度
const updateDrawerWidth = () => {
  // 这里可以自定义宽度逻辑，比如根据窗口宽度
  if (window.innerWidth < 600) {
    drawerWidth.value = window.innerWidth - 40;
  } else {
    drawerWidth.value = 400;
  }
};
  // 复制到剪贴板
  async function copyToClipboard(text: string): Promise<boolean> {
    try {
      await navigator.clipboard.writeText(text);
       showSuccess("Sucesso de cópia");
      return true;
    } catch {
      const textarea = document.createElement("textarea");
      textarea.value = text;
      textarea.style.position = "fixed";
      textarea.style.opacity = "0";
      document.body.appendChild(textarea);
      textarea.select();
      try {
        document.execCommand("copy");
         showSuccess("Sucesso de cópia");
        return true;
      } catch {
        return false;
      } finally {
        document.body.removeChild(textarea);
      }
    }
    
  }
onMounted(() => {
  updateDrawerWidth();
  window.addEventListener("resize", updateDrawerWidth);
});
</script>

<style lang="scss" scoped>
.user-drawer {
  background: #002664;
  padding: 0;
  position: fixed !important;
  top: 0;
  right: 0;
  height: 100vh;
  z-index: 999 !important;
  // width:  !important;
  :deep(.v-navigation-drawer__content) {
    display: flex;
    flex-direction: column;
    background: #002664 !important;
  }

  .close-btn {
    height: 22px;
    width: 22px;
    text-align: right;
    float: right;
    margin: 8px 0;
    margin-right: 8px;
    background: #c9cad8 !important;
    color: #2b324d;
    :deep(.v-icon) {
      font-size: 18px;
    }
  }
  .cody-icon {
    width: 16px;
    height: 16px;
    margin-left: 4px;
     cursor: pointer;
  }
  .top-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 8px;
    .user-edit-btn {
      height: 28px;
      background: linear-gradient(360deg, #c9b737 0%, #2abb27 100%);
      display: flex;
      align-items: center;
      border-radius: 14px 0 0 14px;
      justify-content: center;
      padding: 0 8px;
      font-size: 13px;
      line-height: 28px;
      cursor: pointer;
      .user-icon {
        width: 13px;
        margin-right: 4px;
      }
      span {
        padding-top: 2px;
      }
    }
  }
  :deep(.v-card) {
    border-radius: 12px;
    color: white;
  }
}

.balance-card,
.vip-card {
  background: #161e43 !important;
  .balance-title {
    background: linear-gradient(0deg, #c9b737, #2abb27);
    height: 40px;
    line-height: 40px;
    .text-h6 {
      color: #ffdf00;
    }
  }
  .balance-progress {
    border-radius: 5px;
    overflow: inherit;
    :deep(.v-progress-linear__background) {
      background: #41496e !important;
      opacity: 1 !important;
      border-radius: 5px;
    }
  }
  :deep(.balance-progress .v-progress-linear__determinate) {
    border-radius: 5px !important;
    &::after {
      content: "";
      position: absolute;
      display: block;
      background: url("@/assets/images/purse-icon.png") no-repeat center;
      background-size: 100% 100%;
      width: 21px;
      height: 21px;
      right: -10.5px;
      top: -6px;
      z-index: 9999;
    }
  }
  .balance-text {
    color: #f8ce41;
    font-size: 14px;
  }
  .balance-bottom {
    height: 30px;
    border-radius: 5px;
    border: 2px solid #41496e;
    padding: 0 14px;
    font-size: 13px;
    img {
      width: 20px;
      margin-right: 4px;
    }
  }
  .vip-title {
    height: 40px;
    background: url("@/assets/images/user-title-bg.png") no-repeat center;
    background-size: 100% 100%;
    font-size: 15px;
    .vip-icon {
      width: 20px;
      margin-right: 4px;
    }
    .right-icon {
      width: 10px;
      margin-left: 4px;
    }
  }
  .text-tips {
    font-weight: 400;
    font-size: 10px;
    color: #d3cbd8;
    line-height: 19px;
  }
  .text-color {
    color: #ffdf00;
  }
}

.logout-btn {
  height: 40px !important;
  background: #161e43 !important;
  color: #ffdf00 !important;
}
</style>
