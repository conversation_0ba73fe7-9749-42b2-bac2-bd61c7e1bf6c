export interface CustomerQuestion {
  id: number;
  keyword: string;
  response: string;
  created_at: string;
  updated_at: string;
}

export interface CustomerQuestionResponse {
  data: CustomerQuestion[];
}

export interface TelegramGroupLinkResponse {
  data: string;
}

export interface ChatRequest {
  message: string;
}

export interface ChatResponse {
  reply: string;
  timestamp: string;
  status_code: number;
  data: {
    reply: string;
    timestamp: string;
  };
} 