# WebSocket 登录信息流程优化

## 优化目标
避免重复发送登录信息，简化登录状态管理，提高代码可维护性。

## 主要改进

### 1. 集中化登录状态管理
将登录状态管理集中到 `WebSocketService` 类中：

```typescript
// 登录状态管理
private isLoggedIn = false;
private loginAttempts = 0;
private lastLoginTime = 0;
private lastLoginMessageId = "";
private readonly LOGIN_COOLDOWN = 5000; // 登录消息冷却时间（毫秒）
private readonly MAX_LOGIN_ATTEMPTS = 3;
```

### 2. 智能登录检查
新增 `shouldSkipLogin` 方法，避免重复发送：
- 检查是否已登录成功且消息ID相同
- 检查冷却时间
- 检查最大尝试次数

### 3. 自动登录流程
WebSocket 连接成功后自动发送登录信息：

```typescript
this.ws.onopen = async () => {
  // ... 其他逻辑
  
  // 自动发送登录信息
  try {
    await this.sendLogin();
  } catch (error) {
    console.error("自动发送登录信息失败:", error);
  }
  
  resolve();
};
```

### 4. 登录状态同步
收到服务器登录响应时自动更新状态：

```typescript
// 处理登录成功消息
if (type === "login" && success === true) {
  console.log("收到登录成功消息，开始发送心跳");
  this.isLoggedIn = true;
  this.startHeartbeat();
} else if (type === "login" && success === false) {
  console.log("登录失败:", data.message);
  this.isLoggedIn = false;
}
```

### 5. 简化应用层调用
App.vue 中的登录逻辑大幅简化：

```typescript
async function websocketToken() {
  const userInfo = store.state.auth.user;
  if (!userInfo || !userInfo.uuid) {
    console.log("用户信息不完整，跳过WebSocket登录");
    return;
  }

  try {
    await nextTick();
    await websocketService.connect();
    
    // 使用优化后的登录方法
    const success = await websocketService.sendLogin();
    if (success) {
      console.log("WebSocket登录消息发送成功");
      websocketLoginSent.value = true;
      websocketLoginSuccess.value = true;
      localStorage.setItem('isLogin', 'false');
    }
  } catch (error) {
    console.error("发送WebSocket登录消息失败:", error);
  }
}
```

## 新增公共方法

### `sendLogin(forceResend = false): Promise<boolean>`
发送登录信息，支持强制重发参数。

### `resetLoginState(): void`
重置登录状态，在断开连接或token过期时调用。

### `getLoginStatus()`
获取当前登录状态信息：
```typescript
{
  isLoggedIn: boolean;
  loginAttempts: number;
  lastLoginTime: number;
  canSendLogin: boolean;
}
```

## 优化效果

1. **避免重复发送**：通过智能检查机制，避免在短时间内重复发送相同的登录消息
2. **简化代码**：应用层代码从 130+ 行简化到 20+ 行
3. **统一管理**：登录状态集中管理，避免状态不一致
4. **自动化**：连接成功后自动发送登录信息，减少手动调用
5. **更好的错误处理**：统一的错误处理和状态重置机制

## 向后兼容性
保持了原有的公共接口，现有代码可以无缝迁移到新的优化版本。
