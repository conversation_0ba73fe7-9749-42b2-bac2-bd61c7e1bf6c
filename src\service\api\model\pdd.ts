import type { CommonSearchParams } from './common';

export interface PddUserActivityItem {
  id: number;
  activity_id: number;
  activity_name: string;
  participant_count: number;
  success_count: number;
  register_count: number;
  recharge_count: number;
  broadcast_management: string;
  eligible_users: string;
  bonus_type: string;
  start_time: number;
  end_time: number;
  status: number;
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
  user_id: number;
  participation_count: number;
  lock_count: number;
  accumulated_amount: number;
  target_amount: number;
  uuid: number;
  nickname: string;
  username: string;
  phone: string;
  email: string;
  CPF: string;
  pix: string;
  accountType: number;
  customerName: string;
  merchantUserId: string;
  avatar: string;
  gender: number;
  reg_ip: string;
  level: number;
  invite_code: string;
  source: number;
  father_id: number;
  is_recharge: number;
  is_black: number;
  is_login: number;
  remake: string;
}

export interface PddUserActivityListResponse {
  status_code: number;
  data: PddUserActivityItem[];
  count: number;
}

export interface PddUserActivityParams extends CommonSearchParams {
  user_id?: number;
  activity_id?: number;
  phone?: string;
  username?: string;
  nickname?: string;
  page:number;
}

export interface PddActivitySuccessItem {
  id: number;
  user_id: string;
  user_nickname: string;
  complete_time: string;
  participant_count: number;
  register_count: number;
  recharge_count: number;
  recharge_amount: number;
  bonus: number;
}

export interface PddActivitySuccessListResponse {
  status_code: number;
  data: PddActivitySuccessItem[];
  count: number;
}
