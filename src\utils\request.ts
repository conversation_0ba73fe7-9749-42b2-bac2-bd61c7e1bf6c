import { createAlova } from 'alova'
import { VueHook } from 'alova/vue'
import { useRequest } from 'alova/vue'

// 创建alova实例
export const alova = createAlova({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:9000',
  statesHook: VueHook,
  requestAdapter: {
    // 请求适配器
    request: async (config) => {
      const response = await fetch(config.url, {
        ...config,
        headers: {
          ...config.headers,
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        }
      })
      return response.json()
    }
  }
})

// 导出useRequest hook
export { useRequest } 