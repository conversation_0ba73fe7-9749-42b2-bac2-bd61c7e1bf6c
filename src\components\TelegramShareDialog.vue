<template>
  <v-dialog v-model="dialog" max-width="400" class="telegram-dialog">
    <v-card class="share-dialog">
      <v-card-title class="d-flex justify-center pt-6">
        <span class="text-h6 dialog-title">Junte-se a nós</span>
        <v-btn icon class="close-btn" @click="close">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text class="pa-6">
        <div class="reward-list">
          <div class="reward-item d-flex align-center mb-4">
            <div class="number pink--text">1</div>
            <div class="text">Recompensas VIP de login diário.</div>
          </div>
          <div class="reward-item d-flex align-center mb-4">
            <div class="number pink--text">2</div>
            <div class="text">
              Convide novos usuários para completar sua primeira recarga e
              participar de recompensas diárias de classificação em tempo real.
            </div>
          </div>
          <div class="reward-item d-flex align-center mb-4">
            <div class="number pink--text">3</div>
            <div class="text">
              Recompensas de classificação em tempo real de apostas diárias.
            </div>
          </div>
          <div class="reward-item d-flex align-center mb-4">
            <div class="number pink--text">4</div>
            <div class="text">
              Compartilhe o prêmio da plataforma de vez em quando.
            </div>
          </div>
          <div class="reward-item d-flex align-center mb-4">
            <div class="number pink--text">5</div>
            <div class="text">
              Junte-se ao canal oficial do Telegram e receba recompensas em
              envelope vermelho.
            </div>
          </div>
        </div>
        <v-btn
          block
          class="mt-6 white--text submit-btn"
          height="48"
          @click="joinTelegram"
        >
          Junte-se ao canal de Telegram
        </v-btn>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { subscriptions } from "@/api/customer-service.ts";

const dialog = ref(false);
const telegramLink = ref(""); // 替换为实际的 Telegram 频道链接

const show = () => {
  getCustomerServiceGroups();
  dialog.value = true;
};

const close = () => {
  dialog.value = false;
};

const getCustomerServiceGroups = async () => {
  const res = await subscriptions();
  console.log(res)
  telegramLink.value += res[0].chat_id;
  console.log(telegramLink.value);
};
const joinTelegram = () => {
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
  if (isIOS) {
    // iOS需要用户交互触发window.open
    setTimeout(() => {
      window.location.href = telegramLink.value;
    }, 100);
  } else {
    window.open(telegramLink.value, "_self");
  }
};

defineExpose({
  show,
  close,
});
</script>

<style lang="scss" scoped>
.telegram-dialog {
  :deep(.v-dialog) {
    @media (max-width: 768px) {
      width: 90% !important;
      margin: 0 auto;
    }
  }
}

.share-dialog {
  border-radius: 20px !important;
  background: #1a1c2e;
  :deep(.v-card-text) {
    @media (max-width: 768px) {
      padding-top: 0 !important;
    }
  }
  :deep(.v-card-title) {
    color: white;
    position: relative;

    .close-btn {
      position: absolute;
      right: 16px;
      top: 16px;
      background: none;
    }

    .dialog-title {
      font-size: 1.25rem;
      @media (max-width: 768px) {
        font-size: 1.1rem;
      }
    }
  }
}

.reward-list {
  .reward-item {
    @media (max-width: 768px) {
      margin-bottom: 5px !important;
    }
    .number {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: rgba(255, 32, 110, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      font-weight: bold;
      flex-shrink: 0;
    }

    .text {
      color: white;
      flex: 1;
      font-size: 14px;
      line-height: 1.4;
      @media (max-width: 768px) {
        font-size: 13px;
      }
    }
  }
}

.submit-btn {
  background: linear-gradient(0deg, #c9b737, #2abb27);
  color: white;
  border-radius: 20px;
  height: 40px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  @media (max-width: 768px) {
    height: 35px !important;
    font-size: 13px;
    padding: 0 12px;
  }
}
</style>
