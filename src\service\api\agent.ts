/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-06-13 09:15:59
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\service\api\agent.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from "../request";

export interface AgentItem {
  id: number;
  agent_id: number;
  agent_name: string;
  agent_level_type: number;
  agent_level_type_name: string;
  commission_rate: number;
  daily_withdrawal_limit: number;
  monthly_withdrawal_limit: number;
  contact_person: string;
  contact_phone: string;
  contact_email: string;
  username: string;
  withdrawal_restricted: number;
  parent_id: number;
  registered_users: number;
  recharge_users: number;
  recharge_amount: number;
  number_of_subordinates: number;
  agent_level: number;
  status: number;
  status_text: string;
  last_active_time: number;
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
}

export interface AgentListParams {
  page: number;
  size: number;
  agent_name?: string;
  status?: Api.Common.EnableStatus;
}

export interface AgentStatusParams {
  agent_id: number;
  status: Api.Common.EnableStatus;
}

export function fetchGetAgentList(params: any) {
  return request({
    url: "/backend/agent/list",
    method: "GET",
    params,
  });
}
// 更新代理商状态
export function fetchUpdateAgentStatus(params: any) {
  return request({
    url: "/backend/agent/update_status",
    method: "POST",
    data: params,
  });
}
// 获取当前用户角色信息
export function fetchGetAgentUserRole(params: any) {
  return request({
    url: "/backend/agent/user_role",
    method: "GET",
    params,
  });
}
// 新增代理商
export function fetchCreateAgent(params: any) {
  return request({
    url: "/backend/agent/create",
    method: "POST",
    data: params,
  });
}
// 更新代理商信息
export function fetchUpdateAgent(params: any) {
  return request({
    url: "/backend/agent/update",
    method: "POST",
    data: params,
  });
}

export function fetchDeleteAgent(agent_id: number) {
  return request({
    url: "/backend/agent/deleteAgent",
    method: "delete",
    params: { agent_id },
  });
}

export function fetchGetSubordinateList(agent_id: number) {
  return request({
    url: "/backend/agent/fetchGetSubordinateList",
    method: "GET",
    params: { agent_id },
  });
}

export function fetchGetAgentLevelTypes(params: any) {
  return request({
    url: "/backend/agent/level_types",
    method: "GET",
    params,
  });
}
// 代理商c端注册人数列表
export function fetchGetAgentRegistered_users(params: any) {
  return request({
    url: "/backend/agent/registered_users",
    method: "GET",
    params,
  });
}
// 代理商c端充值人数列表
export function fetchGetAgentRechargeUsers(params: any) {
  return request({
    url: "/backend/agent/recharge_users",
    method: "GET",
    params,
  });
}
// 代理商层级管理
export function fetchGetAgentHierarchy(params: any) {
  return request({
    url: "/backend/agent/hierarchy",
    method: "GET",
    params,
  });
}

// 限制代理商提现
export function restrictWithdrawal(params: any) {
  return request({
    url: "/backend/agent/restrict_withdrawal",
    method: "POST",
    data: params,
  });
}

// 删除代理商及其下级
export function deleteSubAgents(params: any) {
  return request({
    url: "/backend/agent/delete_sub_agents",
    method: "POST",
    data: params,
  });
}

// 代理商基础信息
export function agentInfo(params: any) {
  return request({
    url: "/backend/agent/info",
    method: "GET",
    params,
  });
}

// 获取提现账号列表
export function agentWithdrawalAccounts(params: any) {
  return request({
    url: "/backend/agent/withdrawal-accounts/list",
    method: "GET",
    params,
  });
}

// 代理商获取验证码
export function agentSendSms(params: any) {
  return request({
    url: "/backend/agent/send-sms",
    method: "POST",
    data: params,
  });
}

// 新增提现账号
export function agentWithdrawalAccountsAdd(params: any) {
  return request({
    url: "/backend/agent/withdrawal-accounts/create",
    method: "POST",
    data: params,
  });
}

// 获取支持的账号类型
export function agentWithdrawalAccountsTypes(params: any) {
  return request({
    url: "/backend/agent/withdrawal-account-types",
    method: "GET",
    params,
  });
}

// 获取仪表板数据指标
export function agentDataStats(params: any) {
  return request({
    url: "/backend/agent/data/stats",
    method: "GET",
    params,
  });
}

// 获取代理商列表指标数据
export function agentListStats(params: any) {
  return request({
    url: "/backend/agent/list/stats",
    method: "GET",
    params,
  });
}

// 获取佣金流水列表
export function agentDataCommission(params: any) {
  return request({
    url: "/backend/agent/data/commission",
    method: "GET",
    params,
  });
}

// 获取用户数据列表
export function agentDataUsers(params: any) {
  return request({
    url: "/backend/agent/data/users",
    method: "GET",
    params,
  });
}

// 获取用户数据列表
export function agentDataWithdrawal(params: any) {
  return request({
    url: "/backend/agent/data/withdrawal",
    method: "GET",
    params,
  });
}

// 创建提现申请
export function agentWithdrawalCreate(params: any) {
  return request({
    url: "/backend/agent/data/withdrawal/create",
    method: "POST",
    data: params,
  });
}
