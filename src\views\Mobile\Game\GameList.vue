<template>
    <div class="mobile-game-list">
        <!-- Game List -->
        <v-row v-if="games.length > 0">
            <v-col
                :cols="4"
                v-for="game in games"
                :key="game.id"
                v-memo="[game.id]"
                class="game-card pa-2"
                @click="navigateToGame(game)"
            >
                <div class="game-image mt-2">
                    <v-img
                        :src="game.icon"
                        :alt="game.game_name"
                        class="game-image"
                        aspect-ratio="0.84"
                        lazy-src="https://via.placeholder.com/40x40?text=..."
                    >
                        <template #error>
                            <!-- 图片加载失败时什么都不显示 -->
                        </template>
                    </v-img>
                </div>
                <div class="game-title">{{ game.game_name }}</div>
                <div class="game-provider">{{ game.manufacturer }}</div>
            </v-col>
        </v-row>
        <!-- Empty State -->
        <div v-if="!games.length && !loading" class="empty-state">
            <v-icon size="64" color="grey">mdi-gamepad-variant-outline</v-icon>
            <div class="empty-text">Nenhum jogo encontrado</div>
        </div>
        <!-- Load More Button -->
        <div v-if="hasMore" class="text-center pa-4">
            <v-btn :loading="loading" @click="loadMore" class="more-btn">
                Carregar mais
            </v-btn>
        </div>
        <!-- Loading State -->
        <div v-if="loading && !games.length" class="loading-container">
            <v-progress-circular
                indeterminate
                color="primary"
            ></v-progress-circular>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { searchGetGameList } from "@/api/game";
import type { GameItem } from "@/types/game";

const router = useRouter();
const route = useRoute();
const loading = ref(false);
const games = ref<GameItem[]>([]);
const currentPage = ref(1);
const pageSize = 30; // 常量即可
const hasMore = ref(true);

// 获取搜索关键词
const getSearchKey = () => (route.query.keyword as string) || "";

// 获取游戏列表
const fetchGames = async () => {
    const searchKey = getSearchKey().trim();
    if (!searchKey && currentPage.value === 1) {
        games.value = [];
        hasMore.value = false;
        return;
    }

    loading.value = true;
    try {
        const response = await searchGetGameList({
            search_key: searchKey,
            page_no: currentPage.value,
            page_size: pageSize,
        });

        if (response?.data) {
            if (currentPage.value === 1) {
                games.value = response.data;
            } else {
                games.value = [...games.value, ...response.data];
            }
            hasMore.value =
                Array.isArray(response.data) &&
                response.data.length === pageSize;
        }
    } catch (err) {
        console.error("Failed to search games:", err);
    } finally {
        loading.value = false;
    }
};

// 加载更多
const loadMore = () => {
    if (!loading.value && hasMore.value) {
        currentPage.value++;
        fetchGames();
    }
};

// 跳转到游戏详情
const navigateToGame = (game: GameItem) => {
    router.push(`/m/game/${game.game_uid}?uuid=` + game.id);
};

onMounted(() => {
    fetchGames();
});
</script>

<style lang="scss" scoped>
.mobile-game-list {
    padding: 16px;
    background: #110e3b;
    min-height: 100vh;
    color: white;
}

.header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;

    .back-btn {
        color: white;
    }

    .title {
        font-size: 18px;
        font-weight: 500;
    }
}

.games-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 16px;
}

.game-card {
    padding: 0;
    position: relative;
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
        transform: scale(1.05);
        // transform: translateY(-8px);
    }
    .game-image {
        position: relative;
        overflow: hidden;

        .favorite-btn {
            position: absolute;
            top: 0;
            right: 0;
            width: 24px;
            height: 24px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 0 0 0 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
    }

    .game-title {
        font-size: 12px;
        margin-top: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .game-provider {
        font-size: 12px;
        color: #ffdf00;
        margin-top: 4px;
    }
}

.more-btn {
    background: linear-gradient(0deg, #c9b737, #2abb27);
    border-radius: 20px;
    height: 40px;
    min-width: 160px;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    color: rgba(255, 255, 255, 0.5);
    text-align: center;

    .empty-text {
        margin-top: 16px;
        font-size: 16px;
    }
}
</style>
