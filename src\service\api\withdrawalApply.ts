import { request } from '../request';

// 获取提现申请列表
export function fetchWithdrawalApplyList(params: any) {
  return request({
    url: '/backend/walletWithdrawalApplication/list',
    method: 'get',
    params
  });
}

// 更新提现申请状态
export function updateWithdrawalApplyStatus(data: { id: number; status: number; reason?: string }) {
  return request({
    url: '/backend/walletWithdrawalApplication/status',
    method: 'post',
    data
  });
}
