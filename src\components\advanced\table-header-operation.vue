<script setup lang="ts">
import { $t } from "@/locales";

defineOptions({ name: "TableHeaderOperation" });
import { useAuth } from "@/hooks/business/auth";

const { hasAuth } = useAuth();

interface Props {
  disabledDelete?: boolean;
  loading?: boolean;
  isNoDelete?: boolean;
  isNoAdd?: boolean;
}

defineProps<Props>();

interface Emits {
  (e: "add"): void;
  (e: "delete"): void;
  (e: "refresh"): void;
}

const emit = defineEmits<Emits>();

const columns = defineModel<UI.TableColumnCheck[]>("columns", {
  default: () => [],
});

function add() {
  emit("add");
}

function batchDelete() {
  emit("delete");
}

function refresh() {
  emit("refresh");
}
</script>

<template>
  <div class="table-header-operation-wrapper">
    <ElSpace direction="horizontal" wrap justify="end" class="lt-sm:w-200px">
      <slot name="prefix"></slot>
      <slot name="default">
        <ElButton  v-if="!isNoAdd&& hasAuth(1)" plain type="primary" @click="add">
          <template #icon>
            <icon-ic-round-plus class="text-icon" style="color: #04be74" />
          </template>
          {{ $t("common.add") }}
        </ElButton>
        <ElPopconfirm
          :title="$t('common.confirmDelete')"
          @confirm="batchDelete"
          v-if="!isNoDelete && hasAuth(2)"
        >
          <template #reference>
            <ElButton class="custom-danger-button" :disabled="disabledDelete">
              <template #icon>
                <icon-ic-round-delete
                  class="text-icon"
                  style="color: #fb136c"
                />
              </template>
              {{ $t("common.batchDelete") }}
            </ElButton>
          </template>
        </ElPopconfirm>
      </slot>
      <ElButton @click="refresh">
        <template #icon>
          <icon-mdi-refresh
            class="text-icon"
            style="color: #646cff"
            :class="{ 'animate-spin': loading }"
          />
        </template>
        {{ $t("common.refresh") }}
      </ElButton>
      <TableColumnSetting v-model:columns="columns" />
      <slot name="suffix"></slot>
    </ElSpace>
  </div>
</template>

<style scoped>
.table-header-operation-wrapper {
  border: none;
  border-radius: 0;
  background: #fff;
  padding: 0;
  display: inline-flex;
  overflow: visible;
}

:deep(.el-space) {
  gap: 0 !important;
  border-collapse: collapse;
}

:deep(.el-space__item) {
  margin: 0 !important;
  padding: 0;
}

:deep(.el-button) {
  height: 35px;
  min-width: 0;
  padding: 0 16px;
  border: 1px solid #e5e6eb;
  border-radius: 0;
  font-size: 14px;
  color: #606266;
  background: #fff !important;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: none;
  transition: background 0.2s;
  margin: 0;
}

:deep(.el-button + .el-button),
:deep(.el-space__item) {
  .el-button {
    border-right: none;
  }

  &:last-child {
    .el-button {
      border-right: 1px solid #e5e6eb;
    }
  }
}

/* :deep(.el-button--primary) {
  color: #4cb050;
} */

:deep(.custom-danger-button) {
  color: #ff5b5b !important;
  background-color: #fff !important;
  border-color: #e5e6eb !important;
}

:deep(.custom-danger-button:hover) {
  background-color: #f5f7fa !important;
  border-color: #e5e6eb !important;
  color: #ff5b5b !important;
}

:deep(.custom-danger-button:focus) {
  background-color: #fff !important;
  border-color: #e5e6eb !important;
  color: #ff5b5b !important;
}

:deep(.custom-danger-button:disabled) {
  color: #c0c4cc !important;
  background-color: #fff !important;
  border-color: #e5e6eb !important;
}

:deep(.el-button--danger) {
  color: #ff5b5b !important;
  background-color: #fff !important;
  border-color: #e5e6eb !important;
}

:deep(.el-button:disabled) {
  color: #c0c4cc;
}

:deep(.el-button):hover:not(:disabled) {
  background: #f5f7fa !important;
}

:deep(.icon-mdi-refresh) {
  color: #6b7cff;
  font-size: 18px;
  margin-right: 5px;
}

:deep(.icon-ic-round-plus) {
  color: #4cb050;
  font-size: 18px;
  margin-right: 5px;
}

:deep(.icon-ic-round-delete) {
  color: #ff5b5b;
  font-size: 18px;
  margin-right: 5px;
}

/* 为列设置按钮添加样式 */
:deep(.table-column-setting .icon) {
  color: #909399;
  font-size: 18px;
}
</style>
