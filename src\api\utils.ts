// 定义基础参数接口
interface BaseParams {
  page?: number;
  pageSize?: number;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
  [key: string]: any;
}

// 定义归一化配置接口
interface NormalizeConfig {
  defaultPage?: number;
  defaultPageSize?: number;
  maxPageSize?: number;
  allowedSortFields?: string[];
}

// 默认配置
const DEFAULT_CONFIG: NormalizeConfig = {
  defaultPage: 1,
  defaultPageSize: 20,
  maxPageSize: 100,
  allowedSortFields: []
};

// 参数归一化函数
export function normalizeParams(params: BaseParams, config: NormalizeConfig = {}): BaseParams {
  const mergedConfig = { ...DEFAULT_CONFIG, ...config };
  
  // 处理分页参数
  const page = Number(params.page) || mergedConfig.defaultPage!;
  const pageSize = Math.min(
    Number(params.pageSize) || mergedConfig.defaultPageSize!,
    mergedConfig.maxPageSize!
  );

  // 处理排序参数
  let sortField = params.sortField;
  let sortOrder = params.sortOrder;
  
  if (sortField && mergedConfig.allowedSortFields?.length) {
    if (!mergedConfig.allowedSortFields.includes(sortField)) {
      sortField = undefined;
      sortOrder = undefined;
    }
  }

  // 构建归一化后的参数
  const normalized: BaseParams = {
    ...params,
    page,
    pageSize
  };

  if (sortField) {
    normalized.sortField = sortField;
    normalized.sortOrder = sortOrder === 'desc' ? 'desc' : 'asc';
  } else {
    delete normalized.sortField;
    delete normalized.sortOrder;
  }

  // 移除空值参数
  Object.keys(normalized).forEach(key => {
    if (normalized[key] === null || normalized[key] === undefined || normalized[key] === '') {
      delete normalized[key];
    }
  });

  return normalized;
}

// 使用示例
/*
const params = {
  page: '2',
  pageSize: '50',
  sortField: 'createdAt',
  sortOrder: 'desc',
  search: 'test'
};

const config = {
  defaultPageSize: 10,
  maxPageSize: 30,
  allowedSortFields: ['createdAt', 'updatedAt']
};

const normalized = normalizeParams(params, config);
// 输出: { page: 2, pageSize: 30, sortField: 'createdAt', sortOrder: 'desc', search: 'test' }
*/ 