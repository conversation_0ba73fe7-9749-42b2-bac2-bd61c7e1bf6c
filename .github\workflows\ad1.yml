name: Test Environment CI/CD

on:
  workflow_dispatch:
    inputs:
      version:
        description: '输入版本号 (例如: v1.2.0)'
        required: true
        type: string

env:
  DOCKER_IMAGE: ${{ secrets.DOCKERHUB_USERNAME }}/box777-admin
  SERVER_HOST_1: **************  # 第一台服务器
  SERVER_HOST_2: *************   # 第二台服务器
  SERVER_PORT: 22
  PROJECT_PATH: ~/project
  CONTAINER_NAME: box777-admin
  APP_PORT: 8009
  NODE_OPTIONS: "--max_old_space_size=4096"

jobs:
  build-and-deploy:
    runs-on: ubuntu-22.04

    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'pnpm'

      - name: Debug workspace structure
        run: |
          echo "=== Root directory ==="
          ls -la
          echo "=== Packages directory ==="
          ls -la packages
          find packages -name "package.json" -type f
          cat pnpm-workspace.yaml

      - name: Clean workspace
        run: |
          df -h
          rm -rf node_modules .next dist

      - name: Install dependencies
        run: |
          pnpm config list
          HUSKY=0 pnpm install --no-frozen-lockfile --ignore-scripts
        env:
          NODE_OPTIONS: ${{ env.NODE_OPTIONS }}
          DEBUG: "*"

      - name: Build application
        run: pnpm build
        env:
          NODE_ENV: production
          NODE_OPTIONS: ${{ env.NODE_OPTIONS }}

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: |
            ${{ env.DOCKER_IMAGE }}:${{ github.event.inputs.version }}
            ${{ env.DOCKER_IMAGE }}:latest
          build-args: |
            NODE_ENV=production

      # 部署到第一台服务器
      - name: Deploy to Server 1
        uses: appleboy/ssh-action@master
        with:
          host: ${{ env.SERVER_HOST_1 }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: ${{ env.SERVER_PORT }}
          script: |
            set -euxo pipefail
            mkdir -p ${{ env.PROJECT_PATH }}
            cd ${{ env.PROJECT_PATH }}

            # 带重试的镜像拉取
            for i in {1..3}; do
              if docker pull ${{ env.DOCKER_IMAGE }}:${{ github.event.inputs.version }}; then
                echo "✅ 镜像拉取成功"
                break
              else
                echo "⚠️ 镜像拉取失败，重试 $i/3..."
                sleep 5
                [ $i -eq 3 ] && exit 1
              fi
            done

            # 停止并清理旧容器
            docker stop ${{ env.CONTAINER_NAME }} || true
            docker rm ${{ env.CONTAINER_NAME }} || true

            # 启动新容器
            docker run -d \
              --name ${{ env.CONTAINER_NAME }} \
              -p ${{ env.APP_PORT }}:80 \
              --restart unless-stopped \
              ${{ env.DOCKER_IMAGE }}:${{ github.event.inputs.version }}

            # 健康检查
            echo "⏳ 检查容器状态..."
            for i in {1..10}; do
              CONTAINER_STATUS=$(docker inspect -f '{{.State.Status}}' ${{ env.CONTAINER_NAME }} 2>/dev/null || echo "missing")
              [ "$CONTAINER_STATUS" = "running" ] && break
              sleep 5
              echo "等待容器就绪 ($i/10)..."
              [ $i -eq 10 ] && { echo "❌ 容器启动超时"; docker logs ${{ env.CONTAINER_NAME }} --tail 50; exit 1; }
            done

            echo "✅ 服务器1部署验证通过"

            # 清理多余镜像
            #CURRENT_IMAGE="${{ env.DOCKER_IMAGE }}:${{ github.event.inputs.version }}"
            #docker images --format "{{.Repository}}:{{.Tag}}" | grep "${{ env.DOCKER_IMAGE }}" | grep -v "$CURRENT_IMAGE" | xargs -r docker rmi

      # 部署到第二台服务器
      - name: Deploy to Server 2
        uses: appleboy/ssh-action@master
        with:
          host: ${{ env.SERVER_HOST_2 }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: ${{ env.SERVER_PORT }}
          script: |
            set -euxo pipefail
            mkdir -p ${{ env.PROJECT_PATH }}
            cd ${{ env.PROJECT_PATH }}

            # 带重试的镜像拉取
            for i in {1..3}; do
              if docker pull ${{ env.DOCKER_IMAGE }}:${{ github.event.inputs.version }}; then
                echo "✅ 镜像拉取成功"
                break
              else
                echo "⚠️ 镜像拉取失败，重试 $i/3..."
                sleep 5
                [ $i -eq 3 ] && exit 1
              fi
            done

            # 停止并清理旧容器
            docker stop ${{ env.CONTAINER_NAME }} || true
            docker rm ${{ env.CONTAINER_NAME }} || true

            # 启动新容器
            docker run -d \
              --name ${{ env.CONTAINER_NAME }} \
              -p ${{ env.APP_PORT }}:80 \
              --restart unless-stopped \
              ${{ env.DOCKER_IMAGE }}:${{ github.event.inputs.version }}

            # 健康检查
            echo "⏳ 检查容器状态..."
            for i in {1..10}; do
              CONTAINER_STATUS=$(docker inspect -f '{{.State.Status}}' ${{ env.CONTAINER_NAME }} 2>/dev/null || echo "missing")
              [ "$CONTAINER_STATUS" = "running" ] && break
              sleep 5
              echo "等待容器就绪 ($i/10)..."
              [ $i -eq 10 ] && { echo "❌ 容器启动超时"; docker logs ${{ env.CONTAINER_NAME }} --tail 50; exit 1; }
            done

            echo "✅ 服务器2部署验证通过"

            # 清理多余镜像
            #CURRENT_IMAGE="${{ env.DOCKER_IMAGE }}:${{ github.event.inputs.version }}"
            #docker images --format "{{.Repository}}:{{.Tag}}" | grep "${{ env.DOCKER_IMAGE }}" | grep -v "$CURRENT_IMAGE" | xargs -r docker rmi

  notify:
    runs-on: ubuntu-22.04
    needs: [build-and-deploy]
    if: always()
    steps:
      - name: Notify Feishu
        env:
          DEPLOY_STATUS: ${{ needs.build-and-deploy.result }}
          SERVER_URL_1: "http://${{ env.SERVER_HOST_1 }}:${{ env.APP_PORT }}"
          SERVER_URL_2: "http://${{ env.SERVER_HOST_2 }}:${{ env.APP_PORT }}"
        run: |
          if [ "$DEPLOY_STATUS" = "success" ]; then
            TITLE="✅ 部署成功通知"
            TEMPLATE="green"
            ACCESS_URL="**访问地址**: \n- [服务器1]($SERVER_URL_1)\n- [服务器2]($SERVER_URL_2)"
          else
            TITLE="❌ 部署失败通知"
            TEMPLATE="red"
            ACCESS_URL=""
          fi

          FEISHU_MSG='{
            "msg_type": "interactive",
            "card": {
              "header": {
                "title": { "tag": "plain_text", "content": "'"$TITLE"'" },
                "template": "'"$TEMPLATE"'"
              },
              "elements": [{
                "tag": "div",
                "text": {
                  "content": "**仓库名**: ${{ github.repository }}\n**所在分支**: ${{ github.ref_name }}\n**Workflow**: ${{ github.workflow }}\n**镜像版本**: ${{ github.event.inputs.version }}\n**日志链接**: [点击查看](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})\n**部署状态**: '"${TITLE#* }"' \n'"$ACCESS_URL"'",
                  "tag": "lark_md"
                }
              }]
            }
          }'

          curl -X POST "${{ secrets.FEISHU_WEBHOOK }}" \
            -H 'Content-Type: application/json' \
            -d "$FEISHU_MSG"
