import { Router } from 'vue-router'
import { store } from '@/store'

export function setupRouterGuards(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const timeout = setTimeout(() => {
      next(false)
    }, 5000)

    try {
      const isAuthenticated = store.getters['auth/isAuthenticated']
      const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

      if (requiresAuth && !isAuthenticated) {
        next('/home')
      } else {
        next()
      }
    } finally {
      clearTimeout(timeout)
    }
  })
} 