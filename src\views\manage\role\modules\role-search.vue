<script setup lang="ts">
import {ref} from 'vue';
import {$t} from '@/locales';
import {enableStatusOptions} from '@/constants/business';
import {translateOptions} from '@/utils/common';

defineOptions({name: 'RoleSearch'});

const activeName = ref(['role-search']);

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const model = defineModel<Api.SystemManage.RoleSearchParams>('model', {required: true});

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}
</script>

<template>
  <div class="search-wrapper">
    <ElForm :model="model">
      <ElRow :gutter="24">
        <ElCol :span="6">
          <ElFormItem :label="$t('page.manage.role.roleName')" prop="role_name">
            <ElInput v-model="model.role_name" :placeholder="$t('page.manage.role.form.roleName')"/>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem :label="$t('page.manage.role.roleStatus')" prop="status">
            <ElSelect v-model="model.status" :placeholder="$t('page.manage.role.form.roleStatus')" clearable>
              <ElOption
                v-for="{ label, value } in translateOptions(enableStatusOptions)"
                :key="value"
                :label="label"
                :value="value"
              ></ElOption>
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem>
            <ElButton type="primary" @click="search">搜索</ElButton>
            <ElButton @click="reset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<style scoped></style>
