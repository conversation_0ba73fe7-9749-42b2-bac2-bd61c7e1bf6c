<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useForm, useFormRules } from "@/hooks/common/form";
import {
  fetchGetAllRoles,
  fetchAgentGetAllRoles,
  fetchAddUser,
  fetchUpdateUser,
} from "@/service/api";
import { $t } from "@/locales";
import { enableStatusOptions, userGenderOptions } from "@/constants/business";
import { localStg } from "@/utils/storage";

defineOptions({ name: "UserOperateDrawer" });

interface Props {
  /** the type of operation */
  operateType: UI.TableOperateType;
  /** the edit row data */
  rowData?: Api.SystemManage.User | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: "submitted"): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>("visible", {
  default: false,
});

const isManage = ref(true);
watch(
  () => localStg.get("isManage"),
  (newValue) => {
    console.log("isManage", newValue);
    isManage.value = JSON.parse(newValue || "false");
  },
  { immediate: true },
);

const { formRef, validate, restoreValidation } = useForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<UI.TableOperateType, string> = {
    add: $t("page.manage.user.addUser"),
    edit: $t("page.manage.user.editUser"),
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.SystemManage.User,
  "user_name" | "gender" | "phone" | "status" | "role_id" | "level" | "password"
>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    user_name: "",
    gender: undefined,
    phone: "",
    status: undefined,
    role_id: "",
    level: "",
    password: "",
  };
}

type RuleKey = Extract<keyof Model, "user_name" | "status">;

const rules: Record<RuleKey, App.Global.FormRule> = {
  user_name: defaultRequiredRule,
  status: defaultRequiredRule,
  gender: defaultRequiredRule,
};

/** the enabled role options */
const roleOptions = ref<CommonType.Option<string>[]>([]);

async function getRoleOptions() {
  const { error, data } = isManage.value
    ? await fetchGetAllRoles()
    : await fetchAgentGetAllRoles();
  console.log("data:", data);
  const key = isManage.value ? "id" : "role_id";
  const val = isManage.value ? 1 : 0;
  if (!error && data) {
    const options = data.data
      .filter((item) => item[key] !== val)
      .map((item) => ({
        label: item.role_name,
        value: item[key],
      }));
    roleOptions.value = options;
  }
}

function handleInitModel() {
  model.value = createDefaultModel();
  if (props.operateType === "edit" && props.rowData) {
    model.value = {
      ...props.rowData,
      gender: props.rowData.gender,
      status: props.rowData.status,
    };
    console.log("model.value:", model.value);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const submitData = {
    ...model.value,
    gender: Number(model.value.gender),
    status: Number(model.value.status),
  };
  if(!isManage.value){
    delete submitData.level
  }
  if (props.operateType === "add") {
    const { error } = await fetchAddUser(submitData);
    if (!error) {
      window.$message?.success($t("common.addSuccess"));
      closeDrawer();
      emit("submitted");
    }
  } else {
    const { error } = await fetchUpdateUser(submitData);
    if (!error) {
      window.$message?.success($t("common.updateSuccess"));
      closeDrawer();
      emit("submitted");
    }
  }
}

watch(visible, () => {
  if (visible.value) {
    getRoleOptions();
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <ElDrawer v-model="visible" :title="title" :size="360">
    <ElForm ref="formRef" :model="model" :rules="rules" label-position="top">
      <ElFormItem label="用户名" prop="user_name">
        <ElInput v-model="model.user_name" placeholder="请输入用户名" />
      </ElFormItem>
      <ElFormItem label="密码" prop="password">
        <ElInput
          v-model="model.password"
          type="password"
          placeholder="请输入密码"
          show-password
        />
      </ElFormItem>
      <ElFormItem label="手机号" prop="phone">
        <ElInput v-model="model.phone" placeholder="请输入手机号" />
      </ElFormItem>
      <ElFormItem label="性别" prop="gender">
        <ElRadioGroup v-model="model.gender">
          <ElRadio
            v-for="item in userGenderOptions"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </ElRadioGroup>
      </ElFormItem>
      <ElFormItem label="状态" prop="status">
        <ElRadioGroup v-model="model.status">
          <ElRadio
            v-for="item in enableStatusOptions"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          />
        </ElRadioGroup>
      </ElFormItem>
      <ElFormItem label="角色" prop="role_id">
        <ElSelect v-model="model.role_id" placeholder="请选择角色">
          <ElOption
            v-for="{ label, value } in roleOptions"
            :key="value"
            :label="label"
            :value="value"
          />
        </ElSelect>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElSpace :size="16">
        <ElButton @click="closeDrawer">{{ $t("common.cancel") }}</ElButton>
        <ElButton type="primary" @click="handleSubmit">{{
          $t("common.confirm")
        }}</ElButton>
      </ElSpace>
    </template>
  </ElDrawer>
</template>

<style scoped></style>
