<template>
  <div v-if="show" class="coin-effect-overlay">
    <!-- 第一阶段：coin1.gif -->
    <img
      v-if="currentStep === 0"
      src="@/assets/images/coin1.gif"
      class="coin-effect-gif coin-effect-gif1"
      @load="onLoad"
    />
    <!-- 第二阶段：coin.gif -->
    <img
      v-else-if="currentStep === 1"
      src="@/assets/images/coin.gif"
      class="coin-effect-gif coin-effect-gif2"
      @load="onLoad"
    />
    <!-- 第三阶段：金币雨效果 -->
    <div v-else-if="currentStep === 2" class="coin-rain-container">
      <!-- 金币雨效果将在这里渲染 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import coinGif from "@/assets/images/coin2.gif";

const props = defineProps<{ show: boolean }>();
const emit = defineEmits(["finish"]);

// 步骤控制
const currentStep = ref(0);
const steps = 3; // 三个步骤：coin1.gif -> coin.gif -> 金币雨

// 金币雨相关状态
let isRaining = false;
let animationId: number | null = null;
let coins: Coin[] = [];
let rainTimer: number | null = null;
let windowWidth = window.innerWidth;
let windowHeight = window.innerHeight;

// 金币配置
const coinConfig = {
  minSize: 20,
  maxSize: 40,
  minSpeed: 2,
  maxSpeed: 5,
  minRotationSpeed: 1,
  maxRotationSpeed: 5,
  maxCoins: 100, // PC端最大金币数
  spawnInterval: 30, // 生成新金币的间隔（毫秒）
  fadeStartDistance: 0.3, // 开始淡出的距离比例（屏幕高度的30%）
  fadeEndDistance: 1.0, // 完全淡出的距离比例（屏幕高度的100%）
};

// 检测是否为移动端
const isMobile = () => {
  return (
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    ) || window.innerWidth <= 768
  );
};

// 获取移动端优化的配置
const getOptimizedConfig = () => {
  if (isMobile()) {
    return {
      ...coinConfig,
      maxCoins: 30, // 移动端最大金币数
      spawnInterval: 50, // 增加生成间隔，减少密度
      minSize: 15, // 稍微减小金币尺寸
      maxSize: 30,
      minSpeed: 1.5, // 稍微降低速度
      maxSpeed: 4,
    };
  }
  return coinConfig;
};

// GIF加载完成处理
const onLoad = () => {
  setTimeout(() => {
    if (currentStep.value < steps - 1) {
      currentStep.value++;
      // 如果进入金币雨阶段，开始金币雨效果
      if (currentStep.value === 2) {
        nextTick(() => {
          startCoinRain();
        });
      }
    } else {
      emit("finish");
    }
  }, 1000); // 每个gif显示1秒，可根据实际调整
};

// 金币类
class Coin {
  element: HTMLDivElement;
  size: number;
  x: number;
  y: number;
  speed: number;
  rotation: number;
  rotationSpeed: number;
  opacity: number;
  fadeDelay: number;

  constructor() {
    this.element = document.createElement("div");
    this.element.className = "coin";

    const optimizedConfig = getOptimizedConfig();
    this.size =
      Math.random() * (optimizedConfig.maxSize - optimizedConfig.minSize) +
      optimizedConfig.minSize;
    this.x = Math.random() * windowWidth;
    this.y = -this.size;
    this.speed =
      Math.random() * (optimizedConfig.maxSpeed - optimizedConfig.minSpeed) +
      optimizedConfig.minSpeed;
    this.rotation = 0;
    this.rotationSpeed =
      Math.random() *
        (optimizedConfig.maxRotationSpeed - optimizedConfig.minRotationSpeed) +
      optimizedConfig.minRotationSpeed;
    this.element.style.width = `${this.size}px`;
    this.element.style.height = `${this.size}px`;
    this.element.style.backgroundImage = `url('${coinGif}')`;
    this.element.style.backgroundSize = "contain";
    this.element.style.backgroundRepeat = "no-repeat";
    this.element.style.position = "absolute";
    this.element.style.pointerEvents = "none";
    this.opacity = 1;
    this.fadeDelay = Math.random() * 0.2; // 随机延迟淡出开始时间，增加自然感

    // 将金币添加到金币雨容器中
    const rainContainer = document.querySelector(".coin-rain-container");
    if (rainContainer) {
      rainContainer.appendChild(this.element);
    }

    this.update();
  }

  update(): boolean {
    this.y += this.speed;
    this.rotation += this.rotationSpeed;

    // 计算下落距离的比例
    const fallRatio = this.y / windowHeight;

    // 根据下落距离计算透明度，加入随机延迟
    if (fallRatio >= coinConfig.fadeStartDistance + this.fadeDelay) {
      // 使用缓动函数使淡出更加自然
      const fadeProgress =
        (fallRatio - (coinConfig.fadeStartDistance + this.fadeDelay)) /
        (coinConfig.fadeEndDistance - coinConfig.fadeStartDistance);

      // 使用缓动函数，让淡出更加平滑
      this.opacity = 1 - easeOutCubic(Math.min(1, fadeProgress));
      this.element.style.opacity = Math.max(0, this.opacity).toString();

      // 随着淡出，稍微缩小金币
      const scale = 1 - 0.3 * fadeProgress;
      this.element.style.transform = `translate(${this.x}px, ${this.y}px) rotate(${this.rotation}deg) scale(${scale})`;
    } else {
      this.element.style.transform = `translate(${this.x}px, ${this.y}px) rotate(${this.rotation}deg)`;
    }

    // 超出屏幕底部或完全透明时移除
    if (this.y > windowHeight + this.size || this.opacity <= 0.05) {
      this.remove();
      return false;
    }
    return true;
  }

  remove() {
    if (this.element && this.element.parentNode) {
      this.element.parentNode.removeChild(this.element);
      const index = coins.indexOf(this);
      if (index > -1) {
        coins.splice(index, 1);
      }
    }
  }
}

// 缓动函数，使动画更加自然
function easeOutCubic(x: number): number {
  return 1 - Math.pow(1 - x, 3);
}

// 开始金币雨
function startCoinRain() {
  if (isRaining) return;
  isRaining = true;

  const optimizedConfig = getOptimizedConfig();
  let lastSpawnTime = 0;

  function animate(timestamp: number) {
    // 生成新金币
    if (
      timestamp - lastSpawnTime > optimizedConfig.spawnInterval &&
      coins.length < optimizedConfig.maxCoins
    ) {
      // 移动端减少一次性生成的金币数量
      const spawnCount = isMobile()
        ? Math.floor(Math.random() * 2) + 1
        : Math.floor(Math.random() * 3) + 1;
      for (let i = 0; i < spawnCount; i++) {
        if (coins.length < optimizedConfig.maxCoins) {
          coins.push(new Coin());
        }
      }
      lastSpawnTime = timestamp;
    }

    // 批量更新金币，倒序遍历便于移除
    for (let i = coins.length - 1; i >= 0; i--) {
      if (!coins[i].update()) {
        // 已在 coin.remove() 里移除
      }
    }

    // 继续动画
    if (isRaining) {
      animationId = requestAnimationFrame(animate);
    }
  }

  // 使用nextTick确保DOM已更新
  nextTick(() => {
    animationId = requestAnimationFrame(animate);

    // 移动端缩短金币雨持续时间
    const rainDuration = isMobile() ? 3000 : 4000;
    if (rainTimer) {
      clearTimeout(rainTimer);
    }
    rainTimer = window.setTimeout(stopCoinRain, rainDuration);
  });
}

// 停止金币雨
function stopCoinRain() {
  isRaining = false;
  if (animationId) {
    cancelAnimationFrame(animationId);
    animationId = null;
  }

  // 获取所有金币元素
  const coinElements = document.querySelectorAll(".coin");

  // 添加淡出效果
  coinElements.forEach((element) => {
    element.classList.add("fade-out");
    // 只在淡出时添加 transition，提升性能
    (element as HTMLElement).style.transition = "opacity 0.5s ease-out";
  });

  // 等待淡出动画完成后移除元素
  setTimeout(() => {
    coinElements.forEach((element) => {
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }
    });
    // 清空金币数组
    coins = [];
    // 触发完成事件
    emit("finish");
  }, 500); // 与CSS中的过渡时间相匹配
}

// 窗口大小改变时更新尺寸
function handleResize() {
  windowWidth = window.innerWidth;
  windowHeight = window.innerHeight;
}

// 监听show属性变化
watch(
  () => props.show,
  (newShow) => {
    if (newShow) {
      // 重置步骤并开始
      currentStep.value = 0;
    } else {
      // 停止金币雨并重置
      stopCoinRain();
      currentStep.value = 0;
    }
  }
);

// 组件挂载时添加事件监听
onMounted(() => {
  window.addEventListener("resize", handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  stopCoinRain();
  if (rainTimer) {
    clearTimeout(rainTimer);
    rainTimer = null;
  }
  // 彻底清理所有金币 DOM
  const coinElements = document.querySelectorAll(".coin");
  coinElements.forEach((element) => {
    if (element.parentNode) {
      element.parentNode.removeChild(element);
    }
  });
  coins = [];
});
</script>

<style scoped>
.coin-effect-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99999;
  pointer-events: none;
  overflow: hidden;
}

.coin-effect-gif {
  width: 100vw;
  height: 100vh;
  max-width: none;
  max-height: none;
  object-fit: cover;
}
.coin-effect-gif2 {
  width: 50vw;
  height: 50vh;
}

.coin-rain-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 金币样式 */
:deep(.coin) {
  position: absolute;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  /* 移除 transition，淡出时动态添加 */
}

:deep(.coin.fade-out) {
  opacity: 0 !important;
}

/* 移动端优化 */
/* 移動端樣式調整 */
@media (max-width: 768px) {
  .coin-effect-gif2 {
    width: 100vw;
    height: 100vh;
  }
  .coin-effect-gif1 {
    margin-left: 30%;
  }
  :deep(.coin) {
    /* 移动端减少过渡效果，提升性能 */
    transition: opacity 0.3s ease-out;
    /* 减少GPU负担 */
    will-change: transform, opacity;
  }

  .coin-rain-container {
    /* 移动端优化渲染 */
    transform: translateZ(0);
    backface-visibility: hidden;
  }
}
</style>
