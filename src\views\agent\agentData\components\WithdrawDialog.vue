<template>
  <el-dialog
    :model-value="visible"
    title="提现"
    width="500px"
    @close="handleClose"
  >
    <div>
      <p>账户总额：R${{ accountBalance ?? 0 }}</p>
      <p>可提现：R${{ withdrawableAmount }}</p>

      <el-form
        ref="withdrawFormRef"
        :model="withdrawForm"
        :rules="rules"
        label-width="100px"
        style="margin-top: 20px;"
      >
        <el-form-item label="提现金额" prop="amount">
          <el-input
            v-model="withdrawForm.amount"
            placeholder="请输入提现金额"
            :max="withdrawableAmount"
            type="number"
            @input="handleAmountInput"
          />
        </el-form-item>
        <el-form-item label="选择类型" prop="withdrawal_method">
          <el-select
            v-model="withdrawForm.withdrawal_method"
            placeholder="请选择提现方式"
            @change="handleMethodChange"
          >
            <el-option
              v-for="method in availableMethods"
              :key="method.code"
              :label="method.name"
              :value="method.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="账号" prop="account_number">
          <el-select
            v-model="withdrawForm.account_number"
            placeholder="请选择提现账号"
            @change="handleAccountChange"
          >
            <el-option
              v-for="account in availableAccounts"
              :key="account.id"
              :label="account.accountNumber"
              :value="account.id"
            />
          </el-select>
          <el-button
            type="primary"
            link
            @click="handleAddAccount"
            style="margin-left: 10px;"
          >
            添加账号
          </el-button>
        </el-form-item>
        <el-form-item label="姓名" prop="account_name">
          <el-input v-model="withdrawForm.account_name" placeholder="请输入姓名" disabled />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="withdrawForm.phone" placeholder="请输入手机号" disabled />
        </el-form-item>
        <el-form-item label="验证码" prop="verification_code">
          <el-input
            v-model="withdrawForm.verification_code"
            placeholder="请输入验证码"
            style="width: calc(100% - 120px); margin-right: 10px;"
            disabled
          />
          <el-button @click="handleVerifyCode" :disabled="isVerifying">
            {{ isVerifying ? '获取中...' : '获取验证码' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave">
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import { ElDialog, ElButton, ElForm, ElFormItem, ElInput, ElSelect, ElOption, ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { agentWithdrawalAccounts, agentWithdrawalAccountsTypes,agentWithdrawalCreate,agentSendSms } from '@/service/api/agent';

const router = useRouter();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  accountBalance: {
    type: Number,
    default: 0
  },
  commissionRate: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['update:visible', 'save']);

const withdrawFormRef = ref();
const withdrawForm = ref({
  amount: '',
  withdrawal_method: '',
  account_type: '',
  account_number: '',
  account_name: '',
  phone: '',
  verification_code: '',
});

// 可提现金额计算
const withdrawableAmount = computed(() => {
  if (props.accountBalance <= 0) return 0;
  return props.accountBalance * (1 - props.commissionRate);
});

// 定义提现方式接口
interface WithdrawalType {
  code: string;
  name: string;
}

// 可用的提现方式
const availableMethods = ref<WithdrawalType[]>([]);

// 可用的提现账号
interface WithdrawAccount {
  id: string;
  accountNumber: string;
  name: string;
  phoneNumber: string;
}

const availableAccounts = ref<WithdrawAccount[]>([]);

// 表单验证规则
const rules = {
  amount: [
    { required: true, message: '请输入提现金额', trigger: 'blur' },
    { validator: validateAmount, trigger: 'blur' }
  ],
  withdrawal_method: [
    { required: true, message: '请选择提现方式', trigger: 'change' }
  ],
  account_number: [
    { required: true, message: '请选择提现账号', trigger: 'change' }
  ],
  verification_code: [
    { required: true, message: '请输入验证码', trigger: 'blur' }
  ]
};

// 金额验证
function validateAmount(rule: any, value: any, callback: any) {
  if (!value) {
    callback(new Error('请输入提现金额'));
  } else if (value > withdrawableAmount.value) {
    callback(new Error('提现金额不能超过可提现金额'));
  } else {
    callback();
  }
}

// 处理金额输入
function handleAmountInput(value: string) {
  if (Number(value) > withdrawableAmount.value) {
    withdrawForm.value.amount = withdrawableAmount.value.toString();
  }
}

// 获取提现方式列表
async function fetchWithdrawalTypes() {
  try {
    const response = await agentWithdrawalAccountsTypes({});
    console.log(response)
    availableMethods.value = response.data.data;
  } catch (error) {
    ElMessage.error('获取提现方式失败');
  }
}

// 获取提现账号列表
async function fetchWithdrawalAccounts(typeId: string) {
  try {
    const response = await agentWithdrawalAccounts({ account_type:typeId});
    console.log("agentWithdrawalAccounts",response)
    availableAccounts.value = response.data.data.map((account: any) => ({
      id: account.id,
      accountNumber: account.account_number,
      name: account.name,
      phoneNumber: account.phone_number
    }));
  } catch (error) {
    ElMessage.error('获取提现账号失败');
  }
}

// 处理提现方式变更
async function handleMethodChange(value: string) {
  withdrawForm.value.withdrawal_method = value;
  withdrawForm.value.account_type = value;
  await fetchWithdrawalAccounts(value);
  // 清空已选择的账号相关信息
  withdrawForm.value.account_number = '';
  withdrawForm.value.account_name = '';
  withdrawForm.value.phone = '';
}

// 处理账号变更
function handleAccountChange(value: string) {
  const selectedAccount = availableAccounts.value.find(acc => acc.id === value);
  if (selectedAccount) {
    withdrawForm.value.account_number = selectedAccount.accountNumber;
    withdrawForm.value.account_name = selectedAccount.name;
    withdrawForm.value.phone = selectedAccount.phoneNumber;
  }
}

// 处理添加账号
function handleAddAccount() {
  router.push('/agent/merchantmanagement');
}

const isVerifying = ref(false);

// 处理验证码获取
async function handleVerifyCode() {
  if (!withdrawForm.value.phone) {
    ElMessage.warning('请先选择提现账号');
    return;
  }

  try {
    isVerifying.value = true;
    const response = await agentSendSms({
      phone: withdrawForm.value.phone
    });

    if (response.data.code === 0) {
      ElMessage.success('验证码已发送');
      // 自动填入验证码
      withdrawForm.value.verification_code = response.data.data.verification_code;
    } else {
      ElMessage.error(response.data.message || '验证码发送失败');
    }
  } catch (error) {
    ElMessage.error('验证码发送失败');
  } finally {
    isVerifying.value = false;
  }
}

watch(() => props.visible, (newVal) => {
  if (newVal) {
    withdrawFormRef.value?.resetFields();
  }
});

const handleClose = () => {
  emit('update:visible', false);
};

const handleSave = async () => {
  if (!withdrawFormRef.value) return;

  try {
    await withdrawFormRef.value.validate();

    // 调用提现创建API
    const response = await agentWithdrawalCreate({
      amount: Number(withdrawForm.value.amount),
      withdrawal_method: withdrawForm.value.withdrawal_method,
      account_type: withdrawForm.value.account_type,
      account_number: withdrawForm.value.account_number,
      account_name: withdrawForm.value.account_name,
      phone: withdrawForm.value.phone,
      verification_code: withdrawForm.value.verification_code
    });

    if (response.data.code === 0) {
      ElMessage.success('提现申请成功');
      emit('save', withdrawForm.value);
      emit('update:visible', false);
    } else {
      ElMessage.error(response.data.message || '提现申请失败');
    }
  } catch (error) {
    ElMessage.error('请检查表单填写是否完整');
  }
};

// 在组件挂载时获取提现方式列表
onMounted(() => {
  fetchWithdrawalTypes();
  // fetchWithdrawalAccounts(undefined)
});
</script>

<style scoped>
.el-form .el-input, .el-form .el-select {
  width: 100%;
}
</style>
