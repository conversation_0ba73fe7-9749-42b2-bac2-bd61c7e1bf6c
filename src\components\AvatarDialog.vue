<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  modelValue: boolean,
  currentAvatar: string,
  currentNickname: string,
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'select', avatar: string): void
}>()

// 昵称
const nickname = ref(props.currentNickname)

// 头像列表
const avatars = [
    { id: 1, url: 'https://images.box777bet.com/avatar/1.png' },
    { id: 2, url: 'https://images.box777bet.com/avatar/2.png' },
    { id: 3, url: 'https://images.box777bet.com/avatar/3.png' },
    { id: 4, url: 'https://images.box777bet.com/avatar/4.png' },
    { id: 5, url: 'https://images.box777bet.com/avatar/5.png' },
    { id: 6, url: 'https://images.box777bet.com/avatar/6.png' },
    { id: 7, url: 'https://images.box777bet.com/avatar/7.png' },
    { id: 8, url: 'https://images.box777bet.com/avatar/8.png' },
    { id: 9, url: 'https://images.box777bet.com/avatar/9.png' },
    { id: 10, url: 'https://images.box777bet.com/avatar/10.png' },
    { id: 11, url: 'https://images.box777bet.com/avatar/11.png' },
    { id: 12, url: 'https://images.box777bet.com/avatar/12.png' },
    { id: 13, url: 'https://images.box777bet.com/avatar/13.png' },
    { id: 14, url: 'https://images.box777bet.com/avatar/14.png' },
    { id: 15, url: 'https://images.box777bet.com/avatar/15.png' },
    { id: 16, url: 'https://images.box777bet.com/avatar/16.png' },
    { id: 17, url: 'https://images.box777bet.com/avatar/17.png' },
    { id: 18, url: 'https://images.box777bet.com/avatar/18.png' },
    { id: 19, url: 'https://images.box777bet.com/avatar/19.png' },
    { id: 20, url: 'https://images.box777bet.com/avatar/20.png' },
    
]

// 选择的头像
const selectedAvatar = ref(props.currentAvatar)

// 处理头像选择
const handleSelect = (avatar: string) => {
  selectedAvatar.value = avatar
}

// 保存选择
const handleSave = () => {
  emit('update', {
    avatar: selectedAvatar.value,
    nickname: nickname.value
  })
}
</script>

<template>
  <v-dialog
    :model-value="modelValue"
    @update:model-value="emit('update:modelValue', $event)"
    max-width="360"
    class="avatar-dialog"
    @close="emit('update:modelValue', $event)"
  >
    <v-card class="avatar-card">
      <div class="dialog-header">
        <div class="title">Alterar apelido favorito</div>
        <v-btn icon="mdi-close" variant="text" @click="emit('update:modelValue', false)" class="close-btn" />
      </div>

      <div class="nickname-input">
        <v-text-field
          v-model="nickname"
          variant="outlined"
          hide-details
          class="mb-4"
        />
      </div>

      <div class="avatar-grid">
        <div class="subtitle mb-2">Modificações</div>
        <div class="grid-container">
          <div
            v-for="avatar in avatars"
            :key="avatar.id"
            class="avatar-item"
            :class="{ 'selected': selectedAvatar === avatar.url }"
            @click="handleSelect(avatar.url)"
          >
            <v-avatar size="60">
              <v-img :src="avatar.url" />
            </v-avatar>
          </div>
        </div>
      </div>

      <v-btn
        block
        color="#FFDF00"
        class="save-btn"
        @click="handleSave"
      >
        Salvar
      </v-btn>
    </v-card>
  </v-dialog>
</template>

<style scoped lang="scss">
.avatar-dialog {
  :deep(.v-card) {
    background: #1B2550;
    border-radius: 12px;
    padding: 20px;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .title {
    color: #fff;
    font-size: 18px;
    font-weight: 500;
  }

  .close-btn {
    color: #fff;
  }
}

.nickname-input {
  :deep(.v-field) {
    border-radius: 8px;
    background: #202D60;
    
    input {
      color: #fff;
    }
  }
}

.avatar-grid {
  margin-bottom: 20px;

  .subtitle {
    color: #fff;
    font-size: 16px;
  }

  .grid-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-top: 10px;
  }

  .avatar-item {
    cursor: pointer;
    transition: all 0.3s ease;

    &.selected {
      :deep(img){
        border-radius: 50%;
        border: 2px solid #FF5989;
      }
    }

    &:hover {
      transform: scale(1.05);
    }

    .v-avatar {
      border: 2px solid transparent;
    }
  }
}

.save-btn {
  height: 44px;
  font-size: 16px;
  border-radius: 22px;
  text-transform: none;
}
</style> 