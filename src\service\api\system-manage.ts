import { de } from 'element-plus/es/locale';
import { request } from '../request';

/** get user list */
export function fetchGetUserList(params?: Api.SystemManage.UserSearchParams) {
  return request<Api.SystemManage.UserList>({
    url: '/backend/admin/adminList',
    method: 'get',
    params
  });
}

/** add user */
export function fetchAddUser(data?: any) {
  return request({
    url: '/backend/admin/adminAddBackend',
    method: 'post',
    data
  });
}

/** delete user */
export function fetchDeleteUser(data?: any) {
  return request({
    url: '/backend/admin/adminDelBackend',
    method: 'post',
    data
  });
}

/** delete more user */
export function fetchBatchDeleteUser(data?: any) {
  return request({
    url: '/backend/admin/adminDeleteMoreBackend',
    method: 'post',
    data
  });
}

/** update user */
export function fetchUpdateUser(data?: any) {
  return request({
    url: '/backend/admin/adminUpdateBackend',
    method: 'post',
    data
  });
}

/** get menu list */
export function fetchGetMenuList() {
  return request<Api.SystemManage.MenuList>({
    url: '/systemManage/getMenuList/v2',
    method: 'get'
  });
}

/** get all pages */
export function fetchGetAllPages() {
  return request<string[]>({
    url: '/systemManage/getAllPages',
    method: 'get'
  });
}

/** get menu tree */
export function fetchGetMenuTree() {
  return request<Api.SystemManage.MenuTree[]>({
    url: '/systemManage/getMenuTree',
    method: 'get'
  });
}

/** 获取所有问答 */
export function fetchGetAllKeywords(params?: Api.SystemManage.FAQSearchParams) {
  return request<Api.SystemManage.FAQList>({
    url: '/backend/admin/getAllKeywords',
    method: 'get',
    params
  });
}

/** 新增问答 */
export function fetchAddKeyword(data: Api.SystemManage.FAQAddParams) {
  return request({
    url: '/backend/admin/addKeywordHandler',
    method: 'post',
    data
  });
}

/** 更新问答 */
export function fetchUpdateKeyword(data: Api.SystemManage.FAQUpdateParams) {
  return request({
    url: '/backend/admin/updateKeyword',
    method: 'post',
    data
  });
}

/** 删除问答 */
export function fetchDeleteKeyword(id: number) {
  return request({
    url: '/backend/admin/deleteKeyword',
    method: 'post',
    data: { id }
  });
}
