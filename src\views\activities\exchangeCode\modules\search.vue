<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-03 17:43:12
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-06-26 09:38:44
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\views\activities\exchangeCode\modules\search.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import { ref } from "vue";
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElDatePicker,
} from "element-plus";

interface SearchModel {
  start_time?: string;
  end_time?: string;
}

interface Props {
  model: SearchModel;
}

interface Emits {
  (e: "update:model", value: SearchModel): void;
  (e: "search"): void;
  (e: "reset"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const dateRange = ref<[string, string]>(["", ""]);
const handleSearch = () => {
  emit("search");
};

const handleReset = () => {
  dateRange.value = ["", ""];
  emit("reset");
};
</script>

<template>
  <div class="search-wrapper">
    <ElForm inline>
      <ElRow :gutter="16">
        <ElCol :span="10">
          <ElFormItem label="时间范围">
            <ElDatePicker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="x"
              @change="
                (val: [string, string] | null) => {
                  if (val) {
                    model.start_time = val[0];
                    model.end_time = val[1];
                  } else {
                    model.start_time = undefined;
                    model.end_time = undefined;
                  }
                }
              "
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="10">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<style lang="scss" scoped>
.search-card {
  :deep(.el-form--inline) {
    .el-form-item {
      margin-right: 16px;
      margin-bottom: 16px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
