<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-09 19:09:53
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-12 10:34:21
 * @FilePath: \betdoce-web\src\views\Mobile\ActivityMenu\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <div class="page-activities">
        <img class="img-list" src="@/assets/images/h5/activity1.png" alt="" @click="router.push('/activity/Atividades-Pinduoduo')">
        <img class="img-list" src="@/assets/images/h5/activity2.png" alt="" @click="router.push('/aposta')">
        <img class="img-list" src="@/assets/images/h5/activity3.png" alt="" @click="router.push('/connection')">
    </div>
</template>

<script setup>
import { useRouter } from "vue-router";
const router = useRouter();
</script>

<style scoped lang="scss"> 
.page-activities{
    padding: 20px;

    .img-list{
        width: 100%;
        cursor: pointer;
        transition: transform 0.2s ease;
        
        &:active {
            transform: scale(0.98);
        }
    }
}
</style>