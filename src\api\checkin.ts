import request from './request'

// 定义接口返回的数据类型
export interface CheckinRewardRule {
  cost: number
  day: number
}

export interface VipLevelRule {
  name: string
  data: CheckinRewardRule[]
}

export interface CheckinInfo {
  user_id: number
  current_streak: number
  last_checkin: string
  vip_level: number
  reward_rules: VipLevelRule[]
}

export interface CheckinResponse {
  status_code: number
  data: CheckinInfo
}

// 获取签到前相关信息
export const getCheckinInfo = (userId:string) => {
  return request.Get<CheckinResponse>('/checkin/info',{params:{user_id: userId,_t:Date.now()}})
}

export interface CheckinRankItem {
  user_id: number
  checkin_date: number
  streak: number
  reward: number
  reward_claimed: boolean
  created_at: number
  updated_at: number
  uuid: number
  nickname: string
  username: string
  phone: string
  email: string
  CPF: string
  pix: string
  accountType: number
  customerName: string
  merchantUserId: string
  avatar: string
  gender: number
  reg_ip: string
  level: number
  invite_code: string
  source: number
  father_id: number
  is_recharge: number
  is_black: number
  is_login: number
  remake: string
  created_by: string
  updated_by: string
}

export interface CheckinRankResponse {
  status_code: number
  data: CheckinRankItem[]
}

/**
 * 获取签到排行榜
 * @param page 页码
 * @param size 每页数量
 */
export const getCheckinRank = (page: number = 1, size: number = 20) => {
  return request.Get<CheckinRankResponse>('/checkin/rank', {
    params: { size }
  })
} 