# WebSocket连接保持功能

## 概述

本项目实现了一个完整的WebSocket连接保持系统，确保在localStorage中的token有效且store.state.auth.user有值时，整个项目的WebSocket保持正常连接，心跳正常发送。

## 核心组件

### 1. WebSocketService (`src/utils/websocket.ts`)
- 基础的WebSocket连接管理
- 自动重连机制
- 心跳检测和发送
- 消息处理和事件监听
- Token过期处理

### 2. NetworkMonitor (`src/utils/networkMonitor.ts`)
- 网络状态监控
- 页面可见性检测
- WebSocket连接质量检查
- 自动重连触发

### 3. WebSocketKeeper (`src/utils/websocketKeeper.ts`)
- **核心连接保持器**
- 定期检查token和用户信息
- 自动维护WebSocket连接
- 心跳状态监控和修复

### 4. WebSocketStatus (`src/components/WebSocketStatus.vue`)
- 开发时的状态监控组件
- 实时显示连接状态
- 手动操作接口

## 工作原理

### 连接保持逻辑

1. **条件检查**：
   - localStorage中存在有效token
   - store.state.auth.user.uuid存在
   - 网络连接正常

2. **状态监控**：
   - 每30秒检查一次WebSocket状态
   - 监控心跳健康状况
   - 检测token和用户信息变化

3. **自动修复**：
   - 连接断开时自动重连
   - 心跳异常时重启心跳机制
   - token或用户变化时强制重连

### 启动流程

```javascript
// App.vue中的初始化
onMounted(async() => {
  // 1. 初始化系统
  initializeSystem();
  
  // 2. 初始化WebSocket服务
  initWebSocket();
  
  // 3. 启动网络监控
  networkMonitor.startMonitoring();
  
  // 4. 启动WebSocket连接保持器
  websocketKeeper.start();
});
```

### 监听机制

```javascript
// 监听用户状态变化
watch(
  () => store.state.auth.user?.uuid,
  (user) => {
    if (user || localStorage.getItem("token")) {
      // 有token时调用websocketToken
      websocketToken();
    } else {
      // 用户登出时重置WebSocket登录状态
      websocketService.disconnect();
      WebSocketStatus.updateStatus()
    }
  },
  { immediate: true }
);
```

## 配置选项

### WebSocketKeeper配置
```javascript
// 检查间隔（默认30秒）
websocketKeeper.setCheckInterval(30000);

// 手动启动/停止
websocketKeeper.start();
websocketKeeper.stop();

// 手动检查
await websocketKeeper.manualCheck();
```

### WebSocketService配置
```javascript
// 心跳间隔（默认30秒）
private heartbeatInterval = 30000;

// 心跳超时（默认60秒）
private heartbeatTimeout = 60000;

// 最大重连次数（默认5次）
private maxReconnectAttempts = 5;
```

## 状态监控

### 开发环境监控
在开发环境中，页面右上角会显示WebSocket状态监控组件，包括：
- 连接状态
- 心跳状态
- Token状态
- 用户信息状态
- 保持器状态

### 手动操作
状态监控组件提供以下操作：
- 手动检查
- 强制重连
- 启动/停止保持器

## 测试页面

访问 `/websocket-test` 页面可以进行详细的WebSocket功能测试，包括：
- 实时状态显示
- 手动操作按钮
- 详细日志记录

## 最佳实践

### 1. 确保条件满足
```javascript
// 确保token有效
const token = getToken();
if (!token) {
  console.log('Token无效，WebSocket不会连接');
  return;
}

// 确保用户信息存在
const userInfo = store.state.auth.user;
if (!userInfo?.uuid) {
  console.log('用户信息不完整，WebSocket不会连接');
  return;
}
```

### 2. 监听连接状态
```javascript
// 监听连接事件
websocketService.onConnect(() => {
  console.log('WebSocket已连接');
});

// 监听token过期事件
websocketService.onTokenExpired((event) => {
  console.log('Token已过期:', event.detail.message);
  // 处理token过期逻辑
});
```

### 3. 网络状态处理
```javascript
// 监听网络状态变化
watch(
  () => networkMonitor.networkStatus.value,
  (newStatus, oldStatus) => {
    if (oldStatus === NetworkStatus.OFFLINE && newStatus === NetworkStatus.ONLINE) {
      console.log('网络恢复，重新发送WebSocket登录消息');
      // 重新建立连接
    }
  }
);
```

## 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查token是否有效
   - 检查用户信息是否完整
   - 检查网络连接状态

2. **心跳异常**
   - 查看浏览器控制台日志
   - 检查服务器心跳响应
   - 确认心跳间隔设置

3. **频繁重连**
   - 检查token是否频繁变化
   - 确认网络稳定性
   - 调整重连间隔

### 调试方法

1. **启用状态监控**
```javascript
// 在localStorage中设置
localStorage.setItem('showWebSocketStatus', 'true');
```

2. **查看详细日志**
```javascript
// 在控制台中查看WebSocket相关日志
// 所有日志都以"WebSocket"开头
```

3. **使用测试页面**
```javascript
// 访问测试页面进行详细测试
router.push('/websocket-test');
```

## 性能考虑

- 检查间隔设置为30秒，平衡了及时性和性能
- 只在页面可见时进行检查，避免后台资源消耗
- 使用防抖机制避免频繁操作
- 自动清理定时器和事件监听器

## 安全考虑

- Token验证在每次连接时进行
- 自动处理token过期情况
- 敏感信息不在日志中输出
- 连接失败时有重试限制
