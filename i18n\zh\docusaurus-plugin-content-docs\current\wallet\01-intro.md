---
sidebar_position: 1
---

# UniPass Wallet 是什么？

UniPass Wallet 是一个基于 [UniPass Contract](../contract/01-intro.md) 开发的低门槛的支持邮件社交恢复的智能合约钱包解决方案。通过 UniPass Wallet，开发者可以在产品内提供流畅的免私钥、免 gas 的用户体验，从而快速地吸引海量的 Web2 用户。

UniPass 能够帮助用户优雅地管理他们的私钥，而无需助记词等 Web2 用户陌生且经常错误使用的工具。UniPass 致力于帮助更多人进入和使用 Web3 产品，拥有完整的去中心化身份，并且在抗审查上不做任何妥协。通过我们首创的邮件社交恢复方案，用户在创建账户时无需保存助记词。当密钥损毁或丢失时，用户可以通过选定作为守护者的多位互联网用户，通过向链上合约提交账户恢复邮件的方式，找回他们的账户，实现零门槛的社交恢复。

UniPass Wallet 支持任意代币支付手续费，可兼容所有主流 EVM 链，并将覆盖网页版、移动端、浏览器插件全平台，且支持多种调用方式。UniPass Wallet 兼容最新的 ERC-4337 账户抽象协议，是领先的智能合约钱包解决方案。

## UniPass Wallet 的特色

**`免私钥`**

用户使用邮箱和密码注册、登录账户，无需接触助记词、私钥。

**`抗审查`**

用户完全掌握账户的控制权，无需依赖第三方服务。

**`免 Gas`**

支持使用任意链的任意资产支付交易手续费。

**`邮件恢复`**

用户可以通过守护者邮箱向链上合约提交账户恢复邮件，找回账户。

**`隐私保护`**

在链上采用零知识证明技术实现邮件脱敏验证，保护用户的邮箱隐私。

**`多平台`**

用户端覆盖网页版、移动端、浏览器插件全平台，支持多种调用方式。

**`支持多链`**

可以兼容所有 EVM 链，并且保证多链地址一致。

## 尝试钱包

尝试钱包 —> [**https://wallet.unipass.id/**](https://wallet.unipass.id/)
