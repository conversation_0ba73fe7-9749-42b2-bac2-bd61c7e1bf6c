<template>
  <div class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto">
    <AccountSearch v-model:model="searchParams" @reset="
      () => {
        resetSearchParams();
      }
    " @search="
        () => {
          if (!searchParams.sms_channel) {
            searchParams.sms_channel = undefined;
          }
          getDataByPage();
        }
      ">
      <template #table-operation> </template>
    </AccountSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="flex justify-end mb-10">
        <TableHeaderOperation v-model:columns="columnChecks" :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading" @add="handleAdd" @delete="handleBatchDelete" @refresh="getData"><span
            style="width: 1px; height: 35px; background: #e5e6eb"></span></TableHeaderOperation>
      </div>

      <div class="h-[calc(100%-130px)]">
        <ElTable v-loading="loading" height="100%" class="sm:h-full" :data="data" row-key="id"
          @selection-change="checkedRowKeys = $event.map((row) => row.id)">
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination v-if="mobilePagination.total" layout="total,prev,pager,next,sizes" v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']" @size-change="mobilePagination['size-change']" />
      </div>
      <AccountOperateDrawer v-model:visible="drawerVisible" :operate-type="operateType" :row-data="editingData"
        @submitted="getData" />
    </ElCard>
  </div>
</template>

<script setup lang="tsx">
import { tagMap, statusOptions } from "@/constants/common";
import { ElButton, ElPopconfirm, ElTag, ElSwitch, ElInput } from "element-plus";
import { useTable, useTableOperate } from "@/hooks/common/table";
import AccountSearch from "./modules/account-search.vue";
import AccountOperateDrawer from "./modules/account-operate-drawer.vue";
import {
  fetchGetAccountList,
  fetchUpdateAccount,
  fetchBlackAccount,
  fetchUpdateCashWithdrawableFromBetVolume,
} from "@/service/api/account";
import { useAuth } from "@/hooks/business/auth";
import { useI18n } from "vue-i18n";
import { formatNumber, getBrazilDate } from "@/utils/format";
import { ref } from "vue";
import moment from "moment";

const { t } = useI18n();
const { hasAuth } = useAuth();

defineOptions({ name: "AccountManage" });

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  columnChecks,
} = useTable({
  apiFn: fetchGetAccountList,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 20,
    father_id: undefined,
    sms_channel: undefined,
    phone: undefined,
    email: undefined,
    nickname: undefined,
    is_black: undefined,
    level: undefined,
    uuid: undefined,
  },
  columns: () => [
    { prop: "index", label: "序号", width: 64 },
    { prop: "uuid", label: "用户ID", width: 100 },
    { prop: "phone", label: "手机号", width: 120 },
    { prop: "invite_code", label: "邀请码", minWidth: 140 },
    {
      prop: "sms_channel",
      label: "注册方式",
      width: 120,
      formatter: (row: any) => {
        const map: Record<string | number, string> = {
          indiahm_sms: "telefone",
          waotp: "WhatsApp",
        };
        return map[row.sms_channel] || "-";
      },
    },
    {
      prop: "total_balance",
      label: "余额",
      width: 120,
      formatter: (row: any) => (
        <span>{formatNumber((row.total_balance || 0) / 100)}</span>
      ),
    },
    {
      prop: "digital_balance",
      label: "赠金",
      width: 120,
      formatter: (row: any) => (
        <span>{formatNumber((row.digital_balance || 0) / 100)}</span>
      ),
    },
    {
      prop: "cash_balance",
      label: "现金",
      width: 120,
      formatter: (row: any) => (
        <span>{formatNumber((row.cash_balance || 0) / 100)}</span>
      ),
    },
    {
      prop: "cash_withdrawable_from_bet_volume",
      label: "打码提现",
      width: 120,
      formatter: (row: any) => (
        <span>
          {formatNumber((row.cash_withdrawable_from_bet_volume || 0) / 100)}
        </span>
      ),
    },
    // 新增可提现金额列（可编辑）
    {
      prop: "user_with_amount",
      label: "可提现金额",
      width: 120,
      formatter: (row: any) => {
        return editingRowId.value === row.id ? (
          <ElInput
            size="small"
            style="width: 90px"
            v-model={editingValue.value}
            onBlur={() => saveWithdrawable(row)}
            onKeydown={(e: any) => {
              if (e.key === "Enter") {
                saveWithdrawable(row);
              }
            }}
            autofocus
          />
        ) : (
          <span
            style="cursor:pointer;color:#409EFF"
            onClick={() => startEdit(row)}
          >
            {formatNumber((row.user_with_amount || 0) / 100)}
          </span>
        );
      },
    },
    // 新增打码金额列（只读）
    {
      prop: "turnover",
      label: "打码量",
      width: 120,
      formatter: (row: any) => (
        <span>{formatNumber((row.turnover || 0) / 100)}</span>
      ),
    },
    {
      prop: "total_deposit",
      label: "充值金额",
      width: 120,
      formatter: (row: any) => (
        <span>{formatNumber((row.total_deposit || 0) / 100)}</span>
      ),
    },
    {
      prop: "total_withdrawal",
      label: "提现金额",
      width: 120,
      formatter: (row: any) => (
        <span>{formatNumber((row.total_withdrawal || 0) / 100)}</span>
      ),
    },
    {
      prop: "deposit_difference",
      label: "充值差",
      width: 120,
      formatter: (row: any) => {
        const diff = (row.deposit_difference || 0) / 100;
        return (
          <span class={diff >= 0 ? "text-success" : "text-danger"}>
            {formatNumber(diff)}
          </span>
        );
      },
    },
    { prop: "deposit_count", label: "充值单量", width: 100 },
    { prop: "withdrawal_count", label: "提现单量", width: 100 },
    { prop: "email", label: "邮箱", minWidth: 150 },
    { prop: "nickname", label: "昵称", minWidth: 120 },
    { prop: "father_uuid", label: "父级ID", width: 100 },
    {
      prop: "reg_ip",
      label: "注册IP",
      minWidth: 200,
      formatter: (row: any) => (
        <span>
          {row.reg_ip}
          {row.same_ip_user_count > 1 && (
            <ElTag size="small" type="warning" class="ml-1">
              {row.same_ip_user_count}人
            </ElTag>
          )}
        </span>
      ),
    },
    { prop: "level", label: "等级", width: 80 },
    {
      prop: "is_black",
      label: "是否拉黑",
      width: 100,
      formatter: (row: any) => (
        <ElTag type={row.is_black ? "danger" : "success"}>
          {row.is_black ? "已拉黑" : "正常"}
        </ElTag>
      ),
    },
    // {
    //   prop: "is_login",
    //   label: "登录状态",
    //   width: 100,
    //   formatter: (row: any) => (
    //     <ElTag type={row.is_login ? "success" : "info"}>
    //       {row.is_login ? "在线" : "离线"}
    //     </ElTag>
    //   ),
    // },
    {
      prop: "online_status",
      label: "登录状态",
      width: 100,
      formatter: (row: any) => {
        let text = "";
        let type: any = "info";
        switch (row.online_status) {
          case 1:
            text = "在线";
            type = "success";
            break;
          case 2:
            text = "游戏中";
            type = "warning";
            break;
          case 3:
            text = "离线";
            type = "info";
            break;
          default:
            text = "-";
            type = "info";
        }
        return <ElTag type={type}>{text}</ElTag>;
      },
    },
    { prop: "remake", label: "备注", minWidth: 140 },
    {
      prop: "created_at",
      label: "创建时间",
      width: 180,
      formatter: (row: any) => row.created_at
          ? moment(getBrazilDate(row.created_at)).format("YYYY-MM-DD HH:mm:ss")
          : "-",
    },
    {
      width: 140,
      prop: "operate",
      label: "操作",
      align: "center",
      fixed: "right",
      formatter: (row: any) => (
        <div class="flex-center gap-2">
          {hasAuth(3) && (
            <ElButton
              type="primary"
              plain
              size="small"
              onClick={() => edit(row.id)}
            >
              编辑
            </ElButton>
          )}
          {hasAuth(2) && (
            <ElButton
              type={row.is_black ? "success" : "danger"}
              plain
              size="small"
              onClick={() => handleBlack(row.id, row.is_black ? 0 : 1)}
            >
              {row.is_black ? "取消拉黑" : "拉黑"}
            </ElButton>
          )}
        </div>
      ),
    },
  ],
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  onDeleted,
  checkedRowKeys,
} = useTableOperate(data, getData, "id");

const editingRowId = ref<number | null>(null);
const editingValue = ref<string>("");

function startEdit(row: any) {
  editingRowId.value = row.id;
  editingValue.value = ((row.user_with_amount || 0) / 100).toString();
}

async function saveWithdrawable(row: any) {
  const newValue = Math.round(Number(editingValue.value) * 100);
  if (isNaN(newValue)) {
    window.$message?.warning("请输入有效数字");
    return;
  }
  if (newValue === row.user_with_amount) {
    editingRowId.value = null;
    return;
  }
  const { error } = await fetchUpdateCashWithdrawableFromBetVolume({
    user_id: row.id,
    amount: newValue,
  });
  if (!error) {
    window.$message?.success("保存成功");
    row.user_with_amount = newValue;
    getData();
  }
  editingRowId.value = null;
}

async function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.warning("请选择要删除的用户");
    return;
  }
}

async function handleBlack(id: number, isBlack: number) {
  const { error } = await fetchBlackAccount({ ids: [id], is_doblack: isBlack });
  if (!error) {
    window.$message?.success(isBlack ? "拉黑成功" : "取消拉黑成功");
    getData();
  }
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;

  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
