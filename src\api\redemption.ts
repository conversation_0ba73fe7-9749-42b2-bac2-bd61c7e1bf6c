/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-19 10:47:29
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-05-27 17:20:54
 * @FilePath: \betdoce-admind:\new_project\betdoce-web\src\api\redemption.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from './request'

export interface RedemptionRecordItem {
  id: number
  redemption_code: string
  activity_period: string
  bonus: number
  amount_type: number
  status: number // 0: Pending?, 1: Collected?
  exchange_time: string
}

export interface RedemptionRecordsResponse {
  status_code: number
  data: RedemptionRecordItem[]
  count: number
}

/**
 * 获取兑换记录
 */
export const getRedemptionRecords = (params: {page:number,size:number}) => {
  // The API endpoint might need pagination params in the future
  // For now, fetching without params based on the cURL command
  return request.Get<RedemptionRecordsResponse>('/user/reward',{params:{...params,_t:Date.now()}})
} 