<template>
  <v-dialog
    :model-value="show"
    max-width="320"
    persistent
    transition="dialog-bottom-transition"
    @update:model-value="val => $emit('update:show', val)"
  >
    <v-card class="activity-invite-modal">
      <v-card-title class="text-h6" style="color: #ff0000; text-align: center;">
        Parabéns!
      </v-card-title>
      <v-card-text style="color: #ff0000; text-align: center; font-size: 1.2rem; font-weight: 600;">
        Você ganhou R$ {{ (amount/100).toFixed(2) }}!
      </v-card-text>
      <v-card-actions class="justify-center">
        <v-btn
          color="primary"
          variant="text"
          class="join-btn"
          @click="handleClose"
        >
          Fechar
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
const props = defineProps<{
  show: boolean,
  amount: number
}>();
const emit = defineEmits(['update:show', 'close']);
function handleClose() {
  emit('update:show', false);
  emit('close');
}
</script>

<style scoped>
.activity-invite-modal {
  position: relative;
  background: linear-gradient(180deg, #fdf6c7 0%, #facc2e 100%);
  border-radius: 20px !important;
  overflow: hidden;
  .v-card-title, .v-card-text {
    color: #ff0000 !important;
    text-align: center;
  }
}
.join-btn {
  height: 56px;
  min-width: 180px;
  width: 80%;
  margin: 16px auto 8px auto;
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: none;
  border-radius: 22px;
  background: url("@/assets/images/h5/activity-button.png") no-repeat !important;
  background-size: 100% 100% !important;
  background-position: center !important;
  color: white !important;
  box-shadow: none;
  justify-content: center;
  align-items: center;
}
</style> 