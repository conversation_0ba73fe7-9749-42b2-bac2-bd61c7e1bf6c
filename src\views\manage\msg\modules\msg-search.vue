<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-16 14:17:05
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-20 14:05:18
 * @FilePath: \betdoce-admin\src\views\manage\msg\modules\msg-search.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-form :inline="true" :model="model" class="search-form">
    <el-form-item label="用户ID">
      <el-input v-model="model.uuid" placeholder="用户ID"></el-input>
    </el-form-item>
    <el-form-item label="通道选择">
      <el-select
        v-model="model.package_channel_id"
        style="width: 200px"
        placeholder="请选择"
      >
        <el-option label="全部" value=""></el-option>
        <el-option
          v-for="item in packages"
          :key="item.channel_id"
          :label="item.name"
          :value="item.channel_id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="手机号">
      <el-input v-model="model.mobile" placeholder="手机号"></el-input>
    </el-form-item>
    <el-form-item label="短信内容">
      <el-input v-model="model.content" placeholder="短信内容"></el-input>
    </el-form-item>
    <el-form-item label="IP">
      <el-input v-model="model.ip" placeholder="IP"></el-input>
    </el-form-item>
    <el-form-item label="发送时间">
      <el-date-picker
        v-model="model.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        date-format=""
        value-format="x"
      >
      </el-date-picker>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-button @click="handleReset">重置</el-button>
      <slot name="extra-operation"></slot>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";

const props = defineProps({
  model: {
    type: Object,
    required: true,
  },
  packages: {
    type: Array,
    required: true,
    default: () => [],
  },
});

const emit = defineEmits(["update:model", "search", "reset"]);

const handleSearch = () => {
  emit("search");
};

const handleReset = () => {
  emit("reset");
};
</script>

<style lang="scss" scoped>
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
}
</style>
