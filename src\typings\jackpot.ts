export interface JackpotConfig {
  id: number;
  pool_name: string;
  game_type: string;
  period_number: number;
  period_hours: number;
  initial_seed: number;
  draw_rate: number;
  min_pool_amount: number;
  current_pool_amount: number;
  decay_rate: number;
  is_decay_enabled: boolean;
  platform_comm_rate: number;
  first_place_rate: number;
  second_place_rate: number;
  third_place_rate: number;
  remaining_distribution: number;
  max_reward_rank: number;
  leaderboard_size: number;
  leaderboard_status: boolean;
  reward_status: boolean;
  reward_type: number;
  start_time: string;
  end_time: string;
  status: number;
  status_text: string;
  reward_status_text: string;
  create_by: number;
  update_by: number;
  create_at: string;
  update_at: string;
}

export interface JackpotListParams {
  page: number;
  size: number;
  sort?: string;
}

export interface JackpotListResponse {
  list: JackpotConfig[];
  total: number;
}
