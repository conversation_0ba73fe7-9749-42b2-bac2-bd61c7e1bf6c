<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-16 13:51:34
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\views\manage\source\modules\source-operate-drawer.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { enableStatusOptions } from '@/constants/business';
import { statusOptions,positionOptions } from '@/constants/common';
import {
  fetchAddCarousel,
  fetchUpdateCarousel,
  fetchGetCarouselDetail
} from '@/service/api';
import ImageUpload from '@/components/upload/ImageUpload.vue';

interface Props {
  /** the type of operation */
  operateType: UI.TableOperateType;
  /** the edit row data */
  rowData?: any | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const title = computed(() => {
  const titles: Record<UI.TableOperateType, string> = {
    add: "新增轮播图",
    edit: "编辑轮播图"
  };
  return titles[props.operateType];
});

const formRef = ref();
const formData = reactive({
  title: '',
  position: 1,
  weight: 1,
  status: 1,
  image_url: '',
  hyperlink: ''
});

const rules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  position: [{ required: true, message: '请选择位置', trigger: 'blur' }],
  weight: [{ required: true, message: '请输入权重', trigger: 'blur' }],
  image_url: [{ required: true, message: '请上传图片', trigger: 'blur' }],
  hyperlink: [{ required: true, message: '请输入链接', trigger: 'blur' }]
};

async function fetchDetail() {
  if (!props.rowData?.id) return;
  try {
    const res = await fetchGetCarouselDetail({
      id: props?.rowData?.id
    });
    if (res.data) {
      Object.assign(formData, res.data);
    }
  } catch (error) {
    window.$message?.error('获取详情失败');
  }
}

function closeDrawer(){
  visible.value = false;
}

async function handleSubmit() {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    if (props.operateType === 'edit') {
      const { error } = await fetchUpdateCarousel({
        id: props?.rowData?.id,
        ...formData
      });
      if (!error) {
        window.$message?.success("更新成功");
        closeDrawer();
        emit('submitted');
      }
    } else {
      const { error } = await fetchAddCarousel(formData);
      if (!error) {
        window.$message?.success("创建成功");
        closeDrawer();
        emit('submitted');
      }
    }
  } catch (error) {
    window.$message?.error("操作失败");
  }
}

watch(visible, async () => {
  if (visible.value) {
    if (props.operateType === 'edit') {
      // await fetchDetail();
     Object.assign(formData, props.rowData);
    } else {
      Object.assign(formData, {
        title: '',
        position: 1,
        weight: 1,
        status: 1,
        image_url: '',
        hyperlink: ''
      });
    }
  }
});
</script>

<template>
  <ElDrawer
    :size="360"
    v-model="visible"
    :title="title"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
    >
      <ElFormItem label="标题" prop="title">
        <ElInput v-model="formData.title" placeholder="请输入标题" />
      </ElFormItem>
      <ElFormItem label="位置" prop="position">
        <ElSelect v-model="formData.position" placeholder="请选择位置">
          <ElOption
            v-for="item in positionOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="权重" prop="weight">
        <ElInputNumber v-model="formData.weight" :min="1" :max="999" placeholder="请输入权重" />
      </ElFormItem>
      <ElFormItem label="图片" prop="image_url">
        <ImageUpload
          v-model="formData.image_url"
          :max-size="5"
          :show-tip="true"
          tip-text="支持 jpg、png 格式图片，大小不超过 5MB"
        />
      </ElFormItem>
      <ElFormItem label="链接" prop="hyperlink">
        <ElInput v-model="formData.hyperlink" placeholder="请输入链接" />
      </ElFormItem>
      <ElFormItem label="状态" prop="status">
        <ElRadioGroup v-model="formData.status">
          <ElRadio v-for="{ label, value } in statusOptions" :key="value" :value="value" :label="label" />
        </ElRadioGroup>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="flex justify-end gap-2">
        <ElButton @click="closeDrawer">取消</ElButton>
        <ElButton
          type="primary"
          @click="handleSubmit"
        >
          确定
        </ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<style scoped>
</style> 