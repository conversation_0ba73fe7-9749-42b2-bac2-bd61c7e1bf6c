<template>
  <div class="overall-background">
    <v-container class="pa-0 overall-main">
      <!-- Top Yellow Header Area -->
      <div class="top-header-area">
        <h1 class="text-center header-title">Meu cartão bancário</h1>
      </div>

      <!-- Main Content Area (Dark Background with Cards) -->
      <div class="bank-card-container">
        <v-container fluid class="pa-0">
          <p class="text-center mb-6 all-cards-text">
            Todos os cartões bancários({{ totalCards }})
          </p>
          <v-row>
            <v-col
              cols="12"
              sm="6"
              md="4"
              class="pa-0"
              v-for="(card, index) in bankCards"
              :key="index"
            >
              <!-- New wrapper for card and button -->
              <div class="bank-card-wrapper">
                <v-card :color="card.color" dark flat tile class="bank-card">
                  <v-card-title class="card-title">{{
                    card.name
                  }}</v-card-title>
                  <v-card-subtitle class="card-subtitle">{{
                    card.pix
                  }}</v-card-subtitle>
                </v-card>
                <!-- <PERSON><PERSON> moved outside v-card -->
                <v-btn
                  :color="card.buttonColor"
                  dark
                  depressed
                  block
                  class="card-button"
                  @click="handleViewBank(card)"
                  >{{ card.buttonText }}</v-btn
                >
              </div>
            </v-col>
          </v-row>
        </v-container>
      </div>

      <!-- Bottom Yellow/Green Footer Area -->
      <div class="bottom-footer-area">
        <v-btn large depressed block @click="addBank" class="add-card-button"
          >+ Adicionar cartão bancário</v-btn
        >
      </div>
      <BindCardDialog
        :show="showBindCardDialog"
        @update:show="handleUpdate"
        :bankObj="bankObj"
        :type="type"
      ></BindCardDialog>
    </v-container>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, toRefs } from "vue";
import BindCardDialog from "./components/BindCardDialog.vue";
import { bindPaymentAccountList } from "@/api/user";

// 绑卡列表
const showBindCardDialog = ref(false);
const type = ref("add");
const bankObj = ref({ cpf: null });
const state = reactive({
  totalCards: 80,
  bankCards: [],
});
const colorLists = [
  {
    color: "#2E7D32",
    buttonText: "Ver número do cartão",
    buttonColor: "#00897B",
  }, // Green card, Teal button
  {
    color: "#F57C00",
    buttonText: "Ver número do cartão",
    buttonColor: "#FFB300",
  }, // Orange card, Yellow button
  {
    color: "#303F9F",
    buttonText: "Ver número do cartão",
    buttonColor: "#1976D2",
  }, // Blue card, darker Blue button
  {
    color: "#00897B",
    buttonText: "Ver número do cartão",
    buttonColor: "#00897B",
  }, // Teal card, Teal button
  {
    color: "#C62828",
    buttonText: "Ver número do cartão",
    buttonColor: "#E53935",
  }, // Red card, darker Red button
  {
    color: "#546E7A",
    buttonText: "Ver número do cartão",
    buttonColor: "#78909C",
  }, // Dark Gray/Blue card, lighter Gray/Blue button
];

const getBindPaymentAccountList = () => {
  bindPaymentAccountList().then((res) => {
    console.log(res);
    if (!res) return;
    state.bankCards = res?.map((e) => ({
      ...e,
      ...colorLists[Math.floor(Math.random() * colorLists.length)],
    }));
    state.totalCards = res.length;
  });
};
// 绑卡更新
const handleUpdate = (e) => {
  showBindCardDialog.value = false;
  if (e) {
    getBindPaymentAccountList();
  }

  bankObj.value = { cpf: null };
};

// 查看卡信息
const handleViewBank = (e) => {
  type.value = "edit";
  showBindCardDialog.value = true;
  bankObj.value = e;
};

// 添加卡
const addBank = () => {
  type.value = "add";
  showBindCardDialog.value = true;
};

onMounted(() => {
  getBindPaymentAccountList();
});

const { totalCards, bankCards } = toRefs(state);
</script>

<style scoped lang="scss">
.overall-background {
  // background-color: #4caf50; /* Solid green background - keeping this for the main body background */
  // min-height: 100vh;

  display: flex; /* Use flexbox for layout */
  flex-direction: column; /* Stack children vertically */
  justify-content: center;
  .overall-main {
    width: 80%;
    margin-top: 20px;
    margin-bottom: 20px;
    border-radius: 20px;
    overflow: hidden;
    // padding-bottom: 20px !important;
  }
}

.top-header-area {
  background: linear-gradient(
    87deg,
    #e5d51f,
    #3b922a,
    #e5d51f
  ); /* Apply specified gradient */
  padding: 20px 0;
  text-align: center; /* Center align header content */
}

.header-title {
  color: #333; /* Dark text color for header title */
  font-size: 1.5rem; /* Adjust font size */
  font-weight: bold;
}

.bank-card-container {
  background-color: #1a237e; /* Dark navy background color for card area */
  padding: 20px; /* Add padding to the dark background area */
  flex-grow: 1; /* Allow this area to take up remaining space */
  width: 100%; /* Ensure it takes full width */
  box-sizing: border-box; /* Include padding in width */
}

/* Remove padding from the inner v-container */
.bank-card-container .v-container--fluid {
  padding: 0 !important;
}

.all-cards-text {
  color: #b0bec5; /* Light gray color for the card count text */
  font-size: 0.9rem; /* Adjust font size */
  margin-bottom: 15px !important; /* Adjust bottom margin */
}

/* Adjust spacing between columns */
.v-row {
  margin-left: -10px;
  margin-right: -10px;
}

.v-col {
  padding: 10px;
}

.bank-card-wrapper {
  display: flex;
  flex-direction: column;
  margin: 6px; /* Add space between rows of card-button pairs */
}

.bank-card {
  background-size: cover; /* Adjust background image sizing if added */
  position: relative; /* Needed for potential background patterns */
  overflow: hidden; /* Hide overflowing background pattern */
  border-radius: 20px !important; /* Set border radius to 20px */
  flex-grow: 1; /* Allow card to take up available space */
}

/* Adding a placeholder for a potential background pattern */
.bank-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("~@/assets/card-pattern.png"); /* Placeholder: Replace with actual pattern path */
  background-size: cover;
  opacity: 0.1; /* Adjust opacity as needed */
  z-index: 0; /* Ensure pattern is behind content */
}

.bank-card .v-card__title,
.bank-card .v-card__subtitle {
  z-index: 1; /* Ensure content is above pattern */
  position: relative; /* Ensure content respects z-index */
}

.card-title {
  font-size: 1.25rem !important; /* Adjust title font size */
  font-weight: bold !important;
  padding: 16px 16px 16px 16px !important; /* Adjust padding */
  /* Adjusted padding to add space at the bottom */
}

.card-subtitle {
  font-size: 0.875rem !important; /* Adjust subtitle font size */
  padding: 0px 16px 16px 16px !important; /* Adjust padding */
}

.card-actions {
  /* This class is no longer used for the button's parent, but keeping it in case */
  padding: 0px 16px 16px 16px !important; /* Adjust padding around the button */
}

.card-button {
  height: 40px !important; /* Adjust button height */
  font-weight: bold !important;
  border-radius: 20px !important; /* Set border radius to 20px */
  // margin-top: 4px; /* Add 4px space between card and button */
}

.bottom-footer-area {
  background: linear-gradient(
    87deg,
    #e5d51f,
    #3b922a,
    #e5d51f
  ); /* Apply specified gradient */
  // padding: 15px 20px; /* Adjust padding */
  text-align: center; /* Center align footer content */
}

.add-card-button {
  font-weight: bold !important;
  height: 50px !important; /* Adjust height for the larger button */
  border-radius: 4px !important; /* Add slight border radius */
  color: #333 !important; /* Dark text color */
  background: linear-gradient(
    87deg,
    #e5d51f,
    #3b922a,
    #e5d51f
  ) !important; /* Apply the gradient background */
  border: none !important; /* Remove border */
  box-shadow: none !important; /* Remove shadow */
}

/* Ensure the inner v-container has no padding and full width */
.v-container--fluid.pa-0 {
  padding: 0 !important;
  width: 100% !important;
  max-width: 100% !important; /* Override any max-width */
}

/* Adjust padding within the dark background area */
.bank-card-container .v-row {
  padding: 0 10px; /* Match the v-col padding */
}
@media (max-width: 768px) {
  .bank-card-wrapper {
    margin: 0;
  }
  .bank-card {
    font-size: 12px !important;
    border-radius: 10px 10px 0 0 !important;
    margin-top: -6px;
  }
  .card-button {
    font-size: 12px !important;
    border-radius: 0 !important;
    height: 40px !important;
  }
  .card-title {
    font-size: 16px !important;
    padding: 10px 0 0 10px !important;
    line-height: 1;
  }
  .card-subtitle {
    font-size: 12px !important;
    padding: 0px 0 0 10px !important;
  }
  .all-cards-text {
    margin-bottom: 20px !important;
  }
  .bank-card-container {
    padding-top: 5px;
  }
}
</style>
