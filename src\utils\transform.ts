/**
 * 将记录转换为选项
 * @param record 记录对象
 * @param valueKey 值键名
 * @param labelKey 标签键名
 * @returns 选项数组
 */
export function transformRecordToOption<T extends Record<string, any>>(
  record: T,
  valueKey: keyof T = 'value',
  labelKey: keyof T = 'label'
) {
  return Object.entries(record).map(([key, value]) => {
    // 尝试将 key 转换为数字
    const numericKey = Number(key);
    return {
      [valueKey]: !isNaN(numericKey) ? numericKey : key,
      [labelKey]: value
    };
  });
}

/**
 * 将枚举转换为选项
 * @param enumObj 枚举对象
 * @param valueKey 值键名
 * @param labelKey 标签键名
 * @returns 选项数组
 */
export function transformEnumToOption<T extends Record<string, any>>(
  enumObj: T,
  valueKey: keyof T = 'value',
  labelKey: keyof T = 'label'
) {
  return Object.entries(enumObj)
    .filter(([key]) => isNaN(Number(key)))
    .map(([key, value]) => ({
      [valueKey]: value,
      [labelKey]: key
    }));
}

/**
 * 将数组转换为选项
 * @param arr 数组
 * @param valueKey 值键名
 * @param labelKey 标签键名
 * @returns 选项数组
 */
export function transformArrayToOption<T extends any[]>(
  arr: T,
  valueKey: string = 'value',
  labelKey: string = 'label'
) {
  return arr.map((item, index) => ({
    [valueKey]: index,
    [labelKey]: item
  }));
} 