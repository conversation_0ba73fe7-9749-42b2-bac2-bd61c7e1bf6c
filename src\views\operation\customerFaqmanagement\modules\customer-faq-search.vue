<script setup lang="ts">
import { ref } from 'vue'
import { $t } from '@/locales'

interface Props {
  model: any
}

interface Emits {
  (e: 'search'): void
  (e: 'reset'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref()

// 问题类型选项
const keywordTypeOptions = [
  { label: '所有类型', value: undefined },
  { label: '欢迎语', value: 1 },
  { label: '充值问题', value: 2 },
  { label: '提现问题', value: 3 },
  { label: '账户问题', value: 4 },
  { label: '游戏问题', value: 5 },
  { label: '活动优惠', value: 6 },
  { label: '其他问题', value: 7 }
]

function handleSearch() {
  emit('search')
}

function handleReset() {
  formRef.value?.resetFields()
  emit('reset')
}
</script>

<template>
  <div class="search-wrapper">
    <ElForm ref="formRef" :model="model">
      <ElRow :gutter="16">
        <ElCol :span="6">
          <ElFormItem label="问题类型" prop="keyword_type">
            <ElSelect
              v-model="model.keyword_type"
              placeholder="请选择问题类型"
              clearable
              style="width: 100%"
            >
              <ElOption
                v-for="item in keywordTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<style lang="scss" scoped>
</style>
