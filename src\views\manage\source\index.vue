<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <SourceSearch
      v-model:model="searchParams"
      @reset="resetSearchParams"
      @search="getDataByPage"
    >
      <template #table-operation>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          isNoDelete
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
    </SourceSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-60px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="checkedRowKeys = $event.map((row) => row.id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
      <SourceOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </ElCard>
  </div>
</template>

<script setup lang="tsx">
import { tagMap, statusOptions, positionOptions } from "@/constants/common";
import { ElButton, ElPopconfirm, ElTag, ElSwitch, ElImage } from "element-plus";
import { useTable, useTableOperate } from "@/hooks/common/table";
import { fetchGetCarouselList, fetchUpdateCarouselState } from "@/service/api";
import SourceSearch from "./modules/source-search.vue";
import SourceOperateDrawer from "./modules/source-operate-drawer.vue";
import { useAuth } from "@/hooks/business/auth";

const { hasAuth } = useAuth();

defineOptions({ name: "SourceManage" });

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  columnChecks,
} = useTable({
  apiFn: fetchGetCarouselList,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 20,
    status: undefined,
  },
  columns: () => [
    { prop: "index", label: "序号", width: 64 },
    { prop: "title", label: "标题", minWidth: 200 },
    {
      prop: "position",
      label: "位置",
      width: 120,
      formatter: (row: any) => {
        const label = positionOptions.find(
          (item) => item.value === row.position,
        )?.label;
        return label || "未知位置";
      },
    },
    { prop: "weight", label: "权重", width: 100 },

    {
      prop: "image_url",
      label: "图片",
      width: 150,
      formatter: (row: any) => (
        <ElImage
          style="width: 100px; height: 60px"
          src={row.image_url}
          preview-teleported="body"
          preview-src-list={[row.image_url]}
          fit="cover"
        />
      ),
    },
    {
      prop: "hyperlink",
      label: "链接",
      minWidth: 200,
      showOverflowTooltip: true,
    },
    {
      prop: "status",
      label: "状态",
      width: 100,
      formatter: (row: any) => {
        if (row.status === undefined) {
          return "";
        }
        const label = statusOptions.find(
          (item) => item.value === row.status,
        )?.label;
        return <ElTag type={tagMap[row.status]}>{label}</ElTag>;
      },
    },
    {
      width: 140,
      prop: "operate",
      label: "操作",
      align: "center",
      formatter: (row: any) => (
        <div class="flex-center">
          {hasAuth(2) && (
            <ElButton
              plain
              type={row.status === 1 ? "danger" : "success"}
              size="small"
              onClick={() =>
                handleStatusChange(row.id, row.status === 1 ? 0 : 1)
              }
            >
              {row.status === 1 ? "禁用" : "启用"}
            </ElButton>
          )}
          {hasAuth(3) && (
            <ElButton
              type="primary"
              plain
              size="small"
              onClick={() => edit(row.id)}
            >
              编辑
            </ElButton>
          )}
        </div>
      ),
    },
  ],
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  onDeleted,
  checkedRowKeys,
} = useTableOperate(data, getData);

async function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.warning("请选择要删除的资源");
    return;
  }
}

async function handleStatusChange(id: number, value: number) {
  const { error } = await fetchUpdateCarouselState({
    ids: [id],
    status: value,
  });
  if (!error) {
    window.$message?.success("状态更新成功");
    getData();
  }
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<style lang="scss" scoped>
:deep(.el-card) {
  border: none;
  border-radius: 0 0 4px 4px;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
