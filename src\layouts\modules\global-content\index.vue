<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { LAYOUT_SCROLL_EL_ID } from '@sa/materials';
import { useAppStore } from '@/store/modules/app';
import { useThemeStore } from '@/store/modules/theme';
import { useRouteStore } from '@/store/modules/route';
import { useTabStore } from '@/store/modules/tab';

defineOptions({ name: 'GlobalContent' });

interface Props {
  /** Show padding for content */
  showPadding?: boolean;
}

withDefaults(defineProps<Props>(), {
  showPadding: true
});

const appStore = useAppStore();
const themeStore = useThemeStore();
const routeStore = useRouteStore();
const tabStore = useTabStore();

const transitionName = computed(() => (themeStore.page.animate ? themeStore.page.animateMode : ''));

// 兜底机制：如果 reloadFlag 长时间为 false，强制恢复
const fallbackTimeout = ref<number>();
watch(
  () => appStore.reloadFlag,
  (val) => {
    if (!val) {
      // 清除之前的定时器
      if (fallbackTimeout.value) {
        clearTimeout(fallbackTimeout.value);
      }
      // 设置新的兜底定时器
      fallbackTimeout.value = setTimeout(() => {
        if (!appStore.reloadFlag) {
          console.warn('GlobalContent fallback: forcing reloadFlag to true');
          appStore.reloadFlag = true;
        }
      }, 2000) as unknown as number;
    } else {
      // 清除定时器
      if (fallbackTimeout.value) {
        clearTimeout(fallbackTimeout.value);
        fallbackTimeout.value = undefined;
      }
    }
  },
  { immediate: true }
);

function resetScroll() {
  const el = document.querySelector(`#${LAYOUT_SCROLL_EL_ID}`);

  el?.scrollTo({ left: 0, top: 0 });
}

function handleBeforeLeave() {
  appStore.setContentXScrollable(true);
}

function handleAfterEnter() {
  appStore.setContentXScrollable(false);
}
</script>

<template>
  <RouterView v-slot="{ Component, route }" :key="$route.fullPath">
    <Transition
      :name="transitionName"
      mode="out-in"
      @before-leave="handleBeforeLeave"
      @after-leave="resetScroll"
      @after-enter="handleAfterEnter"
    >
      <KeepAlive :include="routeStore.cacheRoutes" :exclude="routeStore.excludeCacheRoutes">
        <component
          :is="Component"
          v-if="appStore.reloadFlag"
          :key="tabStore.getTabIdByRoute(route)"
          :class="{ 'p-16px': showPadding }"
          class="flex-grow bg-layout transition-300"
        />
      </KeepAlive>
    </Transition>
  </RouterView>
</template>

<style></style>
