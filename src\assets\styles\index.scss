@import "./_device.scss";

// 全局设备适配样式
html {
  font-size: 16px;

  @include mobile-only {
    font-size: 14px;
  }
}

// 安全区域适配
body {
  @include mobile-only {
    @include safe-area-inset("top");
    @include safe-area-inset("bottom");
  }
}

// 触摸设备优化
@include touch-device {
  * {
    cursor: default !important;
    -webkit-tap-highlight-color: transparent;
  }

  input,
  textarea {
    font-size: 16px; // 防止iOS自动缩放
  }
}

// 高DPI设备优化
@include high-dpi {
  img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

