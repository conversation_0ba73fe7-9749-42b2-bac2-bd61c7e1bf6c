<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <RechargePackageSearch
      v-model:model="searchParams"
      @reset="resetSearchParams"
      @search="getDataByPage"
    >
      <template #table-operation>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :isNoDelete="true"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
    </RechargePackageSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-60px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="checkedRowKeys = $event.map((row) => row.id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
      <RechargePackageOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </ElCard>
  </div>
</template>

<script setup lang="tsx">
import { ElButton, ElPopconfirm, ElTag, ElImage } from "element-plus";
import {
  fetchGetRechargePackageList,
  fetchUpdateRechargePackageState,
  fetchDeleteRechargePackage,
} from "@/service/api";
import { useTable, useTableOperate } from "@/hooks/common/table";
import { $t } from "@/locales";
import RechargePackageSearch from "./modules/recharge-package-search.vue";
import RechargePackageOperateDrawer from "./modules/recharge-package-operate-drawer.vue";
import { useAuth } from "@/hooks/business/auth";

const { hasAuth } = useAuth();

defineOptions({ name: "RechargePackageManage" });

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  columnChecks,
} = useTable({
  apiFn: fetchGetRechargePackageList,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 20,
    recharge_package_name: undefined,
    status: undefined,
    reward_type: undefined,
  },
  columns: () => [
    { prop: "index", label: $t("common.index"), width: 64 },
    { prop: "recharge_package_name", label: "套餐名称", minWidth: 120 },
    {
      prop: "recharge_amount",
      label: "充值金额",
      minWidth: 100,
      formatter: (row) => {
        return `R$ ${(row.recharge_amount / 100)?.toFixed(2) || "0.00"}`;
      },
    },
    {
      prop: "cash_amount",
      label: "赠送金额",
      minWidth: 100,
      formatter: (row) => {
        if (row.cash_amount !== 0) {
          return (
            "现金:" + `R$ ${(row.cash_amount / 100)?.toFixed(2) || "0.00"}`
          );
        } else if (row.bonus_amount !== 0) {
          return (
            "赠送:" + `R$ ${(row.bonus_amount / 100)?.toFixed(2) || "0.00"}`
          );
        } else if (row.bet_cash_amount !== 0) {
          return (
            "打码提现:" +
            `R$ ${(row.bet_cash_amount / 100)?.toFixed(2) || "0.00"}`
          );
        }
      },
    },
    {
      label: "购买次数限制",
      minWidth: 150,
      formatter: (row) => {
        const limits = [];
        if (row.one_month) {
          limits.push(`一个月: ${row.one_month}次`);
        }
        if (row.three_months) {
          limits.push(`三个月: ${row.three_months}次`);
        }
        if (row.one_year) {
          limits.push(`一年: ${row.one_year}次`);
        }
        return limits.join("\n");
      },
    },
    {
      prop: "status",
      label: "状态",
      width: 100,
      formatter: (row) => {
        if (row.status === undefined) {
          return "";
        }
        const tagMap: Record<Api.Common.EnableStatus, UI.ThemeColor> = {
          1: "success",
          0: "error",
        };
        const label = ["禁用", "启用"][row.status];
        return <ElTag type={tagMap[row.status]}>{label}</ElTag>;
      },
    },
    {
      width: 140,
      prop: "operate",
      label: $t("common.operate"),
      align: "center",
      formatter: (row: any) => (
        <div class="flex-center">
          {hasAuth(2) && (
            <ElPopconfirm
              title={$t("common.confirmDelete")}
              onConfirm={() => handleDelete(row.id)}
            >
              {{
                reference: () => (
                  <ElButton type="danger" plain size="small">
                    {$t("common.delete")}
                  </ElButton>
                ),
              }}
            </ElPopconfirm>
          )}
          {hasAuth(3) && (
            <ElButton
              type="primary"
              plain
              size="small"
              onClick={() => handleEdit(row.id)}
            >
              {$t("common.edit")}
            </ElButton>
          )}
        </div>
      ),
    },
  ],
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  onDeleted,
  checkedRowKeys,
} = useTableOperate<Api.Finance.RechargePackageItem>(data, getData, "id");

async function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.warning("请选择要删除的套餐");
    return;
  }
}

async function handleDelete(id: number) {
  const { error } = await fetchDeleteRechargePackage({ id: id.toString() });
  if (!error) {
    window.$message?.success("删除成功");
    getData();
  }
}

async function handleStatusChange(id: number, value: number) {
  const { error } = await fetchUpdateRechargePackageState({
    ids: [id],
    status: value,
  });
  if (!error) {
    window.$message?.success("状态更新成功");
    getData();
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
