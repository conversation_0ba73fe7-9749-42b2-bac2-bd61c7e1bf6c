<script setup lang="ts">
import { ref, computed,watch } from 'vue';
import { ElMessage } from 'element-plus';
import { $t } from '@/locales';
import { fetchAddKeyword, fetchUpdateKeyword } from '@/service/api';

interface Props {
  operateType: 'add' | 'edit';
  rowData?: Api.SystemManage.FAQItem;
}

interface Emits {
  (e: 'submitted'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const title = computed(() => {
  const operateTypeMap = {
    add: $t('common.add'),
    edit: $t('common.edit')
  };
  return operateTypeMap[props.operateType];
});

const formRef = ref();
const loading = ref(false);

const form = ref({
  keyword: '',
  response: ''
});

function closeDrawer() {
  visible.value = false;
}

function resetForm() {
  form.value = {
    keyword: '',
    response: ''
  };
}

function setForm() {
  if (props.operateType === 'edit' && props.rowData) {
    form.value = {
      keyword: props.rowData.keyword,
      response: props.rowData.response
    };
  }
}

async function handleSubmit() {
  await formRef.value?.validate();
  loading.value = true;
  try {
    if (props.operateType === 'add') {
      const { error } = await fetchAddKeyword(form.value);
      if (!error) {
        window.$message?.success("添加成功");
        closeDrawer();
        emit('submitted');
      }
    } else {
      const { error } = await fetchUpdateKeyword({
        ...form.value,
        id: props.rowData?.id
      });
      if (!error) {
        window.$message?.success("更新成功");
        closeDrawer();
        emit('submitted');
      }
    }
  } finally {
    loading.value = false;
  }
}

watch(visible, val => {
  if (val) {
    setForm();
  } else {
    resetForm();
  }
});
</script>

<template>
  <ElDrawer
    v-model="visible"
    :title="title"
    :size="360"
    destroy-on-close
    @close="closeDrawer"
  >
    <ElForm
      ref="formRef"
      :model="form"
      label-width="80px"
      label-position="top"
    >
      <ElFormItem
        label="提问"
        prop="keyword"
        :rules="[{ required: true, message: '请输入提问内容' }]"
      >
        <ElInput
          v-model="form.keyword"
          type="textarea"
          :rows="3"
          placeholder="请输入提问内容"
        />
      </ElFormItem>
      <ElFormItem
        label="回答"
        prop="response"
        :rules="[{ required: true, message: '请输入回答内容' }]"
      >
        <ElInput
          v-model="form.response"
          type="textarea"
          :rows="5"
          placeholder="请输入回答内容"
        />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <ElSpace class="w-full justify-end">
        <ElButton @click="closeDrawer">{{ $t('common.cancel') }}</ElButton>
        <ElButton type="primary" :loading="loading" @click="handleSubmit">
          {{ $t('common.confirm') }}
        </ElButton>
      </ElSpace>
    </template>
  </ElDrawer>
</template> 