<template>
  <v-container fluid class="messages-container pa-4" max-width="940">
    <div class="content-wrapper">
      <div class="header-section mb-6">
        <div class="d-flex align-center">
          <h1 class="text-h4 font-weight-bold">Mensagens</h1>
          <v-badge
            v-if="unreadCount > 0"
            :content="unreadCount"
            color="error"
            class="pl-5"
          ></v-badge>
        </div>
      </div>

      <v-card class="message-list-card">
        <v-card-text class="pa-0">
          <div v-if="loading" class="d-flex justify-center align-center py-8">
            <v-progress-circular indeterminate color="primary"></v-progress-circular>
          </div>

          <div v-else-if="messagesList.length === 0" class="empty-messages">
            <div class="text-center pa-8">
              <v-icon size="64" color="#FFDF00">mdi-email-outline</v-icon>
              <div class="mt-3 text-grey">Nenhuma mensagem ainda</div>
            </div>
          </div>

          <v-list v-else class="message-list pa-0">
            <v-list-item
              v-for="message in messagesList"
              :key="message.id"
              :class="['message-item', { 'unread': !message.IsRead }]"
              @click="openMessage(message)"
            >
              <template v-slot:prepend>
                <v-avatar color="#FFDF00" class="message-avatar">
                  <v-icon :icon="getMessageIcon(message.type)" color="white"></v-icon>
                </v-avatar>
              </template>
              
              <div class="message-item-content">
                <div class="message-top">
                  <div class="message-title">{{ message.subject }}</div>
                  <div class="message-date">{{ formatDate(message.created_at) }}</div>
                </div>
                <div class="message-preview">{{ stripHtmlTags(message.content) }}</div>
              </div>
              <div v-if="!message.IsRead" class="unread-indicator"></div>
            </v-list-item>
          </v-list>
        </v-card-text>
        <!-- 加载更多 -->
        <div v-if="hasMore" class="text-center pa-4">
          <v-btn
              :loading="loading"
              @click="loadMore"
              class="more-btn"
          >
            Carregar mais
          </v-btn>
        </div>
      </v-card>
    </div>
  </v-container>

  <!-- Message Detail Dialog -->
  <v-dialog v-model="showMessageDetail" max-width="500" class="message-dialog" @show="calculateContentHeight">
    <v-card v-if="selectedMessage" class="message-detail-card">
      <v-card-title class="message-title">
        <div class="title-text">{{ selectedMessage.subject }}</div>
        <v-btn
          icon="mdi-close"
          variant="text"
          size="small"
          @click="showMessageDetail = false"
          class="close-btn"
        ></v-btn>
      </v-card-title>
      
      <v-divider></v-divider>
      
      <v-card-text class="message-content" :style="{ height: messageContentHeight }">
        <div class="message-date mb-3">
          {{ formatDate(selectedMessage.created_at) }}
        </div>
        
        <div class="message-html-content" v-html="selectedMessage.content"></div>
      </v-card-text>
      
      <v-card-actions class="align-center justify-center">
        <v-btn
          class="text-none text-subtitle-1 submit-btn"
          color="primary"
          variant="flat"
          @click="handleCloseCLick()"
        >
          Fechar
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import { getUserNoticeList, readNotice } from '@/api/auth';
import { formatDate } from '@/utils/date';

interface MessageData {
  id: string;
  title: string;
  preview: string;
  content: string;
  created_at: number;
  notice_type: number;
  status: number;
  IsRead: boolean;
  subject: string;
  type: number;
}

interface NoticeResponse {
  data: MessageData[];
  count: number;
}

// 状态
const loading = ref(false);
const showMessageDetail = ref(false);
const selectedMessage = ref<MessageData | null>(null);
const messagesList = ref<MessageData[]>([]);
const total = ref(0)
const hasMore = computed(() => messagesList.value.length < total.value)
const pageNo = ref(1)

// 获取 store
const store = useStore();

const messageContentHeight = ref('auto');

// 计算消息内容区域的高度
const calculateContentHeight = async () => {
  await nextTick();
  const vh = window.innerHeight;
  const safeAreaTop = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--sat') || '0');
  const safeAreaBottom = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--sab') || '0');
  
  // 标题高度 + 底部按钮高度 + 安全区域 + padding
  const titleHeight = 56;
  const bottomHeight = 72;
  const contentHeight = vh - titleHeight - bottomHeight - safeAreaTop - safeAreaBottom;
  
  messageContentHeight.value = `${contentHeight}px`;
};

// 监听弹窗显示状态
watch(showMessageDetail, async (val) => {
  if (val) {
    await calculateContentHeight();
  }
});

// 监听窗口大小变化
onMounted(() => {
  window.addEventListener('resize', calculateContentHeight);
  loadMessages();
});

// 在组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', calculateContentHeight);
});

// 关闭消息详情
const handleCloseCLick = () => {
  loadMessages(true)
  showMessageDetail.value = false;
}

// 计算未读消息数量
const unreadCount = computed(() => {
  return messagesList.value.filter(msg => !msg.IsRead).length;
});

// 根据消息类型获取对应图标
const getMessageIcon = (type: number): string => {
  switch (type) {
    case 1:
      return 'mdi-bell'; // 系统消息
    case 10:
      return 'mdi-cash-plus'; // 充值消息
    case 20:
      return 'mdi-cash-minus'; // 提现消息
    default:
      return 'mdi-email';
  }
};

// 打开消息详情
const openMessage = async (message: MessageData) => {
  selectedMessage.value = message;
  showMessageDetail.value = true;
  
  // 如果消息未读，调用已读接口
  if (!message.IsRead) {
    try {
      await readNotice({ notice_id: message.id, user_id: store.state.auth.user?.id });
      message.IsRead = true;
      // 打开抽屉前刷新用户信息
      await store.dispatch('auth/fetchUserInfo')
    } catch (error) {
      console.error('Failed to mark message as read:', error);
    }
  }
};

// 加载消息列表
const loadMessages = async (val: boolean) => {
  if(val){
    messagesList.value = []
    pageNo.value = 1
  }
  if(!store.state.auth.user) return
  let user = store.state.auth.user
  try {
    loading.value = true;
    const response = await getUserNoticeList({
      sort: '-id',
      page: pageNo.value,
      size: 10,
      user_id: user?.id
    }) as NoticeResponse;
    
    if (response) {
      messagesList.value = [...messagesList.value, ...response.data];
      total.value = response.count
    }
  } catch (error) {
    console.error('Failed to load messages:', error);
  } finally {
    loading.value = false;
  }
};
const loadMore = () => {
  if (loading.value || !hasMore.value) return
  pageNo.value++
  loadMessages(false)
}
// 去除HTML标签的函数
const stripHtmlTags = (html: string): string => {
  if (!html) return '';
  const div = document.createElement('div');
  div.innerHTML = html;
  return div.textContent || div.innerText || '';
};
</script>

<style lang="scss" scoped>
.more-btn{
  background: linear-gradient(0deg, #C9B737, #2ABB27);
  border-radius: 15px;
  width: 200px;
}

.messages-container {
  min-height: 100%;
  padding: 16px !important;
  
  @media (max-width: 600px) {
    padding: 12px !important;
    min-height: calc(100vh - 120px);
  }
}

.content-wrapper {
  margin: 0 auto;
}

.header-section {
  color: white;
  
  @media (max-width: 600px) {
    margin-bottom: 16px !important;
    
    h1 {
      font-size: 1.5rem !important;
    }
  }
}

.message-list-card {
  background: #2B324D;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.message-list {
  overflow: auto;
  background: #2B324D;
}

.message-item {
  position: relative;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  padding: 16px;
  transition: all 0.2s ease;
  
  @media (max-width: 600px) {
    padding: 12px;
  }
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
  }
  
  &.unread {
    background: rgba(251, 119, 168, 0.05);
    
    .message-title {
      font-weight: bold;
    }
  }
}

.message-avatar {
  margin-right: 16px;
  
  @media (max-width: 600px) {
    margin-right: 12px;
  }
}

.message-item-content {
  flex: 1;
  min-width: 0; // 防止内容溢出
}

.message-top {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  align-items: flex-start;
  
  @media (max-width: 600px) {
    flex-direction: column;
    
    .message-date {
      margin-left: 0;
      margin-top: 4px;
    }
  }
}

.message-title {
  font-size: 16px;
  color: white;
  font-weight: 500;
  
  @media (max-width: 600px) {
    font-size: 14px;
  }
}

.message-date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  white-space: nowrap;
  margin-left: 8px;
}

.message-preview {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  
  @media (max-width: 600px) {
    font-size: 13px;
  }
}

.unread-indicator {
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #FFDF00;
  
  @media (max-width: 600px) {
    right: 12px;
  }
}

.empty-messages {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.5);
}

.message-detail-card {
  background: #1B1A4B;
  border-radius: 24px;
  overflow: hidden;
  margin: 16px;
  display: flex;
  flex-direction: column;
  
  @media (max-width: 600px) {
    margin: 0;
    border-radius: 0;
    height: 100vh;
  }
  
  .message-title {
    color: white;
    box-sizing: border-box;
    padding: 16px;
    background: #272B5A;
    min-height: 56px;
    word-break: break-word;
    position: relative;
    padding-top: calc(env(safe-area-inset-top, 0px) + 16px);
    
    .title-text {
      font-size: 16px;
      font-weight: 500;
      line-height: 1.4;
      margin-top: 4px;
      padding-right: 40px;
      overflow: hidden;
    }
    
    .close-btn {
      color: white;
      position: absolute;
      right: 6px;
      top: 12px;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      
      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }
  
  .message-content {
    padding: 16px;
    color: white;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    
    @media (max-width: 600px) {
      padding: 12px 16px;
    }
    
    .message-date {
      color: rgba(255, 255, 255, 0.6);
      font-size: 13px;
      margin-bottom: 12px;
    }
  }
  
  .v-card-actions {
    padding: 16px;
    background: rgba(39, 43, 90, 0.95);
    backdrop-filter: blur(10px);
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 16px);
    
    .submit-btn {
      width: 200px;
      height: 40px;
      background: linear-gradient(0deg, #C9B737, #2ABB27);
      border-radius: 20px;
      
      @media (max-width: 600px) {
        width: 100%;
      }
    }
  }
}

.message-html-content {
  line-height: 1.6;
  
  :deep(p) {
    margin-bottom: 16px;
  }
  
  :deep(strong) {
    color: #FFDF00;
  }
  
  :deep(ul) {
    list-style-position: inside;
    margin-bottom: 16px;
  }

  :deep(div) {
    margin-bottom: 8px;
  }

  :deep(img) {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 8px 0;
  }
}

// 移动端对话框样式
:deep(.v-dialog) {
  @media (max-width: 600px) {
    margin: 0;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    max-width: 100% !important;
    max-height: 100vh !important;
    border-radius: 0;
    display: flex;
    flex-direction: column;
    
    .v-card {
      height: 100vh;
      max-height: 100vh;
      margin: 0;
      display: flex;
      flex-direction: column;
      border-radius: 0;
    }

    .v-card-title {
      padding-top: calc(env(safe-area-inset-top, 0px) + 16px);
    }

    .message-content {
      flex: 1;
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
      padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
    }

    .v-card-actions {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(39, 43, 90, 0.95);
      backdrop-filter: blur(10px);
      padding: 16px;
      padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 16px);
      z-index: 100;
    }
  }
}
</style> 