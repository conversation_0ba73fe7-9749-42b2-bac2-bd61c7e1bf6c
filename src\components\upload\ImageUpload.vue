<template>
  <div class="image-upload">
    <div class="image-uploader" @click="triggerUpload">
      <div v-if="imageUrl" class="image-preview">
        <ElImage
          :src="formatImageUrl(imageUrl)"
          fit="cover"
          class="h-40px"
        />
        <div class="image-actions">
          <div class="action-buttons">
            <ElIcon class="action-icon" @click.stop="handlePreview">
              <View />
            </ElIcon>
            <ElIcon class="action-icon" @click.stop="handleRemove">
              <Delete />
            </ElIcon>
          </div>
        </div>
      </div>
      <ElIcon v-else class="image-uploader-icon">
        <Plus />
      </ElIcon>
      <input
        ref="fileInput"
        type="file"
        accept="image/*"
        class="hidden"
        @change="handleFileChange"
      />
    </div>
    <div v-if="showTip" class="upload-tip">
      {{ tipText }}
    </div>

    <div v-if="showViewer" class="image-viewer-container">
      <ElImageViewer
        v-model:show="showViewer"
        :url-list="[formatImageUrl(imageUrl)]"
        :initial-index="0"
        :infinite="false"
        :teleported="true"
        :z-index="9999"
        @close="showViewer = false"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage, ElImageViewer } from 'element-plus';
import { Delete, Plus, View } from '@element-plus/icons-vue';
import { fetchUploadImage } from '@/service/api';

const imageBaseUrl = import.meta.env.VITE_IMAGE_BASE_URL;

interface Props {
  modelValue?: string;
  disabled?: boolean;
  showTip?: boolean;
  tipText?: string;
  maxSize?: number; // 单位：MB
  accept?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  disabled: false,
  showTip: true,
  tipText: '支持 jpg、png 格式图片，大小不超过 2MB',
  maxSize: 2,
  accept: 'image/*'
});

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
  (e: 'success', response: any): void;
  (e: 'error', error: any): void;
}>();

const imageUrl = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 格式化图片URL，处理多斜杠的问题
function formatImageUrl(url: string) {
  if (!url) return '';
  // 如果已经是完整的URL，直接返回
  if (url.startsWith('http')) return url;
  // 移除开头的斜杠
  const cleanPath = url.replace(/^\/+/, '');
  // 移除图片基础URL末尾的斜杠
  const cleanBaseUrl = imageBaseUrl.replace(/\/+$/, '');
  return `${cleanBaseUrl}/${cleanPath}`;
}

const loading = ref(false);
const fileInput = ref<HTMLInputElement>();
const showViewer = ref(false);

// 触发文件选择
function triggerUpload() {
  if (props.disabled) return;
  fileInput.value?.click();
}

// 文件选择变化
async function handleFileChange(event: Event) {
  const input = event.target as HTMLInputElement;
  const file = input.files?.[0];
  if (!file) return;

  // 校验文件
  if (!beforeUpload(file)) {
    input.value = '';
    return;
  }

  try {
    loading.value = true;
    const formData = new FormData();
    formData.append('file', file);

    const { data, error } = await fetchUploadImage(formData);
    if (!error && data) {
      const cleanBaseUrl = imageBaseUrl.replace(/\/+$/, '');
      let url = `${cleanBaseUrl}/${data.data}`;
      imageUrl.value =  url;
      emit('success', data.data);
      ElMessage.success('上传成功');
    } else {
      ElMessage.error('上传失败');
      emit('error', error);
    }
  } catch (error) {
    ElMessage.error('上传失败');
    emit('error', error);
  } finally {
    loading.value = false;
    input.value = '';
  }
}

// 上传前的校验
function beforeUpload(file: File) {
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    ElMessage.error('只能上传图片文件！');
    return false;
  }

  const isLtSize = file.size / 1024 / 1024 < props.maxSize;
  if (!isLtSize) {
    ElMessage.error(`图片大小不能超过 ${props.maxSize}MB！`);
    return false;
  }

  return true;
}

// 预览图片
function handlePreview() {
  if (imageUrl.value) {
    showViewer.value = true;
  }
}

// 删除图片
function handleRemove() {
  imageUrl.value = '';
  ElMessage.success('删除成功');
}
</script>

<style lang="scss" scoped>
.image-upload {
  .image-uploader {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    width: 120px;
    height:120px;

    &:hover {
      border-color: var(--el-color-primary);
    }

    .image-uploader-icon {
      font-size: 20px;
      color: #8c939d;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .image-preview {
      width: 100%;
      height: 100%;
      position: relative;

      .el-image {
        width: 100%;
        height: 100%;
      }

      .image-actions {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s;

        &:hover {
          opacity: 1;
        }

        .action-buttons {
          display: flex;
          gap: 20px;

          .action-icon {
            color: #fff;
            cursor: pointer;
            font-size: 40px;
            padding: 12px;
            border-radius: 8px;
            transition: all 0.3s;
            background: rgba(255, 255, 255, 0.1);

            &:hover {
              background: rgba(255, 255, 255, 0.2);
              transform: scale(1.1);
            }

            &.delete-icon:hover {
              color: var(--el-color-danger);
            }
          }
        }
      }
    }
  }

  .upload-tip {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-top: 12px;
  }
}
</style>
