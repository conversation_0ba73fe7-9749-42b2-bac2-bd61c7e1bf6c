/**
 * WebSocket配置文件
 * 统一管理WebSocket相关的配置和行为设置
 */

export interface WebSocketConfig {
  // 连接配置
  connection: {
    maxReconnectAttempts: number;
    reconnectInterval: number;
    connectTimeout: number;
  };
  
  // 心跳配置
  heartbeat: {
    interval: number;
    timeout: number;
    enableAutoRestart: boolean;
  };
  
  // 关闭代码处理配置
  closeCodes: {
    ignoreCodes: number[];
    normalCodes: number[];
    retryableCodes: number[];
  };
  
  // 监控配置
  monitoring: {
    networkCheckInterval: number;
    websocketCheckInterval: number;
    keeperCheckInterval: number;
    enableStatusDisplay: boolean;
  };
  
  // 调试配置
  debug: {
    enableLogs: boolean;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
    enableStatusComponent: boolean;
  };
}

/**
 * 默认WebSocket配置
 */
export const defaultWebSocketConfig: WebSocketConfig = {
  connection: {
    maxReconnectAttempts: 5,
    reconnectInterval: 5000,
    connectTimeout: 10000,
  },
  
  heartbeat: {
    interval: 30000,        // 30秒发送一次心跳
    timeout: 60000,         // 60秒心跳超时
    enableAutoRestart: true, // 启用心跳自动重启
  },
  
  closeCodes: {
    // 忽略的关闭代码（不触发重连，不记录错误）
    ignoreCodes: [
      1006, // 异常关闭 - 通常是正常的网络切换或页面状态变化
    ],
    
    // 正常关闭代码（不触发重连）
    normalCodes: [
      1000, // 正常关闭
      1001, // 离开
    ],
    
    // 可重试的错误代码（触发重连）
    retryableCodes: [
      1002, // 协议错误
      1003, // 不支持的数据类型
      1007, // 数据类型不一致
      1009, // 消息太大
      1011, // 服务器遇到意外情况
      1015, // TLS握手失败
    ],
  },
  
  monitoring: {
    networkCheckInterval: 30000,    // 30秒检查一次网络质量
    websocketCheckInterval: 60000,  // 60秒检查一次WebSocket状态
    keeperCheckInterval: 30000,     // 30秒检查一次连接保持
    enableStatusDisplay: true,      // 启用状态显示
  },
  
  debug: {
    enableLogs: true,
    logLevel: 'info',
    enableStatusComponent: true,
  },
};

/**
 * 当前使用的WebSocket配置
 */
let currentConfig: WebSocketConfig = { ...defaultWebSocketConfig };

/**
 * 获取当前WebSocket配置
 */
export const getWebSocketConfig = (): WebSocketConfig => {
  return currentConfig;
};

/**
 * 更新WebSocket配置
 */
export const updateWebSocketConfig = (config: DeepPartial<WebSocketConfig>): void => {
  currentConfig = mergeConfig(currentConfig, config);
  console.log('WebSocket配置已更新:', currentConfig);
};

/**
 * 深度部分类型
 */
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * 重置WebSocket配置为默认值
 */
export const resetWebSocketConfig = (): void => {
  currentConfig = { ...defaultWebSocketConfig };
  console.log('WebSocket配置已重置为默认值');
};

/**
 * 深度合并配置对象
 */
function mergeConfig(target: any, source: any): any {
  const result = { ...target };
  
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = mergeConfig(target[key] || {}, source[key]);
    } else {
      result[key] = source[key];
    }
  }
  
  return result;
}

/**
 * 根据环境自动调整配置
 */
export const setupEnvironmentConfig = (): void => {
  const isDev = import.meta.env.DEV;
  const isTest = import.meta.env.MODE === 'test';
  
  if (isDev) {
    // 开发环境配置
    updateWebSocketConfig({
      debug: {
        enableLogs: true,
        logLevel: 'debug',
        enableStatusComponent: true,
      },
      monitoring: {
        enableStatusDisplay: true,
      },
    });
  } else if (isTest) {
    // 测试环境配置
    updateWebSocketConfig({
      connection: {
        maxReconnectAttempts: 3,
        reconnectInterval: 2000,
      },
      debug: {
        enableLogs: true,
        logLevel: 'warn',
        enableStatusComponent: false,
      },
    });
  } else {
    // 生产环境配置
    updateWebSocketConfig({
      debug: {
        enableLogs: false,
        logLevel: 'error',
        enableStatusComponent: false,
      },
      monitoring: {
        enableStatusDisplay: false,
      },
    });
  }
};

/**
 * 检查关闭代码是否应该被忽略
 */
export const shouldIgnoreCloseCode = (code: number): boolean => {
  const config = getWebSocketConfig();
  return config.closeCodes.ignoreCodes.includes(code);
};

/**
 * 检查关闭代码是否为正常关闭
 */
export const isNormalCloseCode = (code: number): boolean => {
  const config = getWebSocketConfig();
  return config.closeCodes.normalCodes.includes(code);
};

/**
 * 检查关闭代码是否可重试
 */
export const isRetryableCloseCode = (code: number): boolean => {
  const config = getWebSocketConfig();
  return config.closeCodes.retryableCodes.includes(code);
};

/**
 * 获取关闭代码的处理策略
 */
export const getCloseCodeStrategy = (code: number): 'ignore' | 'normal' | 'retry' | 'unknown' => {
  if (shouldIgnoreCloseCode(code)) return 'ignore';
  if (isNormalCloseCode(code)) return 'normal';
  if (isRetryableCloseCode(code)) return 'retry';
  return 'unknown';
};

/**
 * 日志输出函数（根据配置控制）
 */
export const wsLog = {
  debug: (message: string, ...args: any[]) => {
    const config = getWebSocketConfig();
    if (config.debug.enableLogs && ['debug'].includes(config.debug.logLevel)) {
      console.debug(`[WebSocket Debug] ${message}`, ...args);
    }
  },
  
  info: (message: string, ...args: any[]) => {
    const config = getWebSocketConfig();
    if (config.debug.enableLogs && ['debug', 'info'].includes(config.debug.logLevel)) {
      console.info(`[WebSocket Info] ${message}`, ...args);
    }
  },
  
  warn: (message: string, ...args: any[]) => {
    const config = getWebSocketConfig();
    if (config.debug.enableLogs && ['debug', 'info', 'warn'].includes(config.debug.logLevel)) {
      console.warn(`[WebSocket Warn] ${message}`, ...args);
    }
  },
  
  error: (message: string, ...args: any[]) => {
    const config = getWebSocketConfig();
    if (config.debug.enableLogs) {
      console.error(`[WebSocket Error] ${message}`, ...args);
    }
  },
};

// 自动设置环境配置
setupEnvironmentConfig();
