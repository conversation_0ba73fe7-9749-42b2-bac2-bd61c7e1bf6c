<script setup lang="ts">
import { computed, shallowRef, watch, ref, nextTick } from "vue";
import { $t } from "@/locales";
import { myRoutes } from "@/router/elegant/routes";
import type { GeneratedRoute } from "@elegant-router/types";
import { filterAuthRoutesByRoutes } from "@/store/modules/route/shared";
import { useAuthStore } from "@/store/modules/auth";
import { localStg } from "@/utils/storage";

defineOptions({ name: "MenuAuthModal" });

interface Props {
  routes: string;
}

interface Emits {
  (e: "routeIds", routeIds: string[]): void;
}

const emit = defineEmits<Emits>();

const props = defineProps<Props>();

const visible = defineModel<boolean>("visible", {
  default: false,
});

function closeModal() {
  visible.value = false;
}

const title = computed(
  () => $t("common.edit") + $t("page.manage.role.menuAuth"),
);

const tree = shallowRef<GeneratedRoute[]>([]);

const authStore = useAuthStore();

const authRoutes = computed(() => authStore.userInfo.routes);

function transformRoutesToTree(routes: GeneratedRoute[]) {
  return routes
    .filter((route) => !route.meta?.hideInMenu)
    .map((route) => {
      const node: any = {
        id: route.meta?.parentId
          ? `${route.meta?.parentId}-${route.meta?.id}`
          : route.meta?.id + "",
        parentId: route.meta?.parentId,
        label: route.meta?.i18nKey
          ? $t(route.meta.i18nKey)
          : route.meta?.title || route.name,
        children: route.children
          ? transformRoutesToTree(route.children)
          : undefined,
      };
      return node;
    });
}

async function getTree() {
  const userInfo = JSON.parse(localStg.get("userInfo"));
  console.log("userInfo", userInfo);

  const filterRoutes = filterAuthRoutesByRoutes(
    myRoutes,
    authRoutes.value || "",
  );
  const routes = ["box777", "admin"].includes(userInfo.user_name)
    ? myRoutes
    : filterRoutes;
  console.log(routes);
  tree.value = transformRoutesToTree(routes);
  console.log("tree", tree.value);
}

const checks = shallowRef<string[]>([]);

function checkChange(data: any, checked: boolean) {
  const idx = checks.value.indexOf(data.id);
  if (checked && idx === -1) {
    checks.value.push(data.id);
  } else if (!checked && idx !== -1) {
    checks.value.splice(idx, 1);
  }
}

function getRouteIds(selectedIds: any[]) {
  return selectedIds.filter((id) => id.includes("-"));
}

function handleSubmit() {
  const routeIds = getRouteIds(checks.value);
  emit("routeIds", routeIds);
  closeModal();
}

const treeRef = ref();

async function init() {
  await getTree();
  // 处理路由ID
  if (props.routes) {
    const routeIds = props.routes.split(",");
    routeIds.forEach((routeId) => {
      if (routeId) {
        checks.value.push(routeId);
      }
    });
    // 等待树渲染完成后再设置选中状态
    nextTick(() => {
      if (treeRef.value) {
        treeRef.value.setCheckedKeys(checks.value);
      }
    });
  }
}

watch(visible, (val) => {
  if (val) {
    // 清空之前的选中状态
    checks.value = [];
    init();
  }
});
</script>

<template>
  <ElDialog v-model="visible" :title="title" preset="card" class="w-480px">
    <div class="tree-container">
      <ElTree
        ref="treeRef"
        :data="tree"
        node-key="id"
        show-checkbox
        class="h-full"
        :default-checked-keys="checks"
        default-expand-all
        expand-on-click-node
        @check-change="checkChange"
      />
    </div>
    <template #footer>
      <ElSpace class="w-full justify-end">
        <ElButton size="small" class="mt-16px" @click="closeModal">
          {{ $t("common.cancel") }}
        </ElButton>
        <ElButton
          type="primary"
          size="small"
          class="mt-16px"
          @click="handleSubmit"
        >
          {{ $t("common.confirm") }}
        </ElButton>
      </ElSpace>
    </template>
  </ElDialog>
</template>

<style scoped>
.tree-container {
  height: 400px;
  overflow-y: auto;
  border-radius: 4px;
  padding: 8px;
}

:deep(.el-tree) {
  min-width: 100%;
  display: inline-block;
}
</style>
