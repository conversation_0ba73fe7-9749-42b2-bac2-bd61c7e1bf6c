@font-face {
  font-family: 'Inter';
  font-weight: 100 900;
  font-display: block;
  font-style: normal;
  font-named-instance: Regular;
  src: url(/assets/fonts/Inter.woff2) format('woff2');
}

@font-face {
  font-family: 'Inter';
  font-weight: 100 900;
  font-display: block;
  font-style: italic;
  font-named-instance: 'Italic';
  src: url(/assets/fonts/Inter-italic.woff2) format('woff2');
}

@font-face {
  font-family: 'Plus Jakarta Sans';
  font-style: normal;
  font-weight: 200 800;
  font-display: fallback;
  src: url(/assets/fonts/PlusJakartaSans-latin.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215,
    U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Plus Jakarta Sans';
  font-style: italic;
  font-weight: 200 800;
  font-display: fallback;
  src: url(/assets/fonts/PlusJakartaSans-italic.woff2) format('woff2');
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB,
    U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: 'Fira Code';
  font-style: normal;
  font-weight: 300 700;
  font-display: swap;
  src: url(/assets/fonts/FiraCode.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA,
    U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215,
    U+FEFF, U+FFFD;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --docs-color-primary-100: 26 144 255;
    --docs-color-primary-200: 33 96 253;

    --docs-color-secondary-700: 232 232 236;
    --docs-color-secondary-800: 245 245 247;
    --docs-color-secondary-900: 251 251 251;
    --docs-color-secondary-1000: 255 255 255;

    --docs-color-text-400: 71 71 71;
  }

  html[data-theme='dark'] {
    --docs-color-text-400: 153 153 153;

    --docs-color-secondary-700: 71 71 71;
    --docs-color-secondary-800: 38 38 38;
    --docs-color-secondary-900: 25 25 25;
    --docs-color-secondary-1000: 0 0 0;
  }

  body {
    @apply font-sans antialiased;
  }

  .homepage a {
    --ifm-link-hover-color: currentColor;
  }

  .no-underline-links {
    --ifm-link-hover-decoration: none;
  }

  h1,
  h2,
  h3,
  h4,
  h5 {
    @apply font-jakarta;
  }

  .homepage section,
  .homepage section * {
    @apply box-border;
  }
}

/* .faq-page details summary::-webkit-details-marker,
.faq-page details summary::marker {
  display: none;
  width: 0;
  height: 0;
  list-style: none;
  margin: 0;
}

.faq-page details > summary {
  list-style: none;
} */

.accordion-content *:last-child {
  @apply mb-0;
}

.dyte-accordion img {
  @apply rounded-lg;
}

@layer components {
  .noise-bg {
    background-image: url('/static/landing-page/grid-light.svg'),
      url('/static/landing-page/noise-light.png'),
      linear-gradient(
        to bottom,
        transparent 40%,
        rgb(var(--docs-color-secondary-700))
      );
    background-size: 48px 48px, 100% 100%, 100%;
  }

  html[data-theme='dark'] .noise-bg {
    background-image: url('/static/landing-page/grid-dark.svg'),
      url('/static/landing-page/noise-dark.png'),
      linear-gradient(
        to bottom,
        transparent 40%,
        rgb(var(--docs-color-secondary-900))
      );
  }

  /* .sdk-section {
    min-height: 320px;
    transition-duration: 1.3s;
    transition-timing-function: cubic-bezier(0.165, 0.84, 0.44, 1);
    transition-property: transform opacity;
    opacity: 0;
    transform: translateX(-400px);
  }

  .intersected {
    opacity: 1;
    transform: translateX(0px);
  } */

  .unipass-badge {
    @apply mb-4 inline-block rounded-sm bg-zinc-200 py-1 px-2 text-text-400 dark:bg-zinc-800;
  }
}

/* Fonts */

pre,
code {
  font-variant-ligatures: none;
}

/* UI Kit tokens */
body {
  --dyte-colors-video-bg: 50 50 50;
}

/* Dyte Docs Tokens */
:root {
  --docs-color-primary: #2160fd;
  /* --docs-color-primary-100: #1a90ff; */
  --docs-color-primary-tint: rgba(33, 96, 253, 0.16);
  --docs-color-primary-tint-light: rgba(33 96 253/0.24);

  --docs-color-border: #dadde1;

  --docs-color-text: #000000;
  --docs-color-text-100: #646464;

  --docs-color-background: #ffffff;
  --docs-color-background-100: #f8f8f8;
  --docs-color-background-200: #efefef;
  --docs-color-background-300: #dcdcdc;

  /* Color from prism themes */
  --docs-color-code-background: #f6f8fa;

  --docs-color-android: #44db85;
  --docs-color-apple: var(--docs-color-text) !important;
}

[data-theme='dark'] {
  --docs-color-border: #2e2e2e;

  --docs-color-text: #ffffff;
  --docs-color-text-100: #b4b4b4;

  --docs-color-background: #161616;
  --docs-color-background-100: #1c1c1c;
  --docs-color-background-200: #2a2a2a;
  --docs-color-background-300: #2e2e2e;

  --docs-color-code-background: #1e1e1e;
}

/* Docusaurus Theming */

:root {
  /* Default values */

  --ifm-spacing-horizontal: 1.5rem;
  --ifm-navbar-padding-horizontal: 0.75rem;

  --ifm-font-family-base: 'Inter', system-ui, -apple-system, Segoe UI, Roboto,
    Ubuntu, Cantarell, Noto Sans, sans-serif, BlinkMacSystemFont, 'Segoe UI',
    Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol';

  --ifm-font-family-monospace: 'Fira Code', SFMono-Regular, Menlo, Monaco,
    Consolas, 'Liberation Mono', 'Courier New', monospace;

  /* Theme colors */
  --ifm-color-primary: #2160fd;
  --ifm-color-primary-dark: #1959fc;
  --ifm-color-primary-darker: #0f51f8;
  --ifm-color-primary-darkest: #0042e7;
  --ifm-color-primary-light: #3d72f8;
  --ifm-color-primary-lighter: #477af8;
  --ifm-color-primary-lightest: #6791fc;

  --ifm-navbar-shadow: none;
  --ifm-toc-border-color: #dedede;

  --ifm-table-border-color: var(--docs-color-border);
  --code-border-color: var(--docs-color-border);

  --ifm-code-font-size: 92%;
  --docusaurus-highlighted-code-line-bg: rgba(147, 178, 244, 0.38);
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
html[data-theme='dark'] {
  --ifm-link-color: #1a90ff;
  --ifm-tabs-color-active-border: #1a90ff;
  --ifm-tabs-color-active: #1a90ff;

  --ifm-color-primary: #1a90ff;

  --ifm-footer-background-color: #1c1c1c;
  --ifm-background-surface-color: #161616;
  --ifm-background-color: #161616;
  --ifm-toc-border-color: #2e2e2e;

  --ifm-color-content: #e7e7e7;

  --docusaurus-highlighted-code-line-bg: rgba(105, 105, 105, 0.3);
}

nav.navbar {
  border-bottom: 1px solid var(--ifm-toc-border-color);
}

pre.prism-code {
  border: 1px solid var(--code-border-color);
}

.navbar__item,
.menu__link {
  font-size: 14px;
}

.menu__link {
  border-radius: 4px 0 0 4px;
}

.menu__list-item-collapsible:hover {
  background: none;
}

ul.menu__list > li > a.menu__link--active {
  border-right: 1px solid var(--ifm-color-primary);
}

nav.menu {
  padding-right: 0;
  scrollbar-gutter: auto;
}

.new-badge::after,
.deprecated-badge::after {
  font-size: 11px;
  @apply inline-flex items-center justify-center rounded-sm;
  @apply ml-1.5 px-1 py-0;
}

.new-badge::after {
  content: 'NEW';
  @apply bg-blue-100 text-blue-900;
  @apply dark:bg-blue-900 dark:text-blue-100;
}

.sidebar-menu .new-badge::after {
  @apply border border-solid border-blue-400 bg-blue-100 text-blue-900;
}

.deprecated-badge::after {
  content: 'DEPRECATED';
  @apply bg-red-100 text-red-900;
}

.footer__description {
  @apply text-text-400;
  font-size: 14px;
}

.footer__copyright {
  margin-top: 32px;
  font-size: 14px;
  @apply text-text-400;
}

.footer__row {
  display: flex;
}

.footer__cta {
  padding-right: 24px;
  font-size: 14px;
}

.footer__cta p {
  margin: 0;
}

.footer__cta a {
  --ifm-link-hover-color: #fff;
  margin-top: 0.25rem;
  display: inline-block;
  padding: 0.25rem 1.5rem;
  border-radius: 4px;
  background-color: theme('colors.primary.200');
  color: #ffffff;
  text-decoration: none;
}

.footer__cta a:hover {
  background-color: var(--ifm-color-primary-darker);
}

.footer__data {
  flex: 2;
}

.footer__row .links {
  flex: 3;
}

.footer__title {
  font-size: 14px;
  font-weight: normal;
  @apply mb-2 text-text-400;
}

.footer__item {
  font-size: 14px;
  font-weight: normal;
}

.footer__link-item:hover {
  text-decoration: none;
}

.navbar-sidebar__item {
  padding-right: 0;
}

.navbar-sidebar__back {
  background: none;
  font-size: 14px;
}

.ping::after {
  display: inline-block;
  content: '';
  background: theme('colors.primary.200');
  width: 4px;
  height: 4px;
  border-radius: 4px;
  margin-left: 4px;
}

.dev-portal-link {
  background-color: theme('colors.primary.200');
  border-radius: 4px;
  transition-property: all;
  padding: 0.25rem 0.75rem !important;
  margin: 0 0.5rem;
}

.dev-portal-link:last-child {
  margin-right: 0;
}

.dev-portal-link svg {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 0.2rem;
}

.dev-portal-signup {
  --ifm-link-hover-color: #fff;
  --ifm-navbar-link-hover-color: #fff;
  color: #fff;
}

.dev-portal-signup svg {
  display: none;
}

.dev-portal-signup:hover {
  background-color: var(--ifm-color-primary-darker);
}

.dev-portal-login {
  --ifm-navbar-link-hover-color: theme('colors.primary.100');
  background-color: transparent;
}

.navbar-sidebar .dev-portal-link {
  padding: 0.75rem;
  margin-top: 1rem;
}

/* Custom design for sidebar hide and expand buttons */

aside.theme-doc-sidebar-container {
  position: relative;
  clip-path: inset(0px -140px);
}

.theme-doc-sidebar-container button[title='Collapse sidebar'],
.theme-doc-sidebar-container div[title='Expand sidebar'] {
  position: absolute;
  top: 96px;
  right: -14px;
  width: 28px;
  height: 28px;
  padding: 0;
  border-radius: 4px;
  z-index: 10;
  background-size: 16px;
  background-position: center;
  background-repeat: no-repeat;
  background-color: var(--docs-color-border);

  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' fill='none' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15.707 4.293a1 1 0 0 1 0 1.414L9.414 12l6.293 6.293a1 1 0 0 1-1.414 1.414l-7-7a1 1 0 0 1 0-1.414l7-7a1 1 0 0 1 1.414 0Z' fill='%23181818'/%3E%3C/svg%3E");
}

.theme-doc-sidebar-container div[title='Expand sidebar'] {
  position: sticky;
  margin-left: 16px;
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' fill='none' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.293 4.293a1 1 0 0 0 0 1.414L14.586 12l-6.293 6.293a1 1 0 1 0 1.414 1.414l7-7a1 1 0 0 0 0-1.414l-7-7a1 1 0 0 0-1.414 0Z' fill='%23181818'/%3E%3C/svg%3E");
}

html[data-theme='dark']
  .theme-doc-sidebar-container
  button[title='Collapse sidebar'] {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' fill='none' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15.707 4.293a1 1 0 0 1 0 1.414L9.414 12l6.293 6.293a1 1 0 0 1-1.414 1.414l-7-7a1 1 0 0 1 0-1.414l7-7a1 1 0 0 1 1.414 0Z' fill='%23ffffff'/%3E%3C/svg%3E");
}

html[data-theme='dark']
  .theme-doc-sidebar-container
  div[title='Expand sidebar'] {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' fill='none' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M8.293 4.293a1 1 0 0 0 0 1.414L14.586 12l-6.293 6.293a1 1 0 1 0 1.414 1.414l7-7a1 1 0 0 0 0-1.414l-7-7a1 1 0 0 0-1.414 0Z' fill='%23ffffff'/%3E%3C/svg%3E");
}

.theme-doc-sidebar-container
  :is(div[title='Expand sidebar'], button[title='Collapse sidebar'])
  svg {
  display: none;
}

.sections-menu-trigger {
  /* all: unset; */
  flex: 1;
  display: inline-flex;
  background-color: var(--docs-color-background-100);
  color: var(--docs-color-text);
  height: 48px;
  border-radius: 6px;
  align-items: center;
  justify-content: space-between;
  text-align: left;

  box-sizing: border-box;
  outline: none;

  cursor: pointer;

  box-sizing: border-box;
  border: 1px solid var(--docs-color-background-200);
}

.sections-menu-trigger:hover {
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
}

.sections-menu-trigger,
.sections-menu-item {
  padding: 0 15px;
  padding-left: 10px;
}

.sections-menu-scrollButton {
  height: 16px;
  width: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 25px;
  color: var(--docs-color-text);
  cursor: default;
}

.sections-menu-item {
  box-sizing: border-box;
  min-width: 200px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  outline: none;

  cursor: pointer;
}

.sections-menu-item:hover {
  background-color: var(--docs-color-background-200);
}

.sections-menu-item[data-disabled] {
  cursor: not-allowed;
  background-color: var(--docs-color-border);
}

.sections-menu-trigger .item-indicator,
.sections-menu-item .item-indicator {
  height: 20px;
  width: 20px;
  color: theme('colors.primary.100');
}

.sections-menu-trigger .item-text,
.sections-menu-item .item-text {
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 1.2;
}

.sections-menu-trigger .item-text > svg,
.sections-menu-item .item-text > svg {
  height: 32px;
  width: 32px;
  margin-right: 8px;
}

.sections-menu-content {
  box-sizing: border-box;
  background-color: var(--docs-color-background-100);
  border-radius: 6px;
  padding: 6px 0;

  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
}

.sections-menu-trigger.compact {
  min-width: 5rem;
  height: 2.5rem;
}

.sections-menu-content.compact {
  width: 5rem;
  min-width: 5rem;
}

.compact .sections-menu-item {
  min-width: initial;
  height: 2.5rem;
  width: auto;
}

.loading-container {
  width: 100%;
  height: 100%;
  display: flex;
  place-items: center;
  justify-content: center;
}

.loading-container dyte-spinner {
  width: 48px;
  height: 48px;
  color: theme('colors.primary.100');
}

.dyte-video-showcase {
  margin: 3rem auto;
  display: block;
  width: 100%;
  max-width: 80%;
  border-radius: 8px;
}

.dyte-video-showcase.mobile {
  max-width: 240px;
  border-radius: 1.5rem;
}

img.cover-image {
  display: block;
  width: 100%;
  max-width: 840px;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
  margin: 0 auto;
}

ul.emoji-list {
  list-style-type: none;
  padding-left: 12px;
}

ul.emoji-list li {
  display: flex;
  align-items: flex-start;
  line-height: 32px;
}

ul.emoji-list span {
  display: inline-block;
  margin-right: 12px;
  font-size: 28px;
}

/* Disables link hover decoration enabled by default in docusaurus v2.0.0-beta.22 */
.navbar__link,
.menu__link,
.table-of-contents__link,
.pagination-nav,
.footer {
  --ifm-link-hover-decoration: none;
}

.menu__link {
  /* Background tint only for menu links in sidebar */
  --ifm-menu-color-background-active: var(--docs-color-primary-tint);
}

table {
  border-collapse: collapse;
  border: none;
  background-color: transparent;

  text-align: left;
  font-size: 90%;
}

table :is(tr, td, th, thead) {
  --ifm-table-stripe-background: transparent;
  --table-cell-color: transparent;
  border: none;
  background-color: transparent;
  border-bottom: 1px solid var(--docs-color-border);
}

table thead tr {
  border-bottom: 1px solid var(--docs-color-border);
}

table th {
  font-weight: 500;
  font-size: 14px;
  color: var(--docs-color-text-100);
}

.tabs {
  border-bottom: 1px solid var(--docs-color-border);
}

.tabs__item {
  --ifm-tabs-padding-vertical: 0.75rem;
  --ifm-tabs-color-active: theme('colors.primary.100');
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.tabs__item--active {
  border-bottom: 2px solid theme('colors.primary.100');
}

/* Mobile breakpoint */
@media only screen and (max-width: 996px) {
  :root {
    --ifm-spacing-horizontal: 1rem;
  }

  .footer__row {
    flex-direction: column;
  }

  .footer__data {
    margin-bottom: 3rem;
  }
}

code[data-code='required'] {
  font-size: 12px;
  background-color: var(--ifm-color-danger-contrast-background);
  color: var(--ifm-color-danger);
}

.dropdown > .navbar__link {
  display: flex;
  align-items: center;
}

.dropdown > .navbar__link:after {
  content: '';
  border: none;
  position: static;
  top: auto;
  transform: none;
  width: 12px;
  height: 12px;
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' fill='none' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4.293 8.293a1 1 0 0 1 1.414 0L12 14.586l6.293-6.293a1 1 0 1 1 1.414 1.414l-7 7a1 1 0 0 1-1.414 0l-7-7a1 1 0 0 1 0-1.414Z' fill='%23888888'/%3E%3C/svg%3E");
  background-size: 12px;
  background-repeat: no-repeat;
}

html[data-theme='dark'] .dropdown > .navbar__link:after {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' fill='none' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4.293 8.293a1 1 0 0 1 1.414 0L12 14.586l6.293-6.293a1 1 0 1 1 1.414 1.414l-7 7a1 1 0 0 1-1.414 0l-7-7a1 1 0 0 1 0-1.414Z' fill='%23ffffff'/%3E%3C/svg%3E");
}

.dropdown:hover > .navbar__link:after {
  background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' fill='none' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4.293 8.293a1 1 0 0 1 1.414 0L12 14.586l6.293-6.293a1 1 0 1 1 1.414 1.414l-7 7a1 1 0 0 1-1.414 0l-7-7a1 1 0 0 1 0-1.414Z' fill='%231a90ff'/%3E%3C/svg%3E") !important;
}

.unipass-dropdown + ul.dropdown__menu {
  @apply bg-secondary-900 p-0;
}

.unipass-dropdown .sdk-link {
  @apply flex items-center gap-1.5 rounded-md p-2 text-inherit hover:bg-secondary-700;
  @apply text-xs;
}

.unipass-dropdown .sdk-link > img {
  @apply h-6 w-6;
}

.unipass-dropdown a {
  @apply no-underline;
}

.unipass-dropdown :is(h1, h2, h3, h4, h5) {
  @apply font-medium;
}

.unipass-dropdown h3 {
  @apply !font-bold;
}

.sdks-dropdown h2 {
  @apply text-lg;
}

.sdks-dropdown h3 {
  @apply text-sm;
}

.resources-dropdown-menu h2 {
  @apply text-sm font-semibold;
}

[data-dropdown-sdks] {
  display: none;
}

body:not([data-sdk-menu]) [data-dropdown-sdks='web'] {
  display: flex;
}

body[data-sdk-menu='web'] [data-dropdown-sdks='web'] {
  display: flex;
}

body[data-sdk-menu='mobile'] [data-dropdown-sdks='mobile'] {
  display: flex;
}

body[data-sdk-menu='plugin'] [data-dropdown-sdks='plugin'] {
  display: flex;
}

[data-dropdown-sdks-menu] {
  @apply border-0 border-l-4 border-solid border-transparent pl-4;
}

body[data-sdk-menu='web'] [data-dropdown-sdks-menu='web'],
body[data-sdk-menu='mobile'] [data-dropdown-sdks-menu='mobile'],
body[data-sdk-menu='plugin'] [data-dropdown-sdks-menu='plugin'] {
  @apply border-l-primary;
}

.resources-dropdown-menu a {
  @apply flex items-center gap-2;
}

/* To disable showing search icon in Safari mobile */
input[type='search']::-webkit-search-decoration,
input[type='search']::-webkit-search-cancel-button,
input[type='search']::-webkit-search-results-button,
input[type='search']::-webkit-search-results-decoration {
  display: none;
}

/**
 Image caption, use in markdown as:
 ![Image ALT](./path/to/image)
 _my image caption_
 */
img + em {
  text-align: center;
  display: block;
  margin-top: 1rem;
}

/**
  Style for a terminal image, markdown usage (add #terminal to end of url):
  ![Image ALT](./path/to/image#terminal)
*/
img[src$='#terminal'] {
  display: block;
  max-width: 720px;
  margin-left: auto;
  margin-right: auto;
}

.pad {
  padding: 0 1rem;
}

.center {
  width: 100%;
  max-width: 1080px;
  margin: 1rem auto;
  min-height: 400px;
}

.homepage-content {
  padding-bottom: 6rem;
}

#hero {
  padding: 2rem 0 1.5rem 0;
  margin-bottom: 4rem;
  border-bottom: 1px solid var(--docs-color-border);
}

#hero h2 {
  font-size: 36px;
}

#hero p {
  color: var(--docs-color-text-100);
}

.section-content {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  gap: 12px;
}

.two-cols .section-content {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.has-sub-sections > h3 {
  margin-bottom: 1.5rem;
}

.has-sub-sections > .section-content {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.has-sub-sections > .section-content > .homepage-section {
  margin-bottom: 1rem;
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.homepage-section {
  margin-bottom: 3rem;
}

.homepage-section h3 {
  font-weight: 600;
}

.section-description {
  color: var(--docs-color-text-100);
  margin: 0rem 0 1.25rem 0;
  margin-top: -0.5rem;
}

.has-sub-sections > .section-content .section-description {
  font-size: 14px;
}

.homepage-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  padding: 0.75rem;
  text-decoration: none;
  color: var(--docs-color-text);

  --ifm-link-hover-decoration: none;
  --ifm-link-hover-color: inherit;
  cursor: pointer;

  transition-property: background-color, color;

  border: 1px solid var(--docs-color-border);
  border-radius: 8px;
}

.homepage-card:hover {
  background-color: var(--docs-color-background-100);
}

.icon svg {
  width: auto;
  height: 100%;
}

.homepage-card .icon {
  width: 48px;
  height: 48px;
  /* background-color: #262626;
  border-radius: 8px; */
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.card-content .title {
  font-size: 16px;
  letter-spacing: -0.5px;
  font-weight: 600;
}

.card-content .description {
  font-size: 14px;
  color: var(--docs-color-text-100);
  line-height: 1.5;
}

details {
  font-size: 14px;
  font-weight: 500;
  color: var(--docs-color-text-100) !important;
  background: var(--docs-color-background-100) !important;
  border: solid 2px var(--docs-color-code-background) !important;
}

details code {
  background-color: var(--docs-color-background);
}

details p {
  font-weight: 300;
}

@media screen and (max-width: 1160px) {
  /* Hide icons when header UI breaks */
  .pseudo-icon {
    display: none;
  }
}

@media screen and (max-width: 768px) {
  .section-content {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .two-cols .section-content {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

/* Overriding Docusaurus styles */
.collapsibleContent_node_modules-\@docusaurus-theme-common-lib-components-Details-styles-module {
  border-top: 1px solid var(--code-border-color) !important;
}

.details_node_modules-\@docusaurus-theme-common-lib-components-Details-styles-module
  > summary::before {
  border-color: transparent transparent transparent var(--docs-color-text-100) !important;
}
@media only screen and (min-width: 768px) {
  img[alt='PresetEditor'] {
    max-width: 600px;
  }
  img[alt='APIKeys'] {
    max-width: 600px;
  }
}

.navbar-book-demo svg {
  display: none;
}

.theme-doc-markdown.markdown {
  @apply mt-4;
}

.code-block-error-line {
  background-color: #ff000020;
  display: block;
  margin: 0 calc(-1 * var(--ifm-pre-padding));
  padding: 0 var(--ifm-pre-padding);
  border-left: 3px solid #ff000080;
}

.notesContainer h4 {
  @apply mb-8;
}

.releaseNotes ul {
  @apply mb-0;
}

.notesContainer .type {
  width: 11rem;
  border-right: 2px solid;
  @apply border-b-0 align-top font-semibold text-text-400;
}

@media screen and (max-width: 768px) {
  .notesContainer .type {
    width: 6rem;
  }
}

.notesContainer .deprecated .type {
  color: #de4e2b;
}

.notesContainer .deprecated .sublist {
  background-color: #e01e5a1a;
}

.notesContainer .newAPI .type {
  color: #1264a3;
}

.notesContainer .newAPI .sublist {
  background-color: #1264a31a;
}

.sublist {
  @apply py-0;
}

.sublist tr {
  @apply py-1;
}

.sublist tr:last-child > td {
  @apply border-0;
}

.sublist tr:last-child {
  @apply border-0;
}

.changeline p {
  @apply mb-0;
}

.releaseSidebarHeading {
  @apply !mt-2 border-t-secondary-700 pt-1;
  border-top-style: solid;
  border-top-width: 1px;
}

.hideReferenceSidebarLink {
  display: none;
}

.DocSearch-Button {
  @apply lg:!rounded-md;
}

.DocSearch-Button-Key {
  background: theme(colors.secondary.800) !important;
  top: 0 !important;
  padding: 0 !important;
  @apply !border !border-solid !border-text-400/30;
  @apply !shadow-none;
  @apply flex items-center;
}

.DocSearch-Modal {
  /* after introducting ai chat bot */
  margin-top: 80px !important;
}

.DocSearch-Button-Placeholder {
  @apply !text-xs;
}

article ol {
  list-style-type: decimal !important;
}

article ul {
  list-style-type: disc;
}


/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #2e8555;
  --ifm-color-primary-dark: #29784c;
  --ifm-color-primary-darker: #277148;
  --ifm-color-primary-darkest: #205d3b;
  --ifm-color-primary-light: #33925d;
  --ifm-color-primary-lighter: #359962;
  --ifm-color-primary-lightest: #3cad6e;
  --ifm-code-font-size: 95%;
  font-family: sans-serif;
  font-weight: 400;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.1);
}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme='dark'] {
  --ifm-color-primary: #25c2a0;
  --ifm-color-primary-dark: #21af90;
  --ifm-color-primary-darker: #1fa588;
  --ifm-color-primary-darkest: #1a8870;
  --ifm-color-primary-light: #29d5b0;
  --ifm-color-primary-lighter: #32d8b4;
  --ifm-color-primary-lightest: #4fddbf;
    --ifm-code-font-size: 95%;
  font-family: sans-serif;
  font-weight: 400;
  --docusaurus-highlighted-code-line-bg: rgba(0, 0, 0, 0.3);
}

.boxContainer {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
  grid-gap: 1rem;
  margin-bottom: 1rem;
}

.box {
  min-height: 33%;
  border: 1px solid var(--ifm-color-gray-600);
  border-radius: 14px;
  padding-inline: 1em;
  padding-top: 1.3em;
  cursor: pointer;
}

.box:hover {
  border-color: var(--ifm-color-primary);
}

.box:hover h1 {
  color: var(--ifm-color-primary);
}

.box:hover p {
  color: var(--ifm-color-primary);
}

@media only screen and (min-width: 660px) {
  .boxContainer {
    grid-template-columns: repeat(3, minmax(33%, 1fr));
  }
}

@media only screen and (min-width: 900px) {
  .boxContainer {
    grid-template-columns: repeat(2, minmax(50%, 1fr));
  }
}

@media only screen and (min-width: 1100px) {
  .boxContainer {
    grid-template-columns: repeat(3, minmax(33%, 1fr));
  }
}

/* social media icon */
.header-twitter-link:hover {
  opacity: 0.6;
}

.header-twitter-link:before {
  content: '';
  width: 20px;
  height: 20px;
  display: flex;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M24 4.37a9.6 9.6 0 0 1-2.83.8 5.04 5.04 0 0 0 2.17-2.8c-.95.58-2 1-3.13 1.22A4.86 4.86 0 0 0 16.61 2a4.99 4.99 0 0 0-4.79 6.2A13.87 13.87 0 0 1 1.67 2.92 5.12 5.12 0 0 0 3.2 9.67a4.82 4.82 0 0 1-2.23-.64v.07c0 2.44 1.7 4.48 3.95 4.95a4.84 4.84 0 0 1-2.22.08c.63 2.01 2.45 3.47 4.6 3.51A9.72 9.72 0 0 1 0 19.74 13.68 13.68 0 0 0 7.55 22c9.06 0 14-7.7 14-14.37v-.65c.96-.71 1.79-1.6 2.45-2.61z'/%3E%3C/svg%3E")
    no-repeat;
}

html[data-theme='dark'] .header-twitter-link:before {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='white' d='M24 4.37a9.6 9.6 0 0 1-2.83.8 5.04 5.04 0 0 0 2.17-2.8c-.95.58-2 1-3.13 1.22A4.86 4.86 0 0 0 16.61 2a4.99 4.99 0 0 0-4.79 6.2A13.87 13.87 0 0 1 1.67 2.92 5.12 5.12 0 0 0 3.2 9.67a4.82 4.82 0 0 1-2.23-.64v.07c0 2.44 1.7 4.48 3.95 4.95a4.84 4.84 0 0 1-2.22.08c.63 2.01 2.45 3.47 4.6 3.51A9.72 9.72 0 0 1 0 19.74 13.68 13.68 0 0 0 7.55 22c9.06 0 14-7.7 14-14.37v-.65c.96-.71 1.79-1.6 2.45-2.61z'/%3E%3C/svg%3E")
    no-repeat;
}

.header-github-link:hover {
  opacity: 0.6;
}

.header-github-link:before {
  content: '';
  width: 20px;
  height: 20px;
  display: flex;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12'/%3E%3C/svg%3E")
    no-repeat;
}

html[data-theme='dark'] .header-github-link:before {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='white' d='M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12'/%3E%3C/svg%3E")
    no-repeat;
}

.header-discord-link:hover {
  opacity: 0.6;
}

.header-discord-link:before {
  content: '';
  width: 20px;
  height: 20px;
  display: flex;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13.545 2.907a13.227 13.227 0 0 0-3.257-1.011.05.05 0 0 0-.052.025c-.141.25-.297.577-.406.833a12.19 12.19 0 0 0-3.658 0 8.258 8.258 0 0 0-.412-.833.051.051 0 0 0-.052-.025c-1.125.194-2.22.534-3.257 1.011a.041.041 0 0 0-.021.018C.356 6.024-.213 9.047.066 12.032c.001.014.01.028.021.037a13.276 13.276 0 0 0 3.995 2.02.05.05 0 0 0 .056-.019c.308-.42.582-.863.818-1.329a.05.05 0 0 0-.01-.059.051.051 0 0 0-.018-.011 8.875 8.875 0 0 1-1.248-.595.05.05 0 0 1-.02-.066.051.051 0 0 1 .015-.019c.084-.063.168-.129.248-.195a.05.05 0 0 1 .051-.007c2.619 1.196 5.454 1.196 8.041 0a.052.052 0 0 1 .053.007c.08.066.164.132.248.195a.051.051 0 0 1-.004.085 8.254 8.254 0 0 1-1.249.594.05.05 0 0 0-.03.03.052.052 0 0 0 .003.041c.24.465.515.909.817 1.329a.05.05 0 0 0 .056.019 13.235 13.235 0 0 0 4.001-2.02.049.049 0 0 0 .021-.037c.334-3.451-.559-6.449-2.366-9.106a.034.034 0 0 0-.02-.019Zm-8.198 7.307c-.789 0-1.438-.724-1.438-1.612 0-.889.637-1.613 1.438-1.613.807 0 1.45.73 1.438 1.613 0 .888-.637 1.612-1.438 1.612Zm5.316 0c-.788 0-1.438-.724-1.438-1.612 0-.889.637-1.613 1.438-1.613.807 0 1.451.73 1.438 1.613 0 .888-.631 1.612-1.438 1.612Z'/%3E%3C/svg%3E")
    no-repeat;
}

html[data-theme='dark'] .header-discord-link:before {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='white' d='M13.545 2.907a13.227 13.227 0 0 0-3.257-1.011.05.05 0 0 0-.052.025c-.141.25-.297.577-.406.833a12.19 12.19 0 0 0-3.658 0 8.258 8.258 0 0 0-.412-.833.051.051 0 0 0-.052-.025c-1.125.194-2.22.534-3.257 1.011a.041.041 0 0 0-.021.018C.356 6.024-.213 9.047.066 12.032c.001.014.01.028.021.037a13.276 13.276 0 0 0 3.995 2.02.05.05 0 0 0 .056-.019c.308-.42.582-.863.818-1.329a.05.05 0 0 0-.01-.059.051.051 0 0 0-.018-.011 8.875 8.875 0 0 1-1.248-.595.05.05 0 0 1-.02-.066.051.051 0 0 1 .015-.019c.084-.063.168-.129.248-.195a.05.05 0 0 1 .051-.007c2.619 1.196 5.454 1.196 8.041 0a.052.052 0 0 1 .053.007c.08.066.164.132.248.195a.051.051 0 0 1-.004.085 8.254 8.254 0 0 1-1.249.594.05.05 0 0 0-.03.03.052.052 0 0 0 .003.041c.24.465.515.909.817 1.329a.05.05 0 0 0 .056.019 13.235 13.235 0 0 0 4.001-2.02.049.049 0 0 0 .021-.037c.334-3.451-.559-6.449-2.366-9.106a.034.034 0 0 0-.02-.019Zm-8.198 7.307c-.789 0-1.438-.724-1.438-1.612 0-.889.637-1.613 1.438-1.613.807 0 1.45.73 1.438 1.613 0 .888-.637 1.612-1.438 1.612Zm5.316 0c-.788 0-1.438-.724-1.438-1.612 0-.889.637-1.613 1.438-1.613.807 0 1.451.73 1.438 1.613 0 .888-.631 1.612-1.438 1.612Z'/%3E%3C/svg%3E")
    no-repeat;
}

.header-contact-link:hover {
  opacity: 0.6;
}

.header-contact-link:before {
  content: '';
  width: 20px;
  height: 20px;
  display: flex;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M6.85728,2.44489 C7.99928,3.27790429 8.88915755,4.41552061 9.65001761,5.50316536 L10.0920364,6.14691904 L10.0920364,6.14691904 L10.509,6.76166 L10.509,6.76166 C10.9374,7.38835 10.8351,8.244 10.2531,8.74772 L8.30198,10.1967 C8.10859,10.3404 8.04429,10.6014 8.16028,10.8125 C8.60173,11.6161 9.38819,12.8119 10.2882,13.7119 C11.1891,14.6128 12.4414,15.45 13.3002,15.9412 C13.5229,16.0685 13.803,15.9948 13.9438,15.7803 L15.2131,13.8468 C15.6999,13.1991 16.6088,13.0576 17.2695,13.5149 L17.9332982,13.9735916 C19.1717645,14.8335207 20.5037538,15.8105615 21.521,17.1133 C21.8626,17.5507 21.9133,18.1227 21.7096,18.5981 C20.8728,20.5507 18.7552,22.2136 16.5524,22.1325 L16.2518759,22.1158001 L16.2518759,22.1158001 L16.0189256,22.0957065 L16.0189256,22.0957065 L15.7611336,22.0668244 L15.7611336,22.0668244 L15.4795621,22.0277651 L15.4795621,22.0277651 L15.1752731,21.97714 C15.1227241,21.9676615 15.0692729,21.9576432 15.0149414,21.9470562 L14.6785676,21.8764784 C14.6208039,21.8635009 14.5622043,21.8498968 14.5027909,21.8356372 L14.136722,21.7419821 L14.136722,21.7419821 L13.7521839,21.6312063 L13.7521839,21.6312063 L13.3502388,21.501921 C11.5039131,20.8764078 9.16110938,19.6464875 6.75735,17.2427 C4.35356813,14.8389125 3.12365344,12.4961028 2.49813876,10.6497861 L2.36885301,10.2478433 L2.36885301,10.2478433 L2.25807648,9.86330795 L2.25807648,9.86330795 L2.16442042,9.4972422 L2.16442042,9.4972422 L2.08649611,9.15070812 C2.06298899,9.03857004 2.04187237,8.92986425 2.02291481,8.82476776 L1.97228778,8.52048321 L1.97228778,8.52048321 L1.93322631,8.23891652 L1.93322631,8.23891652 L1.90434165,7.98112978 L1.90434165,7.98112978 L1.88424507,7.74818505 L1.88424507,7.74818505 L1.86754,7.44767 L1.86754,7.44767 C1.78675,5.25221 3.46855,3.11902 5.41215,2.28605 C5.86822,2.09059 6.4206,2.12636 6.85728,2.44489 Z M14.9917005,6.03952315 L15.1077,6.05315 C15.8356,6.18149 16.5042,6.53699 17.0177,7.06867 C17.5311,7.60035 17.863,8.28093 17.9659,9.01287 C18.0428,9.55978 17.6617,10.0654 17.1148,10.1423 C16.6069643,10.2137071 16.1346531,9.89022337 16.0082806,9.40576032 L15.9854,9.29121 C15.9413,8.97753 15.799,8.68585 15.579,8.45799 C15.3955833,8.26809833 15.1660556,8.13065111 14.9140856,8.05827565 L14.7604,8.02276 C14.2166,7.92686 13.8534,7.4082 13.9493,6.86431 C14.03835,6.35926 14.4919571,6.01004352 14.9917005,6.03952315 L14.9917005,6.03952315 Z M15,2.99999 C15.7879,2.99999 16.5682,3.15518 17.2961,3.45671 C18.0241,3.75824 18.6855,4.2002 19.2427,4.75735 C19.7998,5.3145 20.2418,5.97594 20.5433,6.70389 C20.8448,7.43184 21,8.21206 21,8.99999 C21,9.55227 20.5523,9.99999 20,9.99999 C19.48715,9.99999 19.0644908,9.61394571 19.0067275,9.11661025 L19,8.99999 C19,8.4747 18.8966,7.95456 18.6955,7.46926 C18.4945,6.98395 18.1999,6.543 17.8284,6.17156 C17.457,5.80013 17.0161,5.50549 16.5307,5.30447 C16.1262833,5.13695333 15.6977,5.03718667 15.2620796,5.00858435 L15,4.99999 C14.4477,4.99999 14,4.55227 14,3.99999 C14,3.44771 14.4477,2.99999 15,2.99999 Z'/%3E%3C/svg%3E")
    no-repeat;
}

html[data-theme='dark'] .header-contact-link:before {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='white' d='M6.85728,2.44489 C7.99928,3.27790429 8.88915755,4.41552061 9.65001761,5.50316536 L10.0920364,6.14691904 L10.0920364,6.14691904 L10.509,6.76166 L10.509,6.76166 C10.9374,7.38835 10.8351,8.244 10.2531,8.74772 L8.30198,10.1967 C8.10859,10.3404 8.04429,10.6014 8.16028,10.8125 C8.60173,11.6161 9.38819,12.8119 10.2882,13.7119 C11.1891,14.6128 12.4414,15.45 13.3002,15.9412 C13.5229,16.0685 13.803,15.9948 13.9438,15.7803 L15.2131,13.8468 C15.6999,13.1991 16.6088,13.0576 17.2695,13.5149 L17.9332982,13.9735916 C19.1717645,14.8335207 20.5037538,15.8105615 21.521,17.1133 C21.8626,17.5507 21.9133,18.1227 21.7096,18.5981 C20.8728,20.5507 18.7552,22.2136 16.5524,22.1325 L16.2518759,22.1158001 L16.2518759,22.1158001 L16.0189256,22.0957065 L16.0189256,22.0957065 L15.7611336,22.0668244 L15.7611336,22.0668244 L15.4795621,22.0277651 L15.4795621,22.0277651 L15.1752731,21.97714 C15.1227241,21.9676615 15.0692729,21.9576432 15.0149414,21.9470562 L14.6785676,21.8764784 C14.6208039,21.8635009 14.5622043,21.8498968 14.5027909,21.8356372 L14.136722,21.7419821 L14.136722,21.7419821 L13.7521839,21.6312063 L13.7521839,21.6312063 L13.3502388,21.501921 C11.5039131,20.8764078 9.16110938,19.6464875 6.75735,17.2427 C4.35356813,14.8389125 3.12365344,12.4961028 2.49813876,10.6497861 L2.36885301,10.2478433 L2.36885301,10.2478433 L2.25807648,9.86330795 L2.25807648,9.86330795 L2.16442042,9.4972422 L2.16442042,9.4972422 L2.08649611,9.15070812 C2.06298899,9.03857004 2.04187237,8.92986425 2.02291481,8.82476776 L1.97228778,8.52048321 L1.97228778,8.52048321 L1.93322631,8.23891652 L1.93322631,8.23891652 L1.90434165,7.98112978 L1.90434165,7.98112978 L1.88424507,7.74818505 L1.88424507,7.74818505 L1.86754,7.44767 L1.86754,7.44767 C1.78675,5.25221 3.46855,3.11902 5.41215,2.28605 C5.86822,2.09059 6.4206,2.12636 6.85728,2.44489 Z M14.9917005,6.03952315 L15.1077,6.05315 C15.8356,6.18149 16.5042,6.53699 17.0177,7.06867 C17.5311,7.60035 17.863,8.28093 17.9659,9.01287 C18.0428,9.55978 17.6617,10.0654 17.1148,10.1423 C16.6069643,10.2137071 16.1346531,9.89022337 16.0082806,9.40576032 L15.9854,9.29121 C15.9413,8.97753 15.799,8.68585 15.579,8.45799 C15.3955833,8.26809833 15.1660556,8.13065111 14.9140856,8.05827565 L14.7604,8.02276 C14.2166,7.92686 13.8534,7.4082 13.9493,6.86431 C14.03835,6.35926 14.4919571,6.01004352 14.9917005,6.03952315 L14.9917005,6.03952315 Z M15,2.99999 C15.7879,2.99999 16.5682,3.15518 17.2961,3.45671 C18.0241,3.75824 18.6855,4.2002 19.2427,4.75735 C19.7998,5.3145 20.2418,5.97594 20.5433,6.70389 C20.8448,7.43184 21,8.21206 21,8.99999 C21,9.55227 20.5523,9.99999 20,9.99999 C19.48715,9.99999 19.0644908,9.61394571 19.0067275,9.11661025 L19,8.99999 C19,8.4747 18.8966,7.95456 18.6955,7.46926 C18.4945,6.98395 18.1999,6.543 17.8284,6.17156 C17.457,5.80013 17.0161,5.50549 16.5307,5.30447 C16.1262833,5.13695333 15.6977,5.03718667 15.2620796,5.00858435 L15,4.99999 C14.4477,4.99999 14,4.55227 14,3.99999 C14,3.44771 14.4477,2.99999 15,2.99999 Z'/%3E%3C/svg%3E")
    no-repeat;
}
/* social media icon */