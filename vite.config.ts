import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vuetify from 'vite-plugin-vuetify'
import { fileURLToPath } from 'url'
import { VitePWA } from 'vite-plugin-pwa'
import { createHtmlPlugin } from 'vite-plugin-html'

export default defineConfig({
  plugins: [
    vue(),
    vuetify({
      autoImport: true,
      styles: { configFile: 'src/styles/variables.scss' }
    }),
    VitePWA({
      registerType: 'autoUpdate',
      includeAssets: [
        'favicon.ico',
        'apple-touch-icon.png',
        'masked-icon.svg',
        'logo.png', // 举例：你的 logo
        'fonts/*.woff2' // 举例：字体文件
      ],
      manifest: {
        name: 'Box777',
        short_name: 'Box777',
        description: 'Box777 Game Platform',
        theme_color: '#225424',
        icons: [
          {
            src: 'pwa-192x192.png',
            sizes: '192x192',
            type: 'image/png'
          },
          {
            src: 'pwa-512x512.png',
            sizes: '512x512',
            type: 'image/png'
          },
          {
            src: 'pwa-512x512.png',
            sizes: '512x512',
            type: 'image/png',
            purpose: 'any maskable'
          }
        ]
      },
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,woff2,ttf,mp3,mp4}'],
        runtimeCaching: [
          {
            urlPattern: /^https:\/\/zsj\.kk2133lz\.com\/.*/i,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'api-cache',
              networkTimeoutSeconds: 10,
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 24 * 60 * 60 // 24 hours
              },
              cacheableResponse: {
                statuses: [0, 200]
              }
            }
          }
        ]
      }
    }),
    createHtmlPlugin({
      minify: true,
      inject: {
        tags: [
          // {
          //   tag: 'link',
          //   attrs: {
          //     rel: 'preload',
          //     as: 'image',
          //     href: '/logo.png'
          //   },
          //   injectTo: 'head'
          // },
          // {
          //   tag: 'link',
          //   attrs: {
          //     rel: 'preload',
          //     as: 'font',
          //     href: '/fonts/your-font.woff2',
          //     type: 'font/woff2',
          //     crossorigin: ''
          //   },
          //   injectTo: 'head'
          // }
        ]
      }
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      'vuetify/lib': 'vuetify'
    },
    extensions: [
      '.js',
      '.json',
      '.jsx',
      '.mjs',
      '.ts',
      '.tsx',
      '.vue',
    ]
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `
          @use 'sass:math' as math;
          @import "@/styles/variables.scss";
        `
      }
    }
  },
  server: {
    port: 3000,
    open: true,
    host: true,
    proxy: {
      '/front': {
        // target: 'https://zsj.kk2133lz.com',
           target: 'http://************:9000',
        changeOrigin: true,
        secure: true,
        rewrite: (path) => path
      }
    }
  },
  build: {
    sourcemap: true,
    chunkSizeWarningLimit: 1600,
    minify: 'esbuild',
    rollupOptions: {
      output: {
        manualChunks(id) {
          // 1. 基础库分包
          if (id.includes('node_modules')) {
            if (id.includes('vue')) return 'vue-vendor'
            if (id.includes('vuetify')) return 'vuetify-vendor'
            if (id.includes('vue-router')) return 'router-vendor'
            if (id.includes('pinia')) return 'pinia-vendor'
            return 'vendor'
          }
          // 2. 业务页面分包（按需添加/调整）
          // if (id.includes('/src/views/Home/')) return 'home'
          // if (id.includes('/src/views/Game/')) return 'game'
          // if (id.includes('/src/views/UserProfile/')) return 'user-profile'
          // if (id.includes('/src/views/VIP/')) return 'vip'
          // if (id.includes('/src/views/Invite/')) return 'invite'
          // if (id.includes('/src/views/Deposit/')) return 'deposit'
          // if (id.includes('/src/views/Withdraw/')) return 'withdraw'
          // 3. 其它页面可按需添加
        }
      }
    }
  },
  esbuild: {
    drop: process.env.VITE_APP_ENV === 'production' ? ['console', 'debugger'] : []
  },
  optimizeDeps: {
    include: [
      'vuetify',
      'vuetify/components',
      'vuetify/directives',
      'vuetify/styles'
    ],
    exclude: [],
    force: true
  }
}) 