import { request } from '../request';

// 获取消息列表
export function fetchGetNoticeList(params:any) {
  return request({
    url: '/backend/notice/list',
    method: 'get',
    params
  });
}

// 创建消息
export function fetchAddNotice(data:any) {
  return request({
    url: '/backend/notice/create',
    method: 'post',
    data
  });
}

// 更新消息
export function fetchUpdateNotice(data: any) {
  return request({
    url: '/backend/notice/update',
    method: 'post',
    data
  });
}

// 获取消息详情
export function fetchGetNoticeDetail(data: any) {
  return request({
    url: `/backend/notice/show`,
    method: 'post',
    data
  });
}

// 更新消息状态
export function fetchUpdateNoticeState(data:any) {
  return request({
    url: '/backend/notice/state',
    method: 'post',
    data
  });
}