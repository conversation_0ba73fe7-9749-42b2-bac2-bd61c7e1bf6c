import { ref } from 'vue';
import type { Ref } from 'vue';

export type TableOperateType = 'add' | 'edit';

export function useTableOperate<T extends { id: number }>(data: Ref<T[]>, getData: () => Promise<void>) {
  const drawerVisible = ref(false);
  const operateType = ref<TableOperateType>('add');
  const editingData = ref<T | null>(null);
  const checkedRowKeys = ref<T[]>([]);

  function handleAdd() {
    operateType.value = 'add';
    editingData.value = null;
    drawerVisible.value = true;
  }

  function handleEdit(id: number) {
    operateType.value = 'edit';
    editingData.value = data.value.find(item => item.id === id) || null;
    drawerVisible.value = true;
  }

  function onBatchDeleted() {
    checkedRowKeys.value = [];
    getData();
  }

  function onDeleted() {
    getData();
  }

  return {
    drawerVisible,
    operateType,
    editingData,
    handleAdd,
    handleEdit,
    checkedRowKeys,
    onBatchDeleted,
    onDeleted
  };
}
