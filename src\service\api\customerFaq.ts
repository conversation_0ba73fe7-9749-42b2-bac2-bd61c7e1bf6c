/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-24 19:18:24
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\service\api\customerFaq.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from '../request';

// 客户FAQ管理相关接口

/**
 * 获取所有关键词
 */
export function fetchGetAllCustomerKeywords(params?: any) {
  return request({
    url: '/backend/getQuestion/getAllKeywords',
    method: 'get',
    params
  });
}

/**
 * 添加关键词
 */
export function fetchAddCustomerKeyword(data: any) {
  return request({
    url: '/backend/admin/addKeywordHandler',
    method: 'post',
    data
  });
}

/**
 * 更新关键词
 */
export function fetchUpdateCustomerKeyword(data: any) {
  return request({
    url: '/backend/admin/updateKeyword',
    method: 'post',
    data
  });
}

/**
 * 删除关键词
 */
export function fetchDeleteCustomerKeyword(id: number) {
  return request({
    url: '/backend/admin/deleteKeyword',
    method: 'post',
    data: { id }
  });
}
