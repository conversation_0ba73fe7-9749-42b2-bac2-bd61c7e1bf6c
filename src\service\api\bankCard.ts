/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-16 15:17:10
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-06-19 15:02:37
 * @FilePath: \betdoce-admin\src\service\api\bankCard.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from '../request';
import { CommonSearchParams } from '@/types/common';

export interface BankCardParams extends CommonSearchParams {
  user_id?: string;
  phone?: string;
  username?: string;
  bank_name?: string;
  card_number?: string;
  cpf?: string;
  status?: string;
  dateRange?: [string, string];
}

export interface BankCardData {
  id: number;
  user_id: string;
  phone: string;
  username: string;
  bank_name: string;
  card_number: string;
  cpf: string;
  status: 'activated' | 'inactive';
  extended_info: string;
  created_at: string;
}

/** 获取银行卡列表 */
export function fetchGetBankCardList(params: BankCardParams) {
  return request<{
    list: BankCardData[];
    total: number;
  }>({
    url: '/backend/bankCard/list',
    method: 'get',
    params
  });
}

/** 删除银行卡 */
export function fetchDeleteBankCard(data: { id: number | number[] }) {
  return request({
    url: '/backend/bankCard/delete',
    method: 'post',
    data
  });
}

/** 更新银行卡信息 */
export function fetchUpdateBankCard(data: Partial<BankCardData>) {
  return request({
    url: '/backend/bankcard/update',
    method: 'post',
    data
  });
}
