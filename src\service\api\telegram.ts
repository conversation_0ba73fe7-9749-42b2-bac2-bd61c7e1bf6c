import { request } from '../request';

// Telegram管理相关接口

// ========== 订阅号管理 ==========

/**
 * 获取活跃频道列表
 */
export function fetchGetActiveChannels(params?: any) {
  return request({
    url: '/backend/tg/activeChannels',
    method: 'get',
    params
  });
}

/**
 * 添加订阅
 */
export function fetchAddSubscription(data: any) {
  return request({
    url: '/backend/tg/subscription',
    method: 'post',
    data
  });
}

/**
 * 更新订阅
 */
export function fetchUpdateSubscription(data: any) {
  return request({
    url: '/backend/tg/updateSubscription',
    method: 'post',
    data
  });
}

/**
 * 删除订阅
 */
export function fetchDeleteSubscription(id: number) {
  return request({
    url: '/backend/tg/delelteSubscription',
    method: 'post',
    data: { subscription_id:id }
  });
}

// ========== 客服群管理 ==========

/**
 * 获取客服群列表
 */
export function fetchGetCustomerServiceGroups(params?: any) {
  return request({
    url: '/backend/tg/getCustomerServiceGroups',
    method: 'get',
    params
  });
}

/**
 * 添加客服群
 */
export function fetchAddCustomerServiceGroup(data: any) {
  return request({
    url: '/backend/tg/addCustomerServiceGroup',
    method: 'post',
    data
  });
}

/**
 * 更新客服群
 */
export function fetchUpdateCustomerServiceGroup(data: any) {
  return request({
    url: '/backend/tg/updateCustomerServiceGroup',
    method: 'post',
    data
  });
}

/**
 * 删除客服群
 */
export function fetchDeleteCustomerServiceGroup(id: number) {
  return request({
    url: '/backend/tg/deleteCustomerServiceGroup',
    method: 'post',
    data: { id }
  });
}


/**
 * 获取WhatsApp客服账号列表
 */
export function fetchGetWhatsappServiceGroups(params?: any) {
  return request({
    url: '/backend/whatsapp/getAccounts',
    method: 'get',
    params
  });
}

/**
 * 添加客服群
 */
export function fetchAddWhatsappServiceGroup(data: any) {
  return request({
    url: '/backend/whatsapp/createAccount',
    method: 'post',
    data
  });
}

/**
 * 更新客服群
 */
export function fetchUpdateWhatsappServiceGroup(data: any) {
  return request({
    url: '/backend/whatsapp/updateAccount',
    method: 'post',
    data
  });
}

/**
 * 删除客服群
 */
export function fetchDeleteWhatsappServiceGroup(id: number) {
  return request({
    url: '/backend/whatsapp/deleteAccount',
    method: 'post',
    data: { id }
  });
}

/**
 * 启用WhatsApp客服账号
 */
export function enable(id: number) {
  return request({
    url: '/backend/whatsapp/enable',
    method: 'post',
    data: { id }
  });
}

/**
 * 禁用WhatsApp客服账号
 */
export function disable(id: number) {
  return request({
    url: '/backend/whatsapp/disable',
    method: 'post',
    data: { id }
  });
}