<template>
  <v-app class="app-style">
    <HeaderNav @toggle-drawer="toggleDrawer" />

    <v-navigation-drawer
      v-model="drawer"
      :rail="rail"
      :temporary="isMobile"
      :permanent="!isMobile"
      elevation="0"
      :expand-on-hover="!isMobile"
      :rail-width="56"
      width="280"
      class="nav-left"
    >
      <SideNav />
    </v-navigation-drawer>

    <v-main class="main-style">
      <div class="main-content">
        <router-view />
      </div>
    </v-main>
  </v-app>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, provide } from 'vue'
import { VApp } from 'vuetify/components'
import HeaderNav from './components/HeaderNav.vue'
import SideNav from './components/SideNav.vue'

const drawer = ref(false) // 默认隐藏
const rail = ref(false)
const windowWidth = ref(window.innerWidth)

// 判断是否为移动端
const isMobile = computed(() => windowWidth.value <= 768)

// 监听窗口大小变化
const handleResize = () => {
  windowWidth.value = window.innerWidth
  // 在桌面端时显示侧边栏
  drawer.value = !isMobile.value
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  // 初始化时设置抽屉状态
  drawer.value = !isMobile.value
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 提供抽屉状态给子组件
provide('drawer', drawer)

const toggleDrawer = () => {
  drawer.value = !drawer.value
}
</script>

<style lang="scss">
// 全局样式，移除 scoped
html, body {
  overflow: hidden !important;
  height: 100vh;
  margin: 0;
  padding: 0;
}

.v-application {
  overflow: hidden !important;
  height: 100vh !important;
}

.v-application__wrap {
  overflow: hidden !important;
  min-height: 100vh !important;
  max-height: 100vh !important;
}

.app-style {
  height: 100vh;
  display: flex;
  flex-direction: column;
  .nav-left{
    box-shadow: none !important;
    border-color: #002664;
  }
}

.main-style {
  background: #0E753B;
  flex: 1;
  position: relative;
  height: calc(100vh - 64px);
  min-width: 1420px;
  overflow-x: auto;
}

.main-content {
  height: 100%;
  overflow-y: auto;
  overflow-x: auto;
}

:deep(.v-navigation-drawer) {
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

@media (max-width: 768px) {
  :deep(.v-navigation-drawer) {
    width: 100% !important;
    max-width: 280px !important;
  }
}
</style> 