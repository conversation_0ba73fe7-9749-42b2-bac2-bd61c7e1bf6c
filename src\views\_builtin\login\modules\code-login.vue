<script setup lang="ts">
import {computed, ref} from 'vue';
import {$t} from '@/locales';
import {useRouterPush} from '@/hooks/common/router';
import {useForm, useFormRules} from '@/hooks/common/form';
import {useCaptcha} from '@/hooks/business/captcha';
import {useAuthStore} from "@/store/modules/auth";

defineOptions({name: 'CodeLogin'});

const {toggleLoginModule} = useRouterPush();
const {formRef, validate} = useForm();
const {label, isCounting, loading, getCaptcha} = useCaptcha();

interface FormModel {
  phone: string;
  code: string;
  glcode: string;
}

const model = ref<FormModel>({phone: '', code: '', glcode: ''});

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  const {formRules} = useFormRules();

  return {phone: formRules.phone, code: formRules.code};
});
const authStore = useAuthStore();
async function handleSubmit() {
  await validate();
  // 请求
  window.$message?.success($t('page.login.common.validateSuccess'));
  await authStore.login('Admin','123456');
}
</script>

<template>
  <ElForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keyup.enter="handleSubmit">
    <ElFormItem prop="phone">
      <ElInput v-model="model.phone" :placeholder="$t('page.login.common.phonePlaceholder')"/>
    </ElFormItem>
    <ElFormItem prop="code">
      <div class="w-full flex-y-center gap-16px">
        <ElInput v-model="model.code" :placeholder="$t('page.login.common.codePlaceholder')"/>
        <ElButton size="large" :disabled="isCounting" :loading="loading" @click="getCaptcha(model.phone)">
          {{ label }}
        </ElButton>
      </div>
    </ElFormItem>
    <ElFormItem prop="code">
      <div class="w-full flex-y-center gap-16px">
        <ElInput v-model="model.glcode" :placeholder="$t('page.login.common.codePlaceholder')"/>
      </div>
    </ElFormItem>
    <ElSpace direction="vertical" :size="18" fill class="w-full">
      <ElButton type="primary" size="large" round block @click="handleSubmit">
        {{ $t('common.confirm') }}
      </ElButton>
    </ElSpace>
  </ElForm>
</template>

<style scoped></style>
