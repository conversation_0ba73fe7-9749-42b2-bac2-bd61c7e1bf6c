// 移动端样式
@media (max-width: 768px) {
  .banner-wrapper {
    margin-bottom: 80px;
  }
  
  .banner-carousel {
    :deep() {
      height: 120px !important;

      .banner-item {
        height: 120px;
      }

      .banner-image {
        height: 120px;
      }
    }
  }

  .game-tabs {
    display: none !important;
  }

  .search-wrapper {
    position: absolute;
    top: auto;
    bottom: -60px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 32px);
    max-width: 400px;
    box-shadow: none;

    &.is-sticky {
      position: fixed;
      top: 70px;
      bottom: auto;
      left: 50%;
      transform: translateX(-50%);
    }

    &.nav-expanded {
      left: 50%;
      transform: translateX(-50%);
    }
  }

  .notice-bar {
    padding: 0 16px;
  }

  .notice-item {
    font-size: 12px;
    padding-right: 12px;
    
    .separator {
      margin: 0 12px;
    }
  }

  .notice-search-placeholder {
    height: 120px;
  }
}

// 移动端网格菜单样式
.mobile-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  padding: 6px;
  background: rgba(30, 34, 45, 0.6);
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  .v-icon {
    color: rgba(255, 255, 255, 0.8);
  }
  
  .grid-item-text {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
  }
  
  &:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.1);
  }
}

@media (max-width: 768px) {
  .mobile-grid {
    margin-top: -24px;
    border-radius: 16px 16px 0 0;
    position: relative;
    z-index: 2;
  }
  
  .game-column {
    padding: 4px;
  }

  .game-card {
    margin-bottom: 8px;

    :deep(.v-card-text) {
      .text-subtitle-2 {
        font-size: 12px;
      }

      .text-caption {
        font-size: 10px;
      }
    }
  }
}

@media (max-width: 1440px) {
  .search-wrapper {
    right: 24px;
    
    &.nav-expanded {
      right: calc(24px + 240px);
    }
    
    &.is-sticky {
      right: 24px;
      
      &.nav-expanded {
        right: calc(24px + 240px);
      }
    }
  }
} 