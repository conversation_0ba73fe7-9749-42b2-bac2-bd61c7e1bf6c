<template>
  <ElDrawer v-model="visible" :title="title" :size="400">
    <ElTabs v-model="activeTab">
      <ElTabPane label="代理商" name="agent">
        <ElForm
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-position="top"
        >
          <ElFormItem
            label="代理商ID"
            prop="agent_id"
            v-if="operateType === 'edit'"
          >
            <ElInput v-model="formData.agent_id" disabled />
          </ElFormItem>
          <ElFormItem label="代理商名称" prop="agent_name">
            <ElInput
              v-model="formData.agent_name"
              placeholder="请输入代理商名称"
              maxlength="200"
            />
          </ElFormItem>
          <ElFormItem v-if="isManage" label="等级类型" prop="agent_level_type">
            <ElSelect
              :disabled="operateType === 'edit'"
              v-model="formData.agent_level_type"
              placeholder="请选择等级类型"
            >
              <ElOption
                v-for="item in Object.keys(LevelTypes).map((e) => Number(e))"
                :label="LevelTypes[item]"
                :value="item"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="佣金比例" prop="commission_rate">
            <ElInput
              v-model.number="formData.commission_rate"
              placeholder="请输入佣金比例"
              type="number"
              :min="15"
              :max="100"
              step="1"
              @input="handleCommissionRateInput"
            >
              <template #append>%</template>
            </ElInput>
          </ElFormItem>
          <ElFormItem label="日提现额" prop="daily_withdrawal_limit">
            <ElInput
              v-model.number="formData.daily_withdrawal_limit"
              placeholder="请输入日提现额"
              type="number"
              :min="0"
            >
              <template #append>元</template>
            </ElInput>
          </ElFormItem>
          <ElFormItem label="月提现额" prop="monthly_withdrawal_limit">
            <ElInput
              v-model.number="formData.monthly_withdrawal_limit"
              placeholder="请输入月提现额"
              type="number"
              :min="0"
            >
              <template #append>元</template>
            </ElInput>
          </ElFormItem>
          <ElFormItem label="联系人" prop="contact_person">
            <ElInput
              v-model="formData.contact_person"
              placeholder="请输入联系人"
              maxlength="100"
            />
          </ElFormItem>
          <ElFormItem label="联系方式" prop="contact_phone">
            <ElInput
              v-model="formData.contact_phone"
              placeholder="请输入联系方式"
              maxlength="100"
            />
          </ElFormItem>
          <ElFormItem label="邮箱" prop="contact_email">
            <ElInput
              v-model="formData.contact_email"
              placeholder="请输入邮箱"
              maxlength="100"
            />
          </ElFormItem>
          <ElFormItem label="管理账号" prop="username">
            <ElInput
              v-model="formData.username"
              placeholder="请输入管理账号"
              maxlength="50"
            >
            </ElInput>
          </ElFormItem>
          <ElFormItem label="密码" prop="password">
            <ElInput
              v-model="formData.password"
              placeholder="请输入账号密码"
              maxlength="50"
            >
            </ElInput>
          </ElFormItem>

          <ElFormItem label="是否启用" prop="status">
            <ElSwitch
              v-model="formData.status"
              :active-value="1"
              :inactive-value="2"
            />
          </ElFormItem>
        </ElForm>
      </ElTabPane>
      <ElTabPane label="菜单权限" name="routes">
        <ElTree
          ref="routesTreeRef"
          :data="menuRoutesTreeData"
          show-checkbox
          default-expand-all
          node-key="id"
          :props="defaultProps"
        />
      </ElTabPane>
    </ElTabs>
    <template #footer>
      <ElButton @click="visible = false">取消</ElButton>
      <ElButton type="primary" :loading="loading" @click="handleSubmit">
        保存
      </ElButton>
    </template>
  </ElDrawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick, onMounted } from "vue";
import { useAuthStore } from "@/store/modules/auth";
import { filterAuthRoutesByRoutes } from "@/store/modules/route/shared";
import {
  ElDrawer,
  ElForm,
  ElFormItem,
  ElInput,
  ElSwitch,
  ElButton,
  ElSelect,
  ElOption,
  ElTabs,
  ElTabPane,
  ElTree,
} from "element-plus";
import type { FormInstance, FormRules, FormItemRule } from "element-plus";
import {
  fetchCreateAgent,
  fetchUpdateAgent,
  fetchGetAgentLevelTypes,
} from "@/service/api/agent";
import { myRoutes } from "@/router/elegant/routes";
import { $t } from "@/locales";
import type { GeneratedRoute } from "@elegant-router/types";
import type { AgentItem } from "@/service/api/agent";
import { localStg } from "@/utils/storage";

export interface AgentFormData {
  agent_id?: number;
  agent_name: string;
  agent_level_type: number | null;
  commission_rate: number | null;
  daily_withdrawal_limit: number | null;
  monthly_withdrawal_limit: number | null;
  contact_person: string;
  contact_phone: string;
  contact_email: string;
  username: number | null;
  password: number | null;
  status: number;
  id?: number;
  createBy?: string | null;
  createTime?: string | null;
  updateBy?: string | null;
  updateTime?: string | null;
  routes: (string | number)[];
  button_ids: (string | number)[];
}

interface Props {
  /** the type of operation */
  operateType: UI.TableOperateType;
  /** the edit row data */
  rowData?: AgentItem | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: "submitted"): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>("visible", {
  default: false,
});

const authStore = useAuthStore();
const isManage = computed(() => JSON.parse(localStg.get("isManage")));
const authRoutes = computed(() => authStore.userInfo.routes);

const title = computed(() => {
  const titles: Record<UI.TableOperateType, string> = {
    add: "新增代理商",
    edit: "编辑代理商",
  };
  return titles[props.operateType];
});

const formRef = ref<FormInstance>();
const loading = ref(false);

const activeTab = ref("agent");

const formData = reactive<AgentFormData>({
  agent_id: undefined,
  agent_name: "",
  agent_level_type: null,
  commission_rate: null,
  daily_withdrawal_limit: null,
  monthly_withdrawal_limit: null,
  contact_person: "",
  contact_phone: "",
  contact_email: "",
  username: null,
  password: null,
  status: 2,
  id: undefined,
  createBy: null,
  createTime: null,
  updateBy: null,
  updateTime: null,
  routes: [],
  button_ids: [1, 2, 3, 4, 5],
});

const validateMonthlyWithdrawal: FormItemRule["validator"] = (
  rule,
  value,
  callback,
) => {
  if (
    formData.daily_withdrawal_limit !== null &&
    formData.daily_withdrawal_limit !== undefined &&
    value !== null &&
    value !== undefined &&
    (value as number) < (formData.daily_withdrawal_limit as number)
  ) {
    callback(new Error("月提现额必须大于等于日提现额"));
  } else {
    callback();
  }
};

const rules: FormRules = {
  agent_name: [
    { required: true, message: "请输入代理商名称", trigger: "blur" },
    { max: 200, message: "长度不能超过 200 个字符", trigger: "blur" },
  ],
  agent_level_type: [
    { required: true, message: "请选择等级类型", trigger: "change" },
  ],
  commission_rate: [
    { required: true, message: "请输入佣金比例", trigger: "blur" },
    { type: "number", message: "佣金比例必须是数字" },
    {
      validator: (rule, value, callback) => {
        if (value === null || value === undefined || (value as number) < 15) {
          callback(new Error("佣金比例最低为 15%"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
    {
      type: "number",
      max: 100,
      message: "佣金比例最高为 100%",
      trigger: "blur",
    },
  ],
  daily_withdrawal_limit: [
    { type: "number", message: "日提现额必须是数字" },
    {
      validator: (rule, value, callback) => {
        if (value !== null && value !== undefined && (value as number) < 0) {
          callback(new Error("日提现额不能小于 0"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  monthly_withdrawal_limit: [
    { type: "number", message: "月提现额必须是数字" },
    {
      validator: (rule, value, callback) => {
        if (value !== null && value !== undefined && (value as number) < 0) {
          callback(new Error("月提现额不能小于 0"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  contact_person: [
    { required: true, message: "请输入联系人", trigger: "blur" },
    { max: 100, message: "长度不能超过 100 个字符", trigger: "blur" },
  ],
  contact_phone: [
    { required: true, message: "请输入联系电话", trigger: "blur" },
    { max: 100, message: "长度不能超过 100 个字符", trigger: "blur" },
  ],
  contact_email: [
    { max: 100, message: "长度不能超过 100 个字符", trigger: "blur" },
  ],
  username: [
    { required: true, message: "请输入代理商账号", trigger: "blur" },
    { max: 50, message: "长度不能超过 50 个字符", trigger: "blur" },
  ],
  // password: [
  //   { required: true, message: "请输入代理商密码", trigger: "blur" },
  //   { max: 50, message: "长度不能超过 50 个字符", trigger: "blur" },
  // ],
  status: [{ required: true, message: "请选择是否启用", trigger: "change" }],
};

function transformRoutesToAgentroutesTree(routes: GeneratedRoute[]): any[] {
  return routes
    .filter((route) => !route.meta?.hideInMenu && !route.meta?.hideAgentInMenu)
    .map((route) => {
      const node: any = {
        id: route.meta?.parentId
          ? `${String(route.meta.parentId).replace(/-/g, "-")}-${String(route.meta.id).replace(/-/g, "-")}`
          : String(route.meta?.id).replace(/-/g, "-"),
        parentId: route.meta?.parentId
          ? String(route.meta.parentId).replace(/-/g, "-")
          : undefined,
        label: route.meta?.i18nKey
          ? $t(route.meta.i18nKey)
          : route.meta?.title || route.name,
      };
      if (
        "children" in route &&
        Array.isArray(route.children) &&
        route.children.length > 0
      ) {
        node.children = transformRoutesToAgentroutesTree(
          route.children as GeneratedRoute[],
        );
      }
      if (node.children && node.children.length === 0) {
        delete node.children;
      }
      return node;
    });
}

const menuRoutesTreeData = computed(() => {
  const filterRoutes = filterAuthRoutesByRoutes(
    myRoutes,
    authRoutes.value || "",
  );
  const filteredRoutes = filterRoutes.filter(
    (route) =>
      route.name === "game" ||
      route.name === "account" ||
      route.name === "report" ||
      route.name === "finance" ||
      route.name === "manage" ||
      route.name === "agent",
  );
  return transformRoutesToAgentroutesTree(filteredRoutes as GeneratedRoute[]);
});

const defaultProps = {
  children: "children",
  label: "label",
};

const routesTreeRef = ref<InstanceType<typeof ElTree>>();

// 处理佣金比例输入，只允许正整数
function handleCommissionRateInput(value: string | number) {
  if (value === '' || value === null || value === undefined) {
    formData.commission_rate = null;
    return;
  }

  // 转换为数字
  let numValue = Number(value);

  // 如果不是有效数字，设为null
  if (isNaN(numValue)) {
    formData.commission_rate = null;
    return;
  }

  // 取整数部分
  numValue = Math.floor(numValue);

  // 限制范围在15-100之间
  if (numValue < 15) {
    numValue = 15;
  } else if (numValue > 100) {
    numValue = 100;
  }

  formData.commission_rate = numValue;
}

async function handleUpdateModelWhenEdit() {
  if (props.operateType === "edit" && props.rowData) {
    Object.assign(formData, props.rowData);
    formData.agent_id = props.rowData.agent_id;
    formData.agent_name = props.rowData.agent_name;
    formData.agent_level_type = props.rowData.agent_level_type;
    formData.commission_rate = props.rowData.commission_rate ?? null;
    formData.daily_withdrawal_limit =
      Number(props.rowData.daily_withdrawal_limit) / 100;

    formData.monthly_withdrawal_limit =
      Number(props.rowData.monthly_withdrawal_limit) / 100;

    formData.contact_person = props.rowData.contact_person;
    formData.contact_phone = props.rowData.contact_phone;
    formData.contact_email = props.rowData.contact_email;
    formData.username = props.rowData.username ?? null;
    formData.password = props.rowData.password ?? null;
    formData.status = props.rowData.status;
    formData.id = props.rowData.id;
    formData.createBy = props.rowData.createBy ?? null;
    formData.createTime = props.rowData.createTime ?? null;
    formData.updateBy = props.rowData.updateBy ?? null;
    formData.updateTime = props.rowData.updateTime ?? null;
    formData.routes = props.rowData.routes ?? [];

    if (formData.routes && routesTreeRef.value) {
      // 確保路由數據是字符串格式
      const routesString =
        typeof formData.routes === "string"
          ? formData.routes
          : Array.isArray(formData.routes)
            ? formData.routes.join(",")
            : "";
      console.log("處理前的路由數組:", formData.routes);
      console.log("處理前的路由數組:", routesString);
      // 將路由字符串轉換為數組並處理格式
      const stringRoutes = routesString
        .split(",")
        .map((route) => route.trim())
        .filter((route) => route);

      console.log("處理後的路由數組:", stringRoutes);

      // 等待 DOM 更新
      await nextTick();

      // 設置選中的節點
      if (stringRoutes.length > 0) {
        routesTreeRef.value.setCheckedKeys(stringRoutes);
      }
    }
  } else {
    Object.assign(formData, {
      agent_id: undefined,
      agent_name: "",
      agent_level_type: null,
      commission_rate: null,
      daily_withdrawal_limit: null,
      monthly_withdrawal_limit: null,
      contact_person: "",
      contact_phone: "",
      contact_email: "",
      username: null,
      password: null,
      status: 2,
      id: undefined,
      createBy: null,
      createTime: null,
      updateBy: null,
      updateTime: null,
      routes: [],
    });
  }
}

const closeDrawer = () => {
  visible.value = false;
};

function getRouteIds(selectedIds: any[]) {
  return selectedIds.filter((id) => id.includes("-"));
}
async function handleSubmit() {
  if (!formRef.value) return;

  if (routesTreeRef.value) {
    formData.routes = routesTreeRef.value.getCheckedKeys().map(String) || [];
  }
  console.log("Selected routes:", formData.routes);

  try {
    await formRef.value.validate();
    loading.value = true;

    let submitData: Partial<AgentFormData>;

    if (props.operateType === "add") {
      const {
        agent_id,
        id,
        createBy,
        createTime,
        updateBy,
        updateTime,
        ...rest
      } = formData;
      submitData = {
        ...rest,
      };
    } else {
      submitData = { ...formData };
    }

    // 转换提现额度为分
    submitData = {
      ...submitData,
      daily_withdrawal_limit: submitData.daily_withdrawal_limit
        ? submitData.daily_withdrawal_limit * 100
        : 0,
      monthly_withdrawal_limit: submitData.monthly_withdrawal_limit
        ? submitData.monthly_withdrawal_limit * 100
        : 0,
    };

    delete submitData.createBy;
    delete submitData.createTime;
    delete submitData.updateBy;
    delete submitData.updateTime;

    if (props.operateType === "add") {
      delete submitData.id;
    }

    submitData.routes = getRouteIds(formData.routes).join(",");
    submitData.button_ids = [1, 2, 3, 4, 5];
    if (props.operateType === "add") {
      const { error } = await fetchCreateAgent(submitData as any);
      if (!error) {
        window.$message?.success("新增成功");
        closeDrawer();
        emit("submitted");
      }
    } else {
      const { error } = await fetchUpdateAgent(submitData as any);
      if (!error) {
        window.$message?.success("更新成功");
        closeDrawer();
        emit("submitted");
      }
    }
  } catch (error: any) {
    console.error(error);
    if (
      error &&
      typeof error === "object" &&
      "response" in error &&
      error.response &&
      typeof error.response === "object" &&
      "data" in error.response &&
      error.response.data &&
      typeof error.response.data === "object" &&
      "message" in error.response.data
    ) {
      window.$message?.error(error.response.data.message);
    } else if (error && typeof error === "object" && "message" in error) {
      window.$message?.error(error.message);
    } else {
      window.$message?.error("发生未知错误");
    }
  } finally {
    loading.value = false;
  }
}

watch(visible, async (newVal) => {
  if (newVal) {
    formRef.value?.resetFields();
    await nextTick();
    activeTab.value = "agent";
    await handleUpdateModelWhenEdit();
  } else {
    formRef.value?.resetFields();
    Object.assign(formData, {
      agent_id: undefined,
      agent_name: "",
      agent_level_type: null,
      commission_rate: null,
      daily_withdrawal_limit: null,
      monthly_withdrawal_limit: null,
      contact_person: "",
      contact_phone: "",
      contact_email: "",
      username: null,
      password: null,
      status: 2,
      id: undefined,
      createBy: null,
      createTime: null,
      updateBy: null,
      updateTime: null,
      routes: [],
    });
    if (routesTreeRef.value) {
      routesTreeRef.value.setCheckedKeys([], false);
    }
  }
});

// Watch for rowData changes and update formData
watch(
  () => props.rowData,
  (newVal) => {
    if (newVal) {
      // Map AgentItem to AgentFormData when rowData is provided
      formData.agent_id = newVal.agent_id;
      formData.agent_name = newVal.agent_name;
      formData.agent_level_type = newVal.agent_level_type;
      formData.commission_rate = newVal.commission_rate;
      formData.daily_withdrawal_limit = newVal.daily_withdrawal_limit;
      formData.monthly_withdrawal_limit = newVal.monthly_withdrawal_limit;
      formData.contact_person = newVal.contact_person;
      formData.contact_phone = newVal.contact_phone;
      formData.contact_email = newVal.contact_email;
      formData.username = newVal.username as any;
      formData.password = null;
      formData.status = newVal.status === 1 ? 1 : 2;
      formData.id = newVal.id;
      formData.createBy = null;
      formData.createTime = null;
      formData.updateBy = null;
      formData.updateTime = null;
      formData.routes = [];
    } else {
      // Reset formData when rowData is null
      Object.assign(formData, {
        agent_id: undefined,
        agent_name: "",
        agent_level_type: null,
        commission_rate: null,
        daily_withdrawal_limit: null,
        monthly_withdrawal_limit: null,
        contact_person: "",
        contact_phone: "",
        contact_email: "",
        username: null,
        password: null,
        status: 2,
        id: undefined,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        routes: [],
      });
    }
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  },
  { immediate: true },
);

// 获取层级下拉值
const LevelTypes = ref();
onMounted(() => {
  fetchGetAgentLevelTypes({}).then((res) => {
    console.log(res);
    LevelTypes.value = res.data.data;
  });
});
</script>
