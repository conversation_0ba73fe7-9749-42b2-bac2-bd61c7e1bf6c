<template>
  <div>
    <!-- P<PERSON> 安装按钮 -->
    <div
      v-if="canInstall && props.isImage"
      class="pwa-install-btn"
      @click="handleInstall"
    >
      <!-- <v-fab icon="mdi-tray-arrow-down" color="#fff"></v-fab> -->
      <img src="@/assets/images/h5/download.png" alt="TT Share" />
    </div>
    <!-- iOS 安装引导弹窗 -->
    <v-dialog v-model="showIOSGuide" max-width="90%" class="ios-guide-dialog">
      <div class="title-img-box">
        <img src="@/assets/images/h5/youxi.png" alt="Share" class="title-img" />
      </div>

      <v-card class="ios-guide-card">
        <v-card-title class="text-h6 text-center pa-2">
          Instalar App
        </v-card-title>
        <v-card-text class="pa-2">
          <div class="guide-steps">
            <div class="step step1">
              <div class="step-text">1.Toque no botão de compartilhamento</div>
              <img
                src="@/assets/images/h5/icon-install-1.png"
                alt="Share"
                class="step-image"
              />
              <img
                src="@/assets/images/h5/point.png"
                alt=""
                class="step-point-image"
              />
            </div>
            <div class="step step2">
              <div class="step-text">
                2.Role para baixo e toque em "Adicionar à Tela de Início"
              </div>
              <img
                src="@/assets/images/h5/icon-install-2.png"
                alt="Add to Home"
                class="step-image"
              />
              <img
                src="@/assets/images/h5/point.png"
                alt=""
                class="step-point-image"
              />
            </div>
            <div class="step">
              <div class="step-text">
                3.Encontre o aplicativo recém-adicionado na área de trabalho<img
                  src="@/assets/images/h5/youxi.png"
                  alt=""
                  class="app-icon"
                />
              </div>
            </div>
          </div>
        </v-card-text>
        <v-card-actions class="pa-4">
          <v-spacer></v-spacer>
          <v-btn color="primary" block @click="showIOSGuide = false">
            Entendi
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { registerSW } from "virtual:pwa-register";
import { showWarning } from "@/utils/toast";
const props = defineProps(["isImage"]);
const canInstall = ref(false);
const showIOSGuide = ref(false);
let deferredPrompt: any = null;

// 检测是否为 iOS 设备
const isIOS = () => {
  return (
    [
      "iPad Simulator",
      "iPhone Simulator",
      "iPod Simulator",
      "iPad",
      "iPhone",
      "iPod",
    ].includes(navigator.platform) ||
    // iPad on iOS 13 detection
    (navigator.userAgent.includes("Mac") && "ontouchend" in document)
  );
};

// 检测是否已经安装
const isStandalone = () => {
  return (
    window.matchMedia("(display-mode: standalone)").matches ||
    (window.navigator as any).standalone ||
    document.referrer.includes("android-app://")
  );
};

// 注册 Service Worker
const updateSW = registerSW({
  onNeedRefresh() {
    if (confirm("Nova versão disponível. Recarregar?")) {
      updateSW();
    }
  },
  onOfflineReady() {
    console.log("App está pronto para uso offline");
  },
});

// 监听 beforeinstallprompt 事件
const handleBeforeInstallPrompt = (e: Event) => {
  e.preventDefault();
  deferredPrompt = e;
  canInstall.value = true;
};
const isChromeOnAndroid = () => {
  // 1. 先检查 `window.chrome`（Chrome 特有）
  const isChromium = !!window.chrome;
  // 2. 检查 userAgent（排除其他 Chromium 浏览器）
  const ua = navigator.userAgent;
  const isChromeUA = /Chrome/.test(ua) && /Google Inc/.test(navigator.vendor);
  // 3. 排除 Edge、Opera、QQ浏览器等
  const isOtherChromium =
    /Edg|OPR|Firefox|QQBrowser|SamsungBrowser|UCBrowser|MicroMessenger/i.test(
      ua
    );
  return isChromium && isChromeUA && !isOtherChromium;
};
//跳转google浏览器
const openChrome = () => {
  try {
    var e = new URL(window.location.href);
    e.searchParams.set("is_open_chrome", "1");
    const chromeIntent = `intent://${e.href.replace(
      /(https|http):\/\//,
      ""
    )}#Intent;package=com.android.chrome;scheme=https;end`;
    window.location.href = chromeIntent;
  } catch (error) {
    console.log(error);
  }
};
//判断是否google浏览器的方法
// 处理安装按钮点击
const handleInstall = async () => {
  if (isIOS()) {
    // 如果是 iOS 设备，显示引导弹窗
    showIOSGuide.value = true;
    return;
  }
  if (!isChromeOnAndroid()) {
    openChrome();
    return;
  }

  if (!deferredPrompt) {
    showWarning("Carregando recursos de instalação");
    return;
  }

  // 显示安装提示
  deferredPrompt.prompt();

  // 等待用户响应
  const { outcome } = await deferredPrompt.userChoice;

  // 根据用户选择处理结果
  if (outcome === "accepted") {
    console.log("Usuário aceitou a instalação");
  } else {
    console.log("Usuário recusou a instalação");
  }

  // 清除 deferredPrompt
  deferredPrompt = null;
  canInstall.value = false;
};

onMounted(() => {
  // 只在非 iOS 设备上监听 beforeinstallprompt 事件
  if (!isIOS() && !isStandalone()) {
    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
  }
  // 如果是 iOS 设备且未安装，显示安装按钮
  if (!isStandalone()) {
    canInstall.value = true;
  }
  console.log(canInstall.value);
});

onUnmounted(() => {
  window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
});

defineExpose({ handleInstall });
</script>

<style scoped lang="scss">
.pwa-install-btn {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }

  img {
    width: 48px;
    height: 48px;
  }
}

.ios-guide-dialog {
  .ios-guide-card {
    border-radius: 16px;
    padding-top: 30px;
    :deep(.v-card-title) {
      line-height: 1;
      padding-bottom: 0 !important;
    }
  }
  .title-img-box {
    display: flex;
    justify-content: center;
  }
  .title-img {
    position: absolute;
    top: -25px;
    z-index: 999;
    width: 60px;
    height: 60px;
  }

  .guide-steps {
    display: flex;
    flex-direction: column;
    gap: 24px;
    // height: 500px;
    overflow: auto;
    .step2 {
      position: relative;
      .step-point-image {
        bottom: -20px;
        right: 30px;
        width: 50px;
        height: 50px;
        position: absolute;
        transform: rotate(-45deg);
      }
    }
    .step1 {
      position: relative;
      .step-point-image {
        bottom: -20px;
        right: 90px;
        width: 50px;
        height: 50px;
        position: absolute;
        transform: rotate(-45deg);
      }
    }
    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .step-number {
        width: 24px;
        height: 24px;
        background: #225424;
        color: white;
        flex-shrink: 0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
      }

      .step-text {
        text-align: center;
        font-size: 16px;
        color: white;
        .app-icon {
          width: 50px;
          height: 50px;
        }
      }

      .step-image {
        width: 100%;
        //max-width: 200px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }
}
</style>
