# WebSocket 登录消息 retry 字段逻辑

## 需求说明
发送登录消息时，`retry` 字段的值应该按照以下规则：
- **第一次登录**：`retry: true`
- **登录成功后的后续登录**：`retry: false`
- **刷新页面、重连 WebSocket 等情况**：`retry: true`

## 实现逻辑

### 1. 基于 localStorage 的状态管理
使用 `localStorage.getItem('isLogin')` 来判断 retry 字段的值，而不是内存中的状态变量。

### 2. retry 字段逻辑
在发送登录消息时：

```typescript
const loginMessage = {
  type: "login",
  data: {
    uuid: userInfo.uuid.toString(),
    token: md5Encrypt(userInfo.uuid + "box777studio"),
    in_game: false,
    md5: fingerprint,
    retry: localStorage.getItem('isLogin') !== 'false', // 根据localStorage中的isLogin状态判断
  },
};
```

**逻辑说明：**
- 当 `localStorage.getItem('isLogin')` 为 `'false'` 时，`retry` 为 `false`
- 当 `localStorage.getItem('isLogin')` 为其他值（包括 `null`、`'true'` 等）时，`retry` 为 `true`

### 3. 登录成功处理
收到登录成功消息时，设置 localStorage：

```typescript
if (type === "login" && success === true) {
  console.log("收到登录成功消息，开始发送心跳");
  this.isLoggedIn = true;
  // 登录成功后设置localStorage状态
  localStorage.setItem('isLogin', 'false');
  this.startHeartbeat();
}
```

### 4. localStorage 状态管理
不同场景下 localStorage 中 `isLogin` 的值：

#### retry 为 true 的场景：
- **首次访问页面**：`localStorage.getItem('isLogin')` 为 `null`
- **页面刷新**：`localStorage.getItem('isLogin')` 可能为任何值，但不是 `'false'`
- **网络中断恢复**：`localStorage.getItem('isLogin')` 不是 `'false'`
- **用户登出后重新登录**：`localStorage.getItem('isLogin')` 不是 `'false'`

#### retry 为 false 的场景：
- **登录成功后的后续重连**：`localStorage.getItem('isLogin')` 为 `'false'`

## 场景示例

### 场景1：首次访问页面
1. 页面加载 → `localStorage.getItem('isLogin')` 为 `null`
2. 发送登录消息 → `retry: true`
3. 登录成功 → `localStorage.setItem('isLogin', 'false')`
4. 后续重连 → `retry: false`

### 场景2：页面刷新
1. 页面刷新 → localStorage 保持不变
2. 如果之前登录成功过，`localStorage.getItem('isLogin')` 为 `'false'`
3. 发送登录消息 → `retry: false`
4. 如果之前未登录成功，发送登录消息 → `retry: true`

### 场景3：用户登出
1. 用户登出 → 可以选择清除或修改 localStorage 中的 `isLogin` 值
2. 重新登录 → 发送登录消息 → `retry: true`

### 场景4：网络中断恢复
1. 网络中断 → localStorage 保持不变
2. 网络恢复 → 根据 localStorage 中的值决定 retry
3. 如果之前登录成功过，`retry: false`；否则 `retry: true`

## 代码调用示例

```typescript
// 获取登录状态
const status = websocketService.getLoginStatus();
console.log('登录状态:', {
  isLoggedIn: status.isLoggedIn,
  loginAttempts: status.loginAttempts,
  canSendLogin: status.canSendLogin
});

// 检查当前 retry 值
const currentRetryValue = localStorage.getItem('isLogin') !== 'false';
console.log('当前 retry 值:', currentRetryValue);

// 重置登录状态
websocketService.resetLoginState();

// 手动设置 localStorage 状态
localStorage.setItem('isLogin', 'false'); // 下次登录 retry 为 false
localStorage.removeItem('isLogin'); // 下次登录 retry 为 true
```

## 优化效果

1. **准确的 retry 标识**：服务器可以根据 retry 字段判断是首次登录还是重连
2. **持久化状态管理**：基于 localStorage，页面刷新后状态依然保持
3. **简化逻辑**：不需要复杂的内存状态管理，直接基于 localStorage 判断
4. **更好的用户体验**：避免不必要的重复处理
5. **便于调试**：可以直接查看和修改 localStorage 中的状态
