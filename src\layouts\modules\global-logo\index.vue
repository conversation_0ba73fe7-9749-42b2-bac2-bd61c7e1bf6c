<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-06-13 22:00:20
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\layouts\modules\global-logo\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import { computed } from 'vue';
import { useAuthStore } from '@/store/modules/auth';
import { $t } from '@/locales';
import { ElMessageBox } from 'element-plus';

const authStore = useAuthStore();
// 获取用户信息
const userName = computed(() => authStore.userInfo?.user_name || '未知用户');
const userRole = computed(() => authStore.userInfo?.roles?.[0] || '管理员');

async function handleLogout() {
  try {
    await window.$messageBox.confirm(
      "确定退出登录吗？",
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: 'warning'
      }
    );
    await authStore.resetStore();
    window.$message?.success("退出成功");
  } catch {
  }
}
</script>

<template>
  <div class="user-panel">
    <div class="avatar">
      {{ userName?.charAt(0)?.toUpperCase() }}
    </div>
    <div class="user-name">{{ userName }}</div>
    <!-- <div class="user-role">[ {{ userRole }} ]</div> -->
    <div class="logout-btn" @click="handleLogout">安全退出</div>
  </div>
</template>

<style scoped>
.user-panel {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #393940;
}
.avatar {
  width: 84px;
  height: 84px;
  border: 1px solid white;
  background: #6d87f7;
  color: #fff;
  font-size: 64px;
  font-weight: bold;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.user-name {
  color: #fff;
  font-size: 18px;
  text-align: center;
}
.user-role {
  color: #bfc2cc;
  font-size: 14px;
  margin-bottom: 8px;
  text-align: center;
}
.logout-btn {
  color: #6080f7;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  transition: color 0.2s;
}
.logout-btn:hover {
  color: #6080f7;
  text-decoration: underline;
}
</style>
