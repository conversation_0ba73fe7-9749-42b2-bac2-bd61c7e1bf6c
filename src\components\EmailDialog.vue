<script setup lang="ts">
import { ref } from 'vue'
import { showError } from '@/utils/toast'

const props = defineProps<{
  modelValue: boolean
  currentEmail: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'save', email: string, code: string): void
}>()

const email = ref(props.currentEmail)
const verificationCode = ref('')
const loading = ref(false)
const codeSent = ref(false)
const countdown = ref(0)

// 发送验证码
const handleSendCode = async () => {
  if (!email.value) {
    showError('Por favor, insira seu e-mail')
    return
  }
  
  try {
    loading.value = true
    // TODO: 调用发送验证码的 API
    // await sendVerificationCode({ email: email.value })
    
    // 开始倒计时
    countdown.value = 60
    codeSent.value = true
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
        codeSent.value = false
      }
    }, 1000)
  } catch (error) {
    showError('Falha ao enviar código')
  } finally {
    loading.value = false
  }
}

// 确认修改
const handleConfirm = async () => {
  try {
    loading.value = true
    emit('save', email.value)
  } catch (error) {
    showError('Falha ao atualizar e-mail')
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <v-dialog
    :model-value="modelValue"
    @update:model-value="emit('update:modelValue', $event)"
    max-width="360"
    class="email-dialog"
  >
    <v-card class="email-card">
      <div class="dialog-header">
        <div class="title">editar e-mail</div>
        <v-btn icon="mdi-close" variant="text" @click="emit('update:modelValue', false)" class="close-btn" />
      </div>

      <div class="email-form">
        <!-- 邮箱输入框 -->
        <v-text-field
          v-model="email"
          label="Seu endereço de email"
          variant="outlined"
          hide-details
          class="mb-4"
          :disabled="loading"
        />

        <!-- 验证码输入框和发送按钮 -->
<!--        <div class="verification-code">-->
<!--          <v-text-field-->
<!--            v-model="verificationCode"-->
<!--            label="Código de verificação"-->
<!--            variant="outlined"-->
<!--            hide-details-->
<!--            :disabled="loading"-->
<!--          />-->
<!--          <v-btn-->
<!--            color="#FF5989"-->
<!--            :disabled="loading || codeSent"-->
<!--            @click="handleSendCode"-->
<!--            class="send-btn"-->
<!--          >-->
<!--            {{ codeSent ? `${countdown}s` : 'Enviar' }}-->
<!--          </v-btn>-->
<!--        </div>-->
      </div>

      <!-- 确认按钮 -->
      <v-btn
        block
        color="#FF5989"
        class="confirm-btn"
        :loading="loading"
        :disabled="loading"
        @click="handleConfirm"
      >
        Confirme
      </v-btn>
    </v-card>
  </v-dialog>
</template>

<style scoped lang="scss">
.email-dialog {
  :deep(.v-card) {
    background: #1B2550;
    border-radius: 12px;
    padding: 20px;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .title {
    color: #fff;
    font-size: 18px;
    font-weight: 500;
  }

  .close-btn {
    color: #fff;
  }
}

.email-form {
  margin-bottom: 20px;

  :deep(.v-field) {
    border-radius: 8px;
    background: #202D60;
    
    input {
      color: #fff;
    }
  }
}

.verification-code {
  display: flex;
  gap: 10px;
  align-items: flex-start;

  .v-text-field {
    flex: 1;
  }

  .send-btn {
    height: 56px;
    min-width: 80px;
    border-radius: 8px;
  }
}

.confirm-btn {
  height: 44px;
  font-size: 16px;
  border-radius: 22px;
  text-transform: none;
}
</style> 