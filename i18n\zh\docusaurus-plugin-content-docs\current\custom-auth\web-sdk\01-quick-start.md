---
sidebar_position: 2
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

# 快速开始

## 安装 SDK

<Tabs>
<TabItem value="npm">

```bash
npm install @unipasswallet/smart-account@0.1.1
```
</TabItem>

<TabItem value="yarn">

```bash
yarn add @unipasswallet/smart-account@0.1.1
```
</TabItem>


<TabItem value="pnpm">

```bash
pnpm add @unipasswallet/smart-account@0.1.1
```
</TabItem>
</Tabs>

### 本地调试链接

请使用 `localhost` 作为本地测试链接, 而不要使用 `127.0.0.1`.

## 相关资料
* [Online Demo](https://up-smart-account-demo.vercel.app/)
* [Demo 源码](https://github.com/UniPassID/smart-account-sdk-demo)