import type { CreateAxiosDefaults } from 'axios';
import type { IAxiosRetryConfig } from 'axios-retry';
import { stringify } from 'qs';
import { isHttpSuccess } from './shared';
import type { RequestOption, ResponseData } from './type';

export function createDefaultOptions<ResponseData = any>(options?: Partial<RequestOption<ResponseData>>) {
  const opts: RequestOption<ResponseData> = {
    onRequest: async config => config,
    isBackendSuccess: response => {
      const data = response.data as ResponseData;
      return data.status_code === 200;
    },
    onBackendFail: async response => {
      const data = response.data as ResponseData;
      if (data.status_code !== 200) {
        const error = new Error(data.data as string || '请求失败');
        (error as any).code = data.error_code;
        throw error;
      }
      return null;
    },
    transformBackendResponse: async response => {
      const data = response.data as ResponseData;
      return { data: data.data, count: data.count };
    },
    onError: async error => {
      console.error('请求错误:', error);
    }
  };

  Object.assign(opts, options);

  return opts;
}

export function createRetryOptions(config?: Partial<CreateAxiosDefaults>) {
  const retryConfig: IAxiosRetryConfig = {
    retries: 0
  };

  Object.assign(retryConfig, config);

  return retryConfig;
}

export function createAxiosConfig(config?: Partial<CreateAxiosDefaults>) {
  const TEN_SECONDS = 10 * 1000;

  const axiosConfig: CreateAxiosDefaults = {
    timeout: TEN_SECONDS,
    headers: {
      'Content-Type': 'application/json'
    },
    validateStatus: isHttpSuccess,
    paramsSerializer: params => {
      return stringify(params);
    }
  };

  Object.assign(axiosConfig, config);

  return axiosConfig;
}
