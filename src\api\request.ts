/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-13 18:50:19
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-07-21 19:44:22
 * @FilePath: \betdoce-web\src\api\request.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createAlova } from "alova";
import VueHook from "alova/vue";
import { store } from "@/store";
import { showError } from "@/utils/toast";
import {
  getToken,
  setToken,
  removeToken,
  getRefreshToken,
  setRefreshToken,
} from "@/utils/auth";
import { normalizeResponseData } from "@/utils";
import { jwtDecode } from "jwt-decode";

// Cria um barramento de eventos para lidar com o popup de login
export const loginEvent = new EventTarget();
export const SHOW_LOGIN_EVENT = "showLogin";

// 跳转到禁止国家页面的函数
const navigateToForbiddenCountry = () => {
  if (typeof window !== "undefined") {
    // 优先使用 router.push，如果失败则使用 window.location.href
    try {
      import("@/router")
        .then(({ default: router }) => {
          router.push("/forbidden-country").catch(() => {
            // 如果 router.push 失败，回退到 window.location.href
            window.location.href = "/forbidden-country";
          });
        })
        .catch(() => {
          // 如果动态导入失败，直接使用 window.location.href
          window.location.href = "/forbidden-country";
        });
    } catch (error) {
      // 如果出现任何错误，使用 window.location.href
      window.location.href = "/forbidden-country";
    }
  }
};

// Gerenciamento de estado de atualização do token
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value: any) => void;
  reject: (reason: any) => void;
}> = [];

// Processa requisições na fila
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });

  failedQueue = [];
};

// Função de atualização do token
const refreshToken = async (): Promise<string | null> => {
  if (isRefreshing) {
    // Se estiver atualizando, retorna uma Promise para aguardar a conclusão
    return new Promise((resolve, reject) => {
      failedQueue.push({ resolve, reject });
    });
  }

  isRefreshing = true;

  try {
    const refreshTokenValue = getRefreshToken();
    if (!refreshTokenValue) {
      throw new Error("No refresh token available");
    }

    // Usa fetch diretamente para chamar a interface refresh_token, evitando usar a instância alova para prevenir loop infinito
    const response = await fetch(`${getBaseURL()}/refresh_token`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: JSON.stringify({ refresh_token: refreshTokenValue }),
    });

    if (!response.ok) {
      throw new Error("Refresh token request failed");
    }

    const { data } = await response.json();

    if (data && data.token) {
      setToken(data.token);
      if (data.refresh_token) {
        setRefreshToken(data.refresh_token);
      }

      processQueue(null, data.token);
      return data.token;
    } else {
      throw new Error("Failed to refresh token");
    }
  } catch (error: any) {
    processQueue(error, null);
    // Falha na atualização, limpa todos os tokens e dispara o login
    removeToken();
    store.dispatch("auth/logout", 1);
    loginEvent.dispatchEvent(new Event(SHOW_LOGIN_EVENT));
    throw error;
  } finally {
    isRefreshing = false;
  }
};
// Verifica se o token está prestes a expirar (dentro de 5 minutos)
const isTokenExpiringSoon = (token: string): boolean => {
  try {
    const decoded = jwtDecode(token);
    const currentTime = Math.floor(Date.now() / 1000);
    const expirationTime = (decoded as any).exp;
    // Se o token expirar em 5 minutos, retorna true
    return expirationTime && expirationTime - currentTime < 300; // 300 segundos = 5 minutos
  } catch (error) {
    // Se a análise falhar, considera o token inválido
    return true;
  }
};

// Define a interface do formato de resposta do backend
interface BackendResponse<T = any> {
  status_code: number;
  error_code: string;
  data: T;
  count?: number;
}

// Define o tipo de resposta
type ResponseType = "json" | "text" | "blob" | "arrayBuffer" | "formData";

// Map de desduplicação de requisições
const deduplicationMap = new Map<string, Promise<any>>();

// Gera a chave da requisição
const generateReqKey = (config: any) => {
  const { url, method, params, data } = config;
  return [url, method, JSON.stringify(params), JSON.stringify(data)].join("&");
};

// Determina baseURL de acordo com o ambiente
const getBaseURL = () => {
  if (import.meta.env.DEV) {
    // Ambiente de desenvolvimento usa proxy
    return "/front";
  }
  // Ambiente de produção usa endereço real da API
  return import.meta.env.VITE_APP_BASE_URL || "https://zsj.kk2133lz.com";
};

// 创建请求实例
const alovaInstance = createAlova({
  baseURL: getBaseURL(),
  timeout: 10000,
  statesHook: VueHook,

  // Desabilita cache de requisições

  // Adaptador de requisição
  requestAdapter: (elements, method) => {
    const requestKey = generateReqKey(elements);
    const controller = new AbortController();
    const responseType =
      ((method.config as any)?.responseType as ResponseType) || "json";

    // Verifica se existe uma requisição idêntica
    if (deduplicationMap.has(requestKey)) {
      return {
        response: async () => {
          return deduplicationMap.get(requestKey);
        },
        headers: async () => ({}),
        abort: () => controller.abort(),
        onUpload: undefined,
        onDownload: undefined,
      };
    }

    // Cria uma nova requisição
    const requestPromise = (async () => {
      try {
        const requestConfig: RequestInit = {
          method: elements.type,
          headers: elements.headers as HeadersInit,
          signal: controller.signal,
        };

        if (elements.data) {
          if (elements.data instanceof FormData) {
            requestConfig.body = elements.data;
          } else {
            requestConfig.body = JSON.stringify(elements.data);
          }
        }

        const response = await fetch(elements.url, requestConfig);
        console.log(response);
        let data;

        // Processa dados de resposta de acordo com responseType
        if (response.status === 200) {
          switch (responseType) {
            case "json":
              data = await response?.json();
              break;
            case "text":
              data = await response?.text();
              break;
            case "blob":
              data = await response?.blob();
              break;
            case "arrayBuffer":
              data = await response?.arrayBuffer();
              break;
            case "formData":
              data = await response?.formData();
              break;
            default:
              data = await response?.json();
          }
        } else {
          data = response;
        }

        const headers = Object.fromEntries(response.headers.entries());

        return { response: data, headers };
      } finally {
        // Remove do Map após a conclusão da requisição
        deduplicationMap.delete(requestKey);
      }
    })();

    // Armazena a Promise da requisição no Map
    deduplicationMap.set(requestKey, requestPromise);

    return {
      response: async () => requestPromise,
      headers: async () => ({}),
      abort: () => {
        controller.abort();
        deduplicationMap.delete(requestKey);
      },
      onUpload: undefined,
      onDownload: undefined,
    };
  },

  // Interceptador de requisição
  beforeRequest: async function (method) {
    const headers: Record<string, string> = {
      Accept: "application/json",
      "X-Requested-With": "XMLHttpRequest",
      "Cache-Control": "no-cache, no-store, must-revalidate",
      Pragma: "no-cache",
      Expires: "0",
    };

    if (method.data && !(method.data instanceof FormData)) {
      headers["Content-Type"] = "application/json";
    }

    const token = getToken();
    if (token) {
      // Verifica se o token está prestes a expirar
      if (isTokenExpiringSoon(token)) {
        try {
          const newToken = await refreshToken();
          if (newToken) {
            headers["Authorization"] = `Bearer ${newToken}`;
          } else {
            headers["Authorization"] = `Bearer ${token}`;
          }
        } catch (error) {
          // Falha na atualização, usa o token original para continuar a requisição
          headers["Authorization"] = `Bearer ${token}`;
        }
      } else {
        headers["Authorization"] = `Bearer ${token}`;
      }
    }

    method.config = {
      ...method.config,
      headers: {
        ...method.config?.headers,
        ...headers,
      },
    };
  },

  // Interceptador de resposta
  responded: {
    onSuccess: async (response: any) => {
      // console.log(response);
      
      // Tratamento de erro de gateway - verifica código de status HTTP
      if (response.response.status && response.response.status >= 400) {
        let errorMessage = "Erro de gateway";

        switch (response.response.status) {
          case 400:
            errorMessage = "Parâmetros de requisição inválidos";
            break;
          case 401:
            // errorMessage = "Acesso não autorizado";
            // store.dispatch("auth/logout", 1);
            // loginEvent.dispatchEvent(new Event(SHOW_LOGIN_EVENT));
            break;
          case 403:
            // 跳转到国家限制页面
            navigateToForbiddenCountry();
            return;
          case 404:
            errorMessage = "Recurso solicitado não encontrado";
            break;
          case 405:
            errorMessage = "Método de requisição não permitido";
            break;
          case 408:
            errorMessage = "Tempo limite da requisição excedido";
            break;
          case 429:
            errorMessage = "Muitas requisições, tente novamente mais tarde";
            break;
          case 500:
            errorMessage = "Erro interno do servidor";
            break;
          case 502:
            errorMessage = "Erro de gateway";
            break;
          case 503:
            errorMessage = "Serviço temporariamente indisponível";
            break;
          case 504:
            errorMessage = "Tempo limite do gateway excedido";
            break;
          default:
            errorMessage = `Erro HTTP ${response.response.status}`;
        }

        showError(errorMessage);
        throw new Error(errorMessage);
      }

      // Verifica o Content-Type da resposta
      const contentType = response.headers?.["content-type"];
      if (contentType && contentType.includes("text/html")) {
        // Se for HTML, pode ser uma página de erro do servidor, lança erro
        const text = await response; // Lê o conteúdo de texto para debug
        console.log("Received HTML response:", text);
        return text;
        // showError(
        //   "O servidor retornou uma página HTML, pode ser um erro de gateway"
        // );
        // throw new Error("Erro de gateway: O servidor retornou uma página HTML");
      }

      // Se não for HTML, segue o fluxo de processamento JSON original
      const json = response.response;

      // Verifica a estrutura dos dados de resposta
      if (!json || typeof json !== "object") {
        showError("Formato de dados de resposta inválido");
        throw new Error("Formato de dados de resposta inválido");
      }

      if ([200].includes(json.status_code)) {
        return json?.count
          ? { data: json.data, count: json?.count || null }
          : json.data;
      } else if ([203].includes(json.status_code)) {
        return { code: 203, msg: json.data };
      } else { 
        switch (json.status_code) {
          case 403:
            store.dispatch("auth/logout", 1);
            loginEvent.dispatchEvent(new Event(SHOW_LOGIN_EVENT));
            showError(
              json.data ||
                json.error_code ||
                "Sessão expirada, faça login novamente"
            );
            throw (
              json.data ||
              json.error_code ||
              "Sessão expirada, faça login novamente"
            );
          case 500:
            showError("Erro interno do servidor");
            throw new Error("Erro interno do servidor");
          case 502:
            showError("Erro de gateway");
            throw new Error("Erro de gateway");
          case 503:
            showError("Serviço temporariamente indisponível");
            throw new Error("Serviço temporariamente indisponível");
          case 504:
            showError("Tempo limite do gateway excedido");
            throw new Error("Tempo limite do gateway excedido");
          default:
            showError(json.data || "Erro desconhecido");
            throw json.data || json.error_code || "Erro desconhecido";
        }
      }
    },

    onError: (error: Error) => {
      console.log(error);

      // Processa diferentes tipos de erro
      if (error.name === "AbortError") {
        console.log("Requisição cancelada");
        return;
      }

      if (error.name === "TypeError" && error.message.includes("fetch")) {
        // Erro de conexão de rede
        showError(
          "Falha na conexão de rede, verifique as configurações de rede"
        );
        throw new Error("Falha na conexão de rede");
      }

      if (error.message.includes("timeout")) {
        // Tempo limite da requisição
        showError(
          "Tempo limite da requisição excedido, tente novamente mais tarde"
        );
        throw new Error("Tempo limite da requisição excedido");
      }

      if (error.message.includes("Failed to fetch")) {
        // Erro de rede
        showError("Erro de rede, verifique a conexão com a internet");
        throw new Error("Erro de rede");
      }

      // Outros erros desconhecidos
      const normalizedError = normalizeResponseData({
        status_code: 500,
        error_code: error.message || "Falha na conexão de rede",
        data: null,
      });
      showError(normalizedError.error_code);
      throw error;
    },
  },
});

export default alovaInstance;
