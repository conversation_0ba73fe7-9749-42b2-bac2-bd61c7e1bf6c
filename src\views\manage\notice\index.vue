<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <NoticeSearch
      v-model:model="searchParams"
      @reset="
        () => {
          resetSearchParams();
          getDataByPage();
        }
      "
      @search="getDataByPage"
    >
      <template #table-operation>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :isNoDelete="true"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
    </NoticeSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-60px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="checkedRowKeys = $event.map((row) => row.id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
      <NoticeOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </ElCard>
  </div>
</template>

<script setup lang="tsx">
import {
  ElButton,
  ElPopconfirm,
  ElTag,
  ElSwitch,
  ElPopover,
} from "element-plus";
import { fetchGetNoticeList, fetchUpdateNoticeState } from "@/service/api";
import { useTable, useTableOperate } from "@/hooks/common/table";
import { $t } from "@/locales";
import NoticeSearch from "./modules/notice-search.vue";
import NoticeOperateDrawer from "./modules/notice-operate-drawer.vue";
import { useAuth } from "@/hooks/business/auth";

const { hasAuth } = useAuth();

defineOptions({ name: "NoticeManage" });

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  columnChecks,
} = useTable<Api.SystemManage.NoticeItem>({
  apiFn: fetchGetNoticeList,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 20,
    notice_type: undefined,
    subject: undefined,
    status: undefined,
  },
  columns: () => [
    { prop: "index", label: $t("common.index"), width: 64 },
    {
      prop: "notice_type",
      label: "消息类型",
      width: 120,
      formatter: (row) => {
        const noticeTypeMap = {
          1: "消息中心",
          2: "系统内公告",
          3: "活动通知",
        };
        return noticeTypeMap[row.notice_type] || "未知类型";
      },
    },
    { prop: "user_uuid", label: "用户ID", minWidth: 80 },
    { prop: "subject", label: "标题", minWidth: 200 },
    {
      prop: "content",
      label: "内容",
      minWidth: 150,
      formatter: (row) => {
        try {
          return (
            <ElPopover placement="top-start" width="400" trigger="hover">
              {{
                reference: () => (
                  <span
                    class="cursor-pointer"
                    style="max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                  >
                    {row.content}
                  </span>
                ),
                default: () => (
                  <div class="notice-content">
                    <div class="content-item">
                      <span class="label">消息内容：</span>
                      <span class="value" v-html={row.content}></span>
                    </div>
                  </div>
                ),
              }}
            </ElPopover>
          );
        } catch (e) {
          return row.content;
        }
      },
    },
    {
      prop: "status",
      label: "状态",
      width: 100,
      formatter: (row) => {
        if (row.status === undefined) {
          return "";
        }

        const tagMap: Record<Api.Common.EnableStatus, UI.ThemeColor> = {
          1: "success",
          0: "error",
        };
        const label = ["禁用", "启用"][row.status];
        return <ElTag type={tagMap[row.status]}>{label}</ElTag>;
      },
    },
    {
      width: 140,
      prop: "operate",
      label: $t("common.operate"),
      align: "center",
      formatter: (row: Api.SystemManage.NoticeItem) => (
        <div class="flex-center">
          {hasAuth(2) && (
            <ElButton
              plain
              type={row.status === 1 ? "danger" : "success"}
              size="small"
              onClick={() =>
                handleStatusChange(row.id, row.status === 1 ? 0 : 1)
              }
            >
              {row.status === 1 ? "禁用" : "启用"}
            </ElButton>
          )}
          {hasAuth(3) && (
            <ElButton
              type="primary"
              plain
              size="small"
              onClick={() => edit(row.id)}
            >
              {$t("common.edit")}
            </ElButton>
          )}
        </div>
      ),
    },
  ],
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  onDeleted,
  checkedRowKeys,
} = useTableOperate<Api.SystemManage.NoticeItem>(data, getData, "id");

async function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.warning("请选择要删除的公告");
    return;
  }
}

async function handleStatusChange(id: number, value: number) {
  const { error } = await fetchUpdateNoticeState({
    ids: [id],
    status: value,
  });
  if (!error) {
    window.$message?.success("状态更新成功");
    getData();
  }
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
