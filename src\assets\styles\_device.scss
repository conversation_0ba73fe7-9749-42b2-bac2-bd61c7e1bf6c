// 设备类型样式混入
@mixin device($type) {
  @if $type == 'mobile' {
    @media screen and (max-width: 767px) { @content; }
  }
  @else if $type == 'tablet' {
    @media screen and (min-width: 768px) and (max-width: 1024px) { @content; }
  }
  @else if $type == 'pc' {
    @media screen and (min-width: 1025px) { @content; }
  }
}

// 方向样式混入
@mixin orientation($direction) {
  @media screen and (orientation: $direction) { @content; }
}

// 设备特定样式
@mixin mobile-only {
  @include device('mobile') { @content; }
}

@mixin tablet-only {
  @include device('tablet') { @content; }
}

@mixin desktop-only {
  @include device('pc') { @content; }
}

@mixin mobile-tablet {
  @media screen and (max-width: 1024px) { @content; }
}

// 触摸设备样式
@mixin touch-device {
  @media (hover: none) and (pointer: coarse) { @content; }
}

// 高DPI设备样式
@mixin high-dpi {
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) { @content; }
}

// 安全区域适配（适用于刘海屏等）
@mixin safe-area-inset($position) {
  @if $position == 'top' {
    padding-top: env(safe-area-inset-top);
  }
  @else if $position == 'bottom' {
    padding-bottom: env(safe-area-inset-bottom);
  }
  @else if $position == 'left' {
    padding-left: env(safe-area-inset-left);
  }
  @else if $position == 'right' {
    padding-right: env(safe-area-inset-right);
  }
} 