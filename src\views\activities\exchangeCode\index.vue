<script setup lang="ts">
import { ref, h, onMounted } from "vue";
import { useTable, useTableOperate } from "@/hooks/common/table";
import {
  fetchExchangeCodeList,
  deleteExchangeCode,
} from "@/service/api/exchangeCode";
import type {
  ExchangeCodeItem,
  ExchangeCodeListParams,
  ExchangeCodeListResponse,
} from "@/service/api/exchangeCode";

import Search from "./modules/search.vue";
import TableHeaderOperation from "@/components/advanced/table-header-operation.vue";
import CreateDrawer from "./modules/create-drawer.vue";
import {
  ElButton,
  ElPopconfirm,
  ElMessageBox,
  ElMessage,
  ElPopover,
} from "element-plus";
import { useAuth } from "@/hooks/business/auth";
import moment from "moment";
import { getBrazilDate } from "@/utils/format";

const { hasAuth } = useAuth();

defineOptions({ name: "ExchangeCodeList" });
const amountTypeOptions = [
  { label: "赠金", value: 1 },
  { label: "现金", value: 2 },
  { label: "打码提现", value: 3 },
];

const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams,
  resetSearchParams,
} = useTable({
  apiFn: fetchExchangeCodeList,
  apiParams: {
    page: 1,
    size: 20,
    sort: "id",
    start_time: undefined,
    end_time: undefined,
  },
  columns: () => [
    { type: "selection", width: 48 },
    { type: "index", label: "序号", width: 64 },
    { prop: "activity_period", label: "活动期数", minWidth: 120 },
    { prop: "redemption_pass", label: "兑换码", minWidth: 120 },
    { prop: "code_count", label: "生成数量", width: 100 },
    {
      prop: "amount_type",
      label: "金额类型",
      width: 100,
      formatter: (row: ExchangeCodeItem) =>
        ["", "赠金", "现金", "打码提现"][row.amount_type],
    },
    {
      prop: "bonus",
      label: "固定金额",
      width: 100,
      formatter: (row: ExchangeCodeItem) =>
        !row.random_odds ? `${(row.bonus / 100).toFixed(2)}` : "-",
    },
    {
      prop: "random_odds",
      label: "随机金额配置",
      minWidth: 250,
      align: "left",
      formatter: (row: ExchangeCodeItem) => {
        if (row.bonus === 0 && !row.random_odds) return "-";

        try {
          const oddsArray = row.random_odds.split(";").filter(Boolean);
          if (!oddsArray.length) return "-";

          // 创建表格中显示的触发按钮
          const triggerContent = h("div", [
            h(
              "span",
              {
                style: {
                  padding: "0 5px",
                },
              },
              row.random_odds,
            ),
          ]);

          // 创建弹出内容
          const popoverContent = h("div", { class: "random-odds-content" }, [
            h("div", { class: "content-header" }, [
              h("span", { class: "title" }, "随机金额配置"),
            ]),
            h("div", { class: "content-body" }, [
              h("div", { class: "odds-table" }, [
                h("div", { class: "table-header" }, [
                  h("span", { class: "col-amount" }, "金额"),
                  h("span", { class: "col-percent" }, "概率"),
                ]),
                h(
                  "div",
                  { class: "table-body" },
                  oddsArray.map((rule) => {
                    const [amount, percent] = rule
                      .split(":")
                      .map((str) => str.trim());
                    const formattedAmount = parseFloat(amount).toFixed(2);
                    const formattedPercent = parseFloat(percent).toFixed(1);

                    return h("div", { class: "table-row" }, [
                      h("span", { class: "col-amount" }, [
                        h("span", { class: "currency" }, "￥"),
                        h("span", { class: "amount" }, formattedAmount),
                      ]),
                      h(
                        "span",
                        { class: "col-percent" },
                        `${formattedPercent}%`,
                      ),
                    ]);
                  }),
                ),
              ]),
            ]),
          ]);

          return h(
            ElPopover,
            {
              trigger: "hover",
              placement: "right",
              width: 200,
              popperClass: "random-odds-popover",
            },
            {
              default: () => popoverContent,
              reference: () => triggerContent,
            },
          );
        } catch (e) {
          console.error("Error parsing random_odds:", e);
          return "-";
        }
      },
    },
    { prop: "exchanged_count", label: "兑换数量", width: 100 },
    {
      prop: "user_type",
      label: "参与对象",
      width: 120,
      formatter: (row: ExchangeCodeItem) => {
        const userTypeMap = {
          1: "全部",
          2: "VIP",
          3: "普通用户",
        };
        return userTypeMap[row.user_type] || "-";
      },
    },
    {
      prop: "deadline_time",
      label: "截止时间",
      width: 180,
      formatter: (row: ExchangeCodeItem) =>
        row.deadline_time
          ? moment(row.deadline_time).format("YYYY-MM-DD HH:mm:ss")
          : "-",
    },
    {
      prop: "create_at_formatted",
      label: "创建时间",
      width: 180,
       formatter: (row: ExchangeCodeItem) =>
        row.deadline_time
          ? moment(row.create_at_formatted).format("YYYY-MM-DD HH:mm:ss")
          : "-",
    },
    {
      prop: "operation",
      label: "操作",
      width: 150,
      fixed: "right",
      formatter: (row: ExchangeCodeItem) => {
        const renderDeleteButton = () =>
          h(
            ElButton,
            {
              type: "danger",
              plain: true,
              size: "small",
            },
            { default: () => "删除" },
          );

        return h("div", { class: "flex-center" }, [
          hasAuth(3) &&
            h(
              ElButton,
              {
                type: "primary",
                plain: true,
                size: "small",
                onClick: () => handleEdit(row.id),
              },
              { default: () => "编辑" },
            ),
          hasAuth(2) &&
            h(
              ElPopconfirm,
              {
                title: "确认删除该兑换码?",
                onConfirm: () => handleDelete(row),
              },
              { reference: renderDeleteButton },
            ),
        ]);
      },
    },
  ],
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  onBatchDeleted,
  onDeleted,
} = useTableOperate(data, getData);

const handleDelete = async (row: ExchangeCodeItem) => {
  try {
    await deleteExchangeCode(row.id);
    ElMessage.success("删除成功");
    onDeleted();
  } catch (error) {
    console.error("Delete exchange code failed:", error);
  }
};

const handleSubmitted = async () => {
  await getDataByPage();
};
const handleSearch = async () => {
  // if (val) {
  //   searchParams.start_time = val.start_time;
  //   searchParams.end_time = val.end_time;
  // }
  getDataByPage();
};
onMounted(() => {
  getDataByPage();
});
</script>

<template>
  <div
    class="exchange-code-list min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <Search
      v-model:model="searchParams"
      @reset="
        () => {
          resetSearchParams();
        }
      "
      @search="handleSearch"
    >
      <template #table-operation>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :loading="loading"
          isNoDelete
          @add="handleAdd"
          @refresh="getData"
        >
          <template #default></template>
        </TableHeaderOperation>
      </template>
    </Search>
    <ElCard class="sm:flex-1-hidden">
      <div class="h-[calc(100%-60px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="checkedRowKeys = $event.map((row) => row.id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
    </ElCard>
    <CreateDrawer
      v-model:visible="drawerVisible"
      :row="editingData"
      @submitted="handleSubmitted"
    />
  </div>
</template>

<style lang="scss" scoped>
.exchange-code-list {
  :deep(.el-table) {
    .cell {
      line-height: 20px;
    }
  }

  .random-odds-trigger {
    display: inline-flex;
    align-items: center;
    padding: 2px 12px;
    background: var(--el-color-primary-light-9);
    border: 1px solid var(--el-color-primary-light-5);
    border-radius: 4px;
    cursor: pointer;

    .trigger-text {
      font-size: 12px;
      color: var(--el-color-primary);
    }
  }
}

.el-popper.random-odds-popover {
  padding: 0;

  .random-odds-content {
    .content-header {
      padding: 8px 12px;
      background: var(--el-fill-color-light);
      border-bottom: 1px solid var(--el-border-color-lighter);

      .title {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
    }

    .content-body {
      padding: 8px 0;

      .odds-table {
        .table-header {
          display: flex;
          align-items: center;
          padding: 0 12px 8px;
          border-bottom: 1px solid var(--el-border-color-lighter);
          font-size: 13px;
          color: var(--el-text-color-secondary);
          font-weight: 500;

          .col-amount {
            flex: 1;
          }
          .col-percent {
            width: 80px;
            text-align: right;
          }
        }

        .table-body {
          .table-row {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            transition: background-color 0.2s;

            &:hover {
              background-color: var(--el-fill-color-light);
            }

            .col-amount {
              flex: 1;
              display: flex;
              align-items: baseline;

              .currency {
                font-size: 12px;
                color: var(--el-text-color-secondary);
                margin-right: 2px;
              }

              .amount {
                font-size: 14px;
                color: var(--el-text-color-primary);
                font-weight: 500;
              }
            }

            .col-percent {
              width: 80px;
              text-align: right;
              font-size: 13px;
              color: var(--el-text-color-regular);
              background: var(--el-fill-color-lighter);
              padding: 2px 8px;
              border-radius: 10px;
            }
          }
        }
      }
    }
  }
}

.flex-center {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
}

// :deep(.el-card__body) {
//   height: calc(100% - 50px);
// }
</style>
