import type { AxiosResponse } from 'axios';
import { BACKEND_ERROR_CODE, createFlatRequest, createRequest } from '@sa/axios';
import { useAuthStore } from '@/store/modules/auth';
import { $t } from '@/locales';
import { localStg } from '@/utils/storage';
import { getServiceBaseURL } from '@/utils/service';
import { getAuthorization, showErrorMsg } from './shared';
import type { RequestInstanceState } from './type';

const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y';
const { baseURL, otherBaseURL } = getServiceBaseURL(import.meta.env, isHttpProxy);

export const request = createFlatRequest<App.Service.Response, RequestInstanceState>(
  {
    baseURL,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    }
  },
  {
    async onRequest(config) {
      const Authorization = `Bearer ${getAuthorization()}`;
      Object.assign(config.headers, { Authorization });
      return config;
    },

    isBackendSuccess(response) {
      const resCodes = [200];
      if (resCodes.includes(response.data.status_code)) {
        return true;
      }
      return false;
    },

    onBackendFail(response, instance) {
      const authStore = useAuthStore();
      const { status_code, error_code, data } = response.data;
      // 处理 40 未授权情况
      if (status_code === 403) {
        authStore.resetStore();
        window.$message?.error(data || '登录已过期，请重新登录');
        return null;
      }
      // 显示错误消息
      //window.$message?.error(data || '请求失败');
      return null;
    },

    transformBackendResponse(response: AxiosResponse<any>) {
      // // 确保 response.data 是一个对象，而不是一个 Promise
      const resolvedData = response.data; // 等待 response.data 解析
      const { data, count } = resolvedData; // 从解析后的数据中提取 data 和 count
      if(count != undefined){
        return { data, count };
      }
      return data
    },

    onError(error) {
      let message = error.message;

      if (error.code === BACKEND_ERROR_CODE) {
        const errorData = error.response?.data?.data;
        message = typeof errorData === 'string' ? errorData : message;
      }

      // 如果是 401 错误，需要登出
      if (error.response?.data?.status_code === 403) {
        const authStore = useAuthStore();
        authStore.resetStore();
      }
      showErrorMsg(request.state, message);
    }
  }
);
