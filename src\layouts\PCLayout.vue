<template>
  <div class="pc-layout">
    <header class="pc-header">
      <slot name="header">
        <!-- 默认PC端头部 -->
        <v-app-bar>
          <!-- 头部内容 -->
        </v-app-bar>
      </slot>
    </header>

    <main class="pc-main">
      <slot></slot>
    </main>

    <footer class="pc-footer">
      <slot name="footer">
        <!-- 默认PC端底部 -->
      </slot>
    </footer>
  </div>
</template>

<script setup lang="ts">
// PC端特定逻辑
</script>

<style lang="scss" scoped>
.pc-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  .pc-main {
    flex: 1;
    width: 100%;
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 16px;
  }
}
</style> 