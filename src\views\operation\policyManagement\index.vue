<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-16 19:16:38
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-07-17 11:02:02
 * @FilePath: \betdoce-admin\src\views\operation\policyManagement\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="policy-management-container">
    <ElCard class="box-card" header="策略管理">
      <ElForm ref="policyFormRef" :model="policyForm" label-width="120px">
        <div class="flex-column gap-3 w-full">
          <ElRow
            v-for="(item, index) in policyForm.strategies"
            :key="index"
            align="middle"
            :gutter="10"
            class="w-full"
          >
            <ElCol :span="4">
              <span class="policy-title-row">
                {{ getPolicyTitle(item.coin_type) }}
              </span>
            </ElCol>
            <ElCol :span="8">
              <span>{{ getPolicyDescription(item.coin_type) }}</span>
            </ElCol>
            <ElCol :span="2">
              <span>现金</span>
            </ElCol>
            <ElCol :span="6">
              <ElInput
                v-model.number="item.cash_threshold"
                placeholder="请输入正整数"
                type="number"
                :min="0"
                @input="handleNumberInput($event, index)"
                @blur="handleNumberBlur(index)"
              />
            </ElCol>
          </ElRow>
        </div>
        <ElFormItem>
          <div class="flex-end w-full mt-4">
            <ElButton @click="cancelPolicySettings">取消</ElButton>
            <ElButton
              type="primary"
              @click="savePolicySettings"
              :loading="saveLoading"
              >保存</ElButton
            >
          </div>
        </ElFormItem>
      </ElForm>
    </ElCard>

    <ElCard class="box-card mt-4" header="日志">
      <ElTable
        :data="logData"
        v-loading="logLoading"
        height="400px"
        style="width: 100%"
      >
        <ElTableColumn prop="id" label="ID" width="80" />
        <ElTableColumn prop="operator_id" label="操作人ID" width="120" />
        <ElTableColumn prop="operation_type" label="操作类型" width="120" />
        <ElTableColumn prop="operation_field" label="操作字段" width="180" />
        <ElTableColumn prop="operation_time" label="操作时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.operation_time) }}
          </template>
        </ElTableColumn>
        <ElTableColumn prop="log_content" label="日志内容" />
      </ElTable>
      <div class="pagination-container mt-4">
        <ElPagination
          v-model:current-page="logPagination.currentPage"
          v-model:page-size="logPagination.pageSize"
          :total="logPagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="getLogDataByPage"
          @current-change="getLogDataByPage"
        />
      </div>
    </ElCard>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import moment from "moment";
import {
  ElCard,
  ElForm,
  ElFormItem,
  ElSwitch,
  ElInput,
  ElButton,
  ElTable,
  ElTableColumn,
  ElPagination,
  ElRow,
  ElCol,
} from "element-plus";
import {
  batchUpdate,
  fetchGetGameStrategyLogs,
  fetchGetGameStrategy,
} from "@/service/api/gameStrategy";
import { getBrazilDate } from "@/utils/format";

// 策略管理表单数据
const policyFormRef = ref();
const saveLoading = ref(false);

const policyForm = reactive({
  strategies: [
    {
      cash_threshold: 0,
      coin_type: "bonus",
    },
    {
      cash_threshold: 0,
      coin_type: "cash",
    },
    {
      cash_threshold: 0,
      coin_type: "both",
    },
  ],
});

// 将表单数据转换为API格式
const convertFormToApiData = () => ({
  strategies: policyForm.strategies.map((item) => ({
    cash_threshold: Number(item.cash_threshold) || 0,
    coin_type: item.coin_type,
  })),
});

// 将API数据转换为表单格式
const convertApiToFormData = (apiData: any) => {
  if (apiData.strategies && Array.isArray(apiData.strategies)) {
    policyForm.strategies = apiData.strategies.map((item: any) => ({
      cash_threshold: (item.cash_threshold || 0) / 100,
      coin_type: item.coin_type || "bonus",
    }));
  } else {
    // 兼容旧数据格式，转换为新格式
    policyForm.strategies = [
      {
        cash_threshold: apiData.bonus_game_threshold || 0,
        coin_type: "bonus",
      },
      {
        cash_threshold: apiData.cash_game_threshold || 0,
        coin_type: "cash",
      },
      {
        cash_threshold: apiData.cash_bonus_game_threshold || 0,
        coin_type: "both",
      },
    ];
  }
};

// 根据coin_type获取策略标题
const getPolicyTitle = (coinType: string) => {
  const titleMap: Record<string, string> = {
    bonus: "赠金游戏",
    cash: "现金游戏",
    both: "现金+赠金游戏",
  };
  return titleMap[coinType] || "未知类型";
};

// 根据coin_type获取策略描述
const getPolicyDescription = (coinType: string) => {
  const descriptionMap: Record<string, string> = {
    bonus: "最低可见门槛",
    cash: "提醒充值门槛",
    both: "提醒充值门槛",
  };
  return descriptionMap[coinType] || "未知描述";
};

// 处理数字输入，只允许正整数
const handleNumberInput = (value: string, index: number) => {
  // 移除非数字字符
  const cleanValue = value.replace(/[^0-9]/g, '');

  // 如果输入的不是纯数字，更新为清理后的值
  if (cleanValue !== value) {
    policyForm.strategies[index].cash_threshold = cleanValue ? parseInt(cleanValue) : 0;
  }
};

// 处理数字输入框失焦事件，确保值为正整数
const handleNumberBlur = (index: number) => {
  const value = policyForm.strategies[index].cash_threshold;

  // 确保值为非负整数
  if (value < 0 || !Number.isInteger(value)) {
    policyForm.strategies[index].cash_threshold = Math.max(0, Math.floor(value || 0));
  }
};

// 获取策略数据
const getPolicyData = async () => {
  try {
    const response = await fetchGetGameStrategy();
    if (response && response.data) {
      convertApiToFormData(response.data.data);
    }
  } catch (error) {
    console.error("获取策略数据失败:", error);
    ElMessage.error("获取策略数据失败");
  }
};

// 格式化时间戳
const formatTime = (timestamp: number) => {
  if (!timestamp) return "-";
  return moment(getBrazilDate(timestamp)).format("YYYY.MM.DD HH:mm:ss");
};

const cancelPolicySettings = () => {
  // 重新获取数据，重置表单
  getPolicyData();
  ElMessage.info("已取消修改");
};

const savePolicySettings = async () => {
  try {
    saveLoading.value = true;
    const apiData = convertFormToApiData();
    const res = await batchUpdate(apiData);

    if (res && res.data) {
      convertApiToFormData(res.data.data);
      ElMessage.success("策略设置保存成功");
      // 保存成功后重新获取日志数据
      getLogData();
    }
  } catch (error) {
    console.error("保存策略设置失败:", error);
    ElMessage.error("保存策略设置失败");
  } finally {
    saveLoading.value = false;
  }
};

// 日志数据相关
const logData = ref([]);
const logLoading = ref(false);
const logPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 获取日志数据
const getLogData = async () => {
  try {
    logLoading.value = true;
    const params = {
      page: logPagination.currentPage,
      size: logPagination.pageSize,
    };
    const response = await fetchGetGameStrategyLogs(params);
    if (response && response.data) {
      logData.value = response.data.data || [];
      logPagination.total = response.data.count || 0;
    }
  } catch (error) {
    console.error("获取日志数据失败:", error);
    ElMessage.error("获取日志数据失败");
  } finally {
    logLoading.value = false;
  }
};

const getLogDataByPage = () => {
  getLogData();
};

onMounted(() => {
  getPolicyData(); // 页面加载时获取策略数据
  getLogData(); // 页面加载时获取日志数据
});
</script>

<style lang="scss" scoped>
.policy-management-container {
  padding: 20px;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-end {
  display: flex;
  justify-content: flex-end;
}

.w-full {
  width: 100%;
  .policy-title-row {
    font-size: 16px;
    font-weight: bold;
    color: #222;
    // margin-bottom: 12px;
    // margin-top: 24px;
    text-align: left;
  }
}

.gap-3 {
  gap: 12px; // 根据需要调整间距
}

.flex-col-gap-3 {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mt-4 {
  margin-top: 16px; // 根据需要调整间距
}

.mb-2 {
  margin-bottom: 8px; // 调整间距
}
</style>
