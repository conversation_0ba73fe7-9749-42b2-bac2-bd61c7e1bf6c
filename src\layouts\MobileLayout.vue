<template>
  <v-app class="mobile-layout">
    <!-- 顶部导航栏 -->
    <MobileHeaderNav
      v-if="!isGamePage"
      @toggle-drawer="drawer = !drawer"
      class="mobile-header"
    />

    <!-- 侧边抽屉 -->
    <v-navigation-drawer
      v-model="drawer"
      temporary
      location="left"
      width="280"
      class="mobile-drawer"
    >
      <SideNav />
    </v-navigation-drawer>

    <!-- 主内容区域 -->
    <v-main class="mobile-main" :class="{ 'game-page': isGamePage }">
      <div class="main-scroll-container">
        <div class="content-wrapper">
          <router-view></router-view>
        </div>
      </div>
    </v-main>

    <!-- 底部导航栏 -->
    <div v-if="!isGamePage" class="mobile-bottom-nav-wrapper">
      <v-bottom-navigation
        v-model="activeTab"
        class="mobile-bottom-nav"
        grow
        color="primary"
      >
        <v-btn value="home" :ripple="false" @click="$router.push('/home')">
          <img
            src="@/assets/images/h5/tabar-icon-01.png"
            v-if="activeTab !== 'home'"
          />
          <img src="@/assets/images/h5/tabar-icon-01-active.png" v-else />
          <span>Jogo</span>
        </v-btn>

        <v-btn
          value="ActivityMenu"
          :ripple="false"
          @click="$router.push('/activity-menu')"
        >
          <img
            src="@/assets/images/h5/tabar-icon-02.png"
            v-if="activeTab !== 'ActivityMenu'"
          />
          <img src="@/assets/images/h5/tabar-icon-02-active.png" v-else />
          <span>Atividade</span>
        </v-btn>
        <v-btn
          :ripple="false"
          @click="$router.push('/activity/Atividades-Pinduoduo')"
        >
          <img class="pinduoduo" src="@/assets/images/menu-icon-06.png" />

          <span>Bônus</span>
        </v-btn>
        <v-btn value="invite" :ripple="false" @click="$router.push('/invite')">
          <img
            src="@/assets/images/h5/tabar-icon-03.png"
            v-if="activeTab !== 'invite'"
          />
          <img src="@/assets/images/h5/tabar-icon-03-active.png" v-else />
          <span>Convidar</span>
        </v-btn>

        <v-btn
          value="userInfo"
          :ripple="false"
          @click="$router.push('/user-info')"
        >
          <img
            src="@/assets/images/h5/tabar-icon-04.png"
            v-if="activeTab !== 'userInfo'"
          />
          <img src="@/assets/images/h5/tabar-icon-04-active.png" v-else />
          <span>Bônus VIP</span>
        </v-btn>
      </v-bottom-navigation>
    </div>

    <FloatButtons v-if="!isGamePage" />
  </v-app>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { useRoute } from "vue-router";
import MobileHeaderNav from "@/layouts/components/MobileHeaderNav.vue";
import SideNav from "./components/SideNav.vue";
import FloatButtons from "@/components/mobile/FloatButtons.vue";

const route = useRoute();
const drawer = ref(false);

// 判断是否为游戏页面
const isGamePage = computed(() => {
  if (route.path.startsWith("/m/game-list")) {
    return false;
  } else {
    return route.path.startsWith("/m/game");
  }
});

// 当前激活的标签
const activeTab = computed(() => {
  const path = route.path;
  if (path.startsWith("/home")) return "home";
  if (path.startsWith("/activity-menu")) return "ActivityMenu";
  if (path.startsWith("/invite")) return "invite";
  if (path.startsWith("/user-info")) return "userInfo";
  return "more";
});
</script>

<style lang="scss">
.mobile-layout {
  .v-layout {
    padding: 0 !important;
    height: 100vh !important;
    position: fixed !important;
    width: 100%;
    left: 0;
    top: 0;
  }

  .v-navigation-drawer {
    z-index: 2001 !important;
  }

  :deep(.v-main) {
    padding: 0 !important;
    height: 100% !important;
  }
}

// 确保底部导航在移动端正确显示
:deep(.v-bottom-navigation) {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  background: none !important;
  height: calc(56px + env(safe-area-inset-bottom, 0px)) !important;
  z-index: 1000 !important;
  padding-bottom: env(safe-area-inset-bottom, 0px) !important;
}
</style>

<style lang="scss" scoped>
@keyframes scaleAnimation {
  0% {
    transform: scale(1); /* 初始大小 */
  }
  50% {
    transform: scale(0.8); /* 放大到1.5倍 */
  }
  100% {
    transform: scale(1); /* 恢复原始大小 */
  }
}
.mobile-layout {
  height: 100vh;

  overflow: hidden;
}

.mobile-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 56px;
  padding-top: env(safe-area-inset-top, 0px);
}

.mobile-main {
  position: fixed;
  top: calc(env(safe-area-inset-top, 0px));
  left: 0;
  right: 0;
  bottom: calc(env(safe-area-inset-bottom, 0px));
  background: #00551d;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
  &.game-page {
    top: 0;
    bottom: 0;
  }
}

.content-wrapper {
  min-height: 100%;
}

.mobile-bottom-nav-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
  border-top: 1px solid #99e597;
  height: calc(56px + env(safe-area-inset-bottom, 0px));
  :deep(.v-bottom-navigation) {
    overflow: visible;
  }
}

.mobile-bottom-nav {
  height: 56px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: #1f3c70 !important;

  :deep(.v-btn) {
    min-width: 0;
    flex: 1;
    color: #acacac;
    height: 56px !important;
    padding-bottom: 0 !important;

    &.v-btn--active {
      color: white !important;
      background: none;
      .v-btn__overlay {
        display: none;
      }
    }

    &:hover {
      color: white;
    }

    img {
      margin-bottom: 4px;
      width: 22px;
      height: 22px;
      object-fit: contain;
    }
    .pinduoduo {
      width: 70px;
      height: 70px;
      margin-top: -50px;
      animation: scaleAnimation 2s infinite; /* 动画持续2秒，循环播放 */
    }
    span {
      font-size: 0.6rem;
      font-weight: 500;
      white-space: nowrap;
      margin-bottom: env(safe-area-inset-bottom, 0px);
    }
  }
}

.mobile-drawer {
  background: #1f3c70;
  position: fixed;
  top: 56px !important;
  bottom: 0;

  :deep(.v-list) {
    background: transparent;

    .v-list-item {
      min-height: 56px;

      &:hover {
        background: rgba(255, 255, 255, 0.05);
      }

      .v-list-item-title {
        color: white;
        font-size: 0.9rem;
      }
    }
  }
}
</style>
