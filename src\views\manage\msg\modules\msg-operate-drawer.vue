<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-16 14:17:30
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-06-21 17:49:29
 * @FilePath: \betdoce-admin\src\views\manage\msg\modules\msg-operate-drawer.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    :title="'人工发送短信'"
    width="50%"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <el-form-item label="套餐渠道ID" prop="package_channel_id">
        <span>{{ formData.package_channel_id }}</span>
      </el-form-item>
      <el-form-item label="发送渠道" prop="send_channel">
        <span>{{ formData.send_channel }}</span>
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <span>{{ formData.mobile }}</span>
        <!-- <el-input v-model="" placeholder="请输入手机号"></el-input> -->
      </el-form-item>
      <el-form-item label="短信模板" prop="template_type">
        <el-select
          v-model="formData.template_type"
          filterable
          placeholder="请选择短信模板"
          style="width: 100%"
          @change="handleSelectTemplateList"
        >
          <el-option
            v-for="item in smsTemplateList"
            :key="item.id"
            :label="item.template_name"
            :value="item.template_code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="模板示例：">
        <span>{{ content }}</span>
        <!-- <el-input v-model="" placeholder="请输入手机号"></el-input> -->
      </el-form-item>
      <el-form-item label="发送内容" prop="content">
        <el-input
          v-model="formData.content"
          placeholder="请输入发送内容"
          type="textarea"
          :rows="4"
        ></el-input>
      </el-form-item>
      <!-- 您可以根據實際需求添加更多表單項 -->
    </el-form>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { manualSend, fetchGetSMSTemplateList } from "@/service/api/syssms";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  operateType: {
    type: String,
    default: "add", // 'add' or 'edit'
  },
  rowData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:visible", "submitted"]);

const formRef = ref(null);
const formData = ref({
  package_channel_id: "",
  send_channel: "",
  mobile: "",
  content: "",
  template_type: "",
  // 初始化其他表單字段
});

const rules = {
  content: [
    {
      required: true,
      message: "请输入短信内容",
      trigger: "blur",
    },
  ],
  template_type: [
    { required: true, message: "请选择短信模板", trigger: "change" },
  ],
  mobile: [
    {
      required: true,
      message: "请输入手机号",
      trigger: "blur",
    },
  ],
  // 定義其他表單規則
};
const smsTemplateList = ref([]);
const getTemplateList = async () => {
  const res = await fetchGetSMSTemplateList();
  smsTemplateList.value = res?.data?.data || [];
};
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      getTemplateList();
      formData.value = { ...props.rowData };
    }
  },
);
const content = ref();
const handleSelectTemplateList = (e) => {
  console.log(e);
  content.value = smsTemplateList.value.filter(
    (e) => e.template_code === formData.value.template_type,
  )[0]?.content;
};
const handleClose = () => {
  formRef.value.resetFields();
  emit("update:visible", false);
};

const handleCancel = () => {
  handleClose();
};

const handleSubmit = async () => {
  const valid = await formRef.value.validate();
  if (valid) {
    try {
      const res = await manualSend({
        record_id: formData.value.id,
        content: formData.value.content,
        send_type: "sms",
        template_type: smsTemplateList.value.filter(
          (e) => e.template_code === formData.value.template_type,
        )[0]?.template_type,
      });
      console.log(res);
      if (res && res.response.status === 200) {
        ElMessage.success("短信发送成功!");
        emit("submitted");
        handleClose();
      } else {
        ElMessage.error(res?.msg || "短信发送失败");
      }
    } catch (error) {
      ElMessage.error("接口请求失败");
    }
  }
};
</script>

<style scoped></style>
