<template>
  <div class="search-wrapper">
    <ElForm ref="formRef" :model="model">
      <ElRow :gutter="16">
        <ElCol :span="4">
          <ElFormItem label="用户ID" prop="user_id">
            <ElInput v-model="model.user_id" placeholder="请输入用户ID" clearable />
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem label="操作类型" prop="operation_type">
            <ElSelect v-model="model.operation_type" placeholder="全部" clearable>
              <ElOption label="全部" :value="''" />
              <ElOption
                v-for="item in operationTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="时间" prop="dateRange">
            <ElDatePicker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="x"
              @change="
                  (val: [string, string] | null) => {
                    if (val) {
                      model.start_time = val[0];
                      model.end_time = val[1];
                    } else {
                      model.start_time = undefined;
                      model.end_time = undefined;
                    }
                  }
                "
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <div class="header-operation">
              <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElForm } from 'element-plus';

interface Props {
  model: Record<string, any>;
}

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();
const dateRange = ref()
const formRef = ref();

const operationTypeOptions = [
  { value: 1, label: '登录' },
  { value: 2, label: '登出' },
  { value: 3, label: '充值' },
  { value: 4, label: '提现' },
  { value: 5, label: '游玩' },
  { value: 6, label: '奖池获奖' },
  { value: 7, label: '后台赠金派发' },
  { value: 8, label: '后台赠金扣减' },
  { value: 9, label: '用户兑换兑换码' },
  { value: 10, label: '拼多多奖金' },
  { value: 11, label: '拼多多助力' },
];

function handleReset() {
  dateRange.value = undefined
  formRef.value?.resetFields();
  emit('reset');
}

function handleSearch() {
  emit('search');
}
</script>

<style lang="scss" scoped>
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 18px;
    margin-right: 18px;
  }
}
</style>
