import { createRouter, createWebHistory } from "vue-router";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { checkAuth } from "@/utils/auth";
import { forceScrollToTop } from "@/utils/scroll";

const MainLayout = () =>
  import(/* webpackChunkName: "layout-main" */ "@/layouts/MainLayout.vue");
const MobileLayout = () =>
  import(/* webpackChunkName: "layout-mobile" */ "@/layouts/MobileLayout.vue");

// 配置 NProgress
NProgress.configure({
  showSpinner: false,
  minimum: 0.3,
  trickleSpeed: 200,
  easing: "ease",
  speed: 500,
});

// 检测设备类型
const isMobileDevice = () => {
  return window.innerWidth <= 768;
};

const routes = [
  {
    path: "/s/:shortCode",
    name: "ShortLink",
    component: () => import("@/views/ShortLink.vue"),
  },
  {
    path: "/activity/Atividades-Pinduoduo",
    name: "PinduoduoActivity",
    component: () => import("@/views/Mobile/Activity/PinduoduoNew.vue"),
    meta: {
      requiresAuth: true,
      layout: "mobile",
    },
  },
  {
    path: "/forbidden-country",
    name: "ForbiddenCountryPc",
    component: () => import("@/views/ForbiddenCountry.vue"),
    meta: { layout: "none" },
  },
  {
    path: "/m/forbidden-country",
    name: "ForbiddenCountryMobile",
    component: () => import("@/views/ForbiddenCountry.vue"),
    meta: { layout: "none" },
  },

  // 桌面端路由
  {
    path: "/",
    component: MainLayout,
    redirect: "/home",
    children: [
      {
        path: "home",
        name: "HomeDesktop",
        component: () => import("@/views/Home/index.vue"),
      },
      {
        path: "home/:code",
        name: "HomeWithCodeDesktop",
        component: () => import("@/views/Home/index.vue"),
      },
      {
        path: "game-list",
        name: "GameListDesktop",
        component: () => import("@/views/Game/index.vue"),
      },
      {
        path: "game/:id",
        name: "GameDetailDesktop",
        component: () => import("@/views/Game/GameDetail.vue"),
        props: true,
        meta: { requiresAuth: true },
      },
      // 共用页面
      {
        path: "deposit",
        name: "Deposit",
        component: () => import("@/views/Deposit/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "desktop",
        },
      },
      {
        path: "withdraw",
        name: "Withdraw",
        component: () => import("@/views/Withdraw/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "desktop",
        },
      },
      {
        path: "account",
        name: "Account",
        component: () => import("@/views/Account/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "desktop",
        },
      },
      {
        path: "records",
        name: "Records",
        component: () => import("@/views/Records/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "desktop",
        },
      },
      {
        path: "connection",
        name: "Connection",
        component: () => import("@/views/Connection/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "desktop",
        },
      },
      {
        path: "aposta",
        name: "Aposta",
        component: () => import("@/views/Aposta/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "desktop",
        },
      },
      {
        path: "premiacao",
        name: "Premiacao",
        component: () => import("@/views/Premiacao/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "desktop",
        },
      },
      {
        path: "vip",
        name: "VIP",
        component: () => import("@/views/VIP/index.vue"),
        meta: {
          layout: "desktop",
        },
      },
      {
        path: "bonus",
        name: "MyBonus",
        component: () => import("@/views/MyBonus/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "desktop",
        },
      },
      {
        path: "messages",
        name: "Messages",
        component: () => import("@/views/Messages/index.vue"),
        meta: {
          layout: "desktop",
        },
      },
      {
        path: "invite",
        name: "Invite",
        component: () => import("@/views/Invite/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "desktop",
        },
      },
      {
        path: "commission",
        name: "Commission",
        component: () => import("@/views/Commission/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "desktop",
        },
      },
      {
        path: "user/profile",
        name: "UserProfile",
        component: () => import("@/views/UserProfile/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "desktop",
        },
      },
      {
        path: "privacy-policy",
        name: "PrivacyPolicy",
        component: () => import("@/views/PrivacyPolicy/index.vue"),
        meta: {
          layout: "desktop",
        },
      },
      {
        path: "terms-of-service",
        name: "TermsOfService",
        component: () => import("@/views/TermsOfService/index.vue"),
        meta: {
          layout: "desktop",
        },
      },
      {
        path: "responsible-gaming",
        name: "ResponsibleGaming",
        component: () => import("@/views/ResponsibleGaming/index.vue"),
        meta: {
          layout: "desktop",
        },
      },
      // {
      //   path: "bank-cards",
      //   name: "BankCardListDesktop",
      //   component: () => import("@/views/BankCardlist/index.vue"),
      //   meta: {
      //     requiresAuth: true,
      //     layout: "desktop",
      //   },
      // },
      {
        path: "bank-cards",
        name: "BankCardListDesktop",
        component: () => import("@/views/Withdraw/bindInfo.vue"),
        meta: {
          requiresAuth: true,
          layout: "desktop",
        },
      },
    ],
  },
  // 移动端路由
  {
    path: "/m",
    component: MobileLayout,
    redirect: "/m/home",
    children: [
      {
        path: "home",
        name: "HomeMobile",
        component: () => import("@/views/Mobile/Home/index.vue"),
      },
      {
        path: "home/:code",
        name: "HomeWithCodeMobile",
        component: () => import("@/views/Mobile/Home/index.vue"),
      },
      {
        path: "game-list",
        name: "GameListMobile",
        component: () => import("@/views/Mobile/Game/index.vue"),
      },
      {
        path: "game/:id",
        name: "GameDetailMobile",
        component: () => import("@/views/Mobile/Game/GameDetail.vue"),
        props: true,
        meta: { requiresAuth: true },
      },
      {
        path: "user-info",
        name: "UserInfoMobile",
        component: () => import("@/views/Mobile/UserInfo.vue"),
        meta: { requiresAuth: true },
      },
      {
        path: "game-search",
        name: "GameSearchMobile",
        component: () => import("@/views/Mobile/Game/search.vue"),
      },
      {
        path: "search/game-list",
        name: "GameSearchListMobile",
        component: () => import("@/views/Mobile/Game/GameList.vue"),
      },
      {
        path: "customer-service",
        name: "CustomerServiceMobile",
        component: () => import("@/views/Mobile/Message/CustomerService.vue"),
        meta: { requiresAuth: true },
      },
      // 共用页面
      {
        path: "deposit",
        name: "DepositMobile",
        component: () => import("@/views/Deposit/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "mobile",
        },
      },
      {
        path: "withdraw",
        name: "WithdrawMobile",
        component: () => import("@/views/Withdraw/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "mobile",
        },
      },
      {
        path: "account",
        name: "AccountMobile",
        component: () => import("@/views/Account/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "mobile",
        },
      },
      {
        path: "records",
        name: "RecordsMobile",
        component: () => import("@/views/Records/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "mobile",
        },
      },
      {
        path: "connection",
        name: "ConnectionMobile",
        component: () => import("@/views/Connection/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "mobile",
        },
      },
      {
        path: "aposta",
        name: "ApostaMobile",
        component: () => import("@/views/Aposta/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "mobile",
        },
      },
      {
        path: "premiacao",
        name: "PremiacaoMobile",
        component: () => import("@/views/Premiacao/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "mobile",
        },
      },
      {
        path: "vip",
        name: "VIPMobile",
        component: () => import("@/views/VIP/index.vue"),
        meta: {
          layout: "mobile",
        },
      },
      {
        path: "bonus",
        name: "MyBonusMobile",
        component: () => import("@/views/MyBonus/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "mobile",
        },
      },
      {
        path: "messages",
        name: "MessagesMobile",
        component: () => import("@/views/Messages/index.vue"),
        meta: {
          layout: "mobile",
        },
      },
      {
        path: "invite",
        name: "InviteMobile",
        component: () => import("@/views/Invite/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "mobile",
        },
      },
      {
        path: "commission",
        name: "CommissionMobile",
        component: () => import("@/views/Commission/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "mobile",
        },
      },
      {
        path: "user/profile",
        name: "UserProfileMobile",
        component: () => import("@/views/UserProfile/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "mobile",
        },
      },
      {
        path: "privacy-policy",
        name: "PrivacyPolicyMobile",
        component: () => import("@/views/PrivacyPolicy/index.vue"),
        meta: {
          layout: "mobile",
        },
      },
      {
        path: "terms-of-service",
        name: "TermsOfServiceMobile",
        component: () => import("@/views/TermsOfService/index.vue"),
        meta: {
          layout: "mobile",
        },
      },
      {
        path: "responsible-gaming",
        name: "ResponsibleGamingMobile",
        component: () => import("@/views/ResponsibleGaming/index.vue"),
        meta: {
          layout: "mobile",
        },
      },
      {
        path: "bank-cards",
        name: "BankCardListMobile",
        component: () => import("@/views/Withdraw/bindInfo.vue"),
        meta: {
          requiresAuth: true,
          layout: "mobile",
        },
      },
      {
        path: "activity-menu",
        name: "ActivityMenuMobile",
        component: () => import("@/views/Mobile/ActivityMenu/index.vue"),
        meta: {
          requiresAuth: true,
          layout: "mobile",
        },
      },
      {
        path: "s/:shortCode",
        name: "ShortLinkMobile",
        component: () => import("@/views/ShortLink.vue"),
        meta: {
          layout: "mobile",
        },
      },
    ],
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置（浏览器前进/后退），则恢复
    if (savedPosition) {
      return savedPosition;
    }

    // 如果路由有 hash，滚动到对应元素
    if (to.hash) {
      return {
        el: to.hash,
        behavior: "smooth",
      };
    }

    // 如果路由有 meta.scrollToTop 为 false，保持当前位置
    if (to.meta.scrollToTop === false) {
      return false;
    }

    // 默认滚动到顶部
    return {
      top: 0,
      behavior: "smooth", // 平滑滚动
    };
  },
});

// 修改设备切换处理函数
const handleDeviceSwitch = (to: any) => {
  console.log("handleDeviceSwitch", to);
  const isMobile = isMobileDevice();
  const path = to.path;
  const isMobilePath = path.startsWith("/m/");

  // 特殊路由不进行设备切换
  if (path === "/s/:shortCode" || path === "/activity/Atividades-Pinduoduo") {
    return true;
  }

  // 如果是移动设备访问桌面端路由，重定向到移动端
  if (isMobile && !isMobilePath) {
    return `/m${path}`;
  }

  // 如果是桌面设备访问移动端路由，重定向到桌面端
  if (!isMobile && isMobilePath) {
    return path.replace("/m", "");
  }

  return true;
};

// 全局前置守卫
router.beforeEach((to, from, next) => {
  console.log("beforeEach", to, from);
  // 先检查设备类型并处理重定向
  const redirectPath = handleDeviceSwitch(to);
  console.log("redirectPath", redirectPath);
  if (redirectPath !== true) {
    next(redirectPath);
    return;
  }

  // 然后检查权限
  const result = checkAuth(to, from);
  if (result === true) {
    NProgress.start();
    next();
  } else {
    next(result);
  }
});

router.afterEach((to, from) => {
  NProgress.done();

  // 强制滚动到顶部（除了有hash的路由）
  if (!to.hash) {
    forceScrollToTop();
  }
});

// 监听窗口大小变化
window.addEventListener("resize", () => {
  const currentPath = router.currentRoute.value.path;
  const redirectPath = handleDeviceSwitch({ path: currentPath });
  if (redirectPath !== true) {
    router.replace(redirectPath);
  }
});

export default router;
