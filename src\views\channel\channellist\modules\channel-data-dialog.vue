<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-04 13:38:20
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-04 17:44:58
 * @FilePath: \betdoce-admin\src\views\channel\channellist\modules\ChannelDataDialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog
    :model-value="visible"
    width="900px"
    :close-on-click-modal="false"
    title="渠道数据"
    @update:model-value="emit('update:visible', $event)"
  >
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <el-tab-pane label="数据统计" name="statistic">
        <channel-data-statistic
          ref="statisticRef"
          :channel="channel"
          :loading="loading"
          @refresh="loadData"
        />
      </el-tab-pane>
      <el-tab-pane label="注册清单" name="register">
        <channel-register-list
          ref="registerRef"
          :channel="channel"
          :loading="loading"
          @refresh="loadData"
        />
      </el-tab-pane>
      <el-tab-pane label="充值清单" name="recharge">
        <channel-recharge-list
          ref="rechargeRef"
          :channel="channel"
          :loading="loading"
          @refresh="loadData"
        />
      </el-tab-pane>
      <el-tab-pane label="游戏记录" name="game">
        <channel-game-record
          ref="gameRef"
          :channel="channel"
          :loading="loading"
          @refresh="loadData"
        />
      </el-tab-pane>
      <el-tab-pane label="提现数据" name="withdraw">
        <channel-withdraw-list
          ref="withdrawRef"
          :channel="channel"
          :loading="loading"
          @refresh="loadData"
        />
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch ,nextTick} from 'vue';
import { ElMessage } from 'element-plus';
import type { TabPaneName } from 'element-plus';
import ChannelDataStatistic from './channel-data-statistic.vue';
import ChannelRegisterList from './channel-register-list.vue';
import ChannelRechargeList from './channel-recharge-list.vue';
import ChannelGameRecord from './channel-game-record.vue';
import ChannelWithdrawList from './channel-withdraw-list.vue';



const props = defineProps<{ visible: boolean; channel: any }>();
const emit = defineEmits(['update:visible']);
const activeTab = ref<TabPaneName>('statistic');
const loading = ref(false);

// 組件引用
const statisticRef = ref();
const registerRef = ref();
const rechargeRef = ref();
const gameRef = ref();
const withdrawRef = ref();

// 加載數據的方法
const loadData = async () => {
  console.log()
  if (!props.channel?.id) return;
  await nextTick(); // 等待 DOM 更新完成
  loading.value = true;
  try {
    // 根據不同的標籤頁加載對應的數據
    switch (activeTab.value) {
      case 'statistic':
        // 加載統計數據
        await statisticRef.value?.fetchStats();
        break;
      case 'register':
        // 加載註冊清單
        await registerRef.value?.loadData();
        break;
      case 'recharge':
        // 加載充值清單
        await rechargeRef.value?.fetchData();
        break;
      case 'game':
        // 加載遊戲記錄
        await gameRef.value?.fetchData();
        break;
      case 'withdraw':
        // 加載提現數據
        await withdrawRef.value?.fetchData();
        break;
    }
  } catch (error) {
    ElMessage.error('數據加載失敗，請稍後重試');
    console.error('數據加載錯誤:', error);
  } finally {
    loading.value = false;
  }
};

// 處理標籤頁切換
const handleTabChange = (tab: TabPaneName) => {
  activeTab.value = tab;
  loadData();
};

// 監聽對話框顯示狀態
watch(() => props.visible, (val) => {
  console.log('props.visible', props.visible);
  if (val) {
    loadData();
  } else {
    activeTab.value = 'statistic';
  }
});
</script>
