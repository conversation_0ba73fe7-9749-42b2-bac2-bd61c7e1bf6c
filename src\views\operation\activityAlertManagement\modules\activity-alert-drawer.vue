<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { fetchCreateActivityAlert, fetchUpdateActivityAlert, fetchGetActivityAlertById } from '@/service/api/activityAlert'
import ImageUpload from '@/components/upload/ImageUpload.vue'

interface Props {
  /** the type of operation */
  operateType: UI.TableOperateType
  /** the edit row data */
  rowData?: any | null
}

const props = defineProps<Props>()

interface Emits {
  (e: 'submitted'): void
}

const emit = defineEmits<Emits>()

const visible = defineModel<boolean>('visible', {
  default: false
})

const title = computed(() => {
  const titles: Record<UI.TableOperateType, string> = {
    add: '新增活动弹窗',
    edit: '编辑活动弹窗'
  }
  return titles[props.operateType]
})

const formRef = ref()

const formData = reactive({
  title: '',
  content: '',
  image_url: '',
  hyperlink: '',
  weight: 1,
  status: 1,
  show_type: 1
})

const rules = {
  title: [{ required: true, message: '请输入弹窗标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入活动简介', trigger: 'blur' }],
  image_url: [{ required: true, message: '请上传活动图片', trigger: 'change' }],
  hyperlink: [{ required: true, message: '请输入活动链接', trigger: 'blur' }],
  weight: [{ required: true, message: '请输入权重', trigger: 'blur' }],
  status: [{ required: true, message: '请选择弹窗状态', trigger: 'change' }],
  show_type: [{ required: true, message: '请选择显示类型', trigger: 'change' }]
}

const statusOptions = [
  { label: '禁用', value: 0 },
  { label: '启用', value: 1 }
]

const showTypeOptions = [
  { label: '每个用户只显示一次', value: 1 },
  { label: '登录时显示', value: 2 },
  { label: '首页显示', value: 3 }
]

async function fetchDetail() {
  if (!props.rowData?.id) return
  try {
    const res = await fetchGetActivityAlertById({
      id: props?.rowData?.id
    })
    if (res.data) {
      Object.assign(formData, res.data)
    }
  } catch (error) {
    window.$message?.error('获取详情失败')
  }
}

function closeDrawer() {
  visible.value = false
}

async function handleSubmit() {
  if (!formRef.value) return
  try {
    await formRef.value.validate()

    if (props.operateType === 'edit') {
      const { error } = await fetchUpdateActivityAlert({
        id: props?.rowData?.id,
        ...formData
      })
      if (!error) {
        window.$message?.success('更新成功')
        closeDrawer()
        emit('submitted')
      }
    } else {
      const { error } = await fetchCreateActivityAlert(formData)
      if (!error) {
        window.$message?.success('创建成功')
        closeDrawer()
        emit('submitted')
      }
    }
  } catch (error) {
    window.$message?.error('操作失败')
  }
}

watch(visible, async () => {
  if (visible.value) {
    if (props.operateType === 'edit' && props.rowData) {
      Object.assign(formData, props.rowData)
    } else {
      Object.assign(formData, {
        title: '',
        content: '',
        image_url: '',
        hyperlink: '',
        weight: 1,
        status: 1,
        show_type: 1
      })
    }
  }
})
</script>

<template>
  <ElDrawer
    :size="400"
    v-model="visible"
    :title="title"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
    >
      <ElFormItem label="弹窗标题" prop="title">
        <ElInput v-model="formData.title" placeholder="请输入弹窗标题" />
      </ElFormItem>

      <ElFormItem label="活动简介" prop="content">
        <ElInput
          v-model="formData.content"
          type="textarea"
          :rows="3"
          placeholder="请输入活动简介"
        />
      </ElFormItem>

      <ElFormItem label="活动图片" prop="image_url">
        <ImageUpload
          v-model="formData.image_url"
          :show-tip="true"
          tip-text="支持 jpg、png 格式图片，大小不超过 2MB"
        />
      </ElFormItem>

      <ElFormItem label="活动链接" prop="hyperlink">
        <ElInput v-model="formData.hyperlink" placeholder="请输入活动链接" />
      </ElFormItem>

      <ElFormItem label="弹窗权重" prop="weight">
        <div>
          <ElInputNumber v-model="formData.weight" :min="1" :max="999" placeholder="请输入权重" />
          <div class="form-tip">数值越大，优先级越高</div>
        </div>
      </ElFormItem>

      <ElFormItem label="弹窗状态" prop="status">
        <ElRadioGroup v-model="formData.status">
          <ElRadio :key="0" :value=1 label="立即上线" />
          <ElRadio :key="0" :value=0 label="暂不上线" />
          <!-- <ElRadio v-for="{ label, value } in statusOptions" :key="value" :value="value" :label="label" /> -->
        </ElRadioGroup>
      </ElFormItem>

      <ElFormItem label="弹出设置" prop="show_type">
        <ElRadioGroup v-model="formData.show_type">
          <ElRadio v-for="{ label, value } in showTypeOptions" :key="value" :value="value" :label="label" />
        </ElRadioGroup>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="flex justify-end gap-2">
        <ElButton @click="closeDrawer">取消</ElButton>
        <ElButton
          type="primary"
          @click="handleSubmit"
        >
          确定
        </ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<style scoped>
.form-tip {
  color: #888;
  font-size: 12px;
  margin-top: 4px;
}
</style>
