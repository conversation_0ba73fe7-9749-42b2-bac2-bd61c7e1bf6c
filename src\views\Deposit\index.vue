<template>
  <v-container class="deposit-container" max-width="940">
    <!-- 顶部导航 -->
    <div class="nav-tabs mb-4 mt-4">
      <v-btn-toggle v-model="activeTab" mandatory class="tab-group">
        <v-btn value="deposit">Deposito</v-btn>
        <v-btn value="games">Jogos</v-btn>
      </v-btn-toggle>
    </div>

    <!-- 加载状态 -->
    <v-progress-circular
      v-if="loading"
      indeterminate
      color="primary"
      class="d-flex justify-center w-100 my-4"
    ></v-progress-circular>

    <!-- 充值记录列表 -->
    <div v-if="activeTab === 'deposit'" class="records-list">
      <div
        v-for="record in withdrawRecords"
        :key="record.id"
        class="record-item"
      >
        <div class="record-info">
          <div class="record-title">
            {{ record.transaction_id }}
            <span
              class="record-status"
              :class="
                record.status === 'completed'
                  ? 'status-success'
                  : 'status-failed'
              "
            >
              {{ record.status === "completed" ? "Concluído" : "Falhou" }}
            </span>
          </div>
          <div class="record-time">{{ record.complete_time }}</div>
        </div>
        <div
          class="record-amount"
          :class="{ 'amount-positive': record.amount > 0 }"
        >
          {{ record.amount > 0 ? "+" : "" }}{{ record.amount / 100 }}
          <div class="record-balance">
            equilibrio: {{ record.balance_after / 100 }}
          </div>
        </div>
      </div>
    </div>

    <!-- 游戏记录列表 -->
    <div v-if="activeTab === 'games'" class="records-list">
      <div
        v-for="record in gameRecords"
        :key="record.serial_number"
        class="record-item"
      >
        <div class="record-info">
          <div class="record-title">
            {{ record.game_category }}
            <span
              class="record-status"
              :class="
                record.win_amount > 0 ? 'status-success' : 'status-failed'
              "
            >
              {{ record.win_amount > 0 ? "Vitória" : "Derrota" }}
            </span>
          </div>
          <div class="record-time">
            {{formatDate(record.create_at) }}
          </div>
          <div class="record-game-info">ID: {{ record.serial_number }}</div>
        </div>
        <div
          class="record-amount"
          :class="{ 'amount-positive': record.win_amount > 0 }"
        >
          {{ record.win_amount > 0 ? "+" : record.bet_amount > 0 ? "-" : ""
          }}{{
            record.win_amount > 0
              ? record.win_amount / 100
              : record.bet_amount / 100
          }}
          <!-- <div class="record-balance">
            Aposta: {{ record.bet_amount / 100 }}
          </div>
          <div class="record-code">Pontos: {{ record.code_amount / 100 }}</div> -->
          <div class="record-code">Conta: {{ record.total_balance / 100 }}</div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <v-pagination
      v-if="total > pageSize"
      v-model="currentPage"
      :length="Math.ceil(total / pageSize)"
      :total-visible="5"
      class="mb-6 pagination"
      @update:model-value="handlePageChange"
    ></v-pagination>
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { getDepositTransactions } from "@/api/wallet";
import { getGameTransactions } from "@/api/game";
import { useStore } from "vuex";
import type { DepositTransaction } from "@/api/wallet";
import type { GameTransaction } from "@/api/game";
import { formatDate } from "@/utils/date";

const store = useStore();

// 当前激活的标签
const activeTab = ref("deposit");

// 充值记录数据
const withdrawRecords = ref<DepositTransaction[]>([]);
const gameRecords = ref<GameTransaction[]>([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 加载充值记录
const loadDepositTransactions = async () => {
  try {
    loading.value = true;
    const response = await getDepositTransactions({
      user_id: store.state.auth.user.id,
      page: currentPage.value,
      page_size: pageSize.value,
    });

    if (response?.data.records) {
      withdrawRecords.value = response.data.records;
      total.value = response.data.total;
    }
  } catch (error) {
    console.error("Failed to load deposit transactions:", error);
  } finally {
    loading.value = false;
  }
};

// 加载游戏记录
const loadGameTransactions = async () => {
  try {
    loading.value = true;
    const response = await getGameTransactions({
      page: currentPage.value,
      size: pageSize.value,
    });

    console.log("[DEBUG] Game transactions API response:", response);

    if (response?.data) {
      gameRecords.value = [];
      // 克隆数组确保新引用
      const newRecords = Array.isArray(response.data) ? [...response.data] : [];
      const newTotal = response.count || 0;

      gameRecords.value = newRecords;
      total.value = newTotal;
    } else {
      console.warn(
        "[DEBUG] No data found in game transactions response. Resetting list."
      );
      gameRecords.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error("Failed to load game transactions:", error);
    gameRecords.value = []; // Reset on error
    total.value = 0;
  } finally {
    loading.value = false;
  }
};

// 处理分页变化
const handlePageChange = (page: number) => {
  currentPage.value = page;

  if (activeTab.value === "deposit") {
    loadDepositTransactions();
  } else {
    loadGameTransactions();
  }
};

// 监听标签切换
watch(activeTab, (newTab) => {
  currentPage.value = 1;
  total.value = 0;
  if (newTab === "deposit") {
    loadDepositTransactions();
  } else if (newTab === "games") {
    loadGameTransactions();
  }
});

// 生命周期钩子
onMounted(() => {
  loadDepositTransactions();
});
</script>

<style lang="scss" scoped>
.deposit-container {
  padding: 0 12px;
}

.nav-tabs {
  .tab-group {
    width: 100%;
    background: transparent;
    border: none;
    gap: 8px;

    :deep(.v-btn) {
      flex: 1;
      background: #343f6b;
      border: none;
      color: rgba(255, 255, 255, 0.6);
      font-size: 14px;
      text-transform: none;
      border-radius: 8px;
      height: 40px;
      &.v-btn--active {
        background: linear-gradient(0deg, #c9b737, #2abb27);
      }
    }
  }
}

.records-list {
  background: #2b324d;
  border-radius: 14px;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .record-info {
    flex: 1;
  }

  .record-title {
    color: #fff;
    font-size: 14px;
    margin-bottom: 4px;
  }

  .record-time {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
  }

  .record-amount {
    text-align: right;
    color: #fff;
    font-size: 14px;
    font-weight: 500;

    &.amount-positive {
      color: #ffdf00;
    }
  }

  .record-balance {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    margin-top: 4px;
  }
}

.record-status {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 4px;

  &.status-success {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
  }

  &.status-failed {
    background: rgba(235, 87, 87, 0.1);
    color: #eb5757;
  }
}

.record-game-info {
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  margin-top: 4px;
}

.record-code {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  margin-top: 4px;
}

@media (max-width: 768px) {
  .nav-tabs {
    position: sticky;
    top: 0;
    z-index: 1;
  }
  .pagination {
    margin-top: 10px;
    :deep() {
      .v-pagination__prev {
        margin: 0;
      }
      .v-pagination__item{
        margin: 0;
        flex: 1;
      }
      .v-pagination__next{
        margin: 0;
      }
      .v-btn--density-default{
        min-width: inherit;
        height: 2rem;
      }
    }
  }
  .record-item {
    padding: 12px;

    .record-title {
      font-size: 13px;
    }

    .record-time,
    .record-balance {
      font-size: 11px;
    }

    .record-amount {
      font-size: 13px;
    }
  }
}
</style>
