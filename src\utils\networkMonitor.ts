import { ref } from "vue";
import websocketService from "./websocket";
import { getToken, getUserInfo } from "./auth";
// import { useStore } from "vuex";
// 网络状态枚举
enum NetworkStatus {
  ONLINE = "online",
  OFFLINE = "offline",
  SLOW = "slow",
  UNKNOWN = "unknown",
}

// 连接质量检测结果
interface ConnectionQuality {
  status: NetworkStatus;
  latency?: number;
  timestamp: number;
}

class NetworkMonitor {
  private isMonitoring = false;
  private qualityCheckTimer: NodeJS.Timeout | null = null;
  private lastQualityCheck = 0;
  private qualityCheckInterval = 30000; // 30秒检查一次网络质量
  private websocketCheckTimer: NodeJS.Timeout | null = null;
  private websocketCheckInterval = 60000; // 60秒检查一次WebSocket状态

  // 响应式状态
  public readonly networkStatus = ref<NetworkStatus>(NetworkStatus.ONLINE);
  public readonly connectionQuality = ref<ConnectionQuality>({
    status: NetworkStatus.ONLINE,
    timestamp: Date.now(),
  });

  /**
   * 开始网络监控
   */
  public startMonitoring(): void {
    if (this.isMonitoring) {
      console.log("网络监控已在运行中");
      return;
    }

    this.isMonitoring = true;
    console.log("开始网络监控");

    // 监听网络状态变化
    window.addEventListener("online", this.handleOnline.bind(this));
    window.addEventListener("offline", this.handleOffline.bind(this));

    // 监听页面可见性变化
    document.addEventListener(
      "visibilitychange",
      this.handleVisibilityChange.bind(this)
    );

    // 监听页面显示事件（移动端兼容）
    window.addEventListener("pageshow", this.handlePageShow.bind(this));

    // 启动网络质量检测
    this.startQualityCheck();

    // 启动WebSocket状态检测
    this.startWebSocketCheck();
  }

  /**
   * 停止网络监控
   */
  public stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    console.log("停止网络监控");

    // 移除事件监听
    window.removeEventListener("online", this.handleOnline.bind(this));
    window.removeEventListener("offline", this.handleOffline.bind(this));
    document.removeEventListener(
      "visibilitychange",
      this.handleVisibilityChange.bind(this)
    );
    window.removeEventListener("pageshow", this.handlePageShow.bind(this));

    // 停止质量检测
    this.stopQualityCheck();

    // 停止WebSocket检测
    this.stopWebSocketCheck();
  }

  /**
   * 处理网络连接恢复
   */
  private handleOnline(): void {
    console.log("网络连接恢复");
    this.networkStatus.value = NetworkStatus.ONLINE;

    // 延迟检查WebSocket连接，给网络一些恢复时间
    setTimeout(() => {
      this.checkAndReconnectWebSocket();
    }, 2000);
  }

  /**
   * 处理网络连接断开
   */
  private handleOffline(): void {
    console.log("网络连接断开");
    this.networkStatus.value = NetworkStatus.OFFLINE;
    this.connectionQuality.value = {
      status: NetworkStatus.OFFLINE,
      timestamp: Date.now(),
    };
  }

  /**
   * 处理页面可见性变化
   */
  private handleVisibilityChange(): void {
    const isVisible = !document.hidden;
    console.log(`页面可见性变化: ${isVisible ? "可见" : "隐藏"}`);

    if (isVisible) {
      // 页面重新可见时，检查网络和WebSocket状态
      setTimeout(() => {
        this.checkNetworkAndWebSocket();
      }, 2000);
    }
  }

  /**
   * 处理页面显示事件（移动端兼容）
   */
  private handlePageShow(event: PageTransitionEvent): void {
    console.log(
      "页面显示事件",
      event.persisted ? "(从缓存恢复)" : "(正常显示)"
    );

    if (event.persisted) {
      // 页面从缓存恢复，需要检查网络状态
      setTimeout(() => {
        this.checkNetworkAndWebSocket();
      }, 3000);
    }
  }

  /**
   * 检查网络和WebSocket状态
   */
  private async checkNetworkAndWebSocket(): Promise<void> {
    // 检查网络状态
    if (!navigator.onLine) {
      console.log("网络未连接，跳过WebSocket检查");
      this.networkStatus.value = NetworkStatus.OFFLINE;
      return;
    }

    // 检查网络质量
    await this.checkConnectionQuality();

    // 检查WebSocket连接
    this.checkAndReconnectWebSocket();
  }

  /**
   * 检查并重连WebSocket
   */
  private async checkAndReconnectWebSocket(): Promise<void> {
    const token = getToken();

    // 在方法内部获取 store
    let userInfo;
    try {
      // const store = useStore();
      // store.state.auth.user
      userInfo = getUserInfo()?.user;
    } catch (error) {
      console.warn("无法获取 store，跳过 WebSocket 重连:", error);
      return;
    }

    if (!token || !userInfo?.uuid) {
      console.log("Token无效或用户信息不完整，跳过WebSocket重连");
      return;
    }

    const isConnected = websocketService.getIsConnected();
    const wsState = websocketService.getState();
    const heartbeatStatus = websocketService.getHeartbeatStatus();

    console.log("WebSocket状态检查:", {
      isConnected,
      wsState,
      heartbeatHealthy: heartbeatStatus.isHealthy,
      networkStatus: this.networkStatus.value,
      token: token ? "存在" : "不存在",
      userUuid: userInfo?.uuid || "无",
    });

    // 检查是否需要重连（更宽松的策略，考虑1006状态）
    const needsReconnect = !isConnected || wsState === 3; // 只在真正断开或已关闭时重连
    const needsHeartbeatFix =
      isConnected && wsState === 1 && !heartbeatStatus.isHealthy;

    if (needsReconnect) {
      console.log("检测到WebSocket连接断开，开始重连");

      try {
        // 重置重连计数器
        websocketService.resetReconnectAttempts();

        // 强制重连
        await websocketService.forceReconnect();

        // 等待连接建立后，确保心跳正常启动
        setTimeout(() => {
          this.ensureHeartbeatRunning();
        }, 3000);
      } catch (error) {
        console.error("WebSocket重连失败:", error);
      }
    } else if (needsHeartbeatFix) {
      console.log("WebSocket已连接但心跳异常，尝试修复心跳");
      this.ensureHeartbeatRunning();
    } else {
      console.log("WebSocket连接正常");
      // 即使连接正常，也要确保心跳在运行
      this.ensureHeartbeatRunning();
    }
  }

  /**
   * 确保心跳正常运行
   */
  private ensureHeartbeatRunning(): void {
    const heartbeatStatus = websocketService.getHeartbeatStatus();
    const isConnected = websocketService.getIsConnected();

    if (isConnected && !heartbeatStatus.isRunning) {
      console.log("WebSocket已连接但心跳未运行，尝试启动心跳");
      // 通过发送一次心跳来启动心跳机制
      websocketService.startHeartbeatManually();
    }
  }

  /**
   * 检查网络连接质量
   */
  private async checkConnectionQuality(): Promise<void> {
    const startTime = Date.now();

    try {
      // 使用简单的ping测试来检测网络质量
      await fetch("/api/ping", {
        method: "HEAD",
        cache: "no-cache",
        signal: AbortSignal.timeout(5000), // 5秒超时
      });

      const latency = Date.now() - startTime;

      let status: NetworkStatus;
      if (latency < 100) {
        status = NetworkStatus.ONLINE;
      } else if (latency < 1000) {
        status = NetworkStatus.SLOW;
      } else {
        status = NetworkStatus.OFFLINE;
      }

      this.connectionQuality.value = {
        status,
        latency,
        timestamp: Date.now(),
      };
      // 检查WebSocket连接
      this.checkAndReconnectWebSocket();
      console.log(`网络质量检测: ${status}, 延迟: ${latency}ms`);
    } catch (error) {
      console.warn("网络质量检测失败:", error);
      this.connectionQuality.value = {
        status: NetworkStatus.OFFLINE,
        timestamp: Date.now(),
      };
    }
  }

  /**
   * 启动网络质量检测
   */
  private startQualityCheck(): void {
    if (this.qualityCheckTimer) {
      clearInterval(this.qualityCheckTimer);
    }

    this.qualityCheckTimer = setInterval(() => {
      // 只在页面可见且网络在线时检查
      if (!document.hidden && navigator.onLine) {
        this.checkConnectionQuality();
      }
    }, this.qualityCheckInterval);

    console.log(`网络质量检测已启动，间隔: ${this.qualityCheckInterval}ms`);
  }

  /**
   * 停止网络质量检测
   */
  private stopQualityCheck(): void {
    if (this.qualityCheckTimer) {
      clearInterval(this.qualityCheckTimer);
      this.qualityCheckTimer = null;
      console.log("网络质量检测已停止");
    }
  }

  /**
   * 启动WebSocket状态检测
   */
  private startWebSocketCheck(): void {
    if (this.websocketCheckTimer) {
      clearInterval(this.websocketCheckTimer);
    }
    this.checkAndReconnectWebSocket();
    this.websocketCheckTimer = setInterval(() => {
      // 只在页面可见且网络在线时检查
      if (!document.hidden && navigator.onLine) {
        this.checkAndReconnectWebSocket();
      }
    }, this.websocketCheckInterval);

    console.log(
      `WebSocket状态检测已启动，间隔: ${this.websocketCheckInterval}ms`
    );
  }

  /**
   * 停止WebSocket状态检测
   */
  private stopWebSocketCheck(): void {
    if (this.websocketCheckTimer) {
      clearInterval(this.websocketCheckTimer);
      this.websocketCheckTimer = null;
      console.log("WebSocket状态检测已停止");
    }
  }

  /**
   * 手动触发网络检查
   */
  public async manualCheck(): Promise<void> {
    console.log("手动触发网络检查");
    await this.checkNetworkAndWebSocket();
  }

  /**
   * 手动触发WebSocket检查
   */
  public async manualWebSocketCheck(): Promise<void> {
    console.log("手动触发WebSocket检查");
    await this.checkAndReconnectWebSocket();
  }

  /**
   * 获取当前网络状态
   */
  public getCurrentStatus(): {
    networkStatus: NetworkStatus;
    connectionQuality: ConnectionQuality;
    isMonitoring: boolean;
    websocketStatus?: {
      isConnected: boolean;
      state: number;
      heartbeatHealthy: boolean;
    };
  } {
    const websocketStatus = {
      isConnected: websocketService.getIsConnected(),
      state: websocketService.getState(),
      heartbeatHealthy: websocketService.getHeartbeatStatus().isHealthy,
    };

    return {
      networkStatus: this.networkStatus.value,
      connectionQuality: this.connectionQuality.value,
      isMonitoring: this.isMonitoring,
      websocketStatus,
    };
  }
}

// 创建单例实例
const networkMonitor = new NetworkMonitor();

export default networkMonitor;
export { NetworkMonitor, NetworkStatus, type ConnectionQuality };
