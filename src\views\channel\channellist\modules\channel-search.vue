<template>
  <div class="search-wrapper">
    <ElForm ref="formRef" :model="model">
      <ElRow :gutter="16">
        <ElCol :span="6">
          <ElFormItem label="渠道名称/负责人" prop="keyword">
            <ElInput v-model="model.keyword" placeholder="请输入渠道名称/负责人" clearable @keyup.enter="handleSearch"/>
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem label="负责人" prop="responsible_by">
            <ElInput v-model="model.responsible_by" placeholder="请输入负责人" clearable @keyup.enter="handleSearch"/>
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem label="状态" prop="status">
            <ElSelect v-model="model.status" placeholder="请选择状态" clearable>
              <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"/>
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { ElForm } from 'element-plus';

interface Props {
  model: Record<string, any>;
}

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref();

const statusOptions = [
  { value: 1, label: '启用' },
  { value: 0, label: '禁用' }
];

function handleReset() {
  formRef.value?.resetFields();
  emit('reset');
}

function handleSearch() {
  emit('search');
}
</script>

<style lang="scss" scoped>
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 18px;
    margin-right: 18px;
  }
}
</style>
