import { transformRecordToOption } from '@/utils/common';

export const yesOrNoRecord: Record<CommonType.YesOrNo, App.I18n.I18nKey> = {
  Y: 'common.yesOrNo.yes',
  N: 'common.yesOrNo.no'
};

export const yesOrNoOptions = transformRecordToOption(yesOrNoRecord);


export const statusOptions = [
  { value: 1, label: '启用' },
  { value: 0, label: '禁用' }
];

export const tagMap: Record<number, 'success' | 'danger'> = {
  1: 'success',
  0: 'danger'
};

export const positionOptions = [
  { value: 1, label: 'PC端' },
  { value: 2, label: '手机端' },
    { value: 3, label: 'PC端+手机端' }
];