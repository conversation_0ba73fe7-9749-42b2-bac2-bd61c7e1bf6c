<script setup lang="ts">
import { computed } from 'vue';
import { $t } from '@/locales';

defineOptions({ name: 'LangSwitch' });

interface Props {
  /** Show tooltip */
  showTooltip?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showTooltip: true
});

const tooltipContent = computed(() => {
  if (!props.showTooltip) return '';
  return '中文';
});
</script>

<template>
  <div>
    <ButtonIcon :tooltip-content="tooltipContent" tooltip-placement="left">
      <SvgIcon icon="heroicons:language" />
    </ButtonIcon>
  </div>
</template>

<style scoped>
:deep(.el-dropdown-menu__item) {
  border-radius: 6px;
}
:deep(.is-active) {
  background-color: var(--el-dropdown-menuItem-hover-fill);
  color: var(--el-dropdown-menuItem-hover-color);
}
</style>
