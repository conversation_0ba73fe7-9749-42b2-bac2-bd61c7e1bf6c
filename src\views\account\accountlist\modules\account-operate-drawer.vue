<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { statusOptions } from '@/constants/common';
import {
  fetchUpdateAccount,
  fetchGetAccountDetail
} from '@/service/api/account';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

interface Props {
  /** the type of operation */
  operateType: UI.TableOperateType;
  /** the edit row data */
  rowData?: any | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const title = computed(() => {
  const titles: Record<UI.TableOperateType, string> = {
    add: "新增用户",
    edit: "编辑用户"
  };
  return titles[props.operateType];
});

const formRef = ref();
const formData = reactive({
  nickname: '',
  username: '',
  phone: '',
  email: '',
  CPF: '',
  pix: '',
  accountType: 0,
  customerName: '',
  merchantUserId: '',
  avatar: '',
  gender: 1,
  level: 0,
  invite_code: '',
  source: 1,
  father_id: 0,
  is_recharge: 0,
  is_black: 0,
  is_login: 1,
  remake: ''
});

const rules = {
  phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
  email: [{ type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }],
  nickname: [{ required: true, message: '请输入昵称', trigger: 'blur' }],
  level: [{ required: true, message: '请选择等级', trigger: 'blur' }]
};

async function fetchDetail() {
  if (!props.rowData?.id) return;
  try {
    const res = await fetchGetAccountDetail({
      id: props?.rowData?.id
    });
    if (res.data) {
      Object.assign(formData, res.data);
    }
  } catch (error) {
    window.$message?.error('获取详情失败');
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    if (props.operateType === 'edit') {
      const { error } = await fetchUpdateAccount({
        id: props?.rowData?.id,
        ...formData
      });
      if (!error) {
        window.$message?.success('更新成功');
        closeDrawer();
        emit('submitted');
      }
    }
  } catch (error) {
    window.$message?.error('操作失败');
  }
}

watch(visible, async () => {
  if (visible.value) {
    if (props.operateType === 'edit') {
      //await fetchDetail();
      Object.assign(formData, props.rowData);
    } else {
      Object.assign(formData, {
        nickname: '',
        username: '',
        phone: '',
        email: '',
        CPF: '',
        pix: '',
        accountType: 0,
        customerName: '',
        merchantUserId: '',
        avatar: '',
        gender: 1,
        level: 0,
        invite_code: '',
        source: 1,
        father_id: 0,
        is_recharge: 0,
        is_black: 0,
        is_login: 1,
        remake: ''
      });
    }
  }
});
</script>

<template>
  <ElDrawer
    :size="360"
    v-model="visible"
    :title="title"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
    >
      <ElFormItem label="手机号" prop="phone">
        <ElInput v-model="formData.phone" placeholder="请输入手机号" />
      </ElFormItem>
      <ElFormItem label="邮箱" prop="email">
        <ElInput v-model="formData.email" placeholder="请输入邮箱" />
      </ElFormItem>
      <ElFormItem label="昵称" prop="nickname">
        <ElInput v-model="formData.nickname" placeholder="请输入昵称" />
      </ElFormItem>
      <!-- <ElFormItem label="等级" prop="level">
        <ElInputNumber v-model="formData.level" :min="0" :max="999" placeholder="请输入等级" />
      </ElFormItem> -->
      <ElFormItem label="性别" prop="gender">
        <ElRadioGroup v-model="formData.gender">
          <ElRadio :label="1">男</ElRadio>
          <ElRadio :label="2">女</ElRadio>
        </ElRadioGroup>
      </ElFormItem>
      <ElFormItem label="备注" prop="remake">
        <ElInput
          v-model="formData.remake"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
        />
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="flex justify-end gap-2">
        <ElButton @click="closeDrawer">取消</ElButton>
        <ElButton
          type="primary"
          @click="handleSubmit"
        >
          确定
        </ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<style scoped>
</style> 