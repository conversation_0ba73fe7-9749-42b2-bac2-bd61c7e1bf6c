// 创建事件总线
class EventBus {
  private events: { [key: string]: EventTarget }

  constructor() {
    this.events = {}
  }

  getEvent(eventName: string): EventTarget {
    if (!this.events[eventName]) {
      this.events[eventName] = new EventTarget()
    }
    return this.events[eventName]
  }

  emit(eventName: string, detail?: any) {
    const event = detail ? new CustomEvent(eventName, { detail }) : new Event(eventName)
    this.getEvent(eventName).dispatchEvent(event)
  }

  on(eventName: string, callback: EventListener) {
    this.getEvent(eventName).addEventListener(eventName, callback)
  }

  off(eventName: string, callback: EventListener) {
    this.getEvent(eventName).removeEventListener(eventName, callback)
  }
}

export const eventBus = new EventBus()

// 登录相关事件名称常量
export const LOGIN_EVENTS = {
  LOGIN_SUCCESS: 'login-success',
  LOGOUT: 'logout'
} as const 