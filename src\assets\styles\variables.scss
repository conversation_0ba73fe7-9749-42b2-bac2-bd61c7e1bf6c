// 颜色变量
$primary-color: #ffd700;
$background-dark: #1a1a2e;
$text-color: #ffffff;

// 透明度
$overlay-bg: rgba(0, 0, 0, 0.3);
$overlay-light: rgba(255, 255, 255, 0.1);

// 间距
$spacing-xs: 0.5rem;
$spacing-sm: 0.75rem;
$spacing-md: 1rem;
$spacing-lg: 1.5rem;
$spacing-xl: 2rem;

// 边框圆角
$border-radius-sm: 4px;
$border-radius-md: 8px;

// 断点
$breakpoint-mobile: 768px;

// 混合器
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin hover-transform {
  transition: transform 0.3s ease;
  &:hover {
    transform: translateY(-5px);
  }
}

@mixin mobile {
  @media (max-width: $breakpoint-mobile) {
    @content;
  }
} 