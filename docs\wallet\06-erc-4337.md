---
sidebar_position: 6
---

import Image from '@theme/IdealImage';
import normalTx from './img/normal-tx-white.png';
import tx4337 from './img/4337tx-white.png';

# Compatible With ERC-4337

## Enabling the 4337 Module in UniPass

Currently, UniPass supports the ERC-4337 protocol. Users can enable the 4337 module in the UniPass wallet to experience ERC-4337 transactions.

UniPass currently uses Stackup's [Bundler](https://github.com/stackup-wallet/stackup-bundler) and Infinitism's [Paymaster](https://github.com/eth-infinitism/account-abstraction/blob/develop/contracts/samples/VerifyingPaymaster.sol).

In the future, UniPass will work with other ecosystem products and service providers to promote and improve the adoption and development of the ERC-4337 protocol.

### Activate 4337 Module

You can enable the 4337 Module in the settings, which currently supports Ethereum and Polygon.

[![Watch the video](https://cdn.loom.com/sessions/thumbnails/8d086e95fea54061b433af679d29bd7d-with-play.gif)](https://www.loom.com/share/8d086e95fea54061b433af679d29bd7d)

### Transactions after enabling the 4337 Module

<Image img={tx4337} />

UniPass users can activate the ERC-4337 compatibility mode by adding the 4337 module transaction in the MainModule.

After activating the 4337 module, users can initiate 4337 transactions, submit them to the Bundler, and go through the standard ERC-4337 verification mode. Users can also sign UniPass tx and submit it to the Relayer for on-chain processing.

### Explanation of Normal Transactions

<Image img={normalTx} />

In UniPass, each user deploys a contract wallet on the chain. For economic reasons, the user's contract wallet only stores the most important data related to the key list, while the common logic of how to use keys to manage contract wallets and how to perform wallet social recovery is placed in a `MainModule` contract shared by all users.

When initiating a transaction, the user signs the desired operation and submits it to the Relayer. Then, the Relayer helps to submit the transaction to the chain, calls the user's contract for verification on the chain, and executes the user's operation after verification passes.