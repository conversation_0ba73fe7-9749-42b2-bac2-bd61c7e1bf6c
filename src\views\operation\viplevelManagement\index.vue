<script setup lang="tsx">
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { EditPen } from "@element-plus/icons-vue";
import {
  fetchGetVipLevelList,
  fetchGetVipSigninConfig,
} from "@/service/api/vipLevel";
import VipLevelDrawer from "./modules/vip-level-drawer.vue";
import VipSigninDrawer from "./modules/vip-signin-drawer.vue";
import { useAuth } from "@/hooks/business/auth";

const { hasAuth } = useAuth();
defineOptions({ name: "VipLevelManagement" });

// VIP等级颜色映射
const vipLevelColors: Record<number, string> = {
  1: "#9ca3af", // VIP1 - 灰色
  2: "#3b82f6", // VIP2 - 蓝色
  3: "#22c55e", // VIP3 - 绿色
  4: "#f59e0b", // VIP4 - 橙色
  5: "#ef4444", // VIP5 - 红色
  6: "#8b5cf6", // VIP6 - 紫色
  7: "#14b8a6", // VIP7 - 青色
  8: "#f97316", // VIP8 - 深橙
  9: "#6b7280", // VIP9 - 深灰
  10: "#f59e0b", // VIP10 - 金色
};

// 数据状态
const levelData = ref<any[]>([]);
const levelLoading = ref(false);
const signinData = ref<any[]>([]);
const signinLoading = ref(false);
const signinDrawerVisible = ref(false);
const signinDrawerData = ref<
  { vip_level: number; day: number; reward: number }[]
>([]);

// VIP等级列定义
const levelColumns1 = [
  {
    prop: "level",
    label: "等级",
    width: 100,
    formatter: (row: any) => (
      <span
        class="px-2 py-1 rounded text-white font-bold"
        style={{ backgroundColor: vipLevelColors[row.level] || "#6b7280" }}
      >
        VIP{row.level}
      </span>
    ),
  },
  { prop: "name", label: "等级名称", minWidth: 120 },
  {
    prop: "max_withdrawal",
    label: "最大可提现金额",
    minWidth: 140,
    formatter: (row: any) =>
      row.max_withdrawal === 999888777
        ? "无限"
        : `R$${(row.max_withdrawal ).toLocaleString()}`,
  },
  {
    prop: "withdrawal_count",
    label: "当天可提现次数",
    minWidth: 140,
    formatter: (row: any) =>
      row.withdrawal_count === 999888777 ? "无限" : `${row.withdrawal_count}次`,
  },
  {
    prop: "monthly_charge",
    label: "近30天充值金额",
    minWidth: 140,
    formatter: (row: any) => `R$${(row.monthly_charge).toLocaleString()}`,
  },
  {
    prop: "monthly_bet",
    label: "近30天投注金额",
    minWidth: 140,
    formatter: (row: any) => `R$${(row.monthly_bet).toLocaleString()}`,
  },
];
// VIP等级列定义
const levelColumns2 = [
  {
    prop: "level",
    label: "等级",
    width: 100,
    formatter: (row: any) => (
      <span
        class="px-2 py-1 rounded text-white font-bold"
        style={{ backgroundColor: vipLevelColors[row.level] || "#6b7280" }}
      >
        VIP{row.level}
      </span>
    ),
  },
  { prop: "name", label: "等级名称", minWidth: 120 },
  {
    prop: "max_withdrawal",
    label: "最大可提现金额",
    minWidth: 140,
    formatter: (row: any) =>
      row.max_withdrawal === 999888777
        ? "无限"
        : `R$${(row.max_withdrawal ).toLocaleString()}`,
  },
  {
    prop: "withdrawal_count",
    label: "当天可提现次数",
    minWidth: 140,
    formatter: (row: any) =>
      row.withdrawal_count === 999888777 ? "无限" : `${row.withdrawal_count}次`,
  },
  {
    prop: "monthly_charge",
    label: "近60天充值金额",
    minWidth: 140,
    formatter: (row: any) => `R$${(row.monthly_charge).toLocaleString()}`,
  },
  {
    prop: "monthly_bet",
    label: "近60天投注金额",
    minWidth: 140,
    formatter: (row: any) => `R$${(row.monthly_bet).toLocaleString()}`,
  },
];
// 签到奖励列定义
const signinColumns = [
  {
    prop: "vip_level",
    label: "等级",
    formatter: (row) => (
      <span
        class="px-2 py-1 rounded text-white font-bold"
        style={{ backgroundColor: vipLevelColors[row.vip_level] || "#6b7280" }}
      >
        VIP{row.vip_level}
      </span>
    ),
  },
  ...Array.from({ length: 7 }, (_, i) => ({
    prop: `day${i + 1}`,
    label: `第${i + 1}天`,
    formatter: (row) =>
      row[`day${i + 1}`] != null ? (row[`day${i + 1}`] / 100)  : "0",
  })),
];

// 获取VIP等级数据
async function getLevelData() {
  try {
    levelLoading.value = true;
    const response = await fetchGetVipLevelList();
    if (response.data) {
      levelData.value = Array.isArray(response.data.data)
        ? response.data.data
        : [];
    }
  } catch (error) {
    console.error("获取VIP等级数据失败:", error);
    ElMessage.error("获取VIP等级数据失败");
  } finally {
    levelLoading.value = false;
  }
}
const rewardType = ref();
// 获取签到配置数据
async function getSigninData() {
  try {
    signinLoading.value = true;
    const response = await fetchGetVipSigninConfig();
    if (response.data) {
      const raw = Array.isArray(response.data.data) ? response.data.data : [];
      signinData.value = mergeVipSigninData(raw);
      rewardType.value = raw[0].reward_type;
    }
  } catch (error) {
    console.error("获取签到配置数据失败:", error);
    ElMessage.error("获取签到配置数据失败");
  } finally {
    signinLoading.value = false;
  }
}

// 根据天数获取奖励
function getRewardByDay(row: any, day: number) {
  const reward = row.rewards?.find((r: any) => r.day === day);
  return reward ? (reward.reward / 100).toFixed(2) : "0.00";
}

// 弹窗控制
const levelDrawerVisible = ref(false);

function handleEditLevel() {
  levelDrawerVisible.value = true;
}

function flattenVipSigninData(
  mergedData: any[],
): { vip_level: number; day: number; reward: number }[] {
  const result: { vip_level: number; day: number; reward: number }[] = [];
  mergedData.forEach((item: any) => {
    console.log(item);
    for (let i = 1; i <= 7; i++) {
      result.push({
        vip_level: item.vip_level,
        day: i,
        reward: item[`day${i}`] ?? 0,
      });
    }
  });
  return result;
}

function handleEditSignin() {
  signinDrawerData.value = flattenVipSigninData(signinData.value);
  signinDrawerVisible.value = true;
}

// 页面加载时获取数据
onMounted(() => {
  getLevelData();
  getSigninData();
});

function mergeVipSigninData(rawData) {
  const result = [];
  const map = {};

  rawData.forEach((item) => {
    if (!map[item.vip_level]) {
      map[item.vip_level] = { vip_level: item.vip_level };
      // 初始化7天
      for (let i = 1; i <= 7; i++) {
        map[item.vip_level][`day${i}`] = null;
      }
      result.push(map[item.vip_level]);
    }
    map[item.vip_level][`day${item.day}`] = item.reward;
  });

  // 可选：按vip_level排序
  result.sort((a, b) => a.vip_level - b.vip_level);

  return result;
}
</script>

<template>
  <div class="min-h-500px gap-16px">
    <!-- VIP等级配置列表 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="font-bold text-lg">等级配置列表</span>
          <ElButton v-if="hasAuth(3)" @click="handleEditLevel">
            <ElIcon><EditPen /></ElIcon> 编辑配置
          </ElButton>
        </div>
      </template>
      <div>
        <ElTable v-loading="levelLoading" :data="levelData.slice(0, 6)">
          <ElTableColumn
            v-for="col in levelColumns1"
            :key="col.prop"
            v-bind="col"
          />
        </ElTable>
        <ElTable v-loading="levelLoading" :data="levelData.slice(6, 11)">
          <ElTableColumn
            v-for="col in levelColumns2"
            :key="col.prop"
            v-bind="col"
          />
        </ElTable>
      </div>
    </ElCard>
    <div style="height: 20px"></div>
    <!-- VIP签到奖励配置 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="font-bold text-lg">VIP签到奖励配置（单位：R$）</span>
          <ElButton v-if="hasAuth(3)" @click="handleEditSignin">
            <ElIcon><EditPen /></ElIcon> 修改配置
          </ElButton>
        </div>
      </template>
      <div>
        <ElTable v-loading="signinLoading" :data="signinData">
          <ElTableColumn
            v-for="col in signinColumns"
            :key="col.prop"
            v-bind="col"
          />
        </ElTable>
      </div>
    </ElCard>

    <!-- 编辑弹窗 -->
    <VipLevelDrawer
      v-model:visible="levelDrawerVisible"
      :operate-type="'edit'"
      :row-data="levelData"
      @submitted="getLevelData"
    />

    <VipSigninDrawer
      v-model:visible="signinDrawerVisible"
      :operate-type="'edit'"
      :reward-type="rewardType"
      :row-data="signinDrawerData"
      @submitted="getSigninData"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 4px;
  border: none;

  .el-card__header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }
}

:deep(.el-table) {
  th.el-table__cell {
    background-color: #f5f7fa;
  }
}

.card-wrapper {
  min-height: 400px;
}
</style>
