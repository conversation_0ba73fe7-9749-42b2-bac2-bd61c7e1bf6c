<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-16 18:05:48
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-06-16 18:20:24
 * @FilePath: \betdoce-admin\src\views\report\invitationRewards\modules\invitation-rewards-search.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="search-wrapper">
    <ElForm ref="formRef" :model="model">
      <ElRow :gutter="16">
        <ElCol :span="6">
          <ElFormItem label="一级父ID" prop="parent_id">
            <ElInput
              v-model="model.parent_id"
              placeholder="请输入一级父ID"
              clearable
              @keyup.enter="handleSearch"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="一级手机号" prop="parent_phone">
            <ElInput
              v-model="model.parent_phone"
              placeholder="请输入手机号"
              clearable
              @keyup.enter="handleSearch"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="注册来源" prop="registration_source">
            <ElSelect
              v-model="model.registration_source"
              placeholder="请选择注册来源"
              clearable
            >
              <ElOption
                v-for="item in registrationSourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElForm } from "element-plus";

interface Props {
  model: {
    parent_id: string;
    parent_phone: string;
    registration_source: string;
  };
}

interface Emits {
  (e: "reset"): void;
  (e: "search"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref();
const registrationSourceOptions = [
 { label: "全部", value: 0 },
  { label: "平台", value: 1 },
  { label: "代理商", value: 2 },
  { label: "渠道", value: 3 },
];

function handleReset() {
  formRef.value?.resetFields();
  emit("reset");
}

function handleSearch() {
  emit("search");
}
</script>

<style lang="scss" scoped>
.search-wrapper {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px 4px 0 0;

  :deep(.el-form-item) {
    margin-bottom: 18px;
    margin-right: 18px;
  }
}

.header-operation {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
}
</style>
