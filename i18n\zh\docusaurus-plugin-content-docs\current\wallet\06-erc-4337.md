---
sidebar_position: 6
---

import Image from '@theme/IdealImage';
import normalTx from './img/normal-tx-white.png';
import tx4337 from './img/4337tx-white.png';

# 开启 ERC-4337 Module

## 在 UniPass Wallet 中开启 4337 Module

目前，UniPass Contract 已经支持 ERC-4337 协议。用户可以在 UniPass Wallet 中开启 4337 Module，体验 ERC-4337 交易。

UniPass 当前使用的是 Stackup 的 [Bundler](https://github.com/stackup-wallet/stackup-bundler) 和 Infinitism 的 [Paymaster](https://github.com/eth-infinitism/account-abstraction/blob/develop/contracts/samples/VerifyingPaymaster.sol)。

未来，UniPass 将与其他生态产品和服务商一道推动 ERC-4337 协议的推广普及和建设完善。

### 开启 4337 Module

您可以在 UniPass Wallet 的设置中开启 4337 Module，目前支持 Ethereum 和 Polygon 链。

[![Watch the video](https://cdn.loom.com/sessions/thumbnails/8d086e95fea54061b433af679d29bd7d-with-play.gif)](https://www.loom.com/share/8d086e95fea54061b433af679d29bd7d)

### 开启 4337 Module 后的交易

<Image img={tx4337} />

UniPass 用户可以通过一笔在 MainModule 中添加 4337 Module 的交易，开启 ERC-4337 兼容模式。

开启 4337 Module 后，用户既可以发起 4337 交易，提交至 Bundler，走标准的 ERC-4337 验证模式；也可以签名 UniPass tx，提交至 Relayer 上链。

### 关于普通交易的说明

<Image img={normalTx} />

在 UniPass 中，每个用户都会在链上部署一个合约钱包。出于经济考虑，用户的合约钱包只会存储与 key list 相关的最重要数据，而关于如何使用 key 管理合约钱包、如何进行钱包社交恢复等通用逻辑，则被放在了一个所有用户共享的 `MainModule` 合约内。

在发起交易时，用户会签名想要执行的操作，并将其提交给 Relayer。然后，Relayer 会帮助将交易提交到链上，在链上调用用户的合约进行验证，并在验证通过后执行用户的操作。