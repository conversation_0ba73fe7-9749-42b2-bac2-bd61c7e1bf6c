import { request } from '../request';

// 获取渠道列表
export function fetchGetChannelList(params?: any) {
  return request({
    url: '/backend/channel/list',
    method: 'get',
    params
  });
}

// 新增渠道
export function fetchCreateChannel(data: any) {
  return request({
    url: '/backend/channel/create',
    method: 'post',
    data
  });
}

// 更新渠道
export function fetchUpdateChannel(data: any) {
  return request({
    url: '/backend/channel/update',
    method: 'post',
    data
  });
}

// 删除渠道
export function fetchDeleteChannel(data: { id: number }) {
  return request({
    url: '/backend/channel/delete',
    method: 'post',
    data
  });
}

// 获取渠道数据统计
export function fetchGetChannelStats(params?: any) {
  return request({
    url: '/backend/channel/stats',
    method: 'get',
    params
  });
}

// 获取渠道注册列表
export function fetchGetChannelRegisters(params?: any) {
  return request({
    url: '/backend/channel/registers',
    method: 'get',
    params
  });
} 

// 获取渠道充值列表
export function fetchGetChannelRecharges(params?: any) {
  return request({
    url: '/backend/channel/recharges',
    method: 'get',
    params
  });
}

// 获取渠道提现记录
export function fetchGetChannelWithdrawRecords(params?: any) {
  return request({
    url: '/backend/channel/withdrawRecords',
    method: 'get',
    params
  });
} 

// 获取渠道游戏记录
export function fetchGetChannelGameRecords(params?: any) {
  return request({
    url: '/backend/channel/gameRecords',
    method: 'get',
    params
  });
} 