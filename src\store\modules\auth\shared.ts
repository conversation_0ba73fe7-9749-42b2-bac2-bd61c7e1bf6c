import { localStg } from '@/utils/storage';

/** Get token */
export function getToken() {
  return localStg.get('token') || '';
}

/** Clear auth storage */
export function clearAuthStorage() {
  localStg.remove('token');
  localStg.remove('userInfo');
  localStg.remove('refreshToken');
}

/** Get user info */
export function getLocalUserInfo() {
  let userInfo: Api.Auth.UserInfo | null = null
  const storedUserInfo = localStg.get('userInfo');
  if (storedUserInfo) {
    userInfo = JSON.parse(storedUserInfo as string);
    if (userInfo && !userInfo.buttons) {
      userInfo.buttons = [];
      userInfo.routes = '';
    }
  } else {
    userInfo = null;
  }
  return userInfo;
}
