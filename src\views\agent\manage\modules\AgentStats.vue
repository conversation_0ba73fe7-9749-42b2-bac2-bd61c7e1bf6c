<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-06 10:27:30
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-28 13:24:56
 * @FilePath: \betdoce-admin\src\views\agent\manage\modules\AgentStats.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div
    class="agent-stats-container grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-16px"
  >
    <ElCard v-for="stat in displayedStats" :key="stat.title" class="card-item">
      <div class="flex flex-col gap-8px">
        <p class="text-base">{{ stat.title }}</p>
        <p class="text-2xl font-bold">{{ stat.value }}</p>
        <p class="text-sm text-gray-500" v-if="stat.subTitle">
          {{ stat.subTitle }}: {{ stat.subValue }}
        </p>
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElCard } from "element-plus";
import { agentListStats } from "@/service/api/agent";

interface Props {
  canManageAgentLevel: boolean;
}

const props = defineProps<Props>();

interface StatsData {
  total_agent_count: number;
  sub_agent_count?: number;
  direct_register_count?: number;
  first_recharge_amount?: number;
  first_withdrawal_amount?: number;
  first_level_agent_count: number;
  total_user_count: number;
  first_level_user_count: number;
  total_recharge_amount: number;
  first_level_recharge_amount: number;
  total_withdrawal_amount: number;
  first_level_withdrawal_amount: number;
}

const statsData = ref<StatsData>({
  total_agent_count: 0,
  first_level_agent_count: 0,
  total_user_count: 0,
  first_level_user_count: 0,
  total_recharge_amount: 0,
  first_level_recharge_amount: 0,
  total_withdrawal_amount: 0,
  first_level_withdrawal_amount: 0,
});

const defaultStats = computed(() => [
  {
    title: "代理商数量",
    value: statsData.value?.sub_agent_count?.toString(),
    subTitle: "",
    subValue: "",
  },
  {
    title: "代理商用户数",
    value: statsData.value?.total_user_count?.toString(),
    subTitle: "直接注册",
    subValue: statsData.value?.direct_register_count?.toString(),
  },
  {
    title: "代理用户充值金额",
    value: `R$ ${((statsData.value?.total_recharge_amount || 0) / 100)?.toFixed(2)}`,
    subTitle: "首充",
    subValue: `R$ ${((statsData.value?.first_recharge_amount || 0) / 100)?.toFixed(2)}`,
  },
  {
    title: "代理用户提现金额",
    value: `R$ ${((statsData.value?.total_withdrawal_amount || 0) / 100)?.toFixed(2)}`,
    subTitle: "首提",
    subValue: `R$ ${((statsData.value?.first_withdrawal_amount || 0) / 100)?.toFixed(2)}`,
  },
]);

const existingStats = computed(() => [
  {
    title: "代理商数量",
    value: statsData.value?.total_agent_count?.toString(),
    subTitle: "一级代理数量",
    subValue: statsData.value?.first_level_agent_count?.toString(),
  },
  {
    title: "代理商用户数",
    value: statsData.value?.total_user_count?.toString(),
    subTitle: "一级代理商用户数",
    subValue: statsData.value?.first_level_user_count?.toString(),
  },
  {
    title: "代理用户充值金额",
    value: `R$${((statsData.value?.total_recharge_amount || 0) / 100)?.toFixed(2)}`,
    subTitle: "一级代理商充值金额",
    subValue: `R$ ${((statsData.value?.first_level_recharge_amount || 0) / 100)?.toFixed(2)}`,
  },
  {
    title: "代理用户提现金额",
    value: `R$${((statsData.value?.total_withdrawal_amount || 0) / 100)?.toFixed(2)}`,
    subTitle: "一级代理商提现金额",
    subValue: `R$ ${((statsData.value?.first_level_withdrawal_amount || 0) / 100)?.toFixed(2)}`,
  },
]);

const displayedStats = computed(() => {
  return props.canManageAgentLevel ? existingStats.value : defaultStats.value;
});

const fetchStatsData = async () => {
  try {
    const response = await agentListStats({});
    console.log(response.data);
    if (response.data?.data) {
      statsData.value = response.data.data;
    }
  } catch (error) {
    console.error("獲取代理商統計數據失敗:", error);
  }
};

onMounted(() => {
  fetchStatsData();
});
defineExpose({
  fetchStatsData,
});
</script>

<style scoped>
.agent-stats-container {
  /* Add any specific container styles here if needed */
}

.card-item {
  /* Add any specific card styles here if needed */
}
</style>
