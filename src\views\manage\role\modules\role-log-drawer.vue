<script setup lang="tsx">
import { ElDialog, ElTable, ElTableColumn, ElPagination } from "element-plus";
import { ref, watch } from "vue";
import moment from "moment";
import {getRoleOperationLogs} from "@/service/api/sysrole"
import { getBrazilDate } from "@/utils/format";
interface Props {
  visible: boolean;
  roleId?: number;
}

interface LogData {
  id: number;
  operator: string;
  operateTime: string;
  operateContent: string;
  logCode: string;
}

const props = defineProps<Props>();
const emit = defineEmits(["update:visible"]);

const loading = ref(false);
const logList = ref<LogData[]>([]);
const pagination = ref({
  total: 0,
  currentPage: 1,
  pageSize: 10
});

const columns = [
  { prop: "operator_name", label: "操作账号", width: 120 },
  {
    prop: "operation_time",
    label: "操作时间",
    width: 160,
    formatter: (row: any) => moment(getBrazilDate(row.operation_time)).format("YYYY-MM-DD HH:mm")
  },
  { prop: "operation_content", label: "操作内容", minWidth: 200 },
  { prop: "log_detail", label: "日志详情", minWidth: 200 }
];

const handleClose = () => {
  emit("update:visible", false);
};

const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page;
  fetchLogList();
};

const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size;
  pagination.value.currentPage = 1;
  fetchLogList();
};

const fetchLogList = async () => {
  if (!props.roleId) return;

  loading.value = true;
  try {
    const res = await getRoleOperationLogs({
      role_id: props.roleId,
      page: pagination.value.currentPage,
      size: pagination.value.pageSize
    });
    // 假设返回结构为 { data: 日志数组, count: 总数 }
    logList.value = res.data.data || [];
    pagination.value.total =  res.data.count || 0;
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// onMounted(() => {
//   if (props.visible && props.roleId) {
//     fetchLogList();
//   }
// });

// 監聽 visible 屬性變化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal && props.roleId) {
      fetchLogList();
    }
  }
);
</script>

<template>
  <ElDialog
    :model-value="visible"
    title="操作日志"
    width="800px"
    :close-on-click-modal="false"
    @update:model-value="(val) => emit('update:visible', val)"
    @close="handleClose"
  >
    <div class="h-[600px] flex flex-col">
      <ElTable
        v-loading="loading"
        :data="logList"
        border
        style="width: 100%"
        height="calc(100% - 50px)"
      >
        <ElTableColumn
          v-for="col in columns"
          :key="col.prop"
          v-bind="col"
        />
      </ElTable>

      <div class="mt-4 flex justify-end">
        <ElPagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </ElDialog>
</template>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding: 20px;
}
</style>
