/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 14:39:01
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-07-10 21:08:42
 * @FilePath: \betdoce-web\src\api\activity.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from './request'

export interface ActivityStatusRecord {
  id: number; // 活動ID
  user_id: number; // 用戶ID
  activity_id: number; // 活動ID
  participation_count: number; // 參與次數
  lock_count: number; // 鎖定次數
  accumulated_amount: number; // 累積金額
  target_amount: number; // 目標金額
  end_time: number; // 結束時間
  invite_link: string; // 邀請鏈接
  current_progress: number; // 當前進度
  current_amount: number; // 當前金額
  current_register_num: number; // 當前註冊人數
  is_completed: number; // 是否完成
  short?: string; // 短鏈接
  // 新增字段
  need_recharge?: number; // 是否需要充值
  recharge_end_time?: number; // 充值倒计时结束时间（时间戳，单位毫秒）
  must_recharge_amount?: number; // 充值金额
}

export interface ActivityResponse {
  status_code: number
  data: {
    is_new_participation: boolean
    params_str: string
    record: ActivityStatusRecord
    short_url: string
  }
  count: number
}

export interface ActivityParams {
  user_id?: number
  activity_id?: number
  invitation_code: string
}
import {  type PddActivityContent } from "@/api/home";

// 获取活动状态
export function getActivityStatus(data: ActivityParams) {
  return request.Post('/activity/lastcycle_status', data)
}

// 获取活动详情
export function getActivityDetails(data: ActivityParams) {
  return request.Post<any>('/activity/details', data)
}

// // 参与活动
// export function joinActivity(data: ActivityParams) {
//   return request.Post('/activity/player_join_activity', data)
// } 

// 拼多多助力活动
export function supportActivity(params: any) {
  return request.Get<any>('/activity/support', {params})
}

// 关闭拼多多助力活动
export function closeTask(data: any) {
  return request.Post('/activity/close_task', data)
}

// 通知派发奖金
export function copyCheck(data: any) {
  return request.Post('/activity/copy_check', data)
}
// 参与活动
export function joinPddActivity(data: any) {
  return request.Post<PddActivityContent>('/activity/join', data)
} 
// 显示一次弹窗
export function oncePopup() {
  return request.Get('/popup/oncePopup', )
} 
// 登录时显示的弹窗
export function loginPopup() {
  return request.Get('/popup/loginPopup', )
} 
// 首页显示的弹窗
export function homePopup() {
  return request.Get('/popup/homePopup', )
} 
// 判断助力弹窗是否打开
export function checkActivityPopup(invite_code: string) {
  return request.Get<any>('/activity/popup', { params: { invite_code } })
} 