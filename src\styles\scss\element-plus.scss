$radius: 4px;

html:root,
html.dark:root {
  --el-color-primary: rgb(var(--primary-color));
  --el-color-primary-light-3: rgb(var(--primary-400-color));
  --el-color-primary-light-5: rgb(var(--primary-300-color));
  --el-color-primary-light-7: rgb(var(--primary-200-color));
  --el-color-primary-light-9: rgb(var(--primary-50-color));

  --el-color-success: rgb(var(--success-color));
  --el-color-success-light-3: rgb(var(--success-400-color));
  --el-color-success-light-5: rgb(var(--success-300-color));
  --el-color-success-light-7: rgb(var(--success-200-color));
  --el-color-success-light-9: rgb(var(--success-50-color));

  --el-color-warning: rgb(var(--warning-color));
  --el-color-warning-light-3: rgb(var(--warning-400-color));
  --el-color-warning-light-5: rgb(var(--warning-300-color));
  --el-color-warning-light-7: rgb(var(--warning-200-color));
  --el-color-warning-light-9: rgb(var(--warning-50-color));

  --el-color-info: rgb(var(--info-color));
  --el-color-info-light-3: rgb(var(--info-400-color));
  --el-color-info-light-5: rgb(var(--info-300-color));
  --el-color-info-light-7: rgb(var(--info-200-color));
  --el-color-info-light-9: rgb(var(--info-50-color));

  --el-color-danger: rgb(var(--error-color));
  --el-color-danger-light-3: rgb(var(--error-400-color));
  --el-color-danger-light-5: rgb(var(--error-300-color));
  --el-color-danger-light-7: rgb(var(--error-200-color));
  --el-color-danger-light-9: rgb(var(--error-50-color));

  --el-menu-item-hover-fill: rgba(0, 0, 0, 0.05);

  --el-menu-item-height: 56px;
}
html.dark:root {
  --el-color-primary-light-9: transparent;
  --el-color-success-light-9: transparent;
  --el-color-warning-light-9: transparent;
  --el-color-info-light-9: transparent;
  --el-color-danger-light-9: transparent;
}

.bg-inverted {
  .el-menu {
   // --el-menu-bg-color: var(--el-fill-color-blank);
    //--el-menu-text-color: var(--el-text-color-primary);
    --el-menu-bg-color: transparent;
    // --el-menu-text-color: #e5eaf3;
    --el-menu-text-color: #e5eaf3;
  }
}

.el-menu {
   --el-menu-bg-color: #35353c;
   --el-menu-text-color: #A5A5AD;
 }

:focus-visible {
  outline: 0;
}

.el-menu--popup {
  min-width: unset;
}
.el-popper {
  border: none;
  border-radius: $radius;
}
.el-menu {
  border-right: none;
  &.el-menu--horizontal {
    border: none;
  }
  .el-sub-menu.is-active {
    > .el-sub-menu__title {
      //color: var(--el-menu-active-color);
      //color: #A5A5AD;
    }
  }
  //修改闪烁问题
  .el-sub-menu__title,
  .el-menu-item {
    transition: none !important;
  }
  .el-sub-menu__title:hover,
  li.el-menu-item:not(.is-disabled):hover,
  .el-menu-item.is-active {
    background-color: unset;
    &::before {
      content: '';
      position: absolute;
      z-index: auto;
      left: 0px;
      right: 0px;
      top: 0;
      bottom: 0;
      border-radius: 0;
      pointer-events: none;
      background-color: var(--el-menu-item-hover-fill);
    }
  }
  li.el-menu-item {
    margin-top: 6px;
    &.is-active,
    &.is-active:hover {
      color: #ffffff;
      font-weight: 500;
      &::before {
        background: linear-gradient(
          135deg,
          #536fe1 0%,
          #6a83e4 50%,
          #8297e7 100%
        );
      }
    }
  }
  li.el-sub-menu {
    margin-top: 2px;
  }
}

html .el-collapse {
  --el-collapse-header-font-size: 16px;
  border: none;
  .el-collapse-item__header {
    border: none;
  }
  .el-collapse-item__wrap {
    border: none;
  }
  .el-collapse-item__content {
    padding-bottom: 0;
  }
}

.el-card {
  --el-card-padding: 16px;
  .el-card__header {
    font-weight: 500;
    font-size: 16px;
    padding-bottom: 0;
  }
}

.el-statistic {
  .el-statistic__content {
    font-weight: 400;
    font-size: 24px;
  }
}

.el-form {
  .el-form-item__label {
    align-items: center;
    line-height: 1.2;
  }
}

.el-dialog {
  .el-dialog__footer {
    overflow: hidden;
  }
}

.el-card {
  display: flex;
  flex-direction: column;
  .el-card__header {
    border: none;
  }
  .el-card__body {
    height: 100%;
    overflow: auto;
  }
}

// .el-tabs {
//   &.segment {
//     border-radius: $radius;
//     border: none;
//     padding: 4px;
//     background-color: var(--el-fill-color-light);
//     --el-tabs-header-height: 30px;
//     .el-tabs__content {
//       display: none;
//     }
//     .el-tabs__header {
//       border: none;
//       .el-tabs__item {
//         border: none;
//         padding: 0 24px;
//         border-radius: $radius;
//       }
//     }
//   }
// }

.el-divider__text {
  font-size: 16px;
}

.el-segmented {
  padding: 5px;
  border-radius: $radius;
  .el-segmented__item {
    border-radius: $radius;
    padding: 4px 20px;
  }
  .el-segmented__item-selected {
    border-radius: $radius;
  }
}

//cell鼠标背景
.el-table--enable-row-hover .el-table__body tr:hover > td {
  background: #c8e3ff !important;
}

.el-table {

  // 去除表头和表体所有单元格的边框
  .el-table__header th{
    border: none !important;
    font-size: 14px;
    font-weight: 600;
    color: #232323;
    
  }

  .el-table__body td {
    border: none !important;
  }

  // 去除表格横向/纵向分割线
  .el-table__cell,
  .el-table__header-wrapper,
  .el-table__body-wrapper {
    border: none !important;
    color: #8B8B8D;
  }

  // 去除表头底部分割线
  .el-table__header-wrapper {
    box-shadow: none !important;
  }

  // 去除表格底部线
  .el-table__footer-wrapper {
    border: none !important;
    box-shadow: none !important;
  }

  // 去除虚线/细线（部分主题可能有伪元素）
  th::after,
  td::after {
    border: none !important;
    background: none !important;
    content: none !important;
  }

  // 去除斑马纹底色（可选）
  &.el-table--striped .el-table__body tr.el-table__row--striped td {
    background: #fff !important;
  }
}

.el-pagination {
  .el-select .el-input__inner {
    border-radius: 4px;
    background: #f7f8fa;
    border: none;
    color: #232323;
    font-size: 14px;
    height: 32px;
  }

  .el-pagination__sizes, .el-pagination__total{
    margin-left: 0 !important;
  }

  

  .el-pagination__jump {
    margin-left: 12px;
    .el-input__inner {
      border-radius: 4px;
      background: #f7f8fa;
      border: none;
      color: #232323;
      font-size: 14px;
      height: 32px;
    }
  }

  .el-pager {
    gap: 8px;
    .number, .el-pagination__jump {
      border-radius: 4px;
      min-width: 32px;
      height: 32px;
      line-height: 32px;
      margin: 0 2px;
      font-size: 15px;
      color: #8B8B8D;
      background: #f7f8fa;
      border: none;
      transition: background 0.2s, color 0.2s;
      cursor: pointer;
      &:hover {
        background: #e6eaff;
       // color: #536fe1;
      }
    }
    .is-active {
      background: #7c83f5 !important;
      color: #fff !important;
      font-weight: 600;
      border: none;
    }
    .more {
      background: transparent;
      color: #bdbdbd;
      cursor: default;
    }
  }

  button, .btn-next, .btn-prev {
    border-radius: 8px;
    min-width: 32px;
    height: 32px;
    background: #f7f8fa;
    color: #bdbdbd;
    border: none;
    margin: 0 8px;
    font-size: 14px;
    transition: background 0.2s, color 0.2s;
    &:hover:not([disabled]) {
      background: #e6eaff;
      //color: #536fe1;
    }
    &[disabled] {
      background: #f7f8fa !important;
      color: #e0e0e0 !important;
      cursor: not-allowed;
    }
  }
}

//顶部收缩
.search-wrapper {
  background-color: #fff;
  padding: 16px;
  border-radius: 4px 4px 0 0;
  --un-shadow: var(--un-shadow-inset) 0 1px 2px 0 var(--un-shadow-color, rgb(0 0 0 / 0.05));
  box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);

  .header-operation {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  :deep(.el-form-item) {
    margin-bottom: 16px;
    width: 100%;

    .el-form-item__content {
      width: 100%;
    }

    .el-select,
    .el-input {
      width: 100%;
    }
  }

  :deep(.el-button + .el-button) {
    margin-left: 8px;
  }
}