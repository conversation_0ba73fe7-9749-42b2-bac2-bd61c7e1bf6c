# WebSocket 1006 异常状态处理

## 概述

WebSocket关闭代码1006表示"异常关闭"，通常发生在以下情况：
- 页面隐藏/显示切换
- 网络连接切换（WiFi到移动网络）
- 浏览器优化策略
- 系统休眠/唤醒

这些情况下的1006关闭通常是正常的，不应该被视为错误。

## 实现的解决方案

### 1. 配置化的关闭代码处理

在 `src/utils/websocketConfig.ts` 中定义了关闭代码的处理策略：

```typescript
closeCodes: {
  // 忽略的关闭代码（不触发重连，不记录错误）
  ignoreCodes: [
    1006, // 异常关闭 - 通常是正常的网络切换或页面状态变化
  ],
  
  // 正常关闭代码（不触发重连）
  normalCodes: [
    1000, // 正常关闭
    1001, // 离开
  ],
  
  // 可重试的错误代码（触发重连）
  retryableCodes: [
    1002, // 协议错误
    1003, // 不支持的数据类型
    1007, // 数据类型不一致
    1009, // 消息太大
    1011, // 服务器遇到意外情况
    1015, // TLS握手失败
  ],
}
```

### 2. 智能的关闭处理逻辑

WebSocket服务现在根据配置策略处理关闭事件：

```typescript
const strategy = getCloseCodeStrategy(event.code);

switch (strategy) {
  case 'ignore':
    // 1006等代码被忽略，不触发重连
    wsLog.info(`关闭代码 ${event.code} 被忽略`);
    return;

  case 'normal':
    // 正常关闭，不触发重连
    wsLog.info(`正常关闭 - 代码: ${event.code}`);
    return;

  case 'retry':
    // 错误关闭，尝试重连
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.scheduleReconnect();
    }
    break;
}
```

### 3. 连接保持器接管

当1006被忽略后，连接保持器会：
- 定期检查连接状态
- 在适当的时机重新建立连接
- 确保在有效条件下维持连接

### 4. 更智能的状态判断

网络监控器和连接保持器现在使用更智能的连接状态判断：

```typescript
// 只在真正断开时认为需要重连
const isReallyDisconnected = !isConnected && wsState === 3;
const isConnecting = wsState === 0; // 连接中状态
```

## 配置选项

### 自定义忽略的关闭代码

```typescript
import { updateWebSocketConfig } from '@/utils/websocketConfig';

// 添加更多忽略的代码
updateWebSocketConfig({
  closeCodes: {
    ignoreCodes: [1006, 1005], // 添加1005也被忽略
  }
});
```

### 调整日志级别

```typescript
// 在生产环境中减少日志输出
updateWebSocketConfig({
  debug: {
    enableLogs: false,
    logLevel: 'error',
  }
});
```

## 监控和调试

### 1. 状态监控组件

在开发环境中，WebSocket状态组件会显示：
- "1006处理: 已忽略" - 表示1006状态被正确处理

### 2. 日志输出

使用新的日志系统，可以看到：
```
[WebSocket Info] WebSocket关闭代码 1006 (异常关闭) 被忽略，这通常是正常的网络切换或页面状态变化
```

### 3. 测试页面

访问 `/websocket-test` 可以：
- 查看详细的连接状态
- 手动触发各种操作
- 观察1006处理的效果

## 最佳实践

### 1. 不要手动处理1006

```typescript
// ❌ 错误做法
websocketService.onClose((event) => {
  if (event.code === 1006) {
    // 手动重连
    websocketService.connect();
  }
});

// ✅ 正确做法
// 让系统自动处理，连接保持器会在适当时机重连
```

### 2. 监听真正的错误

```typescript
// 监听配置的可重试错误
websocketService.onClose((event) => {
  if (isRetryableCloseCode(event.code)) {
    console.log('发生了真正的错误，需要处理');
  }
});
```

### 3. 使用连接保持器

```typescript
// 确保连接保持器在运行
websocketKeeper.start();

// 在需要时手动检查
await websocketKeeper.manualCheck();
```

## 常见问题

### Q: 为什么1006后不立即重连？
A: 1006通常是临时的网络状态变化，立即重连可能失败。连接保持器会在适当时机（30秒后）检查并重连。

### Q: 如何知道1006是否被正确处理？
A: 查看浏览器控制台，应该看到"被忽略"的日志，而不是"尝试重连"。

### Q: 可以修改1006的处理策略吗？
A: 可以，通过 `updateWebSocketConfig` 修改 `closeCodes.ignoreCodes` 配置。

### Q: 1006被忽略后，连接什么时候恢复？
A: 连接保持器每30秒检查一次，如果检测到需要连接且条件满足，会自动重连。

## 技术细节

### 关闭代码映射
- `1006`: 异常关闭 → 忽略策略
- `1000`: 正常关闭 → 正常策略  
- `1002`: 协议错误 → 重试策略

### 状态检查逻辑
```typescript
// WebSocket状态码
// 0: CONNECTING - 连接中
// 1: OPEN - 已连接
// 2: CLOSING - 关闭中  
// 3: CLOSED - 已关闭

// 只在状态为3且未连接时认为真正断开
const isReallyDisconnected = !isConnected && wsState === 3;
```

这种处理方式确保了1006异常状态不会导致不必要的重连尝试，同时保持了连接的稳定性和可靠性。
