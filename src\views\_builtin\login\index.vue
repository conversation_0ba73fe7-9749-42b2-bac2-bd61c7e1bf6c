<script setup lang="ts">
import { computed } from 'vue';
import type { Component } from 'vue';
import { getPaletteColorByNumber, mixColor } from '@sa/color';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { useThemeStore } from '@/store/modules/theme';
import { loginModuleRecord } from '@/constants/app';
import PwdLogin from './modules/pwd-login.vue';

defineOptions({ name: 'LoginPage' });

interface Props {
  /** The login module */
  module?: UnionKey.LoginModule;
}

const props = defineProps<Props>();

const appStore = useAppStore();
const themeStore = useThemeStore();

interface LoginModule {
  label: string;
  component: Component;
}

const moduleMap: Record<UnionKey.LoginModule, LoginModule> = {
  'pwd-login': { label: loginModuleRecord['pwd-login'], component: PwdLogin },
};

const activeModule = computed(() => moduleMap[props.module || 'pwd-login']);

const bgThemeColor = computed(() =>
  themeStore.darkMode ? getPaletteColorByNumber(themeStore.themeColor, 600) : themeStore.themeColor
);

const bgColor = computed(() => {
  const COLOR_WHITE = '#ffffff';
  const ratio = themeStore.darkMode ? 0.5 : 0.2;
  return mixColor(COLOR_WHITE, themeStore.themeColor, ratio);
});
</script>

<template>
  <div class="login-bg">
    <div class="login-form-container">
      <div class="login-title">后台管理中心</div>
      <Transition :name="themeStore.page.animateMode" mode="out-in" appear>
        <component :is="activeModule.component" />
      </Transition>
    </div>
  </div>
</template>

<style scoped>
.login-bg {
  width: 100vw;
  height: 100vh;
  background: url('@/assets/imgs/login-bg.png') no-repeat center center;
  background-size: cover;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  animation: gradientMove 30s ease-in-out infinite;
}

@keyframes gradientMove {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  100% {
    background-position: 0% 0%;
  }
}

.login-form-container {
  width: 420px;
  background: rgba(33, 45, 109, 0.77);
  padding: 50px;
  margin-right: 10vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  backdrop-filter: blur(10px);
  animation: slideIn 0.8s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.login-title {
  align-self: flex-start;
  color: #fff;
  font-size: 24px;
  text-align: left;
  margin-bottom: 20px;
}

@media (max-width: 600px) {
  .login-form-container {
    width: 90vw;
    padding: 50px;
    margin-right: 10vw;
  }
}
</style>
