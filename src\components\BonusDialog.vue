<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'
import { showSuccess, showError } from '@/utils/toast'
import { exchangeCode } from '@/api/jackpot'

interface Props {
  show: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['update:show', 'exchange-success'])

const redeemCode = ref('')
const isLoading = ref(false)

const handleClose = () => {
  emit('update:show', false)
  redeemCode.value = ''
}

const handleRedeem = async () => {
  if (!redeemCode.value) {
    showError('Por favor, insira o código de resgate')
    return
  }

  try {
    isLoading.value = true
    const response = await exchangeCode(redeemCode.value)
    if (response) {
      // showSuccess( 'Código resgatado com sucesso')
      console.log(response)
      // 触发兑换成功事件，传递兑换码和金额信息
      emit('exchange-success', {
        code: redeemCode.value,
        amount: response.amount || 0,
        // message: response.message
      })
      handleClose()
    } else {
      showError( 'Falha ao resgatar o código')
    }
  } catch (error: any) {
    console.error('Exchange code error:', error)
    showError(error?.response?.data?.message || 'Falha ao resgatar o código')
  } finally {
    isLoading.value = false
  }
}
</script>

<template>
  <v-dialog
    :model-value="show"
    @update:model-value="emit('update:show', $event)"
    width="366"
    @click:outside="handleClose"
    class="bonus-dialog"
  >
    <!-- <v-btn
        icon="mdi-close"
        variant="text"
        size="small"
        class="close-btn"
        @click="handleClose"
    /> -->
    <v-card class="bonus-card pa-4">
      <div class="dialog-content">
        <div class="dialog-header mb-4">
          <div class="dialog-title">Codigo de redencao</div>
        </div>

        <v-text-field
          v-model="redeemCode"
          placeholder="Digite o código"
          density="compact"
          variant="outlined"
          clearable
          class="redeem-input"
          :loading="isLoading"
          @keyup.enter="handleRedeem"
        />
        <div class="dialog-subtitle mb-4">Os códigos de resgate serao distribu ídos por meio de eventos de tempos em tempospreste atengäo aos anú ncios oficiais do grupo.</div>
        <div class="dialog-footer">
          <v-btn
            block
            color="primary"
            :loading="isLoading"
            class="redeem-btn"
            @click="handleRedeem"
          >
            Resgatar agora
          </v-btn>
        </div>
      </div>
    </v-card>
  </v-dialog>
</template>

<style lang="scss" scoped>
.bonus-dialog {
  :deep(.v-overlay__content) {
    align-items: center;
    padding: 24px;
  }
}

.bonus-card {
  background: #1E2332;
  border-radius: 20px !important;
  margin-top: 30px;
}

.close-btn{
  position: absolute;
  height: 22px;
  width: 22px;
  background: #c9cad8 !important;
  color: #2b324d;
  right: 24px;
  z-index:9999;
  :deep(.v-icon){
    font-size: 18px;
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
}

.dialog-header {
  text-align: center;
}

.dialog-title {
  color: #fff;
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 8px;
}

.dialog-subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.redeem-input {
  :deep(.v-field) {
    height: 46px;
    line-height: 46px;
    border-radius: 8px;
    background: #2B324D;
    .v-field__input {
      color: #fff;
      font-size: 16px;
      padding: 0 16px;
      height: 46px;
      line-height: 46px;
    }
    .v-field__outline{
      display: none;
    }
  }
}

.dialog-footer {
  margin-top: 8px;
}

.redeem-btn {
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  text-transform: none;
  letter-spacing: 0.5px;
  background: linear-gradient(0deg, #C9B737, #2ABB27);
  border-radius: 22px;
}
</style>