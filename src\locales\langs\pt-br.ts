const local: App.I18n.Schema = {
  system: {
    title: 'Sistema de Administração',
    updateTitle: 'Notificação de Atualização do Sistema',
    updateContent: 'Nova versão do sistema detectada. Deseja atualizar a página agora?',
    updateConfirm: 'Atualizar Agora',
    updateCancel: 'Mais Tarde'
  },
  common: {
    action: 'Ação',
    add: 'Adicionar',
    addSuccess: 'Adicionado com sucesso',
    backToHome: 'Voltar ao Início',
    batchDelete: 'Excluir em Lote',
    cancel: 'Cancelar',
    close: 'Fechar',
    check: 'Verificar',
    expandColumn: 'Expandir Coluna',
    columnSetting: 'Configuração de Coluna',
    config: 'Configuração',
    confirm: 'Confirmar',
    delete: 'Excluir',
    deleteSuccess: 'Excluído com sucesso',
    confirmDelete: 'Confirma a exclusão?',
    edit: 'Editar',
    warning: 'Aviso',
    error: 'Erro',
    index: 'Índice',
    keywordSearch: 'Digite palavras-chave para pesquisar',
    logout: 'Sair',
    logoutConfirm: 'Confirma o logout?',
    lookForward: 'Em breve',
    modify: 'Modificar',
    modifySuccess: 'Modificado com sucesso',
    noData: 'Sem dados',
    operate: 'Operar',
    pleaseCheckValue: 'Verifique se o valor inserido é válido',
    refresh: 'Atualizar',
    reset: 'Redefinir',
    search: 'Pesquisar',
    switch: 'Alternar',
    tip: 'Dica',
    trigger: 'Acionar',
    update: 'Atualizar',
    updateSuccess: 'Atualizado com sucesso',
    userCenter: 'Centro do Usuário',
    yesOrNo: {
      yes: 'Sim',
      no: 'Não'
    }
  },
  request: {
    logout: 'Logout do usuário após falha na solicitação',
    logoutMsg: 'Status do usuário inválido, faça login novamente',
    logoutWithModal: 'Exibir modal e fazer logout após falha na solicitação',
    logoutWithModalMsg: 'Status do usuário inválido, faça login novamente',
    refreshToken: 'Token da solicitação expirou, atualizando token',
    tokenExpired: 'Token expirado'
  },
  theme: {
    themeSchema: {
      title: 'Modo do Tema',
      light: 'Modo Claro',
      dark: 'Modo Escuro',
      auto: 'Seguir Sistema'
    },
    grayscale: 'Modo Escala de Cinza',
    colourWeakness: 'Modo Daltonismo',
    layoutMode: {
      title: 'Modo de Layout',
      vertical: 'Menu Lateral Esquerdo',
      'vertical-mix': 'Menu Lateral Misto',
      horizontal: 'Menu Superior',
      'horizontal-mix': 'Menu Superior Misto',
      reverseHorizontalMix: 'Inverter posição do menu principal e submenu'
    },
    recommendColor: 'Aplicar cor do algoritmo recomendado',
    recommendColorDesc: 'Referência do algoritmo de cor recomendada',
    themeColor: {
      title: 'Cor do Tema',
      primary: 'Cor Primária',
      info: 'Cor de Informação',
      success: 'Cor de Sucesso',
      warning: 'Cor de Aviso',
      error: 'Cor de Erro',
      followPrimary: 'Seguir Cor Primária'
    },
    scrollMode: {
      title: 'Modo de Rolagem',
      wrapper: 'Rolagem Externa',
      content: 'Rolagem do Conteúdo'
    },
    page: {
      animate: 'Animação de Transição de Página',
      mode: {
        title: 'Tipo de Animação de Transição',
        'fade-slide': 'Deslizar',
        fade: 'Fade In/Out',
        'fade-bottom': 'Fade Bottom',
        'fade-scale': 'Fade Scale',
        'zoom-fade': 'Zoom Fade',
        'zoom-out': 'Zoom Out',
        none: 'Nenhum'
      }
    },
    fixedHeaderAndTab: 'Fixar Cabeçalho e Aba',
    header: {
      height: 'Altura do Cabeçalho',
      breadcrumb: {
        visible: 'Exibir Breadcrumb',
        showIcon: 'Exibir Ícone do Breadcrumb'
      },
      multilingual: {
        visible: 'Exibir Multilíngue'
      }
    },
    tab: {
      visible: 'Exibir Aba',
      cache: 'Cache da Aba',
      height: 'Altura da Aba',
      mode: {
        title: 'Estilo da Aba',
        chrome: 'Chrome',
        button: 'Botão'
      }
    },
    sider: {
      inverted: 'Cor Invertida da Barra Lateral',
      width: 'Largura da Barra Lateral',
      collapsedWidth: 'Largura da Barra Lateral Recolhida',
      mixWidth: 'Largura da Barra Lateral Mista',
      mixCollapsedWidth: 'Largura da Barra Lateral Mista Recolhida',
      mixChildMenuWidth: 'Largura do Menu Filho Misto'
    },
    footer: {
      visible: 'Exibir Rodapé',
      fixed: 'Fixar Rodapé',
      height: 'Altura do Rodapé',
      right: 'Rodapé Direito'
    },
    watermark: {
      visible: 'Exibir Marca d\'Água',
      text: 'Texto da Marca d\'Água'
    },
    themeDrawerTitle: 'Configuração do Tema',
    pageFunTitle: 'Função da Página',
    configOperation: {
      copyConfig: 'Copiar Configuração',
      copySuccessMsg: 'Copiado com sucesso, substitua a variável defaultSettings em src/theme/settings.ts',
      resetConfig: 'Redefinir Configuração',
      resetSuccessMsg: 'Redefinido com sucesso'
    }
  },
  route: {
    login: 'Login',
    403: 'Sem Permissão',
    404: 'Página Não Encontrada',
    500: 'Erro do Servidor',
    'iframe-page': 'Página Iframe',
    home: 'Início'
  },
  page: {
    login: {
      common: {
        loginOrRegister: 'Login / Registro',
        userNamePlaceholder: 'Digite o nome de usuário',
        phonePlaceholder: 'Digite o número de telefone',
        codePlaceholder: 'Digite o código de verificação',
        passwordPlaceholder: 'Digite a senha',
        confirmPasswordPlaceholder: 'Digite a senha novamente',
        codeLogin: 'Login com Código',
        confirm: 'Confirmar',
        back: 'Voltar',
        validateSuccess: 'Verificação bem-sucedida',
        loginSuccess: 'Login bem-sucedido',
        welcomeBack: 'Bem-vindo de volta, {userName}!'
      },
      pwdLogin: {
        title: 'Login com Senha',
        rememberMe: 'Lembrar-me',
        forgetPassword: 'Esqueceu a senha?',
        register: 'Registrar conta',
        otherAccountLogin: 'Outras formas de login',
        otherLoginMode: 'Outros métodos de login',
        superAdmin: 'Super Administrador',
        admin: 'Administrador',
        user: 'Usuário Comum'
      },
      codeLogin: {
        title: 'Login com Código',
        getCode: 'Obter código',
        reGetCode: 'Reenviar após {time}s',
        sendCodeSuccess: 'Código enviado com sucesso',
        imageCodePlaceholder: 'Digite o código da imagem'
      },
      register: {
        title: 'Registrar Conta',
        agreement: 'Li e aceito',
        protocol: '《Acordo do Usuário》',
        policy: '《Política de Privacidade》'
      },
      resetPwd: {
        title: 'Redefinir Senha'
      },
      bindWeChat: {
        title: 'Vincular Conta WeChat'
      }
    },
    about: {
      title: 'Sobre',
      introduction: 'SoybeanAdmin é um template de sistema de gerenciamento elegante e poderoso baseado em Vue3, Vite5, TypeScript, Pinia e UnoCSS. Ele possui as mais recentes tecnologias de desenvolvimento frontend populares, incluindo TypeScript, Vue3, Pinia, UnoCSS, etc. Ele fornece soluções ricas de componentes e layouts, com código rigorosamente tipado, e é adequado para o desenvolvimento de vários sistemas de gerenciamento.',
      projectInfo: {
        title: 'Informações do Projeto',
        version: 'Versão',
        latestBuildTime: 'Último Tempo de Build',
        githubLink: 'Link do Github',
        previewLink: 'Link de Visualização'
      },
      prdDep: 'Dependências de Produção',
      devDep: 'Dependências de Desenvolvimento'
    },
    home: {
      branchDesc: 'Para facilitar o desenvolvimento e merge de atualizações, simplificamos o código da branch main, mantendo apenas o menu da página inicial. O restante do conteúdo foi movido para a branch example para manutenção. O conteúdo exibido no endereço de visualização é o conteúdo da branch example.',
      greeting: 'Bom dia, {userName}, hoje é outro dia cheio de energia!',
      weatherDesc: 'Hoje nublado com sol, 20℃ - 25℃!',
      projectCount: 'Contagem de Projetos',
      todo: 'Tarefas',
      message: 'Mensagem',
      downloadCount: 'Contagem de Downloads',
      registerCount: 'Contagem de Registros',
      schedule: 'Cronograma',
      study: 'Estudo',
      work: 'Trabalho',
      rest: 'Descanso',
      entertainment: 'Entretenimento',
      visitCount: 'Contagem de Visitas',
      turnover: 'Volume de Negócios',
      dealCount: 'Contagem de Negócios',
      projectNews: {
        title: 'Notícias do Projeto',
        moreNews: 'Mais Notícias',
        desc1: 'Soybean criou o projeto open source soybean-admin em 28 de maio de 2021!',
        desc2: 'Yanbowe enviou um bug para soybean-admin, a barra de abas múltiplas não se adapta automaticamente.',
        desc3: 'Soybean está se preparando totalmente para o lançamento do soybean-admin!',
        desc4: 'Soybean está ocupado escrevendo a documentação do projeto para soybean-admin!',
        desc5: 'Soybean acabou de escrever algumas coisas na página do workspace, está apresentável!'
      },
      creativity: 'Criatividade'
    },
    function: {
      tab: {
        tabOperate: {
          title: 'Operação de Aba',
          addTab: 'Adicionar Aba',
          addTabDesc: 'Ir para página sobre',
          closeTab: 'Fechar Aba',
          closeCurrentTab: 'Fechar Aba Atual',
          closeAboutTab: 'Fechar Aba "Sobre"',
          addMultiTab: 'Adicionar Múltiplas Abas',
          addMultiTabDesc1: 'Ir para página de múltiplas abas',
          addMultiTabDesc2: 'Ir para página de múltiplas abas (com parâmetros de consulta)'
        },
        tabTitle: {
          title: 'Título da Aba',
          changeTitle: 'Alterar Título',
          change: 'Alterar',
          resetTitle: 'Redefinir Título',
          reset: 'Redefinir'
        }
      },
      multiTab: {
        routeParam: 'Parâmetro de Rota',
        backTab: 'Voltar para function_tab'
      },
      toggleAuth: {
        toggleAccount: 'Alternar Conta',
        authHook: 'Hook de autorização `hasAuth`',
        superAdminVisible: 'Visível para Super Administrador',
        adminVisible: 'Visível para Administrador',
        adminOrUserVisible: 'Visível para Administrador e Usuário'
      },
      request: {
        repeatedErrorOccurOnce: 'Erro de solicitação repetida ocorre apenas uma vez',
        repeatedError: 'Erro de solicitação repetida',
        repeatedErrorMsg1: 'Erro de solicitação personalizada 1',
        repeatedErrorMsg2: 'Erro de solicitação personalizada 2'
      }
    },
    alova: {
      scenes: {
        captchaSend: 'Enviar Código de Verificação',
        autoRequest: 'Solicitação Automática',
        visibilityRequestTips: 'Solicitação automática de dados ao alternar janela do navegador',
        pollingRequestTips: 'Solicitação automática a cada 3 segundos',
        networkRequestTips: 'Solicitação automática após reconexão de rede',
        refreshTime: 'Tempo de Atualização',
        startRequest: 'Iniciar Solicitação',
        stopRequest: 'Parar Solicitação',
        requestCrossComponent: 'Acionar solicitação entre componentes',
        triggerAllRequest: 'Acionar manualmente todas as solicitações automáticas'
      }
    },
    manage: {
      common: {
        status: {
          enable: 'Habilitar',
          disable: 'Desabilitar'
        }
      },
      role: {
        title: 'Lista de Funções',
        roleName: 'Nome da Função',
        roleCode: 'Código da Função',
        roleStatus: 'Status da Função',
        roleDesc: 'Descrição da Função',
        menuAuth: 'Autorização de Menu',
        buttonAuth: 'Autorização de Botão',
        form: {
          roleName: 'Digite o nome da função',
          roleCode: 'Digite o código da função',
          roleStatus: 'Selecione o status da função',
          roleDesc: 'Digite a descrição da função'
        },
        addRole: 'Adicionar Função',
        editRole: 'Editar Função'
      },
      user: {
        title: 'Lista de Usuários',
        userName: 'Nome de Usuário',
        userGender: 'Gênero',
        nickName: 'Apelido',
        userPhone: 'Telefone',
        userEmail: 'Email',
        userStatus: 'Status do Usuário',
        userRole: 'Função do Usuário',
        form: {
          userName: 'Digite o nome de usuário',
          userGender: 'Selecione o gênero',
          nickName: 'Digite o apelido',
          userPhone: 'Digite o telefone',
          userEmail: 'Digite o email',
          userStatus: 'Selecione o status do usuário',
          userRole: 'Selecione a função do usuário'
        },
        addUser: 'Adicionar Usuário',
        editUser: 'Editar Usuário',
        gender: {
          male: 'Masculino',
          female: 'Feminino'
        }
      },
      menu: {
        home: 'Início',
        title: 'Lista de Menus',
        id: 'ID',
        parentId: 'ID do Menu Pai',
        menuType: 'Tipo de Menu',
        menuName: 'Nome do Menu',
        routeName: 'Nome da Rota',
        routePath: 'Caminho da Rota',
        pathParam: 'Parâmetro do Caminho',
        layout: 'Layout',
        page: 'Componente da Página',
        i18nKey: 'Chave de Internacionalização',
        icon: 'Ícone',
        localIcon: 'Ícone Local',
        iconTypeTitle: 'Tipo de Ícone',
        order: 'Ordem',
        constant: 'Rota Constante',
        keepAlive: 'Cache de Rota',
        href: 'Link Externo',
        hideInMenu: 'Ocultar no Menu',
        activeMenu: 'Menu Ativo',
        multiTab: 'Suporte a Múltiplas Abas',
        fixedIndexInTab: 'Índice Fixo na Aba',
        query: 'Parâmetro de Rota',
        button: 'Botão',
        buttonCode: 'Código do Botão',
        buttonDesc: 'Descrição do Botão',
        menuStatus: 'Status do Menu',
        form: {
          home: 'Selecione a página inicial',
          menuType: 'Selecione o tipo de menu',
          menuName: 'Digite o nome do menu',
          routeName: 'Digite o nome da rota',
          routePath: 'Digite o caminho da rota',
          pathParam: 'Digite o parâmetro do caminho',
          page: 'Selecione o componente da página',
          layout: 'Selecione o componente de layout',
          i18nKey: 'Digite a chave de internacionalização',
          icon: 'Digite o ícone',
          localIcon: 'Selecione o ícone local',
          order: 'Digite a ordem',
          keepAlive: 'Selecione se deve fazer cache da rota',
          href: 'Digite o link externo',
          hideInMenu: 'Selecione se deve ocultar no menu',
          activeMenu: 'Selecione o nome da rota do menu ativo',
          multiTab: 'Selecione se suporta múltiplas abas',
          fixedInTab: 'Selecione se deve fixar na aba',
          fixedIndexInTab: 'Digite o índice fixo na aba',
          queryKey: 'Digite a chave do parâmetro de rota',
          queryValue: 'Digite o valor do parâmetro de rota',
          button: 'Selecione se é um botão',
          buttonCode: 'Digite o código do botão',
          buttonDesc: 'Digite a descrição do botão',
          menuStatus: 'Selecione o status do menu'
        },
        addMenu: 'Adicionar Menu',
        editMenu: 'Editar Menu',
        addChildMenu: 'Adicionar Submenu',
        type: {
          directory: 'Diretório',
          menu: 'Menu'
        },
        iconType: {
          iconify: 'Ícone Iconify',
          local: 'Ícone Local'
        }
      }
    }
  },
  form: {
    required: 'Não pode estar vazio',
    userName: {
      required: 'Digite o nome de usuário',
      invalid: 'Formato de nome de usuário incorreto'
    },
    phone: {
      required: 'Digite o número de telefone',
      invalid: 'Formato de telefone incorreto'
    },
    pwd: {
      required: 'Digite a senha',
      invalid: 'Formato de senha incorreto, 6-18 caracteres, incluindo letras, números, sublinhados'
    },
    google_code: {
      required: 'Digite o código do Google',
      invalid: 'Formato do código do Google incorreto'
    },
    confirmPwd: {
      required: 'Digite a confirmação da senha',
      invalid: 'As senhas não coincidem'
    },
    code: {
      required: 'Digite o código de verificação',
      invalid: 'Formato do código de verificação incorreto'
    },
    email: {
      required: 'Digite o email',
      invalid: 'Formato de email incorreto'
    }
  },
  dropdown: {
    closeCurrent: 'Fechar',
    closeOther: 'Fechar Outros',
    closeLeft: 'Fechar à Esquerda',
    closeRight: 'Fechar à Direita',
    closeAll: 'Fechar Todos'
  },
  icon: {
    themeConfig: 'Configuração do Tema',
    themeSchema: 'Esquema do Tema',
    lang: 'Alternar Idioma',
    fullscreen: 'Tela Cheia',
    fullscreenExit: 'Sair da Tela Cheia',
    reload: 'Recarregar Página',
    collapse: 'Recolher Menu',
    expand: 'Expandir Menu',
    pin: 'Fixar',
    unpin: 'Desfixar'
  },
  datatable: {
    itemCount: 'Total de {total} itens'
  }
};

export default local;
