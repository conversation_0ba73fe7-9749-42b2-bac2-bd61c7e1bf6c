<script setup lang="tsx">
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON> } from "element-plus";
import {
  fetchGetRoleList,
  deleteRole,
  deleteAgentRole,
  fetchAgentGetRoleList,
} from "@/service/api";
import { useTable, useTableOperate } from "@/hooks/common/table";
import { $t } from "@/locales";
import { enableStatusRecord } from "@/constants/business";
import RoleOperateDrawer from "./modules/role-operate-drawer.vue";
import RoleSearch from "./modules/role-search.vue";
import RoleLogDrawer from "./modules/role-log-drawer.vue";
import { useAuth } from "@/hooks/business/auth";
import { localStg } from "@/utils/storage";
import { computed, ref, watch } from "vue";

const { hasAuth } = useAuth();
const isManage = ref(true);

watch(
  () => localStg.get("isManage"),
  (newValue) => {
    console.log("isManage", newValue);
    isManage.value = JSON.parse(newValue || "false");
  },
  { immediate: true },
);

const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams,
  resetSearchParams,
} = useTable({
  apiFn: isManage.value ? fetchGetRoleList : fetchAgentGetRoleList,
  apiParams: {
    page: 1,
    size: 20,
    status: undefined,
    role_name: undefined,
  },
  columns: () => [
    // { type: "selection", width: 48 },
    { prop: "index", label: "序号", width: 64 },
    { prop: "role_name", label: "角色名称", minWidth: 120 },
    { prop: "description", label: "角色描述", minWidth: 120 },
    {
      prop: "status",
      label: "角色状态",
      width: 100,
      formatter: (row) => {
        if (row.status === undefined) {
          return "";
        }

        const tagMap: Record<Api.Common.EnableStatus, UI.ThemeColor> = {
          1: "success",
          0: "error",
        };
        const label = ["禁用", "启用"][row.status];
        return <ElTag type={tagMap[row.status]}>{label}</ElTag>;
      },
    },
    {
      prop: "operate",
      label: $t("common.operate"),
      width: 200,
      align: "center",
      formatter: (row) => (
        <div class="flex-center">
          {hasAuth(3) && (
            <ElButton
              type="primary"
              plain
              size="small"
              onClick={() => edit(row.id)}
            >
              {$t("common.edit")}
            </ElButton>
          )}
          {hasAuth(2) && (
            <ElPopconfirm
              title={$t("common.confirmDelete")}
              onConfirm={() => handleDelete(row.id)}
            >
              {{
                reference: () => (
                  <ElButton type="danger" plain size="small">
                    {$t("common.delete")}
                  </ElButton>
                ),
              }}
            </ElPopconfirm>
          )}
          {hasAuth(3) && (
            <ElButton
              type="default"
              plain
              size="small"
              onClick={() => handleViewLog(row.role_id)}
            >
              日志
            </ElButton>
          )}
        </div>
      ),
    },
  ],
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  onBatchDeleted,
  onDeleted,
} = useTableOperate(data, getData);

const logDrawerVisible = ref(false);
const currentRoleId = ref<number>();

function handleViewLog(id: number) {
  currentRoleId.value = id;
  logDrawerVisible.value = true;
}

async function handleBatchDelete() {
  // eslint-disable-next-line no-console
  console.log(checkedRowKeys.value);
  // request

  onBatchDeleted();
}

function handleDelete(id: number) {
  if (isManage.value) {
    deleteRole({ id: id }).then((res) => {
      if (res.response.data.status_code === 200) {
        onDeleted();
      }
    });
  } else {
    deleteAgentRole({ id: id }).then((res) => {
      if (res.response.data.status_code === 200) {
        onDeleted();
      }
    });
  }
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <RoleSearch
      v-model:model="searchParams"
      @reset="resetSearchParams"
      @search="getDataByPage"
    >
      <template #table-operation>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          isNoDelete
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
    </RoleSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="checkedRowKeys = $event"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
        <div class="mt-20px flex justify-start">
          <ElPagination
            v-if="mobilePagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="mobilePagination"
            @current-change="mobilePagination['current-change']"
            @size-change="mobilePagination['size-change']"
          />
        </div>
      </div>
      <RoleOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
      <RoleLogDrawer
        v-model:visible="logDrawerVisible"
        :role-id="currentRoleId"
      />
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border: none;
  border-radius: 0 0 4px 4px;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
