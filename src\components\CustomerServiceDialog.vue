<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 15:09:52
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-27 11:29:14
 * @FilePath: \betdoce-web\src\components\CustomerServiceDialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="customer-service-dropdown" v-show="modelValue">
    <!-- 头部 -->
    <div class="dropdown-header d-flex justify-space-between">
      <div>
        <div class="header-content">
          <div class="title-text">Atendimento Ao cliente</div>
        </div>
        <!-- 服务类型 -->
        <div class="service-type">
         <img
            v-show="invitation_link"
            src="@/assets/images/Telegram.png"
            alt="Service"
            class="service-icon"
            @click="goToHumanService(invitation_link)"
          />
          <img
            v-show="direct_link"
            src="@/assets/images/WhatsApp.png"
            alt="Service"
            class="service-icon"
            @click="goToHumanService(direct_link)"
          />
          <!-- <span>Serviço ao cliente humano</span> -->
        </div>
      </div>
      <div>
        <img
          src="@/assets/images/cliente-icon.png"
          alt="Service"
          class="cliente-icon"
        />
      </div>
      <v-icon
        icon="mdi-close"
        class="close-icon"
        @click="closeDropdown"
      ></v-icon>
    </div>

    <!-- 合并消息和问题列表 -->
    <div class="chat-container" ref="chatContainer">
      <div class="hint-text">Acho que você quer perguntar</div>
      <div class="messages-list">
        <!-- 初始问题列表 -->
        <div class="questions-list">
          <div
            v-for="(question, index) in commonQuestions"
            :key="'q' + index"
            class="question-item"
            :class="{ selected: selectedQuestion === question.keyword }"
            @click="selectQuestion(question)"
          >
            {{ question.keyword }}
          </div>
        </div>
        <!-- 消息列表 -->
        <div
          v-for="(message, index) in messages"
          :key="'m' + index"
          :class="['message-item', message.type]"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <template v-if="message.type === 'received'">
            <img src="@/assets/images/cliente-icon.png" class="avatar" />
            <div class="message-content">
              <div class="message-text">{{ message.content }}</div>
              <div class="message-time">{{ message.time }}</div>
            </div>
          </template>
          <template v-else>
            <div class="message-content">
              <div class="message-text-right">{{ message.content }}</div>
              <div class="message-time">{{ message.time }}</div>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 输入框 -->
    <div class="input-container">
      <input
        v-model="inputMessage"
        type="text"
        class="message-input"
        placeholder="Por favor insira"
        @keyup.enter="sendMessage"
      />
      <v-btn
        color="#FF206E"
        class="send-button"
        @click="sendMessage"
        :disabled="!inputMessage.trim()"
      >
        Enviar
      </v-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onMounted } from "vue";
import {
  getCustomerQuestions,
  customerServiceGroups,
  sendChatMessage,
  getWelcomeResponses,
  whatsappLink,
} from "@/api/customer-service";
import type { CustomerQuestion } from "@/types/customer-service";
import { showWarning } from "@/utils/toast";

const props = defineProps<{
  modelValue: boolean;
}>();

const emit = defineEmits(["update:modelValue", "select-service"]);

const inputMessage = ref("");
const selectedQuestion = ref("");
const commonQuestions = ref<CustomerQuestion[]>([]);
const loading = ref(false);
const linkLoading = ref(false);

// 获取常见问题列表
const fetchQuestions = async () => {
  try {
    loading.value = true;
    const response = await getCustomerQuestions();
    if (response?.data) {
      commonQuestions.value = response.data;
    }
  } catch (error) {
    console.error("获取问题列表失败:", error);
  } finally {
    loading.value = false;
  }
};

const invitation_link = ref();
const direct_link = ref();

// 获取Telegram群组链接
const fetchLink = async () => {
  try {
    linkLoading.value = true;
    const response = await customerServiceGroups();
    console.log(response)
    if (response) {
      invitation_link.value = response[0].invitation_link;
    }
  } catch (error) {
    console.error("获取人工客服链接失败:", error);
  } finally {
    linkLoading.value = false;
  }
};

// 获取WhatsApp链接
const fetchWhatsAppLink = async () => {
  try {
    linkLoading.value = true;
    const response = await whatsappLink();
    if (response?.direct_link) {
      direct_link.value = response?.direct_link;
    }
  } catch (error) {
    console.error("获取WhatsApp链接失败:", error);
  } finally {
    linkLoading.value = false;
  }
};

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      // 弹窗打开时获取问题列表
      fetchQuestions();
      // 获取欢迎语
      getWelcomeResponsesMsg();
      fetchLink()
      fetchWhatsAppLink()
    }
  }
);

const getWelcomeResponsesMsg = () => {
  getWelcomeResponses().then((res) => {
    if(messages.value[0].content !==  res[1]){
// 添加欢迎语
    messages.value.unshift({
      content: res[1],
      type: "received",
      time: "",
    });
    }
    
  });
};
// 组件挂载时获取常见问题
onMounted(() => {
  if (props.modelValue) {
    fetchQuestions();
    getWelcomeResponsesMsg();
  }
});

interface Message {
  content: string;
  type: "sent" | "received";
  time: string;
}

const messages = ref<Message[]>([
  {
    content: "Olá! Como posso ajudar você hoje?",
    type: "received",
    time: "",
  },
]);

const showQuestions = ref(true);

const chatContainer = ref<HTMLElement | null>(null);

const closeDropdown = () => {
  emit("update:modelValue", false);
};

const selectQuestion = (question: CustomerQuestion) => {
  selectedQuestion.value = question.keyword;
  inputMessage.value = question.keyword;
  showQuestions.value = false;

  // 添加用户发送的问题
  messages.value.push({
    content: question.keyword,
    type: "sent",
    time: new Date().toLocaleTimeString("pt-BR", {
      hour: "2-digit",
      minute: "2-digit",
    }),
  });

  // 添加客服回复的答案
  setTimeout(() => {
    messages.value.push({
      content: question.response,
      type: "received",
      time: new Date().toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit",
      }),
    });
    scrollToBottom();
  }, 500);
};

// 滚动到底部的函数
const scrollToBottom = async () => {
  await nextTick();
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
  }
};

// 监听消息列表变化
watch(
  () => messages.value.length,
  () => {
    scrollToBottom();
  }
);

const sendMessage = async () => {
  if (!inputMessage.value.trim()) return;

  showQuestions.value = false; // 发送消息后隐藏问题列表

  // 添加用户发送的消息
  messages.value.push({
    content: inputMessage.value,
    type: "sent",
    time: new Date().toLocaleTimeString("pt-BR", {
      hour: "2-digit",
      minute: "2-digit",
    }),
  });

  // 清空输入
  const message = inputMessage.value;
  inputMessage.value = "";

  try {
    // 发送消息到服务器
    const response = await sendChatMessage({ message });
    if (response) {
      // 添加客服回复
      messages.value.push({
        content: response.reply,
        type: "received",
        time: response.timestamp,
      });
    }
  } catch (error) {
    //添加错误提示
    messages.value.push({
      content:
        "Desculpe, ocorreu um erro ao processar sua mensagem. Por favor, tente novamente.",
      type: "received",
      time: new Date().toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit",
      }),
    });
  }

  scrollToBottom();
};

// 跳转到人工客服
const goToHumanService = async (link) => {
  try {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    if (isIOS) {
      // iOS需要用户交互触发window.open
      setTimeout(() => {
        window.location.href = link;
      }, 100);
    } else {
      window.open(link, "_self");
    }
  } catch (error) {
    console.error("获取人工客服链接失败:", error);
  }
};
</script>

<style lang="scss" scoped>
.customer-service-dropdown {
  position: fixed;
  top: 70px;
  right: 80px;
  width: 360px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  z-index: 999;
  animation: slideDown 0.3s ease;
  height: 600px;

  &::before {
    content: "";
    position: absolute;
    top: -8px;
    right: 128px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #ff206e;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-header {
  background: #ff206e;
  padding: 32px 8px 8px;
  display: flex;
  border-radius: 12px 12px 0 0;
  .header-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .title-text {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
  }

  .close-icon {
    color: #ff206e;
    cursor: pointer;
    font-size: 20px;
    position: absolute;
    right: 4px;
    top: 4px;
    background: #fff;
    border-radius: 50%;
  }
  .cliente-icon {
    width: 64px;
  }
}

.service-type {
  // margin-top: 4px;
  // box-shadow: 0px 5px 13px 1px rgba(103, 109, 111, 0.51);
  border-radius: 20px;
  // background: #fff;
  padding: 4px 8px;
  display: flex;
  cursor: pointer;
  align-items: center;
  gap: 8px;
  color: #ff206e;
  font-family: Helvetica LT Condensed;
  font-weight: bold;
  font-size: 14px;
  color: #fc3f7b;
  .service-icon {
    width: 40px;
    background-color: #fff;
    border-radius: 50%;
  }
}

.chat-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 8px;
  margin: 8px;
  border-radius: 8px;
  scroll-behavior: smooth; // 添加平滑滚动效果
  display: flex;
  flex-direction: column;
}

.hint-text {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.questions-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.question-item {
  padding: 4px 8px;
  border-radius: 6px;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 1px solid #e9e9e9;
  &:hover {
    background: #eee;
  }
  &::before {
    content: "";
    display: block;
    width: 100%;
    width: 2px;
    height: 2px;
    background: #333;
  }

  &.selected {
    background: #ff206e;
    color: #fff;
  }
}

.input-container {
  padding: 12px 16px;
  border-top: 1px solid #eee;
  position: relative;
  .message-input {
    width: 100%;
    height: 40px;
    padding: 0 12px;
    border: 1px solid #eee;
    border-radius: 20px;
    font-size: 14px;
    outline: none;
    color: #333;
    transition: border-color 0.2s;

    &:focus {
      border-color: #ff206e;
    }

    &::placeholder {
      color: #999;
    }
  }
  .send-button {
    position: absolute;
    right: 17px;
    top: 13px;
    border-radius: 20px;
    height: 38px;
  }
}

.message-item {
  display: flex;
  gap: 8px;
  max-width: 80%;
  animation: messageAppear 0.3s ease forwards;
  opacity: 0;
  transform: translateY(10px);

  &.received {
    align-self: flex-start;

    .message-content {
      background: #fff;
      border-radius: 0 12px 12px 12px;
    }

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }
  }

  &.sent {
    align-self: flex-end;
    flex-direction: row-reverse;

    .message-content {
      background: #ff206e;
      border-radius: 12px 0 12px 12px;
      color: #fff;
    }

    .message-time {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}
.message-content {
  padding: 8px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .message-text {
    font-size: 14px;
    line-height: 1.4;
    color: #333;
  }
  .message-text-right {
    font-size: 14px;
    line-height: 1.4;
    color: #fff;
  }

  .message-time {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
  }
}

@keyframes messageAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.human-service-container {
  padding: 12px 16px;
  border-top: 1px solid #eee;
  text-align: center;

  .human-service-button {
    width: 100%;
    height: 40px;
    border-radius: 20px;
  }
}
</style>
