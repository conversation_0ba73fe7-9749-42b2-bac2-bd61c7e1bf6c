# 构建阶段
FROM node:20-alpine as builder

WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm

# 验证安装
RUN pnpm --version

COPY . .

RUN pnpm install

RUN pnpm build

# 生产阶段
FROM nginx:1.28.0-alpine

# 移除 Nginx 默认的 HTML 文件
RUN rm -rf /usr/share/nginx/html/*
# 从第一阶段复制打包好的静态文件到 /usr/share/nginx/html
COPY --from=builder /app/dist/ /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 82

CMD ["nginx", "-g", "daemon off;"]

#
## 复制配置文件
#COPY package.json pnpm-workspace.yaml ./
#COPY .npmrc ./
#
## 调试: 显示源目录结构
#RUN echo "=== Current directory structure ===" && \
#    pwd && \
#    ls -la
#
## 复制 packages 目录并显示结构
#COPY packages ./packages
#RUN echo "=== Packages directory structure ===" && \
#    ls -la packages && \
#    echo "=== Package.json files ===" && \
#    find packages -name "package.json" -type f && \
#    echo "=== Workspace config ===" && \
#    cat pnpm-workspace.yaml
#
## 安装依赖（跳过 prepare 脚本）
#RUN --mount=type=cache,target=/root/.local/share/pnpm/store \
#    HUSKY=0 pnpm install --no-frozen-lockfile --ignore-scripts
#
## 复制所有源代码
#COPY . .
#
## 构建应用
#RUN  HUSKY=0 pnpm install --no-frozen-lockfile --ignore-scripts
#
#RUN pnpm build
#
