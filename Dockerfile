# 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm install --legacy-peer-deps

# 创建样式目录
RUN mkdir -p src/styles

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 创建项目目录
RUN mkdir -p /project

# 复制构建产物到项目目录
COPY --from=builder /app/dist /project

# 设置正确的权限
RUN chown -R nginx:nginx /project && \
    chmod -R 755 /project

# 复制Nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 81

CMD ["nginx", "-g", "daemon off;"]
