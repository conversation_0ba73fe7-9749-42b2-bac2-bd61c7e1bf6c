<template>
  <div class="header-wrapper">
    <div class="header-feiji">
      <img src="@/assets/images/feiji-tp.png" alt="Feiji" class="feiji-image" />
    </div>
    <v-app-bar elevation="2" class="header-bar">
      <!-- 左侧 -->
      <div class="header-left">
        <img
          src="@/assets/images/unfold-icon.png"
          class="toggle-icon"
          @click="$emit('toggle-drawer')"
        />
        <v-toolbar-title class="d-flex align-center">
          <img
            :src="bottomLogoImg"
            alt="Logo"
            height="30"
            class="logo-image"
            @click="router.push('/')"
          />
        </v-toolbar-title>
      </div>

      <!-- 中间导航 -->
      <div class="header-center">
        <v-btn
          v-for="(item, index) in navItems"
          :key="item.to"
          variant="text"
          :ripple="false"
          :class="[
            {
              'nav-btn': isActive(item.to),
            },
            {
              [item.color]: !isActive(item.to),
            },
          ]"
          @click="handleRoute(item.to)"
        >
          {{ item.label }}
        </v-btn>
      </div>

      <!-- 右侧 -->
      <div class="header-right">
        <template v-if="!isLoggedIn">
          <v-btn
            rounded="xl"
            variant="flat"
            @click="handleAuthAction"
            class="auth-btn"
          >
            Entrar
          </v-btn>
        </template>
        <template v-else>
          <div class="d-none d-md-flex align-center">
            <div variant="text" class="balance-btn">
              <div class="balance-wrapper" @click="handleUpdateUserWallet">
                <div class="balance-wrapper">
                  <img
                    src="@/assets/images/purse-icon.png"
                    alt="Coin"
                    class="coin-icon"
                  />
                  <img
                    src="@/assets/images/refresh.png"
                    alt="refresh"
                    class="refresh-icon"
                    :class="{ 'rotate-animation': isRotating }"
                  />
                </div>

                <span class="balance-amount">
                  {{
                    formatNumber(
                      ((userInfo?.userWallet?.total_balance || 0) /
                        100) as number
                    )
                  }}</span
                >
                <img
                  src="@/assets/images/top-up-icon.png"
                  class="top-up-btn"
                  @click="router.push('/records')"
                />
              </div>
            </div>
            <div class="msg-customer">
              <v-badge
                color="error"
                :content="unreadMessages"
                :model-value="unreadMessages > 0"
              >
                <img
                  src="@/assets/images/notify.png"
                  alt="Message"
                  class="msg-icon"
                  @click="router.push('/messages')"
                />
                <!-- <v-icon
                  icon="mdi-message-processing"
                  size="40"
                  @click="router.push('/messages')"
                ></v-icon> -->
              </v-badge>
              <img
                src="@/assets/images/customer.png"
                alt="Customer Service"
                class="customer-icon"
                @click="showCustomerDialog = true"
              />
            </div>
            <div class="user-btn" @click="handleUserInfoClick">
              <div class="user-info">
                <div class="user-name">
                  {{ userInfo?.user?.nickname || "usuário" }}
                </div>
                <div class="user-id">
                  ID: {{ userInfo?.user?.uuid || "--" }}
                </div>
              </div>
              <div class="user-avatar">
                <v-avatar :image="userInfo?.user?.avatar || userTx" size="34">
                </v-avatar>
                <v-icon class="expand-icon" size="20">mdi-chevron-down</v-icon>
              </div>
            </div>
          </div>

          <!-- 移动端显示头像按钮 -->
          <v-btn
            icon
            variant="text"
            class="d-md-none mobile-user-btn"
            @click="handleUserInfoClick"
          >
            <div class="user-avatar">
              <img
                :src="userInfo?.user?.avatar || userTx"
                alt="Avatar"
                class="avatar-image"
              />
              <div class="online-status"></div>
            </div>
          </v-btn>
        </template>
      </div>
    </v-app-bar>

    <!-- 登录弹窗 -->
    <LoginDialog v-model="showLoginDialog" @login="handleLoginSuccess" />

    <!-- 用户信息抽屉 -->
    <UserDrawer v-model="showUserDrawer" @logout="handleLogout" />

    <!-- 客服弹窗 -->
    <CustomerServiceDialog
      v-model="showCustomerDialog"
      @select-service="handleServiceSelect"
    />
    <CommonDialog
      :show="commonDialogShow"
      @update:show="commonDialogShow = $event"
      :dialogObj="dialogObj"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useStore } from "vuex";
import LoginDialog from "@/components/LoginDialog.vue";
import UserDrawer from "@/components/UserDrawer.vue";
import CustomerServiceDialog from "@/components/CustomerServiceDialog.vue";
import userTx from "@/assets/images/tx-icon.png";
import CommonDialog from "@/components/CommonDialog.vue";
import { loginPopup } from "@/api/activity";
import { formatNumber } from "@/utils/index";
import bottomLogoImg from "@/assets/images/h5/bottom-logo.png";

const router = useRouter();
const route = useRoute();
const store = useStore();

// 登录活动弹框
const commonDialogShow = ref(false);
const dialogObj = ref();

const isRotating = ref(false);

const getLoginPopup = () => {
  loginPopup().then((res) => {
    console.log(res);
    if (res.length) {
      commonDialogShow.value = true;
      dialogObj.value = res[0];
    }
  });
};

// 计算属性：判断是否登录
const isLoggedIn = ref(false);

// 计算属性：获取用户信息
interface UserWallet {
  total_balance: number;
}

interface User {
  nickname: string;
  uuid: string;
  avatar: string;
}

interface UserInfo {
  user: User | null;
  userWallet: UserWallet | null;
  unreadNum: number;
}

const userInfo = ref<UserInfo>({
  user: null,
  userWallet: null,
  unreadNum: 0,
});

const showLoginDialog = ref(false);
const showUserDrawer = ref(false);
const unreadMessages = ref(0); // 初始化为0

const loginForm = reactive({
  username: "",
  password: "",
});

// 客服弹窗
const showCustomerDialog = ref(false);

// 监听用户信息变化
watch(
  () => store.state.auth,
  (newVal) => {
    // console.log("store.state.auth",newVal)
    isRotating.value = false;
    if (newVal.user) {
      isLoggedIn.value = true;
      userInfo.value = {
        user: newVal.user,
        userWallet: newVal.userWallet,
        unreadNum: newVal.unreadNum,
      };
      unreadMessages.value = newVal.unreadNum || 0;
      isRotating.value = true;
    } else {
      isLoggedIn.value = false;
      userInfo.value = {
        user: null,
        userWallet: null,
        unreadNum: 0,
      };
      unreadMessages.value = 0;
    }
  },
  { immediate: true, deep: true }
);

const navItems = [
  {
    label: "Retirar",
    to: "/withdraw",
    color: "activewithdraw",
  },
  {
    label: "Depósito",
    to: "/records",
    color: "activerecords",
  },
  {
    label: "Registro de saldo",
    to: "/deposit",
    color: "activedeposit",
  },
  {
    label: "Meu bonus",
    to: "/bonus",
    color: "activebonus",
  },
];

// 检查路由是否激活
const isActive = (path: string) => {
  return route.path === path;
};
const handleRoute = (to: string) => {
  console.log(to, 2134214);
  router.push(to);
};
// 处理登录/退出动作
const handleAuthAction = () => {
  if (isLoggedIn.value) {
    // 处理退出逻辑
    handleLogout();
  } else {
    // 显示登录对话框
    showLoginDialog.value = true;
  }
};

// 处理退出
const handleLogout = async () => {
  try {
    showUserDrawer.value = false;
    await store.dispatch("auth/logout");
    router.push("/");
  } catch (error) {
    console.error("Logout failed:", error);
  }
};

// 处理登录成功
const handleLoginSuccess = () => {
  showLoginDialog.value = false;
  getLoginPopup();
};

// 处理用户余额点击
const handleUpdateUserWallet = async () => {
  if (isLoggedIn.value) {
    isRotating.value = true;
    await store.dispatch("auth/fetchUserInfo");
    setTimeout(() => {
      isRotating.value = false;
    }, 1000);
  }
};
// 处理用户信息点击
const handleUserInfoClick = async () => {
  if (isLoggedIn.value) {
    // 打开抽屉前刷新用户信息
    isRotating.value = true;
    await store.dispatch("auth/fetchUserInfo");
    showUserDrawer.value = true;
    setTimeout(() => {
      isRotating.value = false;
    }, 1000);
  }
};

// 处理客服选择
const handleServiceSelect = (type: "human" | "robot") => {
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
  function openFun(Link: string) {
    if (isIOS) {
      // iOS需要用户交互触发window.open
      setTimeout(() => {
        window.location.href = Link;
      }, 100);
    } else {
      window.open(Link, "_self");
    }
  }

  if (type === "human") {
    openFun("https://t.me/betdoce");
  } else {
    // 处理机器人客服逻辑
    openFun("https://t.me/betdoce_bot");
  }
};

const formatBalance = (balance: number | undefined) => {
  if (!balance) return "0.00";

  const amount = balance / 100; // 转换为实际金额

  if (amount >= 1000000000) {
    // 十亿及以上
    return `R$ ${(amount / 1000000000).toFixed(2)}B`;
  } else if (amount >= 1000000) {
    // 百万及以上
    return `R$ ${(amount / 1000000).toFixed(2)}M`;
  } else if (amount >= 1000) {
    // 千及以上
    return `R$ ${(amount / 1000).toFixed(1)}K`;
  } else {
    return `R$ ${amount.toFixed(2)}`;
  }
};

defineEmits(["toggle-drawer"]);
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables.scss";

.header-wrapper {
  position: relative;
  width: 100%;
  background: #002664;
}

.header-feiji {
  position: fixed;
  top: 60px;
  left: 240px;
  z-index: 99999;
  transform-origin: center;
  animation: float 6s ease-in-out infinite;
  transition: all 0.3s ease;
  filter: drop-shadow(2px 4px 8px rgba(255, 166, 0, 0.2));

  &::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(
      circle,
      rgba(255, 166, 0, 0.2) 0%,
      rgba(255, 166, 0, 0) 70%
    );
    transform: translateY(20px) scale(0.8);
    opacity: 0.5;
    animation: shadow 6s ease-in-out infinite;
  }

  .feiji-image {
    width: 80px;
    height: auto;
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    will-change: transform;

    &:hover {
      transform: scale(1.15) rotate(-8deg) translateY(-5px);
      filter: brightness(1.2) drop-shadow(0 0 20px rgba(255, 166, 0, 0.6));
      animation-play-state: paused;
    }
  }
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  25% {
    transform: translateY(-12px) rotate(2deg) translateX(5px);
  }
  50% {
    transform: translateY(-20px) rotate(-1deg) translateX(-3px);
  }
  75% {
    transform: translateY(-8px) rotate(1deg) translateX(2px);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

@keyframes shadow {
  0% {
    transform: translateY(20px) scale(0.8);
    opacity: 0.5;
  }
  25% {
    transform: translateY(25px) scale(0.75);
    opacity: 0.4;
  }
  50% {
    transform: translateY(30px) scale(0.7);
    opacity: 0.3;
  }
  75% {
    transform: translateY(25px) scale(0.75);
    opacity: 0.4;
  }
  100% {
    transform: translateY(20px) scale(0.8);
    opacity: 0.5;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .header-feiji {
    left: 20px;
    top: 20px;

    .feiji-image {
      width: 60px;
    }

    &::before {
      transform: translateY(15px) scale(0.6);
    }
  }
}

// 当侧边栏收起时的位置调整
:deep(.v-navigation-drawer--rail) ~ .header-feiji {
  left: 80px;
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes gradient {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.header-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 998;

  :deep(.v-toolbar__content) {
    background: #002664 !important;
    background-size: cover !important;
    display: flex;
    justify-content: space-between;

    // display: grid !important;
    // grid-template-columns: 200px 1fr 200px !important;
    padding: 0 16px !important;
  }
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  .toggle-icon {
    width: 22px;
    cursor: pointer;
  }
}

.header-center {
  display: flex !important;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 0 16px;
  // margin-left: 300px;
  .nav-btn {
    // color: #fff !important;
    border-radius: 20px;
    text-transform: none;
    letter-spacing: 0.3px;
    font-family: Inter, Inter;
    font-weight: 600;
    font-size: 18px;
    text-align: left;
    font-style: normal;
    height: 36px;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25),
      inset 0px -2px 2px 0px rgba(0, 0, 0, 0.25);
    background: linear-gradient(90deg, #ffea30 0%, #6db60e 50%, #ffea30 100%);
    &:hover {
      background: linear-gradient(90deg, #ffea30 0%, #6db60e 50%, #ffea30 100%);
      opacity: 1;
    }
  }
  .activewithdraw {
    opacity: 1;
    background: linear-gradient(180deg, #048446 0%, #1774bc 100%);
    border-radius: 20px;
    text-transform: none;
    letter-spacing: 0.3px;
    font-family: Inter, Inter;
    font-weight: 600;
    font-size: 18px;
    text-align: left;
    font-style: normal;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25),
      inset 0px -2px 2px 0px rgba(0, 0, 0, 0.25);
    &::after {
      width: 80% !important;
    }
    &::before {
      width: 40px !important;
    }
    &:hover {
      background: linear-gradient(90deg, #ffea30 0%, #6db60e 50%, #ffea30 100%);
      opacity: 1;
    }
  }
  .activerecords {
    opacity: 1;
    background: linear-gradient(180deg, #048446 0%, #1774bc 100%);
    border-radius: 20px;
    text-transform: none;
    letter-spacing: 0.3px;
    font-family: Inter, Inter;
    font-weight: 600;
    font-size: 18px;
    text-align: left;
    font-style: normal;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25),
      inset 0px -2px 2px 0px rgba(0, 0, 0, 0.25);
    &::after {
      width: 80% !important;
    }
    &::before {
      width: 40px !important;
    }
    &:hover {
      background: linear-gradient(90deg, #ffea30 0%, #6db60e 50%, #ffea30 100%);
      opacity: 1;
    }
  }
  .activedeposit {
    opacity: 1;
    background: linear-gradient(180deg, #048446 0%, #1774bc 100%);
    border-radius: 20px;
    text-transform: none;
    letter-spacing: 0.3px;
    font-family: Inter, Inter;
    font-weight: 600;
    font-size: 18px;
    text-align: left;
    font-style: normal;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25),
      inset 0px -2px 2px 0px rgba(0, 0, 0, 0.25);
    &::after {
      width: 80% !important;
    }
    &::before {
      width: 40px !important;
    }
    &:hover {
      background: linear-gradient(90deg, #ffea30 0%, #6db60e 50%, #ffea30 100%);
      opacity: 1;
    }
  }
  .activebonus {
    opacity: 1;
    background: linear-gradient(180deg, #048446 0%, #1774bc 100%);
    border-radius: 20px;
    text-transform: none;
    letter-spacing: 0.3px;
    font-family: Inter, Inter;
    font-weight: 600;
    font-size: 18px;
    text-align: left;
    font-style: normal;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25),
      inset 0px -2px 2px 0px rgba(0, 0, 0, 0.25);
    &::after {
      width: 80% !important;
    }
    &::before {
      width: 40px !important;
    }
    &:hover {
      background: linear-gradient(90deg, #ffea30 0%, #6db60e 50%, #ffea30 100%);
      opacity: 1;
    }
  }
}

.header-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.logo-image {
  margin-top: 8px;
  height: 38px;
  width: auto;
  object-fit: contain;
  cursor: pointer;
}

// 按钮样式
:deep(.v-btn) {
  text-transform: none;
  font-weight: 500;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

// 余额显示
:deep(.v-chip) {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-weight: 500;
}

// ID 文本
.text-caption {
  color: rgba(255, 255, 255, 0.7);
  padding-right: 15px;
}

@media (max-width: 768px) {
  .header-bar {
    :deep(.v-toolbar__content) {
      grid-template-columns: auto 1fr auto !important;
    }
  }

  .header-center {
    display: none !important;
  }

  .logo-image {
    height: 40px;
  }
}

.login-dialog {
  background: rgba(30, 30, 30, 0.95) !important;

  :deep(.v-card-title) {
    color: #fff;
    font-size: 1.25rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .v-text-field {
    :deep() {
      .v-field__outline {
        --v-field-border-opacity: 0.2;
      }
    }
  }

  .v-text-field--focused {
    :deep() {
      .v-field__outline {
        --v-field-border-opacity: 0.5;
      }
    }
  }

  :deep(.v-btn) {
    text-transform: none;
    letter-spacing: 0.5px;
  }
}
.auth-btn {
  background: linear-gradient(90deg, #c9b737 0%, #2abb27 100%);
  color: #fff;
  border-radius: 6px !important;
  height: 30px !important;
  transition: 0.3s;
  &:hover {
    background: #c9b737;
  }
}
// 移动端适配
@media (max-width: 768px) {
  .auth-btn {
    :deep() {
      min-width: 100px !important;
      .btn-text {
        font-size: 14px !important;
      }
    }
  }
}

.balance-btn {
  background: #333948;
  border-radius: 20px;
  min-height: 34px;
  margin-right: 22px;
  width: auto;
  min-width: 150px;

  .balance-wrapper {
    width: auto;
    display: flex;
    min-height: 34px;
    align-items: center;
    justify-content: space-between;
    // padding-left: 5px;
    gap: 4px;
    position: relative;
    cursor: pointer;
    .coin-icon {
      width: 33px;
      height: 33px;
    }
    .refresh-icon {
      width: 14px;
      height: 14px;
      transition: transform 0.5s ease;
    }
    .balance-amount {
      color: #fff;
      font-size: 14px;
      font-weight: 500;
      word-break: break-all;
    }
    .top-up-btn {
      width: 20px;
      height: 20px;
      margin-right: -10px;
      cursor: pointer;
    }
  }
}

.msg-customer {
  height: 34px;
  width: auto;
  display: flex;
  justify-content: center;
  margin-right: 12px;
  gap: 12px;

  .msg-icon,
  .customer-icon {
    height: 34px;
    cursor: pointer;
  }

  .msg-icon {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      filter: brightness(1.2);
    }

    &:active {
      transform: translateY(0);
    }
  }

  &:after {
    content: "";
    width: 1px;
    height: 34px;
    background: #586995;
  }
}
.user-btn {
  background: transparent;
  height: 34px;
  padding: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  .user-info {
    text-align: right;

    .user-name {
      color: #fff;
      font-size: 14px;
      font-weight: 500;
      line-height: 1.2;
      width: 90px;
      text-align: left;
      overflow: hidden;
    }

    .user-id {
      width: 90px;
      text-align: left;
      color: #7e86c7;
      font-size: 12px;
      line-height: 1;
      margin-top: 2px;
    }
  }

  .user-avatar {
    display: flex;
    align-items: center;
    gap: 4px;

    .expand-icon {
      color: rgba(255, 255, 255, 0.5);
    }
  }
}

.mobile-user-btn {
  .user-avatar {
    width: 32px;
    height: 32px;

    .online-status {
      width: 8px;
      height: 8px;
    }
  }
}

.customer-icon {
  height: 34px;
  cursor: pointer;
  transition: transform 0.3s;

  &:hover {
    transform: scale(1.1);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rotate-animation {
  animation: rotate 1s linear;
}
</style>
