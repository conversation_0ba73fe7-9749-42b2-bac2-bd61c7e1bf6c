/**
 * Namespace Api
 *
 * All backend api type
 */
declare module '@/typings/api' {
  export type EnableStatus = '1' | '2';
}

declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = 1 | 2;

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: string;
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: string;
      /** record status */
      status: EnableStatus | undefined;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      token: string;
      admin:any
    }

    interface UserInfo {
      routes: string;
      buttons: any[];
      id: number;
      user_id: number;
      user_name: string;
      password: string;
      status: number;
      role_id: number;
      phone: string;
      gender: number;
      is_delete: number;
      parent_id: number;
      invitation_code: string;
      created_at: number;
      created_by: string;
      updated_at: number;
      updated_by: string;
      token: string;
      expires: string
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  /**
   * namespace SystemManage
   *
   * backend api module: "systemManage"
   */
  namespace SystemManage {
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /** role */
    type Role = Common.CommonRecord<{
      role_id: number | undefined;
      /** role name */
      role_name: string | undefined;
      /** role description */
      description: string | undefined;
      /** button ids */
      button_ids: string[] | undefined;
      /** route ids */
      route: string | undefined;
    }>;

    /** role search params */
    type RoleSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.Role, 'role_name' | 'status'> & CommonSearchParams
    >;
    /** role list */
    type RoleList = Common.PaginatingQueryRecord<Role>;
    /** all role */
    type AllRole = Pick<Role, 'role_id' | 'role_name'>;
    /**
     * user gender
     *
     * - "1": "男"
     * - "2": "女"
     */
    type UserGender = 0 | 1;

    /** user */
    type User = Common.CommonRecord<{
      /** user name */
      user_name: string;
      /** user gender */
      gender: UserGender | undefined;
      /** user phone */
      phone: string;
      /** user email */
      userEmail: string;
      /** user role code collection */
      role_id: number;
    }>;

    /** user search params */
    type UserSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.User, 'user_name' | 'gender' | 'phone' | 'status'> &
        CommonSearchParams
    >;

    /** user list */
    type UserList = Common.PaginatingQueryRecord<User>;

    /**
     * menu type
     *
     * - "1": directory
     * - "2": menu
     */
    type MenuType = '1' | '2';

    type MenuButton = {
      /**
       * button code
       *
       * it can be used to control the button permission
       */
      code: string;
      /** button description */
      desc: string;
    };

    /**
     * icon type
     *
     * - "1": iconify icon
     * - "2": local icon
     */
    type IconType = '1' | '2';

    type MenuPropsOfRoute = Pick<
      import('vue-router').RouteMeta,
      | 'i18nKey'
      | 'keepAlive'
      | 'constant'
      | 'order'
      | 'href'
      | 'hideInMenu'
      | 'activeMenu'
      | 'multiTab'
      | 'fixedIndexInTab'
      | 'query'
    >;

    type Menu = Common.CommonRecord<{
      /** parent menu id */
      parentId: number;
      /** menu type */
      menuType: MenuType;
      /** menu name */
      menuName: string;
      /** route name */
      routeName: string;
      /** route path */
      routePath: string;
      /** component */
      component?: string;
      /** iconify icon name or local icon name */
      icon: string;
      /** icon type */
      iconType: IconType;
      /** buttons */
      buttons?: MenuButton[] | null;
      /** children menu */
      children?: Menu[] | null;
    }> &
      MenuPropsOfRoute;

    /** menu list */
    type MenuList = Common.PaginatingQueryRecord<Menu>;

    type MenuTree = {
      id: number;
      label: string;
      pId: number;
      children?: MenuTree[];
    };

    interface FAQItem extends Common.CommonRecord {
      id: number;
      keyword: string;
      response: string;
    }

    type FAQList = Common.PaginatingQueryRecord<FAQItem>;

    interface FAQSearchParams extends Common.CommonSearchParams {
      keyword?: string;
      response?: string;
    }

    interface FAQAddParams {
      keyword: string;
      response: string;
    }

    interface FAQUpdateParams extends FAQAddParams {
      id: number;
    }
  }

  interface GameType {
    type_id: number;
    type_name: string;
  }
}
