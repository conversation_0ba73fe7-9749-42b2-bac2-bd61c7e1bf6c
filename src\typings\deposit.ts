export interface DepositRecord {
  id: number;
  user_id: number;
  wallet_id: number;
  amount: number;
  status: 'pending' | 'completed' | 'failed';
  transaction_no: string;
  payment_method: string;
  payment_channel_id: number;
  payment_time: number;
  completed_time: number;
  order_no: string;
  description: string;
  admin_id: number;
  created_at: number;
  updated_at: number;
  deleted_at: number;
  platform_order_id: string;
  cpf: string;
  end_to_end_id: string;
  bank_code: string;
  payer_name: string;
  crypto_currency: string;
  crypto_network: string;
  crypto_address: string;
  crypto_from_address: string;
  crypto_txid: string;
  crypto_exchange_rate: number;
  crypto_amount: number;
  crypto_confirmation_count: number;
  is_recharge: number;
}

export interface DepositListParams {
  page: number;
  size: number;
}

export interface DepositListResponse {
  list: DepositRecord[];
  total: number;
  page: number;
  size: number;
}
