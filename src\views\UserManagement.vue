<template>
  <div class="user-management">
    <h1>用户管理</h1>
    <div class="search-bar">
      <input 
        type="text" 
        v-model="searchQuery" 
        placeholder="搜索用户..."
        @input="handleSearch"
      >
    </div>
    <div class="user-list">
      <div v-if="loading" class="loading">
        加载中...
      </div>
      <div v-else-if="error" class="error">
        {{ error }}
      </div>
      <div v-else-if="users.length === 0" class="no-data">
        没有找到用户
      </div>
      <div v-else class="user-cards">
        <div v-for="user in users" :key="user.id" class="user-card">
          <div class="user-info">
            <h3>{{ user.name }}</h3>
            <p>邮箱: {{ user.email }}</p>
            <p>角色: {{ user.role }}</p>
            <p>状态: {{ user.status }}</p>
          </div>
          <div class="user-actions">
            <button @click="editUser(user)" class="edit-btn">编辑</button>
            <button @click="deleteUser(user.id)" class="delete-btn">删除</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserManagement',
  data() {
    return {
      users: [],
      loading: false,
      error: null,
      searchQuery: ''
    }
  },
  methods: {
    async fetchUsers() {
      this.loading = true
      this.error = null
      try {
        // 这里应该调用实际的API
        const response = await fetch('/api/users')
        const data = await response.json()
        this.users = data
      } catch (err) {
        this.error = '获取用户数据失败'
        console.error('Error fetching users:', err)
      } finally {
        this.loading = false
      }
    },
    handleSearch() {
      // 实现搜索逻辑
      console.log('搜索:', this.searchQuery)
    },
    editUser(user) {
      // 实现编辑用户逻辑
      console.log('编辑用户:', user)
    },
    async deleteUser(userId) {
      if (confirm('确定要删除这个用户吗？')) {
        try {
          // 这里应该调用实际的API
          await fetch(`/api/users/${userId}`, { method: 'DELETE' })
          this.users = this.users.filter(user => user.id !== userId)
        } catch (err) {
          console.error('删除用户失败:', err)
          alert('删除用户失败')
        }
      }
    }
  },
  mounted() {
    this.fetchUsers()
  }
}
</script>

<style scoped>
.user-management {
  padding: 20px;
}

h1 {
  color: #333;
  margin-bottom: 20px;
}

.search-bar {
  margin-bottom: 20px;
}

.search-bar input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.user-list {
  min-height: 200px;
}

.loading, .error, .no-data {
  text-align: center;
  padding: 20px;
  color: #666;
}

.error {
  color: #ff4444;
}

.user-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.user-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.user-info h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.user-info p {
  margin: 5px 0;
  color: #666;
}

.user-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.edit-btn {
  background-color: #4CAF50;
  color: white;
}

.edit-btn:hover {
  background-color: #45a049;
}

.delete-btn {
  background-color: #f44336;
  color: white;
}

.delete-btn:hover {
  background-color: #da190b;
}
</style> 