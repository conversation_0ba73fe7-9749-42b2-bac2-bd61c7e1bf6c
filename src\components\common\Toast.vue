<template>
  <v-snackbar v-model="toast.show" :color="toast.color" :timeout="toast.timeout" location="center">
    {{ toast.text }}

    <template v-slot:actions>
      <v-btn variant="text" @click="closeToast">
        Fechar
      </v-btn>
    </template>
  </v-snackbar>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { toast, closeToast } from '@/utils/toast'

const router = useRouter()

// 监听路由变化，关闭 Toast
const handleRouteChange = () => {
  if (toast.value.show) {
    closeToast()
  }
}

onMounted(() => {
  router.beforeEach((to, from, next) => {
    if (to.path !== from.path) {
      handleRouteChange()
    }
    next()
  })
})

onUnmounted(() => {
  // 清理工作
  closeToast()
})
</script>

<style scoped>
.toast-center {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999 !important;
}
</style>