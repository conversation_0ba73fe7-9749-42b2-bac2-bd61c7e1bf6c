<template>
  <div class="search-wrapper">
    <ElForm :model="props.model" @submit.prevent>
      <ElRow :gutter="16">
        <ElCol :span="8">
          <ElFormItem>
            <ElInput
              v-model="props.model.title"
              placeholder="输入弹窗标题搜索"
              clearable
              @keyup.enter="handleSearch"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
interface Props {
  model: Record<string, any>
}

const props = defineProps<Props>()
const emit = defineEmits(['update:model', 'search', 'reset'])

function handleSearch() {
  emit('search')
}

function handleReset() {
  emit('reset')
}
</script>

<style lang="scss" scoped>
.search-wrapper {
  background-color: #fff;
  padding: 16px;
  border-radius: 4px 4px 0 0;
  --un-shadow: var(--un-shadow-inset) 0 1px 2px 0 var(--un-shadow-color, rgb(0 0 0 / 0.05));
  box-shadow: var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);

  .header-operation {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  :deep(.el-form-item) {
    margin-bottom: 16px;
    width: 100%;

    .el-form-item__content {
      width: 100%;
    }

    .el-select,
    .el-input {
      width: 100%;
    }
  }

  :deep(.el-button + .el-button) {
    margin-left: 8px;
  }
}
</style>
