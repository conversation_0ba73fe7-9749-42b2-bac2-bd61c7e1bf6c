#!/bin/bash

docker build --no-cache -t lkeke/box777-web:test .

docker push lkeke/box777-web:test


cd /opt/docker-compose/box777-web && docker-compose down && docker-compose up -d
#ssh ubuntu@box-777-test "cd /opt/docker-compose/box777-web && docker-compose down"
#ssh ubuntu@box-777-test "cd /opt/docker-compose/box777-web && docker-compose pull"
#ssh ubuntu@box-777-test "cd /opt/docker-compose/box777-web && docker-compose up -d"
#ssh ubuntu@box-777-test "cd /opt/docker-compose/nginx && docker-compose down && docker-compose up -d"
