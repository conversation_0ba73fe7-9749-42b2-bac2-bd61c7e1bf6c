import {
  computed,
  effectScope,
  onScopeDispose,
  reactive,
  ref,
  watch,
} from "vue";
import type { Ref } from "vue";
import {
  ElMessage,
  type PaginationEmits,
  type PaginationProps,
} from "element-plus";
import { jsonClone } from "@sa/utils";
import { useBoolean, useHookTable } from "@sa/hooks";
import { useAppStore } from "@/store/modules/app";
import { $t } from "@/locales";

type RemoveReadonly<T> = {
  -readonly [key in keyof T]: T[key];
};

type TableData = UI.TableData;
type GetTableData<A extends UI.TableApiFn> = UI.GetTableData<A>;
type TableColumn<T> = UI.TableColumn<T>;

export type TableOperateType = "add" | "edit" | "view";

interface ApiResponse<T> {
  status_code: number;
  message: string;
  data: T[];
  count: number;
}

export function useTable<A extends UI.TableApiFn>(
  config: UI.NaiveTableConfig<A>,
) {
  const scope = effectScope();
  const appStore = useAppStore();

  const isMobile = computed(() => appStore.isMobile);

  const { apiFn, apiParams, immediate } = config;

  const SELECTION_KEY = "__selection__";

  const EXPAND_KEY = "__expand__";

  const INDEX_KEY = "__index__";

  const {
    loading,
    empty,
    data,
    columns,
    columnChecks,
    reloadColumns,
    getData,
    searchParams,
    updateSearchParams,
    resetSearchParams,
  } = useHookTable<
    A,
    GetTableData<A>,
    TableColumn<UI.TableDataWithIndex<GetTableData<A>>>
  >({
    apiFn,
    apiParams,
    columns: config.columns,
    transformer: (res: ApiResponse<any>) => {
      console.log("transformer res:", res);
      if (!res || typeof res !== "object") {
        console.error("Invalid response:", res);
        return {
          pageNum: 1,
          pageSize: 10,
          data: [],
          total: 0,
        };
      }

      let listData: any[] = [];
      let total = 0;
      // 处理不同的数据结构
      if (Array.isArray(res.data)) {
        listData = res.data;
        total = res.count || 0;
      } else if (res.data && typeof res.data === "object") {
        // 如果 res.data 是对象，尝试获取其中的列表数据
        if (res.data?.data && Array.isArray(res.data?.data)) {
          listData = res.data?.data;
          total = res.data.count || 0;
        } else if (
          res.data?.data?.result &&
          Array.isArray(res.data?.data?.result)
        ) {
          listData = res.data?.data?.result;
          total = res.data?.data?.count || 0;
        } else {
          console.error("Unexpected data structure:", res);
          listData = [];
          total = 0;
        }
      }

      const recordsWithIndex = listData.map((item: any, index: number) => ({
        ...item,
        index: index + 1,
      }));

      return {
        pageNum: pagination?.currentPage || 1,
        pageSize: pagination?.pageSize || 10,
        data: recordsWithIndex,
        total,
      };
    },
    getColumnChecks: (cols) => {
      const checks: UI.TableColumnCheck[] = [];
      cols.forEach((column) => {
        if (column.type === "selection") {
          checks.push({
            prop: SELECTION_KEY,
            label: $t("common.check"),
            checked: true,
          });
        } else if (column.type === "expand") {
          checks.push({
            prop: EXPAND_KEY,
            label: $t("common.expandColumn"),
            checked: true,
          });
        } else if (column.type === "index") {
          checks.push({
            prop: INDEX_KEY,
            label: $t("common.index"),
            checked: true,
          });
        } else {
          checks.push({
            prop: column.prop as string,
            label: column.label as string,
            checked: true,
          });
        }
      });

      return checks;
    },
    getColumns: (cols, checks) => {
      const columnMap = new Map<string, TableColumn<GetTableData<A>>>();

      cols.forEach((column) => {
        if (column.type === "selection") {
          columnMap.set(SELECTION_KEY, column);
        } else if (column.type === "expand") {
          columnMap.set(EXPAND_KEY, column);
        } else if (column.type === "index") {
          columnMap.set(INDEX_KEY, column);
        } else {
          columnMap.set(column.prop as string, column);
        }
      });

      const filteredColumns = checks
        .filter((item) => item.checked)
        .map(
          (check) => columnMap.get(check.prop) as TableColumn<GetTableData<A>>,
        );

      return filteredColumns;
    },
    onFetched: async (transformed) => {
      const { pageNum, pageSize, total } = transformed;

      updatePagination({
        currentPage: pageNum,
        pageSize,
        total,
      });
    },
    immediate,
  });

  const pagination: Partial<RemoveReadonly<PaginationProps & PaginationEmits>> =
    reactive({
      currentPage: apiParams?.page || 1,
      pageSize: apiParams?.size || 10,
      pageSizes: [10, 15, 20, 25, 30],
      "current-change": (page: number) => {
        pagination.currentPage = page;

        updateSearchParams({
          current: page,
          page: page,
          size: pagination.pageSize!,
          pagesize: pagination.pageSize!,
        });

        getData();

        return true;
      },
      "size-change": (pageSize: number) => {
        pagination.currentPage = 1;
        pagination.pageSize = pageSize;

        updateSearchParams({
          current: pagination.currentPage,
          page: pagination.currentPage,
          size: pageSize,
          pagesize: pageSize,
        });

        getData();
        return true;
      },
    });

  // this is for mobile, if the system does not support mobile, you can use `pagination` directly
  const mobilePagination = computed(() => {
    const p: Partial<RemoveReadonly<PaginationProps & PaginationEmits>> = {
      ...pagination,
      pagerCount: isMobile.value ? 3 : 9,
    };

    return p;
  });

  function updatePagination(update: Partial<PaginationProps>) {
    Object.assign(pagination, update);
  }

  /**
   * get data by page number
   *
   * @param pageNum the page number. default is 1
   */
  async function getDataByPage(pageNum: number = 1) {
    updatePagination({
      currentPage: pageNum,
    });

    updateSearchParams({
      current: pageNum,
      page: pageNum,
      size: pagination.pageSize!,
      pagesize: pagination.pageSize!,
    });

    await getData();
  }
  /**
   * get data by page size
   *
   * @param pageSize the page size. default is 10
   */
  async function getDataByPageSize(pageSize: number = 10) {
    pagination.currentPage = 1;
    pagination.pageSize = pageSize;

    updateSearchParams({
      current: pagination.currentPage,
      page: pagination.currentPage,
      size: pageSize,
      pageSize,
    });

    await getData();
  }

  scope.run(() => {
    watch(
      () => appStore.locale,
      () => {
        reloadColumns();
      },
    );
  });

  onScopeDispose(() => {
    scope.stop();
  });

  return {
    loading,
    empty,
    data,
    columns,
    columnChecks,
    reloadColumns,
    getData,
    getDataByPage,
    getDataByPageSize,
    searchParams,
    updateSearchParams,
    resetSearchParams,
    pagination,
    mobilePagination,
  };
}

export function useTableOperate<T extends UI.TableData = UI.TableData>(
  data: Ref<T[]>,
  getData: () => Promise<void>,
  idField: keyof T = "id" as keyof T,
) {
  const {
    bool: drawerVisible,
    setTrue: openDrawer,
    setFalse: closeDrawer,
  } = useBoolean();
  const operateType = ref<TableOperateType>("add");
  const editingData = ref<T | null>(null);
  const checkedRowKeys = ref<Array<T[keyof T]>>([]);

  /**
   * add table data
   */
  function handleAdd() {
    operateType.value = "add";
    editingData.value = null;
    openDrawer();
  }

  /**
   * edit table data
   *
   * @param id id
   */
  function handleEdit(id: T[keyof T]) {
    operateType.value = "edit";
    editingData.value = data.value.find((item) => item[idField] === id) || null;
    openDrawer();
  }

  /**
   * batch delete table data
   */
  async function onBatchDeleted() {
    if (checkedRowKeys.value.length === 0) {
      ElMessage.warning("请选择要删除的数据");
      return;
    }
    await getData();
    checkedRowKeys.value = [];
  }

  /**
   * delete table data
   */
  async function onDeleted() {
    await getData();
  }

  return {
    drawerVisible,
    operateType,
    editingData,
    handleAdd,
    handleEdit,
    onBatchDeleted,
    onDeleted,
    checkedRowKeys,
  };
}
