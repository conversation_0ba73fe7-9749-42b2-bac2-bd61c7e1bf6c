/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteRecordRaw, RouteComponent } from 'vue-router';
import type { ElegantConstRoute } from '@elegant-router/vue';
import type { RouteMap, RouteKey, RoutePath } from '@elegant-router/types';

/**
 * transform elegant const routes to vue routes
 * @param routes elegant const routes
 * @param layouts layout components
 * @param views view components
 */
export function transformElegantRoutesToVueRoutes(
  routes: ElegantConstRoute[],
  layouts: Record<string, RouteComponent | (() => Promise<RouteComponent>)>,
  views: Record<string, RouteComponent | (() => Promise<RouteComponent>)>
) {
  return routes.flatMap(route => transformElegantRouteToVueRoute(route, layouts, views));
}

/**
 * transform elegant route to vue route
 * @param route elegant const route
 * @param layouts layout components
 * @param views view components
 */
function transformElegantRouteToVueRoute(
  route: ElegantConstRoute,
  layouts: Record<string, RouteComponent | (() => Promise<RouteComponent>)>,
  views: Record<string, RouteComponent | (() => Promise<RouteComponent>)>
) {
  const LAYOUT_PREFIX = 'layout.';
  const VIEW_PREFIX = 'view.';
  const ROUTE_DEGREE_SPLITTER = '_';
  const FIRST_LEVEL_ROUTE_COMPONENT_SPLIT = '$';

  function isLayout(component: string) {
    return component.startsWith(LAYOUT_PREFIX);
  }

  function getLayoutName(component: string) {
    const layout = component.replace(LAYOUT_PREFIX, '');

    if(!layouts[layout]) {
      throw new Error(`Layout component "${layout}" not found`);
    }

    return layout;
  }

  function isView(component: string) {
    return component.startsWith(VIEW_PREFIX);
  }

  function getViewName(component: string) {
    const view = component.replace(VIEW_PREFIX, '');

    if(!views[view]) {
      throw new Error(`View component "${view}" not found`);
    }

    return view;
  }

  function isFirstLevelRoute(item: ElegantConstRoute) {
    return !item.name.includes(ROUTE_DEGREE_SPLITTER);
  }

  function isSingleLevelRoute(item: ElegantConstRoute) {
    return isFirstLevelRoute(item) && !item.children?.length;
  }

  function getSingleLevelRouteComponent(component: string) {
    const [layout, view] = component.split(FIRST_LEVEL_ROUTE_COMPONENT_SPLIT);

    return {
      layout: getLayoutName(layout),
      view: getViewName(view)
    };
  }

  const vueRoutes: RouteRecordRaw[] = [];

  // add props: true to route
  if (route.path.includes(':') && !route.props) {
    route.props = true;
  }

  const { name, path, component, children, ...rest } = route;

  const vueRoute = { name, path, ...rest } as RouteRecordRaw;

  try {
    if (component) {
      if (isSingleLevelRoute(route)) {
        const { layout, view } = getSingleLevelRouteComponent(component);

        const singleLevelRoute: RouteRecordRaw = {
          path,
          component: layouts[layout],
          meta: {
            title: route.meta?.title || ''
          },
          children: [
            {
              name,
              path: '',
              component: views[view],
              ...rest
            } as RouteRecordRaw
          ]
        };

        return [singleLevelRoute];
      }

      if (isLayout(component)) {
        const layoutName = getLayoutName(component);

        vueRoute.component = layouts[layoutName];
      }

      if (isView(component)) {
        const viewName = getViewName(component);

        vueRoute.component = views[viewName];
      }

    }
  } catch (error: any) {
    console.error(`Error transforming route "${route.name}": ${error.toString()}`);
    return [];
  }

  // add redirect to child
  if (children?.length && !vueRoute.redirect) {
    vueRoute.redirect = {
      name: children[0].name
    };
  }

  if (children?.length) {
    const childRoutes = children.flatMap(child => transformElegantRouteToVueRoute(child, layouts, views));

    if(isFirstLevelRoute(route)) {
      vueRoute.children = childRoutes;
    } else {
      vueRoutes.push(...childRoutes);
    }
  }

  vueRoutes.unshift(vueRoute);

  return vueRoutes;
}

/**
 * map of route name and route path
 */
const routeMap: RouteMap = {
  "root": "/",
  "not-found": "/:pathMatch(.*)*",
  "exception": "/exception",
  "exception_403": "/exception/403",
  "exception_404": "/exception/404",
  "exception_500": "/exception/500",
  "document": "/document",
  "document_project": "/document/project",
  "document_project-link": "/document/project-link",
  "document_vue": "/document/vue",
  "document_vite": "/document/vite",
  "document_unocss": "/document/unocss",
  "document_naive": "/document/naive",
  "document_antd": "/document/antd",
  "document_element-plus": "/document/element-plus",
  "document_alova": "/document/alova",
  "403": "/403",
  "404": "/404",
  "500": "/500",
  "account": "/account",
  "account_accountlist": "/account/accountlist",
  "account_banklist": "/account/banklist",
  "account_operationlog": "/account/operationlog",
  "activities": "/activities",
  "activities_acviteconfig": "/activities/acviteconfig",
  "activities_checkin": "/activities/checkin",
  "activities_exchangecode": "/activities/exchangecode",
  "activities_invitation": "/activities/invitation",
  "activities_pdd": "/activities/pdd",
  "activities_poolofprizes": "/activities/poolofprizes",
  "agent": "/agent",
  "agent_agentdata": "/agent/agentdata",
  "agent_billmsg": "/agent/billmsg",
  "agent_manage": "/agent/manage",
  "agent_merchantmanagement": "/agent/merchantmanagement",
  "channel": "/channel",
  "channel_channellist": "/channel/channellist",
  "finance": "/finance",
  "finance_giftmoney": "/finance/giftmoney",
  "finance_paymentchannel": "/finance/paymentchannel",
  "finance_rechargemsg": "/finance/rechargemsg",
  "finance_rechargepackage": "/finance/rechargepackage",
  "finance_withdrawalapplylist": "/finance/withdrawalapplylist",
  "finance_withdrawalmanagement": "/finance/withdrawalmanagement",
  "finance_withdrawalmsg": "/finance/withdrawalmsg",
  "game": "/game",
  "game_manufacturer": "/game/manufacturer",
  "game_order": "/game/order",
  "game_product": "/game/product",
  "home": "/home",
  "login": "/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?",
  "manage": "/manage",
  "manage_faq": "/manage/faq",
  "manage_msg": "/manage/msg",
  "manage_notice": "/manage/notice",
  "manage_role": "/manage/role",
  "manage_shortlink": "/manage/shortlink",
  "manage_source": "/manage/source",
  "manage_user": "/manage/user",
  "manage_user-detail": "/manage/user-detail/:id",
  "operation": "/operation",
  "operation_activityalertmanagement": "/operation/activityalertmanagement",
  "operation_customerfaqmanagement": "/operation/customerfaqmanagement",
  "operation_policymanagement": "/operation/policymanagement",
  "operation_telegrammanagement": "/operation/telegrammanagement",
  "operation_viplevelmanagement": "/operation/viplevelmanagement",
  "report": "/report",
  "report_commissionstatisticsforcoding": "/report/commissionstatisticsforcoding",
  "report_gamelist": "/report/gamelist",
  "report_invitationrewards": "/report/invitationrewards",
  "report_realtimedatacomparison": "/report/realtimedatacomparison",
  "report_rechargeandwithdrawallist": "/report/rechargeandwithdrawallist",
  "report_sublevelusers": "/report/sublevelusers",
  "report_useraccountdynamics": "/report/useraccountdynamics",
  "report_userinvitationstatistics": "/report/userinvitationstatistics"
};

/**
 * get route path by route name
 * @param name route name
 */
export function getRoutePath<T extends RouteKey>(name: T) {
  return routeMap[name];
}

/**
 * get route name by route path
 * @param path route path
 */
export function getRouteName(path: RoutePath) {
  const routeEntries = Object.entries(routeMap) as [RouteKey, RoutePath][];

  const routeName: RouteKey | null = routeEntries.find(([, routePath]) => routePath === path)?.[0] || null;

  return routeName;
}
