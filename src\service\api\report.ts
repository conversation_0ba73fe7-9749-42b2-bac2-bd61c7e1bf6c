/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-19 13:36:04
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\service\api\report.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from '../request';
import type { DailyReportParams, DailyReportResponse } from '@/typings/report';

export function getDailyReport(params: DailyReportParams) {
  return request<DailyReportResponse>({
    url: '/backend/report/getDailyReport',
    method: 'get',
    params
  });
}

/** Real-time data report params */
export interface RealTimeDataParams {
  start_time: string;
  end_time: string;
}

/** Real-time data structure */
export interface RealTimeData {
  date: string;
  register_users: number;
  invited_users: number;
  recharge_amount: number;
  recharge_users: number;
  recharge_orders: number;
  first_recharge_amount: number;
  first_recharge_users: number;
  withdrawal_amount: number;
  withdrawal_users: number;
  withdrawal_orders: number;
}

/** Get real-time data report */
export function getRealTimeDataReport(params: RealTimeDataParams) {
  return request<RealTimeData[]>({
    url: '/backend/report/getRealTimeDataReport',
    method: 'get',
    params
  });
}

// 获取每日转账报表
export function getDailyTransferReport(params: { start_time: string; end_time: string }) {
  return request<DailyTransferReportData[]>({
    url: '/backend/report/getDailyTransferReport',
    method: 'get',
    params
  });
}

// 获取用户邀请统计
export function getInviteReport(params: { start_time: string; end_time: string }) {
  return request<any[]>({
    url: '/backend/invite/invite_report',
    method: 'get',
    params
  });
}

// 获取充值提现榜单
export function getDepositWithdrawalRanking(params:any) {
  return request({
    url: '/backend/wallet/deposit-withdrawal-ranking2',
    method: 'get',
    params
  });
}



// 获取邀请奖励（新）
export function getRewardList(params:any) {
  return request({
    url: '/backend/invite/reward_list',
    method: 'get',
    params
  });
}
