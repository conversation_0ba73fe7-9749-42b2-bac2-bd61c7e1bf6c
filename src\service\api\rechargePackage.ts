import { request } from '../request';

/** 获取充值套餐列表 */
export function fetchGetRechargePackageList(params?: any) {
  return request<any>({
    url: '/backend/walletRechargeCfg/list',
    method: 'get',
    params
  });
}

/** 获取充值套餐详情 */
export function fetchGetRechargePackageDetail(data: { id: number }) {
  return request<any>({
    url: '/backend/finance/rechargePackageDetail',
    method: 'get',
    params: data
  });
}

/** 添加充值套餐 */
export function fetchAddRechargePackage(data: any) {
  return request({
    url: '/backend/walletRechargeCfg/insert',
    method: 'post',
    data
  });
}

/** 更新充值套餐 */
export function fetchUpdateRechargePackage(data: any) {
  return request({
    url: '/backend/walletRechargeCfg/update',
    method: 'post',
    data
  });
}

/** 更新充值套餐状态 */
export function fetchUpdateRechargePackageState(data: { ids: number[], status: number }) {
  return request({
    url: '/backend/finance/rechargePackageUpdateState',
    method: 'post',
    data
  });
}

/** 删除充值套餐 */
export function fetchDeleteRechargePackage(data: any) {
  return request({
    url: '/backend/walletRechargeCfg/delete',
    method: 'post',
    data
  });
}
