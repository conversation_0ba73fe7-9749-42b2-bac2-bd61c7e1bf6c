<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <PaymentChannelSearch
      v-model:model="searchParams"
      @reset="
        () => {
          resetSearchParams();
          getDataByPage();
        }
      "
      @search="getDataByPage"
    >
      <template #table-operation>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :isNoDelete="true"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
    </PaymentChannelSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-60px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="checkedRowKeys = $event.map((row) => row.id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
      <PaymentChannelOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </ElCard>
  </div>
</template>

<script setup lang="tsx">
import { ElButton, ElTag, ElMessageBox } from "element-plus";
import { useTable, useTableOperate } from "@/hooks/common/table";
import { $t } from "@/locales";
import PaymentChannelSearch from "./modules/payment-channel-search.vue";
import PaymentChannelOperateDrawer from "./modules/payment-channel-operate-drawer.vue";
import {
  fetchPaymentChannelList,
  fetchUpdatePaymentChannelStatus,
} from "@/service/api/walletPayChannel";
import { useAuth } from "@/hooks/business/auth";

const { hasAuth } = useAuth();

defineOptions({ name: "PaymentChannelManage" });

const fetchUpdatePaymentChannelState = (data: {
  ids: number[];
  status: number;
}) => {
  return Promise.resolve({
    data: null,
    error: null,
  });
};

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  columnChecks,
} = useTable({
  apiFn: fetchPaymentChannelList,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 20,
    channel_type: undefined,
    status: undefined,
  },
  columns: () => [
    { prop: "index", label: $t("common.index"), width: 64 },
    { prop: "channel_name", label: "通道名称", minWidth: 120 },
    { prop: "channel_type", label: "通道类型", width: 100 },
    {
      prop: "fee",
      label: "手续费",
      minWidth: 120,
      formatter: (row: any) => {
        const fees = [];
        if (row.system_fees) {
          fees.push(`提现: ${row.system_fees}%`);
        }
        if (row.platform_fees) {
          fees.push(`平台: ${row.platform_fees}%`);
        }
        return fees.join("\n");
      },
    },
    {
      prop: "amount_range",
      label: "充值限额",
      minWidth: 120,
      formatter: (row: any) => {
        if (row.min_recharge_amount && row.max_recharge_amount) {
          return `R$${row.min_recharge_amount} - R$${row.max_recharge_amount}`;
        }
        return "";
      },
    },
    {
      prop: "status",
      label: "状态",
      width: 100,
      formatter: (row: any) => {
        if (row.status === undefined) {
          return "";
        }
        const tagMap = {
          1: "success",
          0: "error",
        };
        const label = ["禁用", "启用"][row.status];
        return <ElTag type={tagMap[row.status]}>{label}</ElTag>;
      },
    },
    {
      width: 140,
      prop: "operate",
      label: $t("common.operate"),
      align: "center",
      formatter: (row: any) => (
        <div class="flex-center">
          {hasAuth(2) && (
            <ElButton
              plain
              type={row.status === 1 ? "danger" : "success"}
              size="small"
              onClick={() =>
                handleStatusChange(row.id, row.status === 1 ? 0 : 1)
              }
            >
              {row.status === 1 ? "禁用" : "启用"}
            </ElButton>
          )}
          {hasAuth(3) && (
            <ElButton
              type="primary"
              plain
              size="small"
              onClick={() => edit(row.id)}
            >
              {$t("common.edit")}
            </ElButton>
          )}
        </div>
      ),
    },
  ],
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  onDeleted,
  checkedRowKeys,
} = useTableOperate(data, getData, "id");

async function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.warning("请选择要删除的支付通道");
    return;
  }
}

async function handleStatusChange(id: number, value: number) {
  try {
    // 显示确认弹窗
    await ElMessageBox.confirm(
      `确定要${value === 1 ? "启用" : "禁用"}该支付通道吗？`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: value === 1 ? "success" : "danger",
        iconClass: value === 1 ? "el-icon-success" : "el-icon-warning",
      },
    );

    const { error } = await fetchUpdatePaymentChannelStatus({
      id: id,
      status: value,
    });

    if (!error) {
      window.$message?.success("状态更新成功");
      getData(); // 刷新数据
    } else {
      window.$message?.error("状态更新失败，请重试");
    }
  } catch (err) {
    // 用户取消或请求异常
    if (err !== "cancel") {
      window.$message?.error("发生异常，请检查网络或稍后再试");
    }
  }
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
