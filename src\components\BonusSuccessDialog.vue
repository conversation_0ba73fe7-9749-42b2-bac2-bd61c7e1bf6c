<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

interface Props {
  show: boolean
  amount?: number
}

const props = defineProps<Props>()
const emit = defineEmits(['update:show'])

const handleClose = () => {
  emit('update:show', false)
}
</script>

<template>
  <v-dialog :model-value="show" @update:model-value="emit('update:show', $event)" @click:outside="handleClose"
    class="bonus-success-dialog">
    <div class="bonus-success-container">
      <div class="background-image"></div>
      <div class="popup-card">
        <div class="light-beam">
          <div class="projected-text">PARABÉNS</div>
        </div>
        <div class="screen-section">
          <div class="amount-display">
            <div class="amount-value">R$ {{ (amount || 0) / 100 }}</div>
            <div class="success-text">Recebido com sucesso</div>
          </div>
        </div>
      </div>
    </div>
  </v-dialog>
</template>

<style lang="scss" scoped>
.bonus-success-dialog {


  :deep(.v-overlay__content) {
    align-items: center;
    padding: 0px;
    margin: 0;
    width:inherit;
    max-height: inherit;
    max-width: inherit;
  }
}

.bonus-success-container {
  position: relative;
  width: 500px;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 500px;
  height: 300px;
  background-image: url('@/assets/images/h5/bonusSuccess.png');

  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 20px;
  // filter: blur(2px);
}

.popup-card {
  position: relative;
  z-index: 10;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  // justify-content: space-around;
  transform: perspective(1000px) rotateX(5deg);
  position: relative;
}

.light-beam {
  position: absolute;
  width: 100%;
  top: 30px;
  // height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// .light-beam::before {
//   content: '';
//   position: absolute;
//   top: -20px;
//   left: 50%;
//   transform: translateX(-50%);
//   width: 200px;
//   height: 80px;
//   background: linear-gradient(180deg, rgba(255, 255, 0, 0.8) 0%, rgba(255, 255, 0, 0.2) 50%, transparent 100%);
//   border-radius: 50%;
//   filter: blur(10px);
//   animation: beam-glow 2s ease-in-out infinite alternate;
// }

.projected-text {
  position: relative;
  z-index: 2;
  font-size: 50px;
  font-weight: 800;
  background: linear-gradient(180deg, #fff565, #ffbc64);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 4px 4px 4px rgba(0,0,0,0.25);
  letter-spacing: 2px;

}

.screen-section {

  position: absolute;
  border-radius: 0 0 20px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  bottom: 20px;

  .amount-display {
    text-align: center;
  }

  .amount-value {
    font-size: 40px;
    font-weight: 900;
    background: linear-gradient(180deg, #FFF565 0%, #FFBC64 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0px;
    letter-spacing: 1px;
  }

  .success-text {
    font-size: 26px;
    font-weight: 500;
    background: linear-gradient(180deg, #FFF565 0%, #FFBC64 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 0.5px;
  }

}



@keyframes beam-glow {
  0% {
    opacity: 0.6;
    transform: translateX(-50%) scale(1);
  }

  100% {
    opacity: 1;
    transform: translateX(-50%) scale(1.1);
  }
}

@keyframes text-glow {
  0% {
    text-shadow:
      0 0 10px rgba(255, 245, 101, 0.8),
      0 0 20px rgba(255, 188, 100, 0.6),
      0 0 30px rgba(255, 188, 100, 0.4);
  }

  100% {
    text-shadow:
      0 0 15px rgba(255, 245, 101, 1),
      0 0 25px rgba(255, 188, 100, 0.8),
      0 0 35px rgba(255, 188, 100, 0.6);
  }
}

@media (max-width: 768px) {
  .bonus-success-container {
    width: 90vw;
    height: 220px;
    min-width: 0;
    max-width: 98vw;
  }

  .background-image {
    width: 100%;
    height: 220px;
    border-radius: 14px;
  }

  .popup-card {
    border-radius: 14px;
  }

  .projected-text {
    font-size: 32px;
    letter-spacing: 1px;
  }

  .screen-section {
    bottom: 18px;

    .amount-value {
      font-size: 26px;
    }

    .success-text {
      font-size: 16px;
    }
  }
}
</style>