---
sidebar_position: 2
---

# 邮件恢复

在非托管的智能合约钱包中，当用户遗忘、丢失或因为各种其他原因而无法访问主密钥后，用户将会失去对账户的控制权。此时用户需要通过账户恢复流程，替换账户内的主密钥，从而重新获得对账户的控制权。

目前市面上智能合约账户恢复的方式比较有限，最为人所知的是 Argent 推出的社交恢复；用户需要设置其他 Argent 账户或者 EOA 地址作为守护者，通过多签验证的形式恢复账户。这种账户恢复方式对守护者有较高的门槛，要求守护者必须是区块链用户。

而 UniPass Wallet 通过独创的链上邮件验证技术，推出了邮件恢复方案，用户可以通过设置多个互联网邮箱作为账户的守护者，通过往链上提交邮件的形式实现账户恢复。这将大大降低守护者的门槛，让绝大多数拥有互联网邮箱的用户都可以成为 UniPass 账户的守护者。

在 UniPass Wallet 中，当用户忘记密码，失去对主密钥的控制权后，用户可以开启邮件恢复流程。只需要满足要求的守护者邮箱，发送账户恢复邮件至区块链上，链上合约即可验证邮件的真实性，在合约验证通过后，即可完成账户恢复。

:::tip 使用指南

- 当用户拥有两个以上的守护者邮箱（包含注册邮箱）时，用户通过两个邮箱分别发送账户恢复邮件，即可立即完成账户恢复。
- 当用户只有一个守护者邮箱（默认即为注册邮箱）时，用户发送账户恢复邮件后，需要等待 48 小时账户锁定期，才能完成账户恢复。

为了您的账户安全，强烈建议您设置两个以上的守护者邮箱。
:::

## 邮件社交恢复

除了可以设置您自己的邮箱作为守护者邮箱，您还可以将亲人、朋友或者其他您信任的人设置为您账户的守护者。他们只需要提供一个邮箱地址，就可以成为您的 UniPass 账户的守护者，并在您需要账户恢复的时候，通过发送邮件协助您完成账户的社交恢复。

UniPass Wallet 在守护者管理层设置有权重系统，用户可以自定义各个守护者邮箱的权重系数，满足各种定制化需求。了解更多 >> [**角色及权重**](/i18n/zh/docusaurus-plugin-content-docs/current/architecture/01-key-management.md)


:::tip 高级使用指南：
账户社交恢复将设置 `时间锁恢复` 和 `即时恢复` 两个梯度。当请求恢复的 Guardian 角色合计权重超过 `50` 时，可以开启时间锁恢复，需等待 48 小时账户锁定期；合计权重超过 `100` 时，可以进行即时恢复。

例如您可以选择两个守护者，并将 Guardian 角色的参数设置如下：

| Guardians | Weight |
|--|--|
| Your Email |`60`|
| Guard_A’s Email |`40`|
| Guard_B’s Email |`40`|

此时：
- 您和任一守护者可以完成即时的账户恢复；
- 您可以独立发起带时间锁的账户恢复；
- 当您的邮箱不可用时，两个守护者联合可以发起带时间锁的账户恢复；
- 任意单一守护者无法发起账户恢复。
:::

UniPass Wallet 在账户创建的门槛降到互联网级别的同时，还将账户恢复的守护者门槛降到了互联网级别，真正实现了一个普通用户可自管理、易理解、好操作的智能合约账户。

## 什么是社交恢复

Vitalik Buterin 早在 2014 年就开始推广社交恢复这个想法，2021 年 1 月发表了[**《Why we need wide adoption of social recovery wallets》**](https://vitalik.ca/general/2021/01/11/recovery.html)来进行详细阐述。

社交恢复功能是通过智能合约定义并实现，用户可以设置多个守护者，其中占多数的守护者发起请求时，即可更改帐户的主密钥。

在用户丢失了主密钥后（一般就是删除钱包或丢失包含钱包的设备），可以启动社交恢复 —— 用户可以直接与守护者们联系，要求他们签署一项特殊交易，以将钱包合同中的主密钥更改为新的密钥，从而实现钱包恢复。

## 隐私保护

区别于 Web2 账户由中心化服务商保存的恢复邮箱等账户安全信息，UniPass 作为非托管的智能合约钱包，在链上账户层通过加密哈希算法来存储用户的邮箱地址等敏感信息。而当邮件信息以去中心化上链验证时，也意味着账户邮箱地址可能会以某种形式被公开暴露。

UniPass Wallet 利用零知识证明技术可将用户邮件信息以脱敏形式在链上验证，避免了用户和守护者邮箱信息的泄漏，在实现去中心化验证同时有效保护用户的各方面隐私。