import { request } from '../request';

//短信管理接口
export function fetchGetSMSManagerList(params?:any) {
  return request({
    url: '/backend/sms/manager',
    method: 'get',
    params
  });
}

//短信模板列表
export function fetchGetSMSTemplateList(params?:any) {
  return request({
    url: '/backend/sms/template/list',
    method: 'get',
    params
  });
}

/** 短信手动发送 */
export function manualSend(data:any) {
  return request({
    url: '/backend/sms/manual_send',
    method: 'post',
    data
  });
}
/** 短信更新厂商状态 */
export function packageStatus(data:any) {
  return request({
    url: '/backend/sms/packageStatus',
    method: 'post',
    data
  });
}