/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-12 15:10:50
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\service\api\sysrole.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from '../request';

//角色管理
export function fetchGetRoleList(params?: Api.SystemManage.RoleSearchParams) {
  return request<Api.SystemManage.RoleList>({
    url: '/backend/admin/getRoleList',
    method: 'get',
    params
  });
}

export function fetchGetAllRoles() {
  return request({
    url: '/backend/admin/getRoleList',
    method: 'get'
  });
}

export function fetchAddRole(data?: any) {
  return request<Api.SystemManage.Role>({
    url: '/backend/admin/addRole',
    method: 'post',
    data
  });
}

export function fetchUpdateRole(data?: any) {
  return request<Api.SystemManage.Role>({
    url: '/backend/admin/updateRole',
    method: 'post',
    data
  });
}

// 修改角色
export function deleteRole(data?: any) {
  return request<boolean>({
    url: `/backend/admin/deleteRole`,
    method: 'post',
    data
  });
}

// 代理商角色管理
//角色管理
export function fetchAgentGetRoleList(params?: Api.SystemManage.RoleSearchParams) {
  return request<Api.SystemManage.RoleList>({
    url: '/backend/agent/roles/list',
    method: 'get',
    params
  });
}

export function fetchAgentGetAllRoles() {
  return request({
    url: '/backend/agent/roles/options',
    method: 'get'
  });
}

export function fetchAgentAddRole(data?: any) {
  return request<Api.SystemManage.Role>({
    url: '/backend/agent/roles/add',
    method: 'post',
    data
  });
}

export function fetchAgentUpdateRole(data?: any) {
  return request<Api.SystemManage.Role>({
    url: '/backend/agent/roles/update',
    method: 'post',
    data
  });
}

// 修改角色
export function deleteAgentRole(data?: any) {
  return request<boolean>({
    url: `/backend/agent/roles/delete`,
    method: 'post',
    data
  });
}

// 修改角色
export function getRoleOperationLogs(params?: any) {
  return request<boolean>({
    url: `/backend/admin/getRoleOperationLogs`,
    method: 'get',
    params
  });
}

// 增加角色操作日志
export function saveRoleOperations(data?: any) {
  return request<boolean>({
    url: `/backend/admin/saveRoleOperations`,
    method: 'post',
    data
  });
}
