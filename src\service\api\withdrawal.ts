/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-28 16:07:13
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-06-19 16:50:35
 * @FilePath: \betdoce-admin\src\service\api\withdrawal.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from '../request';
import type { WithdrawalListParams, WithdrawalListResponse,WithdrawalConfigResponse,WithdrawalConfigParams,WhitelistWithdrawalConfigParams,WithdrawalModeConfigParams,WhitelistSearchParams,WhitelistListResponse } from '@/typings/withdrawal';

export function getWithdrawalList(params: WithdrawalListParams) {
  return request<WithdrawalListResponse>({
    url: '/backend/withdrawals/report',
    method: 'get',
    params
  });
}

export function getWithdrawalUserRecords(params: WithdrawalListParams) {
  return request({
    url: '/backend/withdrawals/userRecords',
    method: 'get',
    params
  });
}

// 获取提现配置
export function getWithdrawalConfig() {
  return request<WithdrawalConfigResponse>({
    url: '/backend/walletWithdrawalCfg/get',
    method: 'get'
  });
}

// 更新非白名单提现配置
export function updateWithdrawalConfig(data: WithdrawalConfigParams) {
  return request({
    url: '/backend/walletWithdrawalCfg/nonwhitelist',
    method: 'post',
    data
  });
}


// 更新白名单提现配置
export function updateWhitelistWithdrawalConfig(data: WhitelistWithdrawalConfigParams) {
  return request({
    url: '/backend/walletWithdrawalCfg/whitelist',
    method: 'post',
    data
  });
}

// 更新提现模式配置
export function updateWithdrawalModeConfig(data: WithdrawalModeConfigParams) {
  return request({
    url: '/backend/walletWithdrawalCfg/mode',
    method: 'post',
    data
  });
}

// 获取总额
export function getWithdrawalTotal() {
  return request<any>({
    url: '/backend/walletWithdrawalApplication/totalAmount',
    method: 'get'
  });
}

export function getWithdrawalPaymentMethods(params: any) {
  return request<any>({
    url: '/backend/withdrawals/paymentMethods',
    method: 'get',
    params
  });
}

// 获取白名单列表
export function getWhitelist(params: WhitelistSearchParams) {
  return request<WhitelistListResponse>({
    url: '/backend/walletWithdrawalCfg/whitelist/list',
    method: 'get',
    params,
  });
}

// 获取白名单列表
export function getWhitelistSearch(params: WhitelistSearchParams) {
  return request<WhitelistListResponse>({
    url: '/backend/walletWithdrawalCfg/whitelist/search',
    method: 'get',
    params,
  });
}

// 新增白名单
export function addWhitelist(data: {
  user_id?: string;
  agent_id?: string;
  type: 'user' | 'agent';
  user_phone?: string;
  agent_phone?: string;
  withdrawal_limit?: string; // 根据实际API调整
}) {
  return request({
    url: '/backend/walletWithdrawalCfg/whitelist/add',
    method: 'post',
    data,
  });
}

// 移除白名单
export function removeWhitelist(id: number) {
  return request({
    url: `/backend/walletWithdrawalCfg/whitelist/delete`,
    method: 'post',
    data:{id}
  });
}
