<script setup lang="tsx">
import { <PERSON><PERSON><PERSON><PERSON>, ElTag, ElI<PERSON> } from "element-plus";
import { useTable, useTableOperate } from "@/hooks/common/table";
import {
  fetchGetActivityAlertList,
  fetchUpdateActivityAlertStatus,
} from "@/service/api";
import ActivityAlertSearch from "./modules/activity-alert-search.vue";
import ActivityAlertDrawer from "./modules/activity-alert-drawer.vue";
import { useAuth } from "@/hooks/business/auth";

const { hasAuth } = useAuth();

defineOptions({ name: "ActivityAlertManagement" });

const statusMap = {
  1: { label: "上线中", type: "success" },
  2: { label: "待上线", type: "warning" },
  0: { label: "已下线", type: "danger" },
};

const showTypeMap = {
  1: "仅一次",
  2: "登录时",
  3: "首页",
};

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  columnChecks,
} = useTable({
  apiFn: fetchGetActivityAlertList,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 20,
    title: undefined,
  },
  columns: () => [
    { prop: "id", label: "ID", width: 64 },
    { prop: "title", label: "标题", minWidth: 120 },
    {
      prop: "image_url",
      label: "弹窗预览",
      minWidth: 120,
      formatter: (row) => (
        <ElImage
          src={row.image_url}
          fit="cover"
          style="width: 60px; height: 40px"
          preview-src-list={[row.image_url]}
          preview-teleported
        />
      ),
    },
    { prop: "content", label: "活动简介", minWidth: 180 },
    { prop: "hyperlink", label: "活动链接", minWidth: 160 },
    { prop: "weight", label: "权重", width: 70 },
    {
      prop: "status",
      label: "状态",
      width: 100,
      formatter: (row) => {
        const s = statusMap[row.status] || { label: "未知", type: "info" };
        return <ElTag type={s.type}>{s.label}</ElTag>;
      },
    },
    {
      width: 140,
      prop: "operate",
      label: "操作",
      align: "center",
      formatter: (row) => (
        <div class="flex-center">
          {hasAuth(3) && (
            <ElButton
              type="primary"
              plain
              size="small"
              onClick={() => handleEdit(row.id)}
            >
              编辑
            </ElButton>
          )}
          {hasAuth(2) && (
            <ElButton
              type={row.status === 1 ? "danger" : "success"}
              plain
              size="small"
              onClick={() => handleToggleStatus(row)}
            >
              {row.status === 1 ? "下线" : "上线"}
            </ElButton>
          )}
        </div>
      ),
    },
  ],
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
} = useTableOperate(data, getData, "id");

async function handleToggleStatus(row: any) {
  try {
    const newStatus = row.status === 1 ? 0 : 1;
    await fetchUpdateActivityAlertStatus({ id: row.id, status: newStatus });
    window.$message?.success("状态更新成功");
    getData();
  } catch (error) {
    window.$message?.error("状态更新失败");
  }
}
</script>

<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <ActivityAlertSearch
      v-model:model="searchParams"
      @reset="
        () => {
          resetSearchParams();
          getDataByPage();
        }
      "
      @search="getDataByPage"
    >
      <template #table-operation>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :isNoDelete="true"
          :loading="loading"
          @add="handleAdd"
          @refresh="getData"
        />
      </template>
    </ActivityAlertSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-55px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="checkedRowKeys = $event.map((row) => row.id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
      <ActivityAlertDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
}
</style>
