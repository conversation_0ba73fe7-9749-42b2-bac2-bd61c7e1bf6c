{"version.label": {"message": "Next", "description": "The label for version current"}, "sidebar.tutorialSidebar.category.Introduction": {"message": "Introduction", "description": "The label for category Introduction in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Develop": {"message": "Getting started", "description": "The label for category Develop in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Popup": {"message": "Popup SDK", "description": "The label for category Popup SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Android": {"message": "Android SDK", "description": "The label for category Android SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.iOS": {"message": "iOS SDK", "description": "The label for category iOS SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.RN": {"message": "ReactNative SDK", "description": "The label for category ReactNative SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Flutter": {"message": "Flutter SDK (Legacy)", "description": "The label for category Flutter SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Unity": {"message": "Unity SDK (Legacy)", "description": "The label for category Unity SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Unreal": {"message": "Unreal SDK (Legacy)", "description": "The label for category Unreal SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Experimental": {"message": "Experimental Features (Legacy)", "description": "The label for category Unreal SDK in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Architecture": {"message": "Architecture", "description": "The label for category Architecture in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Terms": {"message": "Terms", "description": "The label for category Terms in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Audits": {"message": "Contract Audits", "description": "The label for category Contract Audits in sidebar tutorialSidebar"}, "sidebar.tutorialSidebar.category.Community": {"message": "Community & Tech Support", "description": "The label for category Community in sidebar tutorialSidebar"}}