<template>
  <v-dialog transition="dialog-bottom-transition" v-model="show" :max-width="isMobile ? '100%' : '616'" persistent
    class="login-dialog">
    <div class="login-card" :class="{ mobile: isMobile }">
      <!-- 标题 -->
      <div style="text-align: right">
        <v-btn class="close-btn" icon="mdi-close" variant="text" @click="show = false"></v-btn>
      </div>
      <!-- 登录方式切换 -->
      <div class="login-tabs">
        <v-btn :class="['tab-btn', { active: activeTab === 'phone' }]" variant="text" :ripple="false"
          @click="activeTab = 'phone'">
          Telefone
        </v-btn>
        <!-- <v-btn
          :class="['tab-btn', { active: activeTab === 'email' }]"
          variant="text"
          :ripple="false"
          @click="activeTab = 'email'"
        >
          Correio
        </v-btn> -->
        <v-btn :class="['tab-btn', { active: activeTab === 'whatsapp' }]" :ripple="false" variant="text"
          @click="activeTab = 'whatsapp'">
          Whatsapp
        </v-btn>
      </div>

      <!-- 手机号输入 -->
      <v-text-field v-model="phone" variant="outlined" density="comfortable" hide-details class="phone-input"
        placeholder="Tu número de celular" bg-color="#1F2937" ref="textField" @click:control="handleInputClick">
        <template #prepend-inner>
          <span class="prefix">+55</span>
        </template>
      </v-text-field>

      <!-- 验证码输入 -->
      <div class="code-input-wrapper">
        <v-icon color="rgba(255,255,255,0.7)" size="small" class="lock-icon">mdi-shield-lock-outline</v-icon>
        <v-text-field v-model="verificationCode" variant="outlined" density="comfortable" hide-details
          class="code-input" placeholder="Código de verificação" bg-color="#1F2937">
          <template #append-inner>
            <v-btn variant="text" class="send-code-btn" :disabled="countdown > 0" @click="sendCode">
              <span>{{ countdown > 0 ? `${countdown}s` : "Enviar" }}</span>
            </v-btn>
          </template>
        </v-text-field>
      </div>

      <!-- 登录按钮 -->
      <v-btn block size="large" class="login-btn" :loading="loading" @click="handleLogin">
        Entrar
      </v-btn>
    </div>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import { showError, showSuccess } from "@/utils/toast";
import { normalizeRequestParams, md5Encrypt } from "@/utils";
import { setToken } from "@/utils/auth";
import { getAndClearTargetRoute } from "@/utils/auth";
import websocketService from "@/utils/websocket";
import { parseUrlParams } from "@/utils/url";
import { getShortLinkDetail, loginDevice } from "@/api/auth";
import { eventBus, LOGIN_EVENTS } from "@/utils/eventBus";
import { generateDeviceFingerprint } from "@/utils/device";

const store = useStore();
const router = useRouter();
const textField = ref();

// 使用自定义的设备检测逻辑
const isMobile = computed(() => {
  return window.innerWidth <= 768;
});

function getDeviceCode() {
  const userAgent = navigator.userAgent;
  let deviceCode = 0; // Default to 0 if no match is found

  const devicePatterns = {
    "1": /Windows NT/i, // PC-Windows
    "2": /Macintosh/i, // PC-MacOS
    "3": /iPhone|iPad|iPod/i, // H5-iOS
    "4": /Android/i, // H5-Android
  };

  for (const [code, pattern] of Object.entries(devicePatterns)) {
    if (pattern.test(userAgent)) {
      deviceCode = parseInt(code);
      break;
    }
  }

  return deviceCode;
}

console.log(getDeviceCode());

const props = defineProps<{
  modelValue: boolean;
}>();
const origin_link = ref("");
const activityObj = ref<{
  activity_id?: string | number;
  invite_code?: string;
  channel?: string;
  agent_invite_code?: string;
  pdd_invite_code?: string;
}>({});

// 定义ShortLinkResponse接口
interface ShortLinkResponse {
  origin_link?: string;
  [key: string]: any;
}
const emit = defineEmits(["update:modelValue", "login"]);

const show = ref(props.modelValue);
const activeTab = ref("phone");
const phone = ref("");
const verificationCode = ref("");
const loading = ref(false);
const countdown = ref(0);

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (val) => {
    if (localStorage.getItem("inviteCode")) {
      getShortLinkDetail(localStorage.getItem("inviteCode") || "")
        .then((res) => {
          const shortLinkRes = res as ShortLinkResponse;
          origin_link.value = shortLinkRes?.origin_link || "";
          if (origin_link.value) {
            activityObj.value = parseUrlParams(origin_link.value);
            // 存储解析后的参数
            console.log(activityObj.value);
            console.log(
              activityObj.value?.invite_code &&
              !Number(activityObj.value?.activity_id)
            );
          }
          show.value = val;
        })
        .catch((err) => {
          show.value = val;
        });
    } else {
      show.value = val;
    }
  }
);

// 监听 show 变化
watch(show, (val) => {
  emit("update:modelValue", val);
});

// 验证手机号
const validatePhone = (phone: string) => {
  if (!phone) {
    showError("Por favor, insira seu número de telefone");
    return false;
  }
  // 验证手机号格式
  const phoneRegex = /^[0-9]{10,}$/;
  if (!phoneRegex.test(phone)) {
    showError("O número de telefone deve ter pelo menos 10 dígitos");
    return false;
  }
  return true;
};

// 验证验证码
const validateCode = (code: string) => {
  if (!code) {
    showError("Por favor, insira o código de verificação");
    return false;
  }
  if (code.length !== 6) {
    showError("O código de verificação deve ter 6 dígitos");
    return false;
  }
  if (!/^\d+$/.test(code)) {
    showError("O código de verificação deve conter apenas números");
    return false;
  }
  return true;
};

// 发送验证码
const sendCode = async () => {
  if (countdown.value > 0) {
    showError(`Aguarde ${countdown.value} segundos para enviar novamente`);
    return;
  }

  if (!validatePhone(phone.value)) return;

  try {
    loading.value = true;
    let res = await store.dispatch("auth/sendVerificationCode", {
      type: activeTab.value === "phone" ? "indiahm_sms" : "waotp",
      phone: phone.value,
    });
    // verificationCode.value = res?.content || "";
    showSuccess("Código enviado com sucesso");
    countdown.value = 60;
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  } catch (error: any) {
    showError(error || "Falha ao enviar código");
  } finally {
    loading.value = false;
  }
};
// 输入框点击事件
const handleInputClick = () => { };
// 处理登录
const handleLogin = async () => {
  if (!validatePhone(phone.value) || !validateCode(verificationCode.value)) {
    return;
  }

  try {
    loading.value = true;
    const { deviceId } = await generateDeviceFingerprint()
    const fingerprint = md5Encrypt(deviceId);
    let params: any = {
      phone: phone.value,
      code: verificationCode.value,
      login_method: 1,
      source: getDeviceCode(),
      md5: fingerprint,
    };
    // 判断是否有活动id
    if (Number(activityObj.value?.activity_id)) {
      params.activity_id = parseInt(activityObj.value?.activity_id as string);
      params.source_code = activityObj.value?.invite_code;
    }
    // 判断是否有渠道id
    if (activityObj.value?.channel) {
      params.channel = localStorage.getItem("inviteCode");
    }

    // 判断是否有代理商邀请码
    if (activityObj.value?.agent_invite_code) {
      params.agent_invite_code = activityObj.value?.agent_invite_code;
    }

    const success = await store.dispatch("auth/login", params);
    if (success) {
      // Reset form
      phone.value = "";
      verificationCode.value = "";
      countdown.value = 0;
      // Close dialog
      emit("update:modelValue", false);
      emit("login");
      handleClose();
      // 触发登录成功事件
      eventBus.emit(LOGIN_EVENTS.LOGIN_SUCCESS);
      if (!activityObj.value?.pdd_invite_code && localStorage.getItem("inviteCode")) {
        localStorage.removeItem("inviteCode");
      }
      localStorage.setItem('isLogin', 'true');
      // 获取目标路由并跳转
      const targetRoute = getAndClearTargetRoute();

      // 显示成功提示，但设置较短的超时时间
      showSuccess("Login realizado com sucesso", 1500);

      // 延迟路由跳转，确保 Toast 能够正常显示
      setTimeout(() => {
        if (targetRoute) {
          router.push(targetRoute);
        }
      }, 500);

      // await loginDevice({ }).send();
    } else {
      // showError("Falha ao fazer login");
    }
  } catch (error: any) {
    console.log(error);
    // showError(error || "Falha ao fazer login");
  } finally {
    loading.value = false;
  }
};

// 关闭弹窗
const handleClose = () => {
  show.value = false;
  phone.value = "";
  verificationCode.value = "";
  activeTab.value = "phone";
  countdown.value = 0;
};
</script>

<style lang="scss" scoped>
.login-dialog {
  :deep(.v-overlay__content) {
    border-radius: 16px;
    overflow: hidden;
  }

  .close-btn {
    height: 22px;
    width: 22px;
    text-align: right;
    background: #c9cad8 !important;
    color: #2b324d;
    margin-top: 10px;

    :deep(.v-icon) {
      font-size: 18px;
    }
  }
}

.login-card {
  background: url("@/assets/images/login-bg.png") no-repeat left #2b324d !important;
  background-size: auto 100%;
  height: 429px;
  padding: 0 20px 0 287px;
  position: relative;

  &.mobile {
    background: #2b324d !important;
    padding: 0 20px;
  }
}

.dialog-header {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-bottom: 24px;

  span {
    color: #ffffff;
    font-size: 16px;
    font-weight: 500;

    &:first-child {
      position: relative;

      &::after {
        content: "";
        position: absolute;
        bottom: -4px;
        left: 0;
        width: 100%;
        height: 2px;
        background: #ffdf00;
      }
    }
  }
}

.login-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  align-items: center;
  justify-content: center;
  margin-top: 30px;

  .tab-btn {
    width: auto;
    height: 40px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 600;
    font-size: 12px;
    color: #ffffff;
    text-align: center;
    font-style: normal;
    text-transform: none;
    position: relative;
    padding: 0;
    margin-bottom: 20px;

    :deep(.v-btn__overlay) {
      display: none !important;
      opacity: 0 !important;
    }

    &::after {
      display: none !important;
    }

    &::before {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 0 !important;
      height: 2px;
      border-radius: 2px !important;
      background: #ffdf00;
      transition: width 0.3s ease;
    }

    &:hover {
      background: none !important;

      &::before {
        width: 40px !important;
      }
    }

    &.active {
      color: white;

      &::before {
        width: 100% !important;
      }
    }
  }
}

.code-input-wrapper {
  position: relative;
  margin-bottom: 16px;

  .lock-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
  }

  .code-input {
    :deep(.v-field) {
      .v-field__input {
        padding-left: 40px; // 为图标留出空间
      }
    }
  }
}

.phone-input,
.code-input {
  margin-bottom: 16px;
  color: #ffdf00 !important;

  :deep(.v-field) {
    border-radius: 12px;
    background: #111827;

    .v-field__input {
      color: white !important;
      min-height: 44px !important;
      padding-left: 12px;
      font-size: 14px;
    }

    .prefix {
      margin-right: 8px;
      font-weight: 500;
      color: #ffdf00 !important;
    }

    .v-field__outline {
      border: none !important;
    }

    .v-field__prepend-inner {
      padding-top: 0;
      line-height: 45px;
    }

    input::placeholder {
      color: rgba(255, 255, 255, 0.6);
      font-size: 14px;
    }

    .v-field__append-inner {
      padding-top: 0;
    }
  }
}

.send-code-btn {
  font-size: 14px;
  text-transform: none;
  height: 45px !important;
  line-height: 45px;
  padding-top: 0 !important;
  color: #ffdf00 !important;
}

.login-btn {
  height: 48px;
  border-radius: 24px;
  background: linear-gradient(0deg, #c9b737 0%, #2abb27 100%);
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  font-size: 18px;
  color: #ffffff;
  line-height: 18px;
  text-align: center;
  font-style: normal;
  text-transform: none;
  margin-top: auto;
  margin-bottom: 20px;
}
</style>
