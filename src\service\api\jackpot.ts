import { request } from '@/service/request';
import type { JackpotConfig } from '@/typings/jackpot';

export interface JackpotListParams {
  page?: number;
  size?: number;
  sort?: string;
}

export interface JackpotListResponse {
  data: JackpotConfig[];
  count: number;
}

// 获取奖池配置列表
export function getJackpotList(params: JackpotListParams) {
  return request<JackpotListResponse>({
    url: '/backend/jackpot/config',
    method: 'get',
    params
  });
}

// 创建奖池配置
export function createJackpotConfig(data: any) {
  return request({
    url: '/backend/jackpot/config/create',
    method: 'post',
    data
  });
}
// 更新奖池配置
export function updateJackpotConfig(data: any) {
  return request({
    url: `/backend/jackpot/config/update`,
    method: 'post',
    data
  });
}

export function fetchJackpotList(params: any) {
  return request({
    url: '/backend/jackpot/config/list',
    method: 'get',
    params
  });
}

export function fetchJackpotRankList(params: any) {
  return request({
    url: '/backend/jackpot/leaderboard/list',
    method: 'get',
    params
  });
}

export function fetchJackpotProfitList(params: any) {
  return request({
    url: '/backend/jackpot/profit/list',
    method: 'get',
    params
  });
}

export function fetchJackpotConfigStatus(data: any) {
  return request({
    url: '/backend/jackpot/config/status',
    method: 'post',
    data
  });
}






