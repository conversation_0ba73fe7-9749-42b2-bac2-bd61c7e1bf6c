<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-07-03 16:55:06
 * @FilePath: \betdoce-admin\src\views\account\accountlist\modules\account-search.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="search-wrapper">
    <ElForm ref="formRef" :model="model">
      <ElRow :gutter="16">
        <ElCol :span="5">
          <ElFormItem label="用户ID" prop="uuid">
            <ElInput
              v-model="model.uuid"
              placeholder="请输入用户ID"
              clearable
              @keyup.enter="handleSearch"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="5">
          <ElFormItem label="手机号" prop="phone">
            <ElInput
              v-model="model.phone"
              placeholder="请输入手机号"
              clearable
              @keyup.enter="handleSearch"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="5">
          <ElFormItem label="邮箱" prop="email">
            <ElInput
              v-model="model.email"
              placeholder="请输入邮箱"
              clearable
              @keyup.enter="handleSearch"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="5">
          <ElFormItem label="昵称" prop="nickname">
            <ElInput
              v-model="model.nickname"
              placeholder="请输入昵称"
              clearable
              @keyup.enter="handleSearch"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem label="状态" prop="is_black">
            <ElSelect
              v-model="model.is_black"
              placeholder="请选择状态"
              clearable
            >
              <ElOption label="正常" :value="0" />
              <ElOption label="已拉黑" :value="1" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <ElRow :gutter="16">
        <ElCol :span="6">
          <ElFormItem label="父级ID" prop="father_id">
            <ElInput
              v-model="model.father_id"
              placeholder="请输入父级ID"
              clearable
              @keyup.enter="handleSearch"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="等级" prop="level">
            <ElInputNumber
              v-model="model.level"
              :min="0"
              :max="999"
              placeholder="请输入等级"
              clearable
              style="width: 100%"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="注册方式" prop="sms_channel">
            <ElSelect
              v-model="model.sms_channel"
              placeholder="请选择注册方式"
              clearable
            >
              <ElOption label="全部" :value="0" />
              <ElOption label="telefone" value="indiahm_sms" />
              <ElOption label="WhatsApp" value="waotp" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <!-- <ElCol :span="3">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol> -->
      </ElRow>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import {
  ElForm,
  ElFormItem,
  ElSelect,
  ElInput,
  ElButton,
  ElInputNumber,
} from "element-plus";

const activeName = ref("account-search");

interface Props {
  model: Record<string, any>;
}

interface Emits {
  (e: "reset"): void;
  (e: "search"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref();

function handleReset() {
  formRef.value?.resetFields();
  emit("reset");
}

function handleSearch() {
  emit("search");
}
</script>

<style lang="scss" scoped></style>
