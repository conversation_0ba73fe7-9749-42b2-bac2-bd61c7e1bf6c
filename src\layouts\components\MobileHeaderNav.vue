<template>
  <div class="mobile-header-wrapper">
    <v-app-bar elevation="2" class="mobile-header-bar">
      <!-- 左侧 -->
      <div class="mobile-header-left">
        <template v-if="!isHomePage">
          <v-btn
            icon="mdi-arrow-left"
            variant="text"
            color="#FFDF00"
            class="back-btn"
            @click="goBack"
          ></v-btn>
        </template>
        <img
          src="@/assets/images/unfold-icon.png"
          class="toggle-icon"
          @click="$emit('toggle-drawer')"
        />
        <img
          :src="bottomLogoImg"
          alt="Logo"
          class="logo-image"
          @click="router.push('/')"
        />
      </div>

      <!-- 右侧 -->
      <div class="mobile-header-right">
        <template v-if="!isLoggedIn">
          <v-btn
            rounded="xl"
            variant="flat"
            @click="handleAuthAction"
            class="auth-btn"
          >
            Entrar
          </v-btn>
        </template>
        <template v-else>
          <div class="d-flex align-center justify-space-between">
            <div variant="text" class="balance-btn">
              <div class="balance-wrapper" @click="handleUpdateUserWallet">
                <div class="balance-wrapper" >
                  <img
                    src="@/assets/images/purse-icon.png"
                    alt="Coin"
                    class="coin-icon"
                  />
                  <img
                    src="@/assets/images/refresh.png"
                    alt="refresh"
                    class="refresh-icon"
                    :class="{ 'rotate-animation': isRotating }"
                  />
                </div>

                <span class="balance-amount">
                  {{
                    formatNumber(
                      ((userInfo?.userWallet?.total_balance || 0) /
                        100) as number
                    )
                  }}</span
                >
                <img
                  src="@/assets/images/top-up-icon.png"
                  class="top-up-btn"
                  @click="router.push('/records')"
                />
              </div>
            </div>
            <div class="msg-customer">
              <v-badge
                :model-value="unreadMessages > 0"
                :content="unreadMessages"
                color="error"
              >
                <!-- <v-icon icon="mdi-message-processing" size="20"  @click="router.push('/messages')"></v-icon> -->
                <img
                  src="@/assets/images/notify.png"
                  alt="Message"
                  class="msg-icon"
                  @click="router.push('/messages')"
                />
              </v-badge>
            </div>
          </div>
        </template>
      </div>
    </v-app-bar>

    <!-- 登录弹窗 -->
    <LoginDialog
      v-model="showLoginDialog"
      @login="handleLoginSuccess"
      class="login-dialog"
    />
    <CommonDialog
      :show="commonDialogShow"
      @update:show="commonDialogShow = $event"
      :dialogObj="dialogObj"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useStore } from "vuex";
import LoginDialog from "@/components/LoginDialog.vue";
import CommonDialog from "@/components/CommonDialog.vue";
import { loginPopup } from "@/api/activity";
import { formatNumber } from "@/utils/index";
import bottomLogoImg from "@/assets/images/h5/bottom-logo.png";

interface UserWallet {
  total_balance: number;
}

interface User {
  nickname: string;
  uuid: string;
  avatar: string;
}

interface UserInfo {
  user: User | null;
  userWallet: UserWallet | null;
  unreadNum: number;
}

const formatBalance = (balance: number | undefined) => {
  if (!balance) return "0.00";

  const amount = Number(balance) / 100; // 转换为实际金额

  if (amount >= 1000000000) {
    // 十亿及以上
    return `R$ ${(amount / 1000000000).toFixed(2)}B`;
  } else if (amount >= 1000000) {
    // 百万及以上
    return `R$ ${(amount / 1000000).toFixed(2)}M`;
  } else if (amount >= 1000) {
    // 千及以上
    return `R$ ${(amount / 1000).toFixed(1)}K`;
  } else {
    return `R$ ${amount.toFixed(2)}`;
  }
};

const route = useRoute();
const router = useRouter();
const store = useStore();
const isLoggedIn = ref(false);
const showLoginDialog = ref(false);
const showUserDrawer = ref(false);
const unreadMessages = ref(0);

// 登录活动弹框
const commonDialogShow = ref(false);
const dialogObj = ref();

const isRotating = ref(false);

const getLoginPopup = () => {
  loginPopup().then((res) => {
    console.log(res);
    if (res.length) {
      commonDialogShow.value = true;
      dialogObj.value = res[0];
    }
  });
};

// 用户信息
const userInfo = ref<UserInfo>({
  user: null,
  userWallet: null,
  unreadNum: 0,
});

// 判断是否在首页
const isHomePage = computed(() => {
  return route.path === "/m/home";
});

// 返回上一页
const goBack = () => {
  router.back();
};

// 监听用户信息变化
watch(
  () => store.state.auth,
  (newVal) => {
    isRotating.value = false;
    console.log("store.state.auth", newVal);
    if (newVal?.user) {
      isLoggedIn.value = true;
      userInfo.value = {
        user: newVal.user,
        userWallet: newVal.userWallet,
        unreadNum: newVal.unreadNum,
      };
      unreadMessages.value = newVal.unreadNum || 0;
      isRotating.value = true;
    } else {
      isLoggedIn.value = false;
      userInfo.value = {
        user: null,
        userWallet: null,
        unreadNum: 0,
      };
      unreadMessages.value = 0;
    }
  },
  { immediate: true, deep: true }
);

// 处理登录/退出动作
const handleAuthAction = () => {
  if (isLoggedIn.value) {
    handleLogout();
  } else {
    showLoginDialog.value = true;
  }
};

// 处理退出
const handleLogout = async () => {
  try {
    showUserDrawer.value = false;
    await store.dispatch("auth/logout");
    router.push("/m");
  } catch (error) {
    console.error("Logout failed:", error);
  }
};

// 处理登录成功
const handleLoginSuccess = () => {
  showLoginDialog.value = false;
  getLoginPopup();
};

// 处理用户信息点击
const handleUpdateUserWallet = async () => {
  if (isLoggedIn.value) {
    isRotating.value = true;
    await store.dispatch("auth/fetchUserInfo");
    setTimeout(() => {
      isRotating.value = false;
    }, 1000);
  }
};

defineEmits(["toggle-drawer"]);
</script>

<style lang="scss" scoped>
.mobile-header-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 2000;
  height: 56px;
}

.mobile-header-bar {
  background: #00551d !important;
  position: fixed !important;
  top: 0;
  left: 0;
  width: 100%;
  height: 56px;

  :deep(.v-toolbar__content) {
    display: flex !important;
    justify-content: space-between !important;
    padding: 0 12px !important;
    background: #00551d !important;
    min-height: 56px !important;
    height: 56px !important;
  }
}

.mobile-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  .back-btn {
    border: 2px solid #ffdf00;
    height: 30px;
    width: 30px;
  }
  .toggle-icon {
    height: 30px;
    cursor: pointer;
  }

  .logo-image {
    height: 32px;
    width: auto;
    object-fit: contain;
    cursor: pointer;
  }
}

.mobile-header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.auth-btn {
  background: linear-gradient(90deg, #ff3e77 0%, #fd9a1c 100%);
  color: #fff;
  border-radius: 6px !important;
  height: 30px !important;
  transition: 0.3s;
  min-width: 80px !important;

  &:hover {
    background: #ffdf00;
  }
}
.balance-btn {
  background: #333948;
  border-radius: 20px;
  min-height: 34px;
  margin-right: 14px;
  width: auto;
  min-width: 150px;

  .balance-wrapper {
    width: auto;
    display: flex;
    min-height: 34px;
    align-items: center;
    justify-content: space-between;
    // padding-left: 5px;
    gap: 4px;
    position: relative;
    cursor: pointer;
    .coin-icon {
      width: 33px;
      height: 33px;
    }
    .refresh-icon {
      width: 14px;
      height: 14px;
      transition: transform 0.5s ease;
    }
    .balance-amount {
      color: #fff;
      font-size: 14px;
      font-weight: 500;
      word-break: break-all;
    }
    .top-up-btn {
      width: 20px;
      height: 20px;
      margin-right: -10px;
      cursor: pointer;
    }
  }
}
.msg-customer {
  margin-top: 6px;
  .msg-icon {
    width: 30px;
    height: 30px;
    cursor: pointer;
  }
}

// 确保登录弹窗在头部下方
:deep(.v-dialog) {
  margin-top: 56px !important;
}

:deep(.v-overlay__content) {
  margin-top: 56px !important;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rotate-animation {
  animation: rotate 1s linear;
}
</style>
