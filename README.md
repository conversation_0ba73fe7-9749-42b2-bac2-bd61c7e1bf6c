# Vue 3 + Vite

This template should help get you started developing with Vue 3 in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

Learn more about IDE Support for Vue in the [Vue Docs Scaling up Guide](https://vuejs.org/guide/scaling-up/tooling.html#ide-support).

# Box777 Plataforma de Jogos
# Box777 游戏平台

Uma plataforma moderna de jogos online construída com Vue 3 + TypeScript + Vite + Vuetify.
// 一个使用 Vue 3 + TypeScript + Vite + Vuetify 构建的现代在线游戏平台

## Configuração do Ambiente de Desenvolvimento
// ## 开发环境设置

```bash
# Instalar dependências
# 安装依赖
pnpm install

# Iniciar servidor de desenvolvimento
# 启动开发服务器
pnpm dev

# Construir versão de produção
# 构建生产版本
pnpm build
```

## Guia de Ferramentas do Projeto
// ## 项目工具使用指南

### Notificações Globais
// ### 全局消息提示

O projeto fornece uma ferramenta de notificação global que suporta quatro tipos de mensagens: sucesso, erro, aviso e informação.
// 项目提供了一个全局消息提示工具，支持成功、错误、警告和信息四种类型的消息提示

#### Uso Básico
// #### 基本使用

```typescript
import { showSuccess, showError, showWarning, showInfo } from '@/utils/toast'

// Mostrar mensagem de sucesso
// 显示成功消息
showSuccess('Operação bem-sucedida')

// Mostrar mensagem de erro
// 显示错误消息
showError('Operação falhou')

// Mostrar mensagem de aviso
// 显示警告消息
showWarning('Atenção...')

// Mostrar mensagem de informação
// 显示信息消息
showInfo('Informação')
```

#### Personalizar Duração da Exibição
// #### 自定义显示时长

```typescript
// Personalizar duração da exibição (5 segundos)
// 自定义显示时长（5秒）
showSuccess('Operação bem-sucedida', 5000)
```

#### Uso em Requisições API
// #### 在 API 请求中使用

As notificações estão integradas ao interceptor de requisições e tratam automaticamente as seguintes situações:
// 消息提示已经集成到请求拦截器中，会自动处理以下情况：

- Mostra notificação de sucesso quando a requisição é bem-sucedida e contém mensagem
// - 请求成功且有消息时显示成功提示
- Mostra notificação de erro quando a requisição falha
// - 请求失败时显示错误提示
- Códigos de status específicos têm mensagens de erro correspondentes:
// - 特定状态码有对应的错误提示：
  - 401: "Sessão expirada, faça login novamente"
  // - 401: "登录已过期，请重新登录"
  - 403: "Sem permissão de acesso"
  // - 403: "没有权限访问"
  - 500: "Erro no servidor"
  // - 500: "服务器错误"

#### Exemplo de Uso em Componentes
// #### 在组件中使用示例

```vue
<script setup lang="ts">
import { showSuccess, showError } from '@/utils/toast'

const handleSubmit = async () => {
  try {
    await submitData()
    showSuccess('Envio bem-sucedido')  // 提交成功
  } catch (error) {
    showError('Falha no envio: ' + error.message)  // 提交失败
  }
}
</script>
```

### Tipos de Mensagens
// ### 消息类型说明

- `success`: Mensagem de sucesso, fundo verde
// - `success`: 成功消息，绿色背景
- `error`: Mensagem de erro, fundo vermelho
// - `error`: 错误消息，红色背景
- `warning`: Mensagem de aviso, fundo amarelo
// - `warning`: 警告消息，黄色背景
- `info`: Mensagem de informação, fundo azul
// - `info`: 信息消息，蓝色背景

### Observações
// ### 注意事项

1. As notificações são exibidas no topo da página
// 1. 消息提示会在页面顶部显示
2. A duração padrão de exibição é de 3 segundos
// 2. 默认显示时长为 3 秒
3. As mensagens podem ser fechadas manualmente clicando no botão fechar
// 3. 可以通过点击关闭按钮手动关闭消息
4. Apenas uma mensagem é exibida por vez
// 4. 同一时间只会显示一条消息

### Normalização de Parâmetros e Respostas
// ### 参数和响应数据归一化

O projeto fornece funções utilitárias para normalizar parâmetros de requisição e respostas da API.
// 项目提供了用于规范化请求参数和 API 响应的工具函数。

#### Normalização de Parâmetros de Requisição
// #### 请求参数归一化

```typescript
import { normalizeRequestParams } from '@/utils'

// Exemplo de uso
// 使用示例
const rawParams = {
  username: '  john.doe  ',
  age: 25,
  tags: ['game', null, 'sport', undefined],
  address: {
    street: '  Main St  ',
    number: null
  }
}

const normalizedParams = normalizeRequestParams(rawParams)
// Resultado:
// {
//   username: 'john.doe',
//   age: 25,
//   tags: ['game', 'sport'],
//   address: {
//     street: 'Main St'
//   }
// }
```

#### Normalização de Respostas
// #### 响应数据归一化

```typescript
import { normalizeResponseData, normalizeErrorResponse } from '@/utils'

// Normalizar resposta de sucesso
// 规范化成功响应
const rawData = {
  userId: 123,
  profile: { name: 'John' }
}
const normalizedResponse = normalizeResponseData(rawData)
// Resultado:
// {
//   code: 0,
//   data: {
//     userId: 123,
//     profile: { name: 'John' }
//   },
//   message: 'success'
// }

// Normalizar resposta de erro
// 规范化错误响应
try {
  // ... código que pode gerar erro
  // ... 可能产生错误的代码
} catch (error) {
  const normalizedError = normalizeErrorResponse(error)
  // Resultado:
  // {
  //   code: -1,
  //   data: null,
  //   message: 'Error message'
  // }
}
```

#### Uso em Componentes Vue
// #### 在 Vue 组件中使用

```vue
<script setup lang="ts">
import { normalizeRequestParams, normalizeResponseData } from '@/utils'

const handleSubmit = async () => {
  // Normalizar parâmetros antes de enviar
  // 发送前规范化参数
  const rawForm = {
    name: '  User Name  ',
    email: '<EMAIL>',
    preferences: ['game', null, 'sport']
  }
  
  const params = normalizeRequestParams(rawForm)
  
  try {
    const response = await api.submitData(params)
    // Normalizar resposta
    // 规范化响应
    const result = normalizeResponseData(response)
    
    if (result.code === 0) {
      // Sucesso
      // 成功处理
    }
  } catch (error) {
    // Tratar erro
    // 错误处理
  }
}
</script>
```

#### Características da Normalização
// #### 归一化特性

1. Normalização de Parâmetros:
// 1. 参数归一化：
   - Remove valores undefined e null
   // - 移除 undefined 和 null 值
   - Remove espaços em branco de strings
   // - 移除字符串首尾空格
   - Filtra valores nulos de arrays
   // - 过滤数组中的空值
   - Processa objetos aninhados recursivamente
   // - 递归处理嵌套对象

2. Normalização de Respostas:
// 2. 响应归一化：
   - Formato padrão: { code, data, message }
   // - 统一格式：{ code, data, message }
   - Código 0 para sucesso
   // - 成功时 code 为 0
   - Mensagem padrão para sucesso/erro
   // - 统一的成功/错误消息

3. Benefícios:
// 3. 优势：
   - Dados consistentes em toda a aplicação
   // - 全应用数据一致性
   - Redução de erros por dados inválidos
   // - 减少无效数据导致的错误
   - Melhor manutenibilidade do código
   // - 提高代码可维护性
