/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-07-25 10:41:06
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-07-25 14:21:49
 * @FilePath: \betdoce-web\src\utils\format.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 格式化工具类
 */

/**
 * 获取巴西时间的辅助函数
 * @returns 巴西时间的Date对象
 */
export const getBrazilTime = (): Date => {
  return new Date(formatDateTime(new Date().toLocaleString("pt-BR", {timeZone: "America/Sao_Paulo"}).replace(",","")));
};

/**
 * 将日期字符串转换为巴西时间的Date对象
 * @param dateString 日期字符串或时间戳
 * @returns 巴西时间的Date对象
 */
export const getBrazilDate = (dateString: string | number): Date => {
  const date = new Date(dateString);
  // 将UTC时间转换为巴西时间
   const brazilTime = new Date(formatDateTime(date.toLocaleString("pt-BR", {timeZone: "America/Sao_Paulo"}).replace(",","")));
  return brazilTime;
};

/**
 * 格式化日期为 YYYY-MM-DD hh:mm:ss 格式
 * @param date Date对象或日期字符串
 * @returns 格式化后的日期字符串
 */
export const formatDateTime = (date: Date | string): string => {
  let dateObj: Date;

  if (typeof date === 'string') {
    // 处理 DD/MM/YYYY hh:mm:ss 格式
    if (date.includes('/')) {
      const [datePart, timePart] = date.split(' ');
      const [day, month, year] = datePart.split('/');
      dateObj = new Date(`${year}-${month}-${day}${timePart ? ` ${timePart}` : ''}`);
    } else {
      dateObj = new Date(date);
    }
  } else {
    dateObj = date;
  }

  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  const seconds = String(dateObj.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};
