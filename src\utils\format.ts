/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>j <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-07-25 11:23:18
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\utils\format.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import moment from "moment";

/**
 * 格式化日期为 YYYY-MM-DD hh:mm:ss 格式
 * @param date Date对象或日期字符串
 * @returns 格式化后的日期字符串
 */
export const formatDateTime = (date: Date | string): string => {
  let dateObj: Date;

  if (typeof date === 'string') {
    // 处理 DD/MM/YYYY hh:mm:ss 格式
    if (date.includes('/')) {
      const [datePart, timePart] = date.split(' ');
      const [day, month, year] = datePart.split('/');
      dateObj = new Date(`${year}-${month}-${day}${timePart ? ` ${timePart}` : ''}`);
    } else {
      dateObj = new Date(date);
    }
  } else {
    dateObj = date;
  }

  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  const seconds = String(dateObj.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};
/**
 * 格式化日期
 * @param timestamp 时间戳
 * @returns 格式化后的日期字符串
 */
export function formatDate(timestamp: number): string {
  const date = moment(timestamp).format("YYYY-MM-DD HH:mm:ss");
  return date
    // .toLocaleString("zh-CN", {
    //   year: "numeric",
    //   month: "2-digit",
    //   day: "2-digit",
    //   hour: "2-digit",
    //   minute: "2-digit",
    //   second: "2-digit",
    //   hour12: false,
    // })
    // .replace(/\//g, "-");
}
/**
 * 获取巴西时间的辅助函数
 * @returns 巴西时间的Date对象
 */
export const getBrazilTime = (): Date => {

  return new Date(formatDateTime(new Date().toLocaleString("pt-BR", {timeZone: "America/Sao_Paulo"}).replace(",","")));
};

/**
 * 将日期字符串转换为巴西时间的Date对象
 * @param dateString 日期字符串或时间戳
 * @returns 巴西时间的Date对象
 */
export const getBrazilDate = (dateString: string | number): Date => {
  const date = new Date(dateString);
  // 将UTC时间转换为巴西时间
  const brazilTime = new Date(formatDateTime(date.toLocaleString("pt-BR", {timeZone: "America/Sao_Paulo"}).replace(",","")));
  console.log(brazilTime)
  return brazilTime;
};

// 格式化數字，支援自定義配置
export const formatNumber = (num: number) => {
  // 檢查是否為 NaN
  if (isNaN(num)) {
    return "0.00";
  }

  const decimals = 2,
    thousandsSeparator = ",",
    decimalSeparator = ".",
    locale = "en-US";

  // 使用 Intl.NumberFormat 進行格式化，啟用千分位分組，並指定 locale
  const formatter = new Intl.NumberFormat(locale, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
    useGrouping: true, // 啟用千分位分組
  });

  // 直接返回 Intl.NumberFormat 格式化後的結果
  return  formatter.format(num);
};
