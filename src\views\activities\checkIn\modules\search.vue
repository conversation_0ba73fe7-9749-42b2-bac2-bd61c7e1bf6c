<script setup lang="ts">
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElDatePicker,
} from "element-plus";
import { ref } from "vue";

interface SearchModel {
  user_id?: string;
  phone?: string;
  level?: string;
  streak?: string;
  created_at?: [string, string];
}

interface Props {
  model: SearchModel;
}

interface Emits {
  (e: "update:model", value: SearchModel): void;
  (e: "search"): void;
  (e: "reset"): void;
}
const dateRange = ref();
const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const updateModel = (key: keyof SearchModel, value: any) => {
  emit("update:model", { ...props.model, [key]: value });
};

const handleSearch = () => {
  emit("search");
};

const handleReset = () => {
  dateRange.value = undefined;
  emit("reset");
};
</script>

<template>
  <div class="search-wrapper">
    <ElForm inline>
      <ElRow :gutter="16">
        <ElCol :span="6">
          <ElFormItem label="用户ID">
            <ElInput
              v-model="model.user_id"
              placeholder="请输入用户ID"
              clearable
              @update:modelValue="(val) => updateModel('user_id', val)"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="手机号">
            <ElInput
              v-model="model.phone"
              placeholder="请输入手机号"
              clearable
              @update:modelValue="(val) => updateModel('phone', val)"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="等级">
            <ElInput
              v-model="model.level"
              placeholder="请输入等级"
              clearable
              @update:modelValue="(val) => updateModel('level', val)"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="连签天数">
            <ElInput
              v-model="model.streak"
              placeholder="请输入连签天数"
              clearable
              @update:modelValue="(val) => updateModel('streak', val)"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="10">
          <ElFormItem label="时间">
            <ElDatePicker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="x"
              @change="
                (val: [string, string] | null) => {
                  if (val) {
                    model.start_time = val[0];
                    model.end_time = val[1];
                  } else {
                    model.start_time = undefined;
                    model.end_time = undefined;
                  }
                }
              "
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="10">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<style lang="scss" scoped>
.search-card {
  :deep(.el-form--inline) {
    .el-form-item {
      margin-right: 16px;
      margin-bottom: 16px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
