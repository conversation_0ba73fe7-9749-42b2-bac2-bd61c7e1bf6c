<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { ElMessage } from "element-plus";
import { fetchUpdateVipSigninReward } from "@/service/api/vipLevel";

interface Props {
  operateType: UI.TableOperateType;
  rowData?: any[] | null;
  rewardType?: number;
}

const props = defineProps<Props>();

interface Emits {
  (e: "submitted"): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>("visible", {
  default: false,
});

// VIP等级颜色映射
const vipLevelColors: Record<number, string> = {
  1: "#9ca3af", // VIP1 - 灰色
  2: "#3b82f6", // VIP2 - 蓝色
  3: "#22c55e", // VIP3 - 绿色
  4: "#f59e0b", // VIP4 - 橙色
  5: "#ef4444", // VIP5 - 红色
  6: "#8b5cf6", // VIP6 - 紫色
  7: "#14b8a6", // VIP7 - 青色
  8: "#f97316", // VIP8 - 深橙
  9: "#6b7280", // VIP9 - 深灰
  10: "#f59e0b", // VIP10 - 金色
};

// 定义签到配置数据类型
interface SigninRewardConfig {
  vip_level: number;
  day: number;
  reward: string;
}

// 表单数据
const formData = ref<SigninRewardConfig[]>([]);
const loading = ref(false);
const activeTab = ref("1-5");
const ruleText = ref("");

// 金额类型选项
const rewardTypeOptions = [
  { label: "现金", value: 1 },
  { label: "赠金", value: 2 },
  { label: "打码提现", value: 3 },
];

// 选中的金额类型
const selectedRewardType = ref(1);

// 初始化表单数据
function initFormData() {
  const defaultData: SigninRewardConfig[] = [];
  for (let vipLevel = 1; vipLevel <= 10; vipLevel++) {
    for (let day = 1; day <= 7; day++) {
      defaultData.push({
        vip_level: vipLevel,
        day: day,
        reward: "",
      });
    }
  }
  if (props.rowData && Array.isArray(props.rowData)) {
    props.rowData.forEach((item: any) => {
      const index = defaultData.findIndex(
        (d) => d.vip_level === item.vip_level && d.day === item.day,
      );
      if (index !== -1) {
        defaultData[index].reward = (item.reward / 100).toFixed(2);
      }
    });

    selectedRewardType.value = props.rewardType || 1;
  }
  formData.value = defaultData;
}

// 监听弹窗显示状态
watch(visible, (newVal) => {
  if (newVal) {
    initFormData();
  }
});

// 获取指定VIP等级和天数的奖励值
function getRewardByVipAndDay(vipLevel: number, day: number): string {
  const config = formData.value.find(
    (item) => item.vip_level === vipLevel && item.day === day,
  );
  return config ? config.reward : "";
}

// 设置指定VIP等级和天数的奖励值
function setRewardByVipAndDay(vipLevel: number, day: number, reward: string) {
  const index = formData.value.findIndex(
    (item) => item.vip_level === vipLevel && item.day === day,
  );
  if (index !== -1) {
    formData.value[index].reward = reward;
  }
}

// 提交表单
async function handleSubmit() {
  try {
    loading.value = true;

    const submitData = {
      reward_type: selectedRewardType.value,
      configs: formData.value.map((item) => {
        return {
          vip_level: item.vip_level,
          day: item.day,
          reward: Math.round(Number(item.reward || 0) * 100),
        };
      }),
      rules_text: ruleText.value,
    };

    const res = await fetchUpdateVipSigninReward(submitData);
    if (res.data) {
      ElMessage.success("VIP签到奖励配置更新成功");
      emit("submitted");
      visible.value = false;
    }
  } catch (error) {
    console.error("更新VIP签到奖励配置失败:", error);
    ElMessage.error("更新VIP签到奖励配置失败");
  } finally {
    loading.value = false;
  }
}

// 获取VIP1-5数据
const vip1to5Levels = computed(() => [1, 2, 3, 4, 5]);

// 获取VIP6-10数据
const vip6to10Levels = computed(() => [6, 7, 8, 9, 10]);

// 保留两位小数的格式化函数
function formatDecimal(value: any) {
  if (value === "" || value === null || value === undefined) return "";
  const num = parseFloat(value);
  if (isNaN(num)) return "";
  return num.toFixed(2);
}

// 输入时只允许两位小数
function onInputDecimal(event: any, vipLevel: number, day: number) {
  let val = typeof event === "string" ? event : event.target.value;
  // 只允许数字和小数点
  val = val.replace(/[^\d.]/g, "");
  // 只保留第一个小数点
  val = val.replace(/\.(?=.*\.)/g, "");
  // 限制小数点后两位
  val = val.replace(/^(\d+)(\.\d{0,2})?.*$/, "$1$2");
  // 如果以小数点开头，补0
  if (val.startsWith(".")) val = "0" + val;
  // 只在不是"0."开头时去除多余的前导0
  if (!val.startsWith("0.") && val !== "0") {
    val = val.replace(/^0+/, "");
    if (val === "") val = "0";
  }
  setRewardByVipAndDay(vipLevel, day, val);
}
</script>

<template>
  <ElDrawer
    v-model="visible"
    title="编辑签到奖励配置"
    :size="1200"
    destroy-on-close
  >
    <div class="p-4">
      <div class="mb-4">
        <span>奖金类型：</span>
        <ElSelect
          v-model="selectedRewardType"
          placeholder="请选择金额类型"
          class="w-40"
        >
          <ElOption
            v-for="option in rewardTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </ElSelect>
      </div>
      <ElTabs v-model="activeTab" type="border-card">
        <!-- VIP1-5标签页 -->
        <ElTabPane label="VIP1-5" name="1-5">
          <div class="signin-config-container">
            <!-- 表头 -->
            <div class="config-header">
              <div class="header-item level-col">等级</div>
              <div class="header-item day-col">第1天</div>
              <div class="header-item day-col">第2天</div>
              <div class="header-item day-col">第3天</div>
              <div class="header-item day-col">第4天</div>
              <div class="header-item day-col">第5天</div>
              <div class="header-item day-col">第6天</div>
              <div class="header-item day-col">第7天</div>
            </div>

            <!-- VIP1-5配置行 -->
            <div
              v-for="vipLevel in vip1to5Levels"
              :key="vipLevel"
              class="config-row"
            >
              <div class="row-item level-col">
                <span
                  class="vip-badge"
                  :style="{ backgroundColor: vipLevelColors[vipLevel] }"
                >
                  VIP{{ vipLevel }}
                </span>
              </div>
              <div v-for="day in 7" :key="day" class="row-item day-col">
                <ElInput
                  :model-value="getRewardByVipAndDay(vipLevel, day)"
                  @input="onInputDecimal($event, vipLevel, day)"
                  type="text"
                  placeholder="0.00"
                  :min="0"
                  step="0.01"
                />
              </div>
            </div>
          </div>
        </ElTabPane>

        <!-- VIP6-10标签页 -->
        <ElTabPane label="VIP6-10" name="6-10">
          <div class="signin-config-container">
            <!-- 表头 -->
            <div class="config-header">
              <div class="header-item level-col">等级</div>
              <div class="header-item day-col">第1天</div>
              <div class="header-item day-col">第2天</div>
              <div class="header-item day-col">第3天</div>
              <div class="header-item day-col">第4天</div>
              <div class="header-item day-col">第5天</div>
              <div class="header-item day-col">第6天</div>
              <div class="header-item day-col">第7天</div>
            </div>

            <!-- VIP6-10配置行 -->
            <div
              v-for="vipLevel in vip6to10Levels"
              :key="vipLevel"
              class="config-row"
            >
              <div class="row-item level-col">
                <span
                  class="vip-badge"
                  :style="{ backgroundColor: vipLevelColors[vipLevel] }"
                >
                  VIP{{ vipLevel }}
                </span>
              </div>
              <div v-for="day in 7" :key="day" class="row-item day-col">
                <ElInput
                  :model-value="getRewardByVipAndDay(vipLevel, day)"
                  @input="onInputDecimal($event, vipLevel, day)"
                  type="text"
                  placeholder="0.00"
                  :min="0"
                  step="0.01"
                />
              </div>
            </div>
          </div>
        </ElTabPane>
      </ElTabs>
      <!-- 规则管理文本输入框 -->
      <div class="mt-6">
        <span>规则管理：</span>
        <ElInput
          v-model="ruleText"
          type="textarea"
          :rows="4"
          placeholder="请输入规则说明..."
          class="w-full"
        />
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3 p-4">
        <ElButton @click="visible = false">取消</ElButton>
        <ElButton type="primary" :loading="loading" @click="handleSubmit">
          保存
        </ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<style lang="scss" scoped>
.signin-config-container {
  width: 100%;

  .config-header {
    display: flex;
    align-items: center;
    background: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-bottom: none;
    padding: 12px 8px;
    font-weight: bold;
    color: #606266;

    .header-item {
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 14px;
    }
  }

  .config-row {
    display: flex;
    align-items: center;
    border: 1px solid #e4e7ed;
    border-top: none;
    padding: 8px;

    &:hover {
      background-color: #f9f9f9;
    }

    .row-item {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 4px;
    }
  }

  // 列宽设置
  .level-col {
    width: 100px;
    min-width: 100px;
  }

  .day-col {
    flex: 1;
    min-width: 120px;
  }
}

.vip-badge {
  display: inline-block;
  padding: 4px 8px;
  color: white;
  border-radius: 4px;
  font-weight: bold;
  font-size: 12px;
  text-align: center;
  min-width: 60px;
}

:deep(.el-input) {
  .el-input__wrapper {
    border-radius: 4px;
  }
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
  padding: 16px 0;
}
</style>
