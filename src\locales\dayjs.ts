/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-30 17:31:32
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-07-25 09:57:32
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\locales\dayjs.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { locale } from 'dayjs';
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/en';
import 'dayjs/locale/pt-br';
import { localStg } from '@/utils/storage';

/**
 * Set dayjs locale
 *
 * @param lang
 */
export function setDayjsLocale(lang: App.I18n.LangType = 'pt-BR') {
  const localMap = {
    'zh-CN': 'zh-cn',
    'en-US': 'en',
    'pt-BR': 'pt-br'
  } satisfies Record<App.I18n.LangType, string>;

  const l = lang || localStg.get('lang') || 'pt-BR';

  locale(localMap[l]);
}
