import { request } from '../request';

export interface DigitalBonusRecord {
  id: number;
  user_id: number;
  user_name: string;
  amount: number;
  source_type: string;
  source_type_name: string;
  source_id: string;
  operation_type: string;
  op_type_name: string;
  balance_before: number;
  balance_after: number;
  status: string;
  status_name: string;
  expiry_time: number;
  expiry_time_str: string;
  admin_id: number;
  admin_name: string;
  remark: string;
  created_at: number;
  created_at_str: string;
}

export interface DigitalBonusListParams {
  user_id?: string;
  source_type?: string;
  status?: string;
  start_time?: string;
  end_time?: string;
  page?: number;
  size?: number;
}

export function getDigitalBonusList(params: DigitalBonusListParams) {
  return request<{ data: DigitalBonusRecord[]; total: number }>({
    url: '/backend/digital-bonus/list',
    method: 'get',
    params,
  });
}
// 7.2手动派发
export function manualAllocate(params: any) {
  return request({
    url: '/backend/digital-bonus/manual-allocate',
    method: 'post',
    data: params
  });
}

// 7.2手动扣减
export function manualReduction(params: any) {
  return request({
    url: '/backend/digital-bonus/manual-reduction',
    method: 'post',
    data: params
  });
}

// 7.2获取用户信息
export function userInfo(params: any) {
  return request({
    url: '/backend/digital-bonus/user-info',
    method: 'get',
     params
  });
}
