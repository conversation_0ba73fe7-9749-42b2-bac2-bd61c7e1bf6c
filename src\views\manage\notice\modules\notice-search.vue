<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-19 10:46:25
 * @LastEditors: Asadhcj <EMAIL>
 * @LastEditTime: 2025-05-29 15:03:40
 * @FilePath: \betdoce-admin\src\views\manage\notice\modules\notice-search.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="search-wrapper">
    <ElForm ref="formRef" :model="model">
      <ElRow :gutter="16">
        <ElCol :span="6">
          <ElFormItem label="消息类型" prop="notice_type">
            <ElSelect v-model="model.notice_type" placeholder="请选择消息类型" clearable>
              <ElOption v-for="item in noticeTypeOptions" :key="item.value" :label="item.label"
                        :value="item.value"/>
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="标题" prop="subject">
            <ElInput v-model="model.subject" placeholder="请输入标题" clearable @keyup.enter="handleSearch"/>
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>


<script setup lang="ts">
import {ref} from 'vue';
import {ElForm, ElFormItem, ElSelect, ElInput, ElButton} from 'element-plus';
import {$t} from '@/locales';

const activeName = ref('notice-search');

interface Props {
  model: Record<string, any>;
}

interface Emits {
  (e: 'reset'): void;

  (e: 'search'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref();

const noticeTypeOptions = [
  {value: 1, label: '消息中心'},
  {value: 2, label: '系统内公告'},
  {value: 3, label: '活动通知'}
];

const statusOptions = [
  {value: 1, label: '启用'},
  {value: 0, label: '禁用'}
];

function handleReset() {
  formRef.value?.resetFields();
  emit('reset');
}

function handleSearch() {
  emit('search');
}
</script>

<style lang="scss" scoped>
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 18px;
    margin-right: 18px;
  }
}
</style>
