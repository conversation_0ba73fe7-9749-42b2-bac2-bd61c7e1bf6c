<template>
  <div class="mobile-side-nav">
    <!-- 移动端用户信息 -->
    <div class="user-info">
      <div class="avatar-wrapper d-flex align-center">
        <v-badge
          :model-value="unreadMessages > 0"
          :content="unreadMessages"
          color="error"
          :offset-x="0"
          :offset-y="4"
        >
          <v-avatar
            size="60"
            :image="userInfo.user?.avatar || userTx"
            color="#2EBB27"
          ></v-avatar>
        </v-badge>
        <div class="ml-4">
          <div class="text-h6">{{ userInfo.user?.nickname || "Guest" }}</div>
          <div class="d-flex align-center">
            <span class="text-caption text-grey"
              >ID: {{ userInfo.user?.uuid || "-" }}</span
            >
            <img
              src="@/assets/images/user-copy-icon.png"
              class="cody-icon"
              @click="copyUserId"
            />
          </div>
        </div>
      </div>
      <v-btn
        class="vip-btn"
        :ripple="false"
        variant="flat"
        @click="router.push('/vip')"
      >
        <template #prepend>
          <img
            src="@/assets/images/purse-icon.png"
            alt="Coin"
            class="coin-icon"
          />
        </template>
        VIP{{ userInfo.user?.level || 0 }}
      </v-btn>
    </div>

    <!-- 移动端菜单列表 -->
    <v-list class="menu-list">
      <v-list-item
        v-for="item in menuItems"
        :key="item.name"
        :ripple="false"
        class="menu-item"
        @click="handleNavClick(item)"
      >
        <template v-slot:prepend>
          <img :src="item.icon" class="icon-img" />
        </template>

        <v-badge
          v-if="item.name === 'Notificação'"
          :model-value="unreadMessages > 0"
          :content="unreadMessages"
          color="error"
          :offset-x="-10"
        >
          <v-list-item-title>{{ item.name }}</v-list-item-title>
        </v-badge>
        <v-list-item-title v-else>{{ item.name }}</v-list-item-title>
        <template v-slot:append>
          <v-icon size="20" color="grey-lighten-1">mdi-chevron-right</v-icon>
        </template>
      </v-list-item>
    </v-list>

    <!-- 客服中心 -->
    <div class="customer-service" @click="handleCustomerService">
      <div class="service-title">— Atendimento ao cliente oficial —</div>
      <img src="@/assets/images/h5/customer-icon.png" width="40" />
    </div>
    <PWAInstallButton ref="PWAInstall" />
    <!-- 添加弹窗组件 -->
    <BonusDialog 
      :show="showBonusDialog" 
      @update:show="showBonusDialog = $event"
      @exchange-success="handleExchangeSuccess" 
    />
    <BonusSuccessDialog 
      :show="showBonusSuccessDialog" 
      :amount="exchangeResult?.amount"
      :code="exchangeResult?.code"
      @update:show="showBonusSuccessDialog = $event" 
    />
    <TelegramShareDialog ref="telegramDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import BonusDialog from "@/components/BonusDialog.vue";
import BonusSuccessDialog from "@/components/BonusSuccessDialog.vue";
import PWAInstallButton from "@/components/PWAInstallButton.vue";
import TelegramShareDialog from "@/components/TelegramShareDialog.vue";
import { subscriptions } from "@/api/customer-service.ts";
import userTx from "@/assets/images/tx-icon.png";
import maueIcon01 from "@/assets/images/h5/maue-icon-01.png";
import maueIcon02 from "@/assets/images/h5/maue-icon-02.png";
import maueIcon03 from "@/assets/images/h5/maue-icon-03.png";
import maueIcon04 from "@/assets/images/h5/maue-icon-04.png";
import maueIcon05 from "@/assets/images/h5/maue-icon-05.png";
import maueIcon06 from "@/assets/images/h5/maue-icon-06.png";
import maueIcon07 from "@/assets/images/h5/maue-icon-07.png";
import maueIcon08 from "@/assets/images/h5/maue-icon-08.png";
import maueIcon09 from "@/assets/images/h5/maue-icon-09.png";
import maueIcon10 from "@/assets/images/h5/maue-icon-10.png";

const router = useRouter();
const store = useStore();
const showBonusDialog = ref(false);
const showBonusSuccessDialog = ref(false);
const telegramDialogRef = ref();
const PWAInstall = ref();
const exchangeResult = ref<{ code: string; amount: number; message: string } | null>(null);

// 用户信息
const userInfo = ref({
  user: null,
  userWallet: null,
  unreadNum: 0,
});

// 监听用户信息变化
watch(
  () => store.state.auth,
  (newVal) => {
    if (newVal.user) {
      userInfo.value = {
        user: newVal.user,
        userWallet: newVal.userWallet,
        unreadNum: newVal.unreadNum,
      };
    } else {
      userInfo.value = {
        user: null,
        userWallet: null,
        unreadNum: 0,
      };
    }
  },
  { immediate: true, deep: true }
);
const unreadMessages = ref(0);

// 监听用户信息变化
watch(
  () => store.state.auth,
  (newVal) => {
    if (newVal.user) {
      unreadMessages.value = newVal.unreadNum || 0;
    } else {
      unreadMessages.value = 0;
    }
  },
  { immediate: true, deep: true }
);

// 复制用户ID
const copyUserId = () => {
  if (userInfo.value.user?.id) {
    navigator.clipboard
      .writeText(userInfo.value.user.id.toString())
      .then(() => {
        // 可以添加复制成功的提示
      })
      .catch((err) => {
        console.error("Failed to copy user ID:", err);
      });
  }
};

const menuItems = [
  {
    name: "Bônus de login VIP",
    icon: maueIcon01,
    path: "/connection",
  },
  {
    name: "Depósito",
    icon: maueIcon02,
    path: "/records",
  },
  {
    name: "Retirar",
    icon: maueIcon03,
    path: "/withdraw",
  },
  {
    name: "Registro de saldo",
    icon: maueIcon04,
    path: "/deposit",
  },
  {
    name: "meu bônus",
    icon: maueIcon05,
    path: "/bonus",
  },
  {
    name: "Resgatar Código",
    icon: maueIcon06,
    path: "/dialog",
  },
  {
    name: "Notificação",
    icon: maueIcon10,
    path: "/messages",
  },
  {
    name: "Baixe o aplicativo",
    icon: maueIcon08,
    path: "/PWA",
  },
  {
    name: "Convidar amigos",
    icon: maueIcon09,
    path: "/invite",
  },
  {
    name: "Assinaturas do Telegram",
    icon: maueIcon07,
    path: "/TT",
  },
];

const handleNavClick = (item: any) => {
  if (item.path === "/dialog") {
    showBonusDialog.value = true;
    return;
  }
  if (item.path === "/PWA") {
    PWAInstall.value.handleInstall();
    return;
  }
  if (item.path === "/TT") {
    telegramDialogRef.value?.show();
    return;
  }
  if (item.path) {
    router.push(item.path);
  }
};
// 处理兑换成功事件
const handleExchangeSuccess = (result: { code: string; amount: number; message: string }) => {
  exchangeResult.value = result;
  showBonusSuccessDialog.value = true;
};
// const telegramLink = ref("https://t.me/"); // 替换为实际的 Telegram 频道链接

// // 处理TT分享
// const handleTTShare = async () => {
//   const res = await subscriptions();

//   telegramLink.value = telegramLink.value + res[0].chat_id;
//   console.log("telegramLink", telegramLink.value);
//   const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
//   if (isIOS) {
//     // iOS需要用户交互触发window.open
//     setTimeout(() => {
//       window.location.href = telegramLink.value;
//     }, 100);
//   } else {
//     window.open(telegramLink.value,'_self');
//   }
// };
// 处理客服入口
const handleCustomerService = () => {
  router.push("/customer-service");
};
</script>

<style lang="scss" scoped>
.mobile-side-nav {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px;
  background: #002664;
}

.user-info {
  padding: 0 8px;
  .avatar-wrapper {
    margin-bottom: 6px;
    display: flex;
    align-items: self-end;
    justify-content: start;
    .cody-icon {
      width: 16px;
      height: 16px;
      margin-left: 4px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .user-id {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
  }

  .vip-btn {
    background: linear-gradient(0deg, #c9b737, #2abb27);
    border-radius: 18px;
    width: fit-content;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(251, 81, 130, 0.3);
    }

    &:active {
      transform: translateY(0);
    }

    .coin-icon {
      width: 20px;
      height: 20px;
      margin-right: 4px;
    }
  }
}

.menu-list {
  display: flex;
  flex-direction: column;
  height: auto;
  overflow: auto;
  gap: 4px;
  .menu-item {
    height: 36px !important;
    min-height: 36px !important;
    line-height: 36px !important;
    padding: 0 16px;
    border-radius: 8px !important;
    background: #152151;
    .icon-img {
      width: 22px;
      height: 22px;
      margin-right: 8px;
    }
  }
}

.customer-service {
  padding: 20px 16px;
  text-align: center;

  .service-title {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    margin-bottom: 12px;
  }

  .service-btn {
    height: 48px;
    border-radius: 8px;
  }
}
</style>
