<template>
  <ElDialog :model-value="visible" :title="type === 'offline' ? '下线奖池' : '上线奖池'" width="500px" @close="onCancel" :close-on-click-modal="false">
    <div style="font-size: 16px; margin-bottom: 18px; margin-top: 8px;">
      确定要{{ type === 'offline' ? '下线' : '上线' }}这个奖池吗？
      {{ type === 'offline' ? '下线后用户将无法参与该奖池活动。' : '上线后用户可以立即参与该奖池活动。' }}
    </div>
    <div style="font-size: 15px; line-height: 2; margin-bottom: 18px;">
      <div><b>奖池ID：</b>{{ jackpotId }}</div>
      <div><b>奖池名称：</b>{{ jackpotName }}</div>
      <div><b>操作：</b>{{ type === 'offline' ? '下线' : '上线' }}</div>
    </div>
    <template #footer>
      <div style="display: flex; justify-content: flex-end; gap: 12px;">
        <ElButton @click="onCancel">取消</ElButton>
        <ElButton :type="type === 'offline' ? 'danger' : 'success'" @click="onConfirm">确认</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<script setup lang="ts">
const props = defineProps<{
  visible: boolean;
  type: 'online' | 'offline';
  jackpotId: number | string;
  jackpotName: string;
}>();
const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

function onCancel() {
  emit('update:visible', false);
  emit('cancel');
}
function onConfirm() {
  emit('confirm');
  emit('update:visible', false);
}
</script>
