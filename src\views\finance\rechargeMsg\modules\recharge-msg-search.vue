<template>
  <div class="search-wrapper">
    <ElForm ref="formRef" :model="model">
      <ElRow :gutter="16">
        <ElCol :span="5">
          <ElFormItem label="用户ID" prop="uuid">
            <ElInput v-model="model.uuid" placeholder="用户ID" clearable style="width: 100%" />
          </ElFormItem>
        </ElCol>

        <ElCol :span="5">
          <ElFormItem label="交易号" prop="transaction_no">
            <ElInput v-model="model.transaction_no" placeholder="交易号" clearable style="width: 100%" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem label="支付方式" prop="payment_method">
            <ElSelect v-model="model.payment_method" placeholder="支付方式" clearable style="width: 100%">
              <ElOption v-for="item in paymentChannelOptions" :key="item.value" :label="item.label"
                :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem label="是否首充" prop="is_recharge">
            <ElSelect v-model="model.is_recharge" placeholder="是否首充" clearable style="width: 100%">
              <ElOption v-for="item in isFirstOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="创建时间" prop="created_at">
            <ElDatePicker
              v-model="model.created_at"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="x"
              style="width: 100%"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="20">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElForm } from 'element-plus';
import { getDepositPaymentMethods } from '@/service/api/wallet';

interface Props {
  model: Record<string, any>;
}

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref();

const isFirstOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 2 }
];

const paymentChannelOptions = ref<Array<{ label: string; value: string }>>([]);

function handleReset() {
  formRef.value?.resetFields();
  emit('reset');
}

function handleSearch() {
  emit('search');
}

onMounted(async () => {
  const { response } = await getDepositPaymentMethods({});
  if (response?.data.status_code === 200) {
    paymentChannelOptions.value = response?.data.data.map((item: any) => ({
      label: item.channel_name,
      value: item.channel_type
    }));
  }
});
</script>

<style lang="scss" scoped>
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 18px;
    margin-right: 18px;
  }
}
</style>
