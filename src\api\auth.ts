import request from "./request";
import store from "@/store";
export interface LoginParams {
  phone: string;
  login_method: number;
  code: string;
  source: number;
  source_code?: string;
  activity_id?: number;
}

export interface LoginResponse {
  token: string;
  user: {
    id: number;
    phone: string;
    balance: number;
    nickname?: string;
    avatar?: string;
  };
}

export interface UserInfo {
  unread_num: number;
  user: {
    id: number;
    uuid: number;
    nickname: string;
    username: string;
    phone: string;
    email: string;
    CPF: string;
    pix: string;
    accountType: number;
    customerName: string;
    merchantUserId: string;
    avatar: string;
    gender: number;
    reg_ip: string;
    level: number;
    invite_code: string;
    source: number;
    father_id: number;
    is_recharge: number;
    is_black: number;
    is_login: number;
    remake: string;
    created_at: number;
    created_by: string;
    updated_at: number;
    updated_by: string;
  };
  user_wallet: {
    id: number;
    user_id: number;
    total_balance: number;
    wallet_id: number;
    cash_balance: number;
    digital_balance: number;
    wallet_status: string;
    total_deposit: number;
    total_withdrawal: number;
    created_at: number;
    created_by: string;
    updated_at: number;
    updated_by: string;
  };
}

export interface UserInfoResponse {
  code: number;
  data: {
    id: number;
    phone: string;
    balance: number;
    nickname?: string;
    avatar?: string;
    vipLevel?: number;
  };
  message: string;
}

export const login = (params: LoginParams) => {
  return request.Post("/login", params);
};

export const loginDevice = (params: any) => {
  return request.Post("/login_device", params);
};

export const refreshToken = (params: any) => {
  return request.Post("/refresh_token", params);
};

export const sendVerificationCode = (type: string, phone: string) => {
  return request.Post("/sendsms", { sms_channel: type, mobile: phone });
};

/**
 * 获取用户详情信息
 */
export const getUserInfo = () => {
  return request.Post<UserInfo>("/user/info");
};

/**
 * 获取系统公告列表
 * @param params 查询参数
 * @returns 公告列表数据
 */
export function getNoticeList(params: {
  notice_type: number;
  sort?: string;
  page: number;
  size: number;
  user_id?: number;
}) {
  return request.Get("/getNoticeList", { params });
}
// /**
//  * 获取奖励公告列表
//  * @param params 查询参数
//  * @returns 公告列表数据
//  */
// export function getJctNoticeList(params: { page: number; size: number }) {
//   return request.Get("/getSystemList", { params });
// }

export function doCheckIn(params: { user_id: number }) {
  return request.Post("/checkin/do", params);
}

// 获取用户消息列表
export function getUserNoticeList(params: {
  sort?: string;
  page: number;
  size: number;
  user_id?: number;
}) {
  return request.Get("/notice/getCenterList", { params:{...params,date:new Date()} });
}
// 获取用户消息详情
export function readNotice(params: { notice_id: string; user_id?: number }) {
  return request.Post("/notice/read", params);
}

// 编辑用户信息
export function updateUserProfile(params: {
  id: number;
  username?: string;
  email?: string;
  nickname?: string;
  CPF?: string;
  pix?: string;
  trc20?: string;
  accountType?: string;
  customerName?: string;
  merchantUserId?: string;
  avatar?: string;
  gender?: string;
  is_black?: string;
}) {
  return request.Post("/user/update", { ...store.state.auth.user, ...params });
}

interface GenerateShortUrlParams {
  activity_id: number;
  user_id: number;
}

interface ShortUrlResponse {
  id: number;
  key: string;
  short_link: string;
  origin_link: string;
  pv: number;
  uv: number;
  create_at: string;
  create_by: number;
  update_at: string;
  update_by: number;
}

/**
 * 生成短链接
 */
export const generateShortUrl = (data: GenerateShortUrlParams) => {
  return request.Post<{ short_link?: string }>("/short_url/generate", data);
};

/**
 * 获取短链详情
 */
export const getShortLinkDetail = (shortCode: string) => {
  return request.Get(`/s/${shortCode}`);
};

interface RankingItem {
  id: number;
  bettor_id: number;
  bet_date: string;
  referrers_id: number;
  level: number;
  digital_amount: number;
  status: number;
}

interface RankingReward {
  referrers_id: number;
  digital_amount: string;
}

interface RankingResponse {
  rank: RankingItem[];
  reward: RankingReward;
}

/**
 * 获取邀请排行榜数据
 */
export const getInviteRanking = (params: {
  activity_id: number;
  user_id: number;
}) => {
  return request.Post<RankingResponse>("/short_url/rank", params);
};

/**
 * 获取上周邀请排行榜数据
 */
export const getInviteWeekRrank = (params: {
  activity_id: number;
  user_id: number;
}) => {
  return request.Post<RankingResponse>("/short_url/week_rank", params);
};
/**
 * 退出登录
 */
export const logoutPost = () => {
  return request.Post<RankingResponse>("/user/logout");
};

// 邀请列表项接口
export interface InviteListItem {
  user_id: string;
  invite_level: number;
  register_time: string;
  commission: string;
}

// 扫码列表项接口
export interface ScanCodeListItem {
  user_id: string;
  invite_level: number;
  total_bet_amount: string;
  last_bet_date: string;
  commission_earned: string;
}

// 邀请列表响应接口
export interface InviteListResponse {
  total: number;
  list: InviteListItem[];
  total_commission: string;
  invite_ctx: string;
  invite_rule: string;
  invite_link: string;
}

// 扫码列表响应接口
export interface ScanCodeListResponse {
  total: number;
  list: ScanCodeListItem[];
  total_bet_commission: string;
}

// 邀请列表查询参数
export interface InviteListParams {
  page: number;
  page_size: number;
  sort_by: string;
  sort_order: string;
  query_inviter_uuid: string;
}

/**
 * 获取邀请清单
 */
export const invertList = (params: InviteListParams) => {
  return request.Post<InviteListResponse>("/invert_list/list_details", params);
};

/**
 * 获取扫码清单
 */
export const scanCodeList = (params: InviteListParams) => {
  return request.Post<ScanCodeListResponse>("/inviter/scan_code_list/details", params);
};

/**
 * 获取邀请概览查询页面
 */
export const overview = (params) => {
  return request.Post("/invert_list/overview", params);
};

/**
 * 获取团队数据
 */
export const teamMembers = (params) => {
  return request.Get("/invert_list/team/members", {
    params: { ...params, date: new Date() },
  });
};

/**
 * 获取团队下级数据
 */
export const subordinates = (id) => {
  return request.Get(`/invert_list/team/subordinates/${id}`, {});
};
