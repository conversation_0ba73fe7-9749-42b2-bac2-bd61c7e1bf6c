import { request } from '../request';
import type { DepositListParams, DepositListResponse } from '@/typings/deposit';

export function getDepositList(params: DepositListParams) {
  return request<DepositListResponse>({
    url: '/backend/deposits/report',
    method: 'get',
    params
  });
}

export function getDepositUserRecords(params: DepositListParams) {
  return request({
    url: '/backend/deposits/userRecords',
    method: 'get',
    params
  });
}
