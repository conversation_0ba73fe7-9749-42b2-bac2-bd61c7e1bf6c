{"name": "Box777", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "test": "vite --mode test", "build": "vite build", "build:test": "vite build --mode test", "preview": "vite preview"}, "dependencies": {"@mdi/font": "^7.4.47", "alova": "^3.2.10", "blueimp-md5": "^2.19.0", "gsap": "^3.12.7", "jwt-decode": "^4.0.0", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "sass": "^1.69.0", "swiper": "^11.2.6", "vite-plugin-pwa": "^0.20.5", "vue": "^3.3.4", "vue-router": "^4.3.0", "vuex": "^4.1.0", "workbox-core": "^7.1.0", "workbox-precaching": "^7.1.0", "workbox-routing": "^7.1.0", "workbox-strategies": "^7.1.0", "workbox-window": "^7.1.0"}, "devDependencies": {"@types/blueimp-md5": "^2.18.2", "@types/jwt-decode": "^3.1.0", "@types/node": "^22.13.10", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^4.2.3", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^4.1.3", "typescript": "^5.0.2", "vite": "^4.5.2", "vite-plugin-html": "^3.2.2", "vite-plugin-vuetify": "^1.0.2", "vue-tsc": "^1.8.5", "vuetify": "^3.8.0"}, "resolutions": {"vuetify": "^3.3.23"}}