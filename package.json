{"name": "my-website", "version": "0.0.0", "private": true, "scripts": {"start-zh": "yarn run start -- --locale zh", "write-translations-zh": "yarn run write-translations -- --locale zh", "translate-md": "mkdir -p i18n/zh/docusaurus-plugin-content-docs/current&&cp -r docs/** i18n/zh/docusaurus-plugin-content-docs/current", "docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc"}, "dependencies": {"@docusaurus/core": "2.0.0-rc.1", "@docusaurus/plugin-ideal-image": "2.0.0-rc.1", "@docusaurus/preset-classic": "2.0.0-rc.1", "@docusaurus/theme-search-algolia": "^2.0.0-rc.1", "@easyops-cn/docusaurus-search-local": "^0.29.3", "@mdx-js/react": "^1.6.22", "caniuse-lite": "^1.0.30001512", "clsx": "^1.2.1", "docusaurus-plugin-sass": "^0.2.2", "prism-react-renderer": "^1.3.5", "raw-loader": "^4.0.2", "react": "^17.0.2", "react-dom": "^17.0.2", "react-feather": "^2.0.10"}, "devDependencies": {"@docusaurus/module-type-aliases": "2.0.0-rc.1", "@tsconfig/docusaurus": "^1.0.5", "prettier": "^2.8.7", "prettier-plugin-tailwindcss": "^0.2.5", "react-markdown": "^8.0.4", "tailwindcss": "^3.2.7", "typescript": "^4.7.4"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=16.14"}}