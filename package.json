{"name": "@sa/elp", "type": "module", "version": "1.3.11", "description": "A fresh and elegant admin template, based on Vue3、Vite3、TypeScript、ElementPlus and UnoCSS. 一个基于Vue3、Vite3、TypeScript、ElementPlus and UnoCSS的清新优雅的中后台模版。", "author": {"name": "Soybean", "email": "<EMAIL>", "url": "https://github.com/soybeanjs"}, "license": "MIT", "homepage": "https://github.com/soybeanjs/soybean-admin", "repository": {"url": "https://github.com/soybeanjs/soybean-admin.git"}, "bugs": {"url": "https://github.com/soybeanjs/soybean-admin/issues"}, "keywords": ["Vue3 admin ", "vue-admin-template", "Vite5", "TypeScript", "element-plus", "element-plus-admin", "UnoCSS"], "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev": "vite --mode test", "dev:prod": "vite --mode prod", "gen-route": "sa gen-route", "lint": "eslint . --fix", "postbuild": "sa print-soybean", "prepare": "simple-git-hooks", "preview": "vite preview", "release": "sa release", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "update-pkg": "sa update-pkg"}, "dependencies": {"@antv/data-set": "0.11.8", "@antv/g2": "5.2.11", "@antv/g6": "5.0.44", "@better-scroll/core": "2.5.1", "@iconify/vue": "4.3.0", "@sa/alova": "workspace:*", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@visactor/vchart": "1.13.6", "@visactor/vchart-theme": "1.12.2", "@visactor/vtable-editors": "1.17.0", "@visactor/vtable-gantt": "1.17.0", "@visactor/vue-vtable": "1.17.0", "@vueuse/components": "12.8.2", "@vueuse/core": "12.8.2", "clipboard": "2.0.11", "crypto-js": "4.2.0", "dayjs": "1.11.13", "defu": "^6.1.4", "dhtmlx-gantt": "9.0.5", "dompurify": "3.2.4", "echarts": "5.6.0", "element-plus": "^2.9.5", "jsbarcode": "3.11.6", "json5": "2.2.3", "moment": "^2.30.1", "nprogress": "0.2.0", "pinia": "3.0.1", "pinyin-pro": "3.26.0", "print-js": "1.6.0", "qrcode.vue": "^3.6.0", "swiper": "11.2.5", "tailwind-merge": "3.0.2", "typeit": "8.8.7", "vditor": "3.10.9", "vue": "3.5.13", "vue-draggable-plus": "0.6.0", "vue-i18n": "11.1.1", "vue-pdf-embed": "2.1.2", "vue-router": "4.5.0", "wangeditor": "4.7.15", "xgplayer": "3.0.21", "xlsx": "0.18.5"}, "devDependencies": {"@amap/amap-jsapi-types": "0.0.15", "@elegant-router/vue": "0.3.8", "@iconify/json": "2.2.314", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybeanjs/eslint-config": "1.5.3", "@types/bmapgl": "0.0.7", "@types/dompurify": "3.2.0", "@types/node": "22.13.9", "@types/nprogress": "0.2.3", "@unocss/eslint-config": "66.0.0", "@unocss/preset-icons": "66.0.0", "@unocss/preset-uno": "66.0.0", "@unocss/transformer-directives": "66.0.0", "@unocss/transformer-variant-group": "66.0.0", "@unocss/vite": "66.0.0", "@vitejs/plugin-vue": "5.2.1", "@vitejs/plugin-vue-jsx": "4.1.1", "eslint": "9.21.0", "eslint-plugin-vue": "10.0.0", "lint-staged": "15.4.3", "sass": "1.85.1", "simple-git-hooks": "2.11.1", "tsx": "4.19.3", "typescript": "5.8.2", "unplugin-icons": "22.1.0", "unplugin-vue-components": "28.4.1", "vite": "6.2.0", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.7.2", "vue-eslint-parser": "10.1.1", "vue-tsc": "2.2.8"}, "simple-git-hooks": {"commit-msg": "pnpm sa git-commit-verify", "pre-commit": "pnpm typecheck && pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "website": "https://elp.soybeanjs.cn"}