---
sidebar_position: 6
---

# UniPass 钱包 vs 其他钱包

## UniPass Wallet vs 普通钱包

我们可以将以太坊上的账户分成两类，一类是外部账户（简称 EOA，Externally Owned Account），另一类是合约账户（简称 CA，Contract Account）。外部账户和合约账户最主要的区别是，外部账户是由私钥控制的，而合约账户则是由智能合约代码控制的，可以实现复杂逻辑。

我们目前常用的普通钱包就是一个外部账户，用户需要自己保管私钥，并在每次需要进行转账签名等操作时，都需要使用私钥进行签名。私钥一旦丢失了，那么用户也会失去对账户和账户内的资产的控制权。

而智能合约钱包则是一个合约账户，是基于智能合约实现的，并非单一私钥控制的钱包。在智能合约钱包中，用户可以实现一系列复杂操作，如密钥更换，社交恢复账户，使用任意代币支付手续费，账户限额等等。

UniPass Wallet 就是一个链上合约账户的解决方案，通过智能合约实现了独特而强大的能力，可以为用户提供接近互联网的用户流程、额外的安全性、邮箱社交恢复、资产隔离、账户限额等丰富的功能。

|  | 普通钱包 | 智能合约钱包 |
| --- | --- | --- |
| 最高权限 | 用户 (私钥) | 用户 (智能合约) |
| 单点风险 | 私钥 | 无 |
| 使用 dApp | ✓ | ✓ |
| 账户恢复 | × | 通过合约恢复 |
| 账户限额 | × | 通过合约实现 |
| 支付 Gas | 必须使用原生代币 | 可以使用任意代币 |
| 用户使用门槛 | 高 | 低 |

## UniPass Wallet vs 其他智能合约钱包

**`UniPass Wallet 一个是多链地址统一的智能合约账户`**

UniPass Wallet 可以在所有 EVM 兼容链上进行合约部署，并保证用户可以在多链上使用统一的合约地址。这意味着用户可以在 Ethereum 及其 Layer 2 链（Arbitrum, Optimism, etc），以及其他 EVM 兼容链（Binance Smart Chain, Polygon, Avalanche, Cardano, etc）上的地址完全一致。

**`采用门限签名，基于多方安全计算 MPC 技术管理密钥`**

其他智能合约钱包解决方案往往还需要依赖外部的 EOA 钱包做私钥管理。UniPass Wallet 通过多方安全计算（Multi-Party Computation，简称 MPC）的门限签名技术（Threshold Signature Scheme，简称 TSS)，避免了依赖于外部 EOA 钱包的安全性和可用性，同时也实现业务流程中私钥全程可用不可见的高安全性。

**`独创链上邮件验证技术，实现密码学安全且隐私保护的邮箱验证`**

邮件协议作为一个诞生五十余年的互联网基础协议，是现代互联网的基石，在人们日常的工作和生活中扮演着不可或缺的角色。

在邮件发展演进过程中，为了防止邮件信息被篡改，阻止域名欺骗行为，实现垃圾邮件过滤，在 2004 年，DKIM（DomainKeys Identified Mail）被引入进了邮件协议。DKIM 是一种标准的电子邮件身份验证方法，它会在外发邮件中添加数字签名。 邮件接收服务器在收到 DKIM 签名的邮件后，可验证邮件是否确实来自发件人，而非冒充发件人的其他人。DKIM 还会执行检查，以确保邮件内容在邮件发送后未发生变化。 

UniPass Wallet 将 DKIM 数字签名的验签方法在区块链上通过智能合约实现，这样就在链上实现邮件内容验证，进而实现通过邮箱管理合约账户，用邮件进行账户的社交恢复等功能。

我们也加入了零知识证明技术，确保用户的邮件信息可以在链上以脱敏形式进行验证，有效保护用户的隐私信息。

**`让普通互联网用户成为账户守护者，实现真正零门槛去中心化的账户社交恢复`**

常见的智能合约钱包的账户社交恢复方案，诸如Argent，要求守护者是拥有以太坊地址或合约钱包的 Web3 用户。这意味着您信任的朋友及家人不是 Web3 用户的时候，他们无法成为您的账户守护者。

而 UniPass Wallet 支持添加没有加密钱包的普通互联网用户的邮箱账户作为守护者，这样就可以让你信任的家人朋友来实现账户社交恢复了。

**`支持任意代币支付手续费，无需原生代币即可使用账户`**

UniPass Wallet 支持任意代币支付手续费。第三方可以通过独立运行 relayer 的方式，实现更多样的可定制的手续费支付策略。

**`支持零资产创建账户获得钱包地址进行收款`**

不同于 EOA 地址，智能合约钱包通常需要先进行链上部署后，才能获得账户地址，进行收款等操作。

UniPass Wallet 实现了可以在未部署账户合约时，提前计算出用户的账户地址，用户便可以开始使用这个地址接收资产。直到用户第一次需要使用这个地址进行合约调用（比如说对外转账）时，才进行账户合约的部署。

这种方式提升了用户的使用体验。用户既不需要在初始创建账户时就需要支付合约创建费用，又同时拥有新账户的完整控制权。

**`梯度安全模型，为不同使用深度的用户提供易用性和安全性平衡的产品`**

在梯度安全模型上，我们从实施攻击的初始动机上阻止攻击者，只要实施攻击的成本远大于攻击所获收益，聪明的攻击者就会放弃这种无利可图的买卖。

- 得益于 UniPass Wallet 灵活的账户层和多类型 keys 支持，只要配置合适的安全设置即可大大增加攻击者实施攻击的成本。基于此我们提供了渐进式的用户安全设置流程设计，如对于一个近乎 0 资产或者低资产的新手用户而言，用户不需要设置更多的安全设置（如更多的守护者邮箱、更多的 2FA），默认的安全等级足以抵御低成本攻击。
- 得益于邮箱和 APP 效的通知体系，随着用户资产额度的提升，适时在资产超过一定门槛时，提醒要求用户增加新的安全设置。

这种梯度安全模型可以让用户在早期保证账户基础安全的同时拥有更流畅的使用体验，而随着资产和账户身份信誉的累积增长，在用户对账户的安全有更高的要求时，用户可以增加更多的安全设置（如设置更多的守护者，添加 2FA，增加硬件钱包作为 master key，定期 keys 轮换，进行 keys 权重调整）等等。