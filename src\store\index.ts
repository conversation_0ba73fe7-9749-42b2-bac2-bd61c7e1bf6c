import { createStore, Store, useStore as baseUseStore, Commit, Dispatch } from 'vuex'
import { InjectionKey } from 'vue'
import auth from './modules/auth'
import jackpot from './modules/jackpot'
import { RootState } from '@/types/store'

// 定义 injection key
export const key: InjectionKey<Store<RootState>> = Symbol()

// 创建 store 实例
export const store = createStore<RootState>({
  modules: {
    auth,
    jackpot
  }
})

// 导出类型化的 useStore 组合式函数
export function useStore(): Store<RootState> {
  return baseUseStore(key)
}

export default store 