<template>
  <v-container fluid class="retirar-container pa-4">
    <div class="content-wrapper wrapper-bg">
      <!-- 标题和详情按钮 -->
      <div class="d-flex justify-space-between align-center mb-4">
        <h2 class="text-h6 font-weight-medium">Reti<PERSON></h2>
        <v-btn
          color="#fff"
          variant="text"
          class="details-btn"
          density="comfortable"
        >
          Detalhes da retirada
        </v-btn>
      </div>

      <!-- 最小提现金额提示 -->
      <v-text-field
        v-model="withdrawAmount"
        variant="outlined"
        density="comfortable"
        hide-details
        class="amount-input mb-3"
        persistent-placeholder
        bg-color="#1F2937"
      >
        <template #prepend-inner>
          <span class="text-error font-weight-medium">R$</span>
        </template>
        <template #placeholder>
          <span class="text-grey-lighten-1">Retirada mínima R$ 50</span>
        </template>
      </v-text-field>

      <!-- 余额信息卡片 -->
      <div class="balance-card mb-6">
        <div class="d-flex justify-space-between">
          <span class="text-grey-lighten-1">Saldo atual:R$4.22</span>
          <span class="text-grey-lighten-1">Saldo que puede retirar: R$0.00</span>
        </div>
      </div>

      <!-- 提现方式选择 -->
      <div class="mb-6">
        <h3 class="text-subtitle-1 mb-3">Escolha o método de retirada</h3>
        <v-select
          v-model="paymentMethod"
          variant="outlined"
          density="comfortable"
          hide-details
          class="payment-input"
          persistent-placeholder
          placeholder="Canal de pagamento rápid"
          bg-color="#1F2937"
          :items="paymentMethods"
          item-title="label"
          item-value="value"
          return-object
        />
      </div>

      <!-- 银行账户选择 -->
      <div class="mb-6">
        <h3 class="text-subtitle-1 mb-3">Escolha a conta bancária</h3>
        <v-text-field
          v-model="accountNumber"
          variant="outlined"
          density="comfortable"
          hide-details
          class="account-input"
          persistent-placeholder
          append-inner-icon="mdi-content-copy"
          bg-color="#1F2937"
          readonly
          placeholder="***********"
        />
      </div>
    </div>
    <div class="content-wrapper">
      <!-- 提交按钮 -->
      <v-btn
          block
          color="#E11D48"
          size="large"
          class="submit-btn"
      >
        Enviar
      </v-btn>
    </div>
    <div class="content-wrapper wrapper-bg">
      <!-- 提现规则 -->
      <div class="rules-section">
        <h3 class="text-subtitle-1 mb-3">Regras de retirad.</h3>
        <v-card
          class="rules-card"
          variant="flat"
          color="#1F2937"
        >
          <v-card-text>
            <ol class="rules-list">
              <li>O valor que pode ser sacado só pode ser aumentado por meio de aposta</li>
              <li>O valor mínimo de saque é de 50 reais, e cada valor de saque é um múltiplo de 10</li>
              <li>O saldo da conta corrente precisa ser apoststado para atingir o valor que pode se! sacado</li>
              <li>DeDoiSigue a recaraaiforbem-sucedidaV2Oretrada emdinheiro correspondente para cada aposta</li>
              <li>Comissão de depósito de convite, comissão de apostas em equipe pode ser retiradadiretamente</li>
              <li>Com excecao das comissões de depósito por convite e comissões de apostas deequipe, todos os bônus nao podem ser sacados, voce precisa apostar para sacar e sópode sacar a parte que ganhou!</li>
            </ol>
          </v-card-text>
        </v-card>
      </div>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const withdrawAmount = ref('')
const paymentMethod = ref(null)
const accountNumber = ref('')

// 支付方式选项
const paymentMethods = [
  { 
    label: 'PIX',
    value: 'pix',
  },
  {
    label: 'Transferência bancária',
    value: 'bank_transfer',
  },
  {
    label: 'Cartão de crédito',
    value: 'credit_card',
  }
]
</script>

<style lang="scss" scoped>
.retirar-container {
  background: #1a1a2e;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1080px;
  margin: 0 auto;
  padding: 24px;
  border-radius: 16px;
  &.wrapper-bg{
    background: #343F6B;
  }
}


.details-btn {
  background: #1B1F2D;
  height: 40px;
  font-size: 14px;
  font-style: normal;
  text-transform: none;
  border-radius: 20px;
}

.min-amount-card,
.balance-card {
  font-family: Inter, Inter;
  font-weight: 500;
  font-size: 14px;
  color: #CBCBCB;
  line-height: 14px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.payment-method-card,
.rules-card {
  border-radius: 12px;
}
.payment-input{
  line-height: 44px;
}
.amount-input,
.payment-input,
.account-input {
  :deep(.v-field) {
    border-radius: 12px;
    background: #1F2937;
    
    .v-field__input {
      color: white !important;
      min-height: 44px !important;
      padding-top: 0;
      padding-bottom: 0;
    }
    
    .v-select__selection {
      color: white;
    }
    
    .v-field__append-inner .v-icon {
      color: rgba(255, 255, 255, 0.7);
    }
    
    .v-field__placeholder {
      color: rgba(255, 255, 255, 0.6) !important;
      font-size: 14px;
    }

    .v-field__outline {
      border-color: rgba(255, 255, 255, 0.1) !important;
      opacity: 0 !important;
    }
  }

  &.v-text-field--focused {
    :deep(.v-field__outline) {
      background: #fff !important;
    }
  }

  // 下拉菜单样式
  :deep(.v-overlay__content) {
    .v-list {
      background: #1F2937;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 8px;
      
      .v-list-item {
        border-radius: 8px;
        min-height: 40px;
        color: white;
        
        &--active {
          background: rgba(255, 255, 255, 0.1);
        }
        
        &:hover {
          background: rgba(255, 255, 255, 0.05);
        }
      }
    }
  }
}

.submit-btn {
  height: 48px;
  background: linear-gradient( 180deg, #FB77A8 0%, #9F314F 100%);
  box-shadow: 0px 4px 4px 0px rgba(0,0,0,0.25);
  border-radius: 20px 20px 20px 20px;
  font-family: Inter, Inter;
  font-weight: normal;
  font-size: 24px;
  color: #FFFFFF;
  line-height: 24px;
  text-align: center;
  font-style: normal;
  text-transform: none;
}

.rules-list {
  padding-left: 20px;
  color: rgba(255, 255, 255, 0.7);
  
  li {
    margin-bottom: 8px;
    line-height: 1.5;
    font-size: 14px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style> 