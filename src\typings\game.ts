export interface TransactionRecord {
  agency_uid: string;
  bet_amount: number;
  code_amount: number;
  create_at: number;
  currency_code: string;
  game_category: string;
  game_data: string;
  game_round: string;
  game_uid: string;
  id: number;
  member_account: string;
  serial_number: string;
  trans_time: number;
  update_at: number;
  user_id: number;
  win_amount: number;
}

export interface TransactionListParams {
  page: number;
  size: number;
  order_id?: string;
  game_code?: string;
  user_id?: string;
  phone?: string;
  register_source?: string;
  type?: string;
  time_range?: [string, string];
}

export interface TransactionListResponse {
  list: TransactionRecord[];
  total: number;
}

export interface InviteActivityRecord {
  id: number;
  user_id: number;
  nick_name: string;
  phone: string;
  game_id: string;
  game_round: string;
  game_category: string;
  bet_amount: number;
  win_amount: number;
  currency_code: string;
  trans_time: number;
  serial_number: string;
}

export interface InviteActivityListParams {
  current: number;
  size: number;
  user_id?: number;
  game_id?: string;
  start_time?: string;
  end_time?: string;
}

export interface InviteActivityListResponse {
  list: InviteActivityRecord[];
  total: number;
}
