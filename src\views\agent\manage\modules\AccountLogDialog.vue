<template>
  <el-dialog
    :visible.sync="visible"
    width="800px"
    title="日志"
    @close="handleClose"
    append-to-body
  >
    <div style="background: #f7fafd; padding: 10px 0 0 0; border-radius: 6px 6px 0 0;">
      <el-form :inline="true" :model="searchForm" size="small" style="padding: 0 16px;">
        <el-form-item label="账号ID">
          <el-input v-model="searchForm.accountId" placeholder="账号ID" style="width: 120px;" />
        </el-form-item>
        <el-form-item label="账号">
          <el-input v-model="searchForm.account" placeholder="账号" style="width: 120px;" />
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select v-model="searchForm.actionType" placeholder="全部" style="width: 100px;">
            <el-option label="全部" value="" />
            <el-option label="登录" value="登录" />
            <el-option label="登出" value="登出" />
            <el-option label="操作" value="操作" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="fetchData">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      :data="tableData"
      border
      style="width: 100%; margin-top: 10px;"
      v-loading="loading"
      element-loading-text="加载中..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(255, 255, 255, 0.8)"
    >
      <el-table-column prop="index" label="序号" width="60" />
      <el-table-column prop="accountId" label="账号ID" width="120" />
      <el-table-column prop="account" label="账号" width="100" />
      <el-table-column prop="time" label="操作时间" width="150" />
      <el-table-column prop="actionType" label="操作类型" width="100" />
      <el-table-column prop="actionTarget" label="操作对象" width="120" />
      <el-table-column prop="detail" label="详情" />
    </el-table>
    <div style="margin-top: 10px; display: flex; align-items: center; justify-content: space-between;">
      <div>每页显示条数
        <el-select v-model="pageSize" size="small" style="width: 60px; margin: 0 4px;" @change="handleSizeChange">
          <el-option v-for="size in [5, 10, 20, 50]" :key="size" :label="size" :value="size" />
        </el-select>
        共 {{ total }} 个项目
      </div>
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @current-change="handlePageChange"
        style="flex: none;"
      />
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';

const props = defineProps<{
  visible: boolean;
  accountId?: string;
}>();
const emits = defineEmits(['update:visible']);

const loading = ref(false);
const searchForm = reactive({
  accountId: '',
  account: '',
  actionType: '',
});
const tableData = ref<any[]>([]);
const total = ref(999);
const pageSize = ref(5);
const currentPage = ref(1);

watch(
  () => props.visible,
  (val) => {
    if (val) {
      fetchData();
    }
  }
);

async function fetchData() {
  loading.value = true;
  try {
    // TODO: 替换为实际API
    tableData.value = Array.from({ length: pageSize.value }, (_, i) => ({
      index: (currentPage.value - 1) * pageSize.value + i + 1,
      accountId: '*********',
      account: '10',
      time: '2021.08.24 16:34',
      actionType: i === 0 ? '登录' : i === 1 ? '登出' : '操作',
      actionTarget: i === 3 ? '代理商管理' : '-',
      detail: '',
    }));
    total.value = 999;
  } finally {
    loading.value = false;
  }
}
function resetSearch() {
  searchForm.accountId = '';
  searchForm.account = '';
  searchForm.actionType = '';
  fetchData();
}
function handleSizeChange(size: number) {
  pageSize.value = size;
  currentPage.value = 1;
  fetchData();
}
function handlePageChange(page: number) {
  currentPage.value = page;
  fetchData();
}
function handleClose() {
  emits('update:visible', false);
}
</script>

<style scoped>
.el-dialog__header {
  background: #21a4e7;
  color: #fff;
  border-radius: 6px 6px 0 0;
  padding: 10px 16px 10px 16px;
}
.el-dialog__title {
  color: #fff;
}
.el-form--inline .el-form-item {
  margin-right: 16px;
}
</style>