/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-19 10:47:29
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-05-25 13:44:48
 * @FilePath: \betdoce-admind:\new_project\betdoce-web\src\api\customer-service.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from './request'
import type { CustomerQuestionResponse, TelegramGroupLinkResponse, ChatRequest, ChatResponse } from '@/types/customer-service'

// 获取常见问题列表
export const getCustomerQuestions = () => {
  return request.Get<CustomerQuestionResponse>('/getQuestion/keywordTop')
}

// 获取Telegram群组邀请链接
export const getTelegramGroupInviteLink = () => {
  return request.Get<TelegramGroupLinkResponse>('/question/getTelegramGroupInviteLink')
}

// 发送消息
export const sendChatMessage = (data: ChatRequest) => {
  return request.Post<ChatResponse>('/question/chat', data)
} 

// 获取客服群
export const customerServiceGroups = () => {
  return request.Get('/tg/customerServiceGroups')
} 

// 获取WhatsApp
export const whatsappLink = () => {
  return request.Get('/whatsapp/link')
} 

// 获取订阅号
export const subscriptions = () => {
  return request.Get('/tg/subscriptions')
} 

// 获取欢迎语
export const getWelcomeResponses = () => {
  return request.Get('/question/getWelcomeResponses')
} 