# WebSocket状态组件拖拽功能

## 功能概述

WebSocketStatus组件现在支持手动拖拽功能，用户可以自由移动状态监控窗口到屏幕上的任意位置。

## 功能特性

### 🎯 核心功能
- **自由拖拽**：点击并拖拽状态栏可移动到任意位置
- **位置记忆**：自动保存位置到localStorage，刷新页面后保持位置
- **边界限制**：自动限制在视窗范围内，防止拖拽到屏幕外
- **双击重置**：双击状态栏可快速重置到右上角位置

### 🎨 视觉体验
- **拖拽手柄**：左侧显示"⋮⋮"拖拽指示器
- **状态反馈**：拖拽时组件轻微放大，提供视觉反馈
- **悬停效果**：鼠标悬停时显示阴影加深效果
- **光标变化**：拖拽时光标变为抓取状态

### 🔧 交互细节
- **左键拖拽**：只响应鼠标左键点击拖拽
- **防止冲突**：拖拽时阻止展开/收起功能触发
- **响应式调整**：窗口大小改变时自动调整位置

## 使用方法

### 基本操作

#### 桌面端
1. **移动位置**：点击状态栏并拖拽到目标位置
2. **重置位置**：双击状态栏快速回到右上角
3. **展开/收起**：点击状态栏（非拖拽时）展开或收起详细信息

#### 移动端
1. **移动位置**：长按状态栏300ms后拖拽到目标位置
2. **重置位置**：快速双击状态栏回到右上角
3. **展开/收起**：轻触状态栏展开或收起详细信息

### 提示信息
- 鼠标悬停在状态栏上会显示提示："拖拽移动位置，双击重置到右上角"

## 技术实现

### 拖拽状态管理
```typescript
// 拖拽相关状态
const isDragging = ref(false);
const position = ref({ x: 10, y: 10 });
const dragOffset = ref({ x: 0, y: 0 });
```

### 事件处理
```typescript
// 开始拖拽
const startDrag = (event: MouseEvent) => {
  // 只响应左键
  if (event.button !== 0) return;
  
  // 阻止事件冒泡和默认行为
  event.stopPropagation();
  event.preventDefault();
  
  // 设置拖拽状态和偏移量
  isDragging.value = true;
  // ...
};

// 拖拽过程
const onDrag = (event: MouseEvent) => {
  if (!isDragging.value) return;
  
  // 计算新位置并限制在边界内
  const newX = event.clientX - dragOffset.value.x;
  const newY = event.clientY - dragOffset.value.y;
  
  position.value = {
    x: Math.max(0, Math.min(newX, maxX)),
    y: Math.max(0, Math.min(newY, maxY))
  };
};
```

### 位置持久化
```typescript
// 保存位置
localStorage.setItem('websocketStatusPosition', JSON.stringify(position.value));

// 恢复位置
const restorePosition = () => {
  const savedPosition = localStorage.getItem('websocketStatusPosition');
  if (savedPosition) {
    position.value = JSON.parse(savedPosition);
  }
};
```

### 响应式处理
```typescript
// 窗口大小改变时调整位置
const handleResize = () => {
  const maxX = window.innerWidth - 250;
  const maxY = window.innerHeight - 100;
  
  if (position.value.x > maxX || position.value.y > maxY) {
    position.value = {
      x: Math.min(position.value.x, maxX),
      y: Math.min(position.value.y, maxY)
    };
  }
};
```

## CSS样式

### 拖拽相关样式
```css
.websocket-status {
  position: fixed;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.status-header {
  cursor: move;
  transition: background-color 0.2s ease;
}

.status-header:hover {
  background: rgba(255, 255, 255, 0.1);
}

.status-header.dragging {
  background: rgba(255, 255, 255, 0.2);
  cursor: grabbing;
}

.drag-handle {
  color: #999;
  margin-right: 6px;
  font-size: 10px;
  cursor: grab;
}
```

## 配置选项

### 默认位置
```typescript
// 默认位置（左上角）
const position = ref({
  x: 10,
  y: 10
});

// 重置到右上角
const resetPosition = () => {
  position.value = {
    x: window.innerWidth - 260,
    y: 10
  };
};
```

### 边界限制
```typescript
// 组件尺寸配置
const COMPONENT_WIDTH = 250;  // 组件宽度
const BOTTOM_MARGIN = 100;    // 底部边距

// 边界计算
const maxX = window.innerWidth - COMPONENT_WIDTH;
const maxY = window.innerHeight - BOTTOM_MARGIN;
```

## 兼容性

### 浏览器支持
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

### 设备支持
- ✅ 桌面端：完整拖拽功能
- ✅ 移动端：触摸拖拽功能（长按拖拽）

## 最佳实践

### 1. 避免频繁拖拽
```typescript
// 使用防抖优化性能
const debouncedSave = debounce(() => {
  localStorage.setItem('websocketStatusPosition', JSON.stringify(position.value));
}, 300);
```

### 2. 错误处理
```typescript
// 位置恢复时的错误处理
try {
  const parsed = JSON.parse(savedPosition);
  if (isValidPosition(parsed)) {
    position.value = parsed;
  }
} catch (error) {
  console.warn('恢复位置失败:', error);
  // 使用默认位置
}
```

### 3. 性能优化
```typescript
// 只在必要时更新DOM
watch(position, (newPos) => {
  requestAnimationFrame(() => {
    // 更新位置
  });
}, { deep: true });
```

## 故障排除

### 常见问题

1. **拖拽不响应**
   - 检查是否在开发环境或已启用状态显示
   - 确认鼠标左键点击

2. **位置不保存**
   - 检查localStorage是否可用
   - 确认没有隐私模式限制

3. **拖拽到屏幕外**
   - 双击重置位置
   - 或清除localStorage中的位置数据

### 调试方法
```typescript
// 查看当前位置
console.log('当前位置:', position.value);

// 查看保存的位置
console.log('保存的位置:', localStorage.getItem('websocketStatusPosition'));

// 重置位置
localStorage.removeItem('websocketStatusPosition');
```

## 移动端特性

### 🎯 移动端优化

1. **长按拖拽**：300ms长按后启动拖拽模式
2. **触觉反馈**：支持振动反馈（如果设备支持）
3. **视觉反馈**：长按时组件放大并加深阴影
4. **防误触**：区分轻触、长按和拖拽操作
5. **响应式布局**：根据屏幕尺寸调整组件大小

### 🔧 移动端技术实现

#### 长按检测
```typescript
// 设置长按检测定时器
longPressTimer.value = setTimeout(() => {
  isLongPress.value = true;
  isDragging.value = true;

  // 触觉反馈
  if (navigator.vibrate) {
    navigator.vibrate(50);
  }
}, 300); // 300ms后认为是长按
```

#### 触摸事件处理
```typescript
// 触摸开始
const startTouchDrag = (event: TouchEvent) => {
  event.preventDefault(); // 防止滚动
  event.stopPropagation(); // 防止事件冒泡

  // 记录触摸点和时间
  const touch = event.touches[0];
  touchStartTime.value = Date.now();

  // 设置长按检测
  longPressTimer.value = setTimeout(() => {
    // 启动拖拽模式
  }, 300);
};

// 触摸移动
const onTouchDrag = (event: TouchEvent) => {
  if (!isDragging.value) return;

  event.preventDefault(); // 防止页面滚动

  const touch = event.touches[0];
  // 计算新位置...
};
```

#### 设备检测
```typescript
const detectTouchDevice = () => {
  isTouchDevice.value = 'ontouchstart' in window ||
                       navigator.maxTouchPoints > 0 ||
                       (navigator as any).msMaxTouchPoints > 0;
};
```

### 📱 移动端CSS优化

```css
.websocket-status {
  /* 禁用触摸选择和缩放 */
  touch-action: none;
  -webkit-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

/* 移动端响应式 */
@media (max-width: 768px) {
  .websocket-status {
    min-width: 180px;
    font-size: 11px;
    padding: 6px;
  }
}

/* 触摸设备特定样式 */
@media (hover: none) and (pointer: coarse) {
  .status-header {
    padding: 4px 6px; /* 增加触摸区域 */
  }

  .status-header:active {
    background: rgba(255, 255, 255, 0.15);
    transform: scale(0.98);
  }
}

/* 长按状态视觉反馈 */
.websocket-status.long-press {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5);
  background: rgba(0, 0, 0, 0.9);
}
```

### 🛡️ 移动端兼容性

#### 支持的移动浏览器
- ✅ iOS Safari 12+
- ✅ Chrome Mobile 60+
- ✅ Firefox Mobile 55+
- ✅ Samsung Internet 8+
- ✅ Edge Mobile 79+

#### 移动端特殊处理
- **防止页面滚动**：拖拽时禁用body滚动
- **防止缩放**：使用touch-action: none
- **防止选择**：禁用文本选择和长按菜单
- **触觉反馈**：支持振动API

这个拖拽功能提供了灵活的用户体验，让开发者可以根据需要调整状态监控窗口的位置，同时在桌面端和移动端都保持了良好的性能和用户体验。
