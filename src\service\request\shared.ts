import { useAuthStore } from '@/store/modules/auth';
import { localStg } from '@/utils/storage';
// import { fetchRefreshToken } from '../api';
import type { RequestInstanceState } from './type';

export function getAuthorization() {
  return localStg.get('token') || '';
}

/** refresh token */
// async function handleRefreshToken() {
//   const { resetStore } = useAuthStore();

//   const rToken = localStg.get('refreshToken') || '';
//   const rToken = localStg.get('refreshToken') || '';
//   const { error, data } = await fetchRefreshToken(rToken);
//   if (!error) {
//     localStg.set('token', data.token);
//     return true;
//   }

//   resetStore();

//   return false;
// }

// export async function handleExpiredRequest(state: RequestInstanceState) {
//   if (!state.refreshTokenFn) {
//     state.refreshTokenFn = handleRefreshToken();
//   }

//   const success = await state.refreshTokenFn;

//   setTimeout(() => {
//     state.refreshTokenFn = null;
//   }, 1000);

//   return success;
// }

export function showErrorMsg(state: RequestInstanceState, message: string) {
  if (state.errorMessageBox) {
    state.errorMessageBox.close();
    state.errorMessageBox = null;
  }

  const messageInstance = window.$message?.error(message);
  if (messageInstance) {
    state.errorMessageBox = messageInstance;
  }
}
