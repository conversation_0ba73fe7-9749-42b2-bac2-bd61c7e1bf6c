<template>
  <el-dialog
    :visible.sync="visible"
    width="500px"
    :title="editData.accountId ? '编辑账号' : '新增账号'"
    @close="handleClose"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="formRef" label-width="90px" style="margin-top: 10px;">
      <el-form-item label="账号ID">
        <el-input v-model="form.accountId" placeholder="账号ID" disabled />
      </el-form-item>
      <el-form-item label="账号名称" prop="accountName" required>
        <el-input v-model="form.accountName" placeholder="请输入账号名称" />
      </el-form-item>
      <el-form-item label="账号" prop="account" required>
        <el-input v-model="form.account" placeholder="账号" />
      </el-form-item>
      <el-form-item label="密码" prop="password" required>
        <el-input v-model="form.password" placeholder="密码" type="password" />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile" required>
        <el-input v-model="form.mobile" placeholder="手机号" />
      </el-form-item>
      <el-form-item label="邮箱">
        <el-input v-model="form.email" placeholder="邮箱" />
      </el-form-item>
      <el-form-item label="菜单分配">
        <el-tree
          :data="menuTree"
          show-checkbox
          node-key="id"
          :default-checked-keys="form.menuIds"
          :props="treeProps"
          @check="handleMenuCheck"
        />
      </el-form-item>
      <el-form-item label="是否启用">
        <el-switch v-model="form.enabled" active-text="启用" inactive-text="禁用" />
      </el-form-item>
    </el-form>
    <div style="text-align: right; margin-top: 10px;">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSave">保存</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps<{
  visible: boolean;
  editData: any;
}>();
const emits = defineEmits(['update:visible', 'saved']);

const formRef = ref();
const loading = ref(false);
const form = reactive({
  accountId: '',
  accountName: '',
  account: '',
  password: '',
  mobile: '',
  email: '',
  menuIds: [],
  enabled: true,
});

const rules = {
  accountName: [{ required: true, message: '请输入账号名称', trigger: 'blur' }],
  account: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  mobile: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
};

const menuTree = [
  {
    id: 1,
    label: '产品管理',
    children: [
      { id: 11, label: '游戏管理' },
      { id: 12, label: '厂商管理' },
      { id: 13, label: 'Item 1.3' },
    ],
  },
  {
    id: 2,
    label: '运营管理',
    children: [
      { id: 21, label: 'enter text...' },
    ],
  },
  {
    id: 3,
    label: '财务管理',
    children: [
      { id: 31, label: 'enter text...' },
    ],
  },
];
const treeProps = { label: 'label', children: 'children' };

watch(
  () => props.visible,
  (val) => {
    if (val) {
      Object.assign(form, {
        accountId: props.editData.accountId || '',
        accountName: props.editData.accountName || '',
        account: props.editData.account || '',
        password: props.editData.password || '',
        mobile: props.editData.mobile || '',
        email: props.editData.email || '',
        menuIds: props.editData.menuIds ? [...props.editData.menuIds] : [],
        enabled: props.editData.enabled !== undefined ? props.editData.enabled : true,
      });
    }
  },
  { immediate: true }
);

function handleMenuCheck(checkedKeys: any) {
  form.menuIds = checkedKeys;
}

function handleSave() {
  formRef.value.validate((valid: boolean) => {
    if (!valid) return;
    loading.value = true;
    // TODO: 調用API保存
    setTimeout(() => {
      ElMessage.success('保存成功');
      loading.value = false;
      emits('update:visible', false);
      emits('saved');
    }, 1000);
  });
}
function handleClose() {
  emits('update:visible', false);
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 18px;
}
</style>