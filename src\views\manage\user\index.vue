<script setup lang="tsx">
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>rm, ElTag } from 'element-plus';
import { fetchGetUserList, fetchDeleteUser, fetchBatchDeleteUser } from '@/service/api';
import { $t } from '@/locales';
import { enableStatusRecord, userGenderRecord } from '@/constants/business';
import { useTable, useTableOperate } from '@/hooks/common/table';
import UserOperateDrawer from './modules/user-operate-drawer.vue';
import UserSearch from './modules/user-search.vue';
import { useAuth } from '@/hooks/business/auth';
const { hasAuth } = useAuth();

defineOptions({ name: 'UserManage' });

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetUserList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 20,
    status: undefined,
    user_name: undefined,
    phone: undefined,
    gender: undefined,
  },
  columns: () => [
    { type: 'selection', width: 48 },
    { prop: 'index', label: $t('common.index'), width: 64 },
    { prop: 'user_id', label: '用户ID', minWidth: 120 },
    { prop: 'user_name', label: '用户名', minWidth: 100 },
    { prop: 'phone', label: '手机号', width: 120 },
    {
      prop: 'gender',
      label: '性别',
      width: 100,
      formatter: row => {
        if (row.gender === undefined) {
          return '';
        }
        const tagMap: Record<Api.SystemManage.UserGender, UI.ThemeColor> = {
          0: 'primary',
          1: 'danger'
        };
        const label = ['男', '女'][row.gender];
        return <ElTag type={tagMap[row.gender]}>{label}</ElTag>;
      }
    },
    { prop: 'role_name', label: '角色', Width: 100 },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      formatter: row => {
        if (row.status === undefined) {
          return '';
        }

        const tagMap: Record<Api.Common.EnableStatus, UI.ThemeColor> = {
          1: 'success',
          0: 'warning'
        };

        const label = ['禁用', '启用'][row.status];

        return <ElTag type={tagMap[row.status]}>{label}</ElTag>;
      }
    },
    {
      prop: 'operate',
      label: $t('common.operate'),
      align: 'center',
      fixed: 'right',
      formatter: row => (
        <div class="flex-center">
          {hasAuth(3) && (
            <ElButton 
              type="primary" 
              plain 
              size="small" 
              onClick={() => edit(row.user_id)}
            >
              {$t('common.edit')}
            </ElButton>
          )}
          {hasAuth(2) && (
            <ElPopconfirm 
              title={$t('common.confirmDelete')} 
              onConfirm={() => handleDelete(row.user_id)}
            >
              {{
                reference: () => (
                  <ElButton type="danger" plain size="small">
                    {$t('common.delete')}
                  </ElButton>
                )
              }}
            </ElPopconfirm>
          )}
        </div>
      )
    }
  ]
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  onBatchDeleted,
  onDeleted
  // closeDrawer
} = useTableOperate(data, getData, 'user_id');

async function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.warning('请选择要删除的用户');
    return;
  }
  const { error } = await fetchBatchDeleteUser({
    user_ids: checkedRowKeys.value
  });
  if (!error) {
    onBatchDeleted();
  }
}

async function handleDelete(id: number) {
  const { error } = await fetchDeleteUser({
    user_id: id
  });
  if (!error) {
    onDeleted();
  }
}

function edit(id: number) {
  console.log('id:', id);
  handleEdit(id);
}

</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto">
    <UserSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" >
      <template #table-operation>
        <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading"
            @add="handleAdd"
            @delete="handleBatchDelete"
            @refresh="getData"
          />
      </template>
    </UserSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-60px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="user_id"
          @selection-change="checkedRowKeys = $event.map(row => row.user_id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
      <UserOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
