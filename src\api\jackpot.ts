import request from "./request";

export interface JackpotBalanceResponse {
  status_code: number;
  data: {
    change: number;
    amount: number;
    game_type?: string;
    time?: string;
    previous_balance: number;
  };
}

/**
 * 获取奖池余额
 * @param gameType 游戏类型
 * @param minutesAgo 多少分钟前的数据
 */
export const getJackpotBalance = (minutesAgo: number = 1) => {
  console.log("調用獎池金額 API，參數:", { minutesAgo });
  return request
    .Get<JackpotBalanceResponse["data"]>("/jackpot/amount", {
      params: {
        minutes_ago: minutesAgo,
        _t: Date.now(),
      },
    })
    .then((response) => {
      console.log("獎池金額 API 原始響應:", response);
      return response;
    })
    .catch((error) => {
      console.error("獎池金額 API 調用失敗:", error);
      throw error;
    });
};

// 排行榜相关类型定义
export interface RankConfig {
  id: number;
  game_type: string;
  activity_id: number;
  rank_position: number;
  weight_coeff: number;
  is_enabled: boolean;
  create_by: number;
  update_by: number;
  create_at: string;
  update_at: string;
}

export interface LeaderboardItem {
  user_id: number;
  username: string;
  bet_amount: number;
  code_amount: number;
  rank_position: number;
  prize_amount: number;
  is_rewarded: boolean;
  bonus: number;
  uuid: number;
  points: number;
  rank:number
}

export interface LeaderboardData {
  activity_id: number;
  available_pool: number;
  game_type: string;
  leaderboard: LeaderboardItem[];
  order_by: string;
  page: number;
  platform_rate: number;
  rank_configs: RankConfig[];
  size: number;
  total_pool: number;
  total: number;
}

export interface LeaderboardResponse {
  status_code: number;
  count: number;
  data: LeaderboardData;
}

/**
 * 获取实时排行榜
 * @param page 页码
 * @param size 每页数量
 */
export const getRealTimeLeaderboard = (page: number = 1, size: number = 5) => {
  return request.Get<LeaderboardResponse>("/jackpot/leaderboard/list", {params:{page, size,_t:Date.now()}});
};

/**
 * 获取用户数据
 * @param page 页码
 * @param size 每页数量
 */
export const getUserDetails = (params:any) => {
  return request.Get("/jackpot/user-details", {params:{...params,_t:Date.now()}});
};

/**
 * 获取昨日排行榜
 * @param gameType 游戏类型
 * @param page 页码
 * @param size 每页数量
 */
export const getYesterdayLeaderboard = (page: number = 1, size: number = 5) => {
  return request.Get<LeaderboardData>("/jackpot/leaderboard/completed-period", {
    params: { page, size },
  });
};

export interface ExchangeCodeResponse {
  status_code: number;
  data: {
    message: string;
    amount?: number;
  };
}

/**
 * 兑换码兑换
 * @param redemptionCode 兑换码
 */
export const exchangeCode = (redemptionCode: string) => {
  return request.Post<ExchangeCodeResponse>("/code/exchangeCode", {
    redemption_code: redemptionCode,
  });
};

// 奖池用户积分详情相关类型定义
export interface UserPointsTypeDetail {
  type: number;
  type_name: string;
  type_total_points: number;
  description: string;
  points: number;
  time: number;
}

export interface UserPointsDetailResponse {
  status_code: number;
  count: number;
  data: UserPointsTypeDetail[];
  error_code: string;
}

/**
 * 获取奖池用户积分详情
 * @param page 页码
 * @param size 每页数量
 * @param params 其他查询参数
 */
export const getUserPointsDetail = (page: number = 1, size: number = 20, params?: any) => {
  return request.Get<UserPointsDetailResponse>("/jackpot/user-points-detail", {
    params: { page, size, ...params, _t:Date.now()},
  });
};
