<script setup lang="ts">
import { ref, h, watch } from "vue";
import { useI18n } from "vue-i18n";
import { ElButton, ElInput, ElMessage } from "element-plus";
import type { PddUserActivityItem } from "@/service/api/model/pdd";
import { getPddParticipantsList } from "@/service/api/pdd";
import moment from "moment";
import { getBrazilDate } from "@/utils/format";

const { t } = useI18n();

const props = defineProps<{
  visible: boolean;
  activityId?: number;
}>();

const emit = defineEmits<{
  (e: "update:visible", visible: boolean): void;
  (e: "refresh"): void;
}>();

const internalVisible = ref(props.visible);
const searchUserId = ref<string | undefined>(undefined);
const loading = ref(false);
const data = ref([]);
const pagination = ref({
  total: 0,
  currentPage: 1,
  pageSize: 10,
  "current-change": (page: number) => {
    pagination.value.currentPage = page;
    getData();
  },
  "size-change": (size: number) => {
    pagination.value.pageSize = size;
    pagination.value.currentPage = 1;
    getData();
  },
});

const columns = [
  { type: "index", label: t("common.index"), minWidth: 60 },
  { prop: "user_id", label: "用户ID", minWidth: 100 },
  { prop: "username", label: "用户昵称", minWidth: 100 },
  { prop: "start_time", label: "开始时间", minWidth: 160,
    formatter: (row: any) => {
      return row.start_time
        ? moment(getBrazilDate(row.start_time)).format("YYYY-MM-DD HH:mm:ss")
        : "-";
    },
  },
  { prop: "participant_count", label: "参与人数", minWidth: 90 },
  { prop: "register_count", label: "注册人数", minWidth: 90 },
  { prop: "deposit_count", label: "充值人数", minWidth: 90 },
  { prop: "deposit_amount", label: "充值金额 (R$)", minWidth: 120 },
  { prop: "bonus", label: "奖金 ($)", minWidth: 90 },
  {
    prop: "status",
    label: "状态",
    minWidth: 80,
    formatter: (row: any) => (row.status === 1 ? "已完成" : "未完成"),
  },
  {
    prop: "operation",
    label: "操作",
    minWidth: 80,
    formatter: (row: any) =>
      row.status === 0
        ? h(
            ElButton,
            {
              type: "primary",
              link: true,
              onClick: () => handleComplete(row),
            },
            () => "完成",
          )
        : "已完成",
  },
];

const getData = async () => {
  if (!props.activityId) return;

  loading.value = true;
  try {
    const { data: response } = await getPddParticipantsList(props.activityId, {
      page: pagination.value.currentPage,
      size: pagination.value.pageSize,
      user_id: searchUserId.value,
    });

    if (response?.data) {
      data.value = response.data.result || [];
      pagination.value.total =  pagination.value.total ? pagination.value.total:response.data.count || 0;
    }
  } catch (error) {
    console.error("Failed to fetch participants list:", error);
  } finally {
    loading.value = false;
  }
};

const handleComplete = async (row: PddUserActivityItem) => {
  try {
    // TODO: 調用完成 API
    await new Promise((resolve) => setTimeout(resolve, 1000)); // 模擬 API 調用
    ElMessage.success("操作成功");
    emit("refresh");
    getData();
  } catch (error) {
    ElMessage.error("操作失敗");
  }
};

watch(
  () => props.visible,
  (newVal) => {
    internalVisible.value = newVal;
    if (newVal && props.activityId) {
      getData();
    } else {
      data.value = [];
      pagination.value.currentPage = 1;
      pagination.value.total = 0;
      searchUserId.value = undefined;
    }
  },
);

watch(internalVisible, (newVal) => {
  emit("update:visible", newVal);
});

const handleSearch = () => {
  pagination.value.currentPage = 1;
  getData();
};

const handleReset = () => {
  searchUserId.value = undefined;
  pagination.value.currentPage = 1;
  getData();
};

const handleClose = () => {
  internalVisible.value = false;
};
</script>

<template>
  <ElDialog
    v-model="internalVisible"
    title="参与人员列表"
    width="80%"
    @close="handleClose"
  >
    <div class="flex items-center gap-10px mb-10px">
      <ElInput
        v-model="searchUserId"
        placeholder="用户ID"
        style="width: 200px"
        clearable
      />
      <ElButton type="primary" @click="handleSearch">搜索</ElButton>
      <ElButton @click="handleReset">重置</ElButton>
    </div>
    <div class="h-[500px]">
      <ElTable v-loading="loading" height="100%" :data="data">
        <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
      </ElTable>
    </div>
    <div class="mt-20px flex justify-start">
      <ElPagination
        v-if="pagination.total"
        layout="total,prev,pager,next,sizes"
        :total="pagination.total"
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        @current-change="pagination['current-change']"
        @size-change="pagination['size-change']"
      />
    </div>
  </ElDialog>
</template>

<style scoped>
/* 可以根據需要添加樣式 */
</style>
