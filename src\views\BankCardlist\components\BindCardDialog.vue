<script setup lang="ts">
import {
  ref,
  defineProps,
  defineEmits,
  reactive,
  toRefs,
  computed,
  watchEffect,
} from "vue";
import { showSuccess, showError } from "@/utils/toast";
import { paymentAccount } from "@/api/user";
import store from "@/store";

interface Props {
  show: boolean;
  bankObj?: {
    cpf: string;
    id: number;
    is_owner: number;
    name: string;
    pix: string;
  };
  type: string;
}
const formRef = ref();
const props = defineProps<Props>();
const emit = defineEmits(["update:show"]);
const userInfo = computed(() => store.state.auth?.user || {});

const isLoading = ref(false);

const handleClose = (e) => {
  state.form = {
    cpf: "",
    pix: "",
    name: "",
  };
  emit("update:show", e);
};

const state = reactive({
  form: {
    cpf: "",
    pix: "",
    name: "",
  },
  rules: {
    name: [
      (value) => {
        if (value) return true;

        return "Nome é obrigatório";
      },
      (value) => {
        if (value.length <= 200) return true;

        return "Máximo de 200 caracteres";
      },
    ],
    cpf: [
      (value) => {
        if (value) return true;

        return "cpf é obrigatório";
      },
      (value) => {
        if (value.length <= 200) return true;

        return "Máximo de 200 caracteres";
      },
    ],
    pix: [
      (value) => {
        if (value) return true;

        return "pix é necessário";
      },
      (value) => {
        if (value.length <= 200) return true;

        return "Máximo de 200 caracteres";
      },
    ],
  },
});
async function handleSubmit() {
  const { valid } = await formRef.value.validate();

  if (valid) {
    isLoading.value = true;
    console.log(state.form);
    paymentAccount(state.form)
      .then((res) => {
        showSuccess("Encadernação de cartão bem-sucedida");
        handleClose(true);
      })
      .finally(() => {
        isLoading.value = false;
      });
  }
}

watchEffect(() => {
  if (props.show && props.type === "edit") {
    state.form = props.bankObj;
  } else if (props.show) {
    state.form.cpf = userInfo.value.CPF;
  }
});
const { form, rules } = toRefs(state);
</script>

<template>
  <v-dialog
    :model-value="show"
    @update:model-value="emit('update:show', $event)"
    width="366"
    @click:outside="handleClose(false)"
    class="bonus-dialog"
  >
    <v-btn
      icon="mdi-close"
      variant="text"
      size="small"
      class="close-btn"
      @click="handleClose(false)"
    />
    <v-card class="bonus-card pa-4">
      <div class="dialog-content">
        <div class="dialog-header">
          <div class="dialog-title">Vincular cartao bancário</div>
        </div>
        <div class="dialog-tip">
          Por favor, preencha as informações corretas, caso contrário a retirada
          falhará
        </div>
        <v-form @submit.prevent ref="formRef" :disabled="props.type === 'edit'">
          <v-text-field
            v-model="form.cpf"
            placeholder="Por favor, insira o CPF"
            :rules="rules.cpf"
            :counter="true"
            :counter-value="200"
            :disabled="userInfo.CPF.length > 0"
          >
            <template v-slot:prepend-inner>
              <img
                src="@/assets/images/cardID.png"
                class="tip-icon"
                alt="Tip"
              />
            </template>
          </v-text-field>
          <v-text-field
            v-model="form.pix"
            placeholder="Por favor insira pix"
            :rules="rules.pix"
            :counter="true"
            :counter-value="200"
          >
            <template v-slot:prepend-inner>
              <img src="@/assets/images/bank.png" class="tip-icon" alt="Tip" />
            </template>
          </v-text-field>
          <v-text-field
            v-model="form.name"
            placeholder="Por favor, insira seu nome"
            :rules="rules.name"
            :counter="true"
            :counter-value="200"
          >
            <template v-slot:prepend-inner>
              <img src="@/assets/images/name.png" class="tip-icon" alt="Tip" />
            </template>
          </v-text-field>
          <div class="dialog-footer">
            <v-btn
              :disabled="props.type === 'edit'"
              block
              color="primary"
              :loading="isLoading"
              class="redeem-btn"
              @click="handleSubmit"
            >
              Vincular cartao bancário
            </v-btn>
          </div>
        </v-form>
      </div>
    </v-card>
  </v-dialog>
</template>

<style lang="scss" scoped>
.bonus-dialog {
  :deep(.v-overlay__content) {
    align-items: center;
    padding: 24px;
    background: #1e2332;
    border-radius: 20px;
  }
  .dialog-tip {
    color: red;
    font-size: 14px;
    line-height: 1;
    margin-bottom: 8px;
  }
}

.bonus-card {
  background: #1e2332;
  border-radius: 12px;
  margin-top: 30px;
  width: 100%;
}

.close-btn {
  position: absolute;
  height: 22px;
  width: 22px;
  background: #c9cad8 !important;
  color: #2b324d;
  right: 24px;
  z-index: 9999;
  :deep(.v-icon) {
    font-size: 18px;
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
}

.dialog-header {
  text-align: center;
}

.dialog-title {
  color: #fff;
  font-size: 15px;
  font-weight: 500;
}

.dialog-subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.redeem-input {
  :deep(.v-field) {
    height: 46px;
    line-height: 46px;
    border-radius: 8px;
    background: #2b324d;
    .v-field__input {
      color: #fff;
      font-size: 16px;
      padding: 0 16px;
      height: 46px;
      line-height: 46px;
    }
    .v-field__outline {
      display: none;
    }
  }
}

.dialog-footer {
  margin-top: 8px;
}

.redeem-btn {
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  text-transform: none;
  letter-spacing: 0.5px;
  background: linear-gradient(0deg, #c9b737, #2abb27);
  border-radius: 22px;
}
.tip-icon {
  width: 30px;
}
</style>
