<template>
  <div class="search-wrapper">
    <ElForm ref="formRef" :model="model">
      <ElRow :gutter="16">
        <ElCol :span="6">
          <ElFormItem label="用户ID" prop="uuid">
            <ElInput v-model="model.uuid" placeholder="用户ID" clearable style="width: 100%" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="是否首提" prop="withdrawal_count">
            <ElSelect v-model="model.withdrawal_count" placeholder="是否首提" clearable style="width: 100%">
              <ElOption v-for="item in isFirstOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="交易号" prop="transaction_number">
            <ElInput v-model="model.transaction_number" placeholder="交易号" clearable style="width: 100%" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="支付方式" prop="payment_method">
            <ElSelect v-model="model.payment_method" placeholder="支付方式" clearable style="width: 100%">
              <ElOption v-for="item in paymentChannelOptions" :key="item.value" :label="item.label"
                :value="item.value" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="钱包ID" prop="wallet_id">
            <ElInput v-model="model.wallet_id" placeholder="钱包ID" clearable style="width: 100%" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="6">
          <ElFormItem label="账户号码" prop="account_num">
            <ElInput v-model="model.account_num" placeholder="账户号" clearable style="width: 100%" />
          </ElFormItem>
        </ElCol>
        <ElCol :span="4">
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElCol>
        <ElCol :span="8">
          <div class="header-operation">
            <slot name="table-operation"></slot>
          </div>
        </ElCol>
      </ElRow>
    </ElForm>
  </div>
</template>

<script setup lang="ts">
import { ref,onMounted } from 'vue';
import { ElForm } from 'element-plus';
import { getWithdrawalPaymentMethods } from '@/service/api/withdrawal';

interface Props {
  model: Record<string, any>;
}

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const formRef = ref();

const isFirstOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 2 }
];

const paymentChannelOptions = ref([]);

function handleReset() {
  formRef.value?.resetFields();
  emit('reset');
}

function handleSearch() {
  emit('search');
}

onMounted(async () => {
  const {response} = await getWithdrawalPaymentMethods({});
  if(response?.data.status_code === 200){
    paymentChannelOptions.value = response?.data.data.map((item: any) => ({
      label: item.channel_name,
      value: item.channel_type
    }));
  }
});
</script>

<style lang="scss" scoped>
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 18px;
    margin-right: 18px;
  }
}
</style>
