// 设备类型枚举
export enum DeviceType {
  PC = 'pc',
  MOBILE = 'mobile',
  TABLET = 'tablet'
}

// 设备配置接口
export interface DeviceConfig {
  type: DeviceType;
  width: number;
  height: number;
  isMobile: boolean;
  isTouch: boolean;
  platform: string;
  orientation?: 'portrait' | 'landscape';
}

// 游戏设备参数接口
export interface GameDeviceParams {
  deviceType: DeviceType;
  platform: string;
  orientation: string;
  screenWidth: number;
  screenHeight: number;
  isApp?: boolean;
} 