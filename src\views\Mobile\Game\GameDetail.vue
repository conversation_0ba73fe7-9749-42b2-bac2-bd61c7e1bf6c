<template>
    <div class="mobile-game-detail">
        <!-- Loading Spinner -->
        <div v-if="loading" class="loading-container">
            <img
                src="@/assets/images/chip.gif"
                alt="loading"
                style="width: 100px; height: 100px"
            />
        </div>

        <!-- Game Frame -->
        <div
            class="game-frame-container"
            :style="{ height: iframeHeight + 'px' }"
        >
            <div class="game-header">
                <v-btn
                    icon="mdi-arrow-left"
                    variant="text"
                    @click="goBack"
                    style="color: #ffdf00"
                ></v-btn>
                <div
                    class="game-header-content"
                    style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        width: 100%;
                    "
                >
                    <img
                        src="@/assets/images/h5/bottom-logo.png"
                        alt="Logo"
                        class="logo-image"
                    />
                    <div class="balance-btn" v-show="gameType !== 3">
                        <div class="balance-wrapper">
                            <div class="balance-wrapper">
                                <img
                                    src="@/assets/images/purse-icon.png"
                                    alt="Coin"
                                    class="coin-icon"
                                />
                                <img
                                    src="@/assets/images/refresh.png"
                                    alt="refresh"
                                    class="refresh-icon"
                                    :class="{ 'rotate-animation': isRotating }"
                                />
                            </div>
                            <span class="balance-amount">
                                {{
                                    formatNumber(
                                        ((userInfo?.userWallet?.total_balance ||
                                            0) / 100) as number
                                    )
                                }}
                            </span>
                            <img
                                src="@/assets/images/top-up-icon.png"
                                class="top-up-btn"
                                @click="router.push('/records')"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Game Frame -->
            <iframe
                v-if="gameUrl"
                ref="gameFrame"
                :src="gameUrl"
                :class="['game-frame', { loading: loading }]"
                :style="{ height: iframeHeight - 50 + 'px' }"
                frameborder="0"
                sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"
                @load="handleIframeLoad"
                @error="handleIframeError"
            ></iframe>
            <iframe
                v-if="htmlContent"
                ref="gameFrameHtml"
                :srcdoc="htmlContent"
                :class="['game-frame', { loading: loading }]"
                :style="{ height: iframeHeight - 50 + 'px' }"
                frameborder="0"
                sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"
                @load="handleIframeLoad"
                @error="handleIframeError"
            ></iframe>
            <div v-if="!gameUrl && !htmlContent" class="error-container">
                <div class="center-imgs">
                    <img src="@/assets/images/h5/bottom-title.png" alt="" />
                    <img src="@/assets/images/h5/bottom-logo.png" alt="" />
                </div>
                <CommonDialog
                    :show="dialogShow"
                    :dialogObj="dialogObj"
                    @update:show="goBack"
                ></CommonDialog>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, onBeforeUnmount, watch } from "vue";
import { useRoute, useRouter, onBeforeRouteLeave } from "vue-router";
import { useStore } from "vuex";
import {
    getGameUrl,
    startGameSession,
    startGameSessionStartv1,
    startGameSessionStartv2,
    startGameSessionStartv3,
    endGameSession,
    endv2GameSession,
    endv3GameSession,
    sessionTransfer,
    setStartGameSeesion,
} from "@/api/game";
import { showError, showSuccess, showWarning } from "@/utils/toast";
import CommonDialog from "@/components/CommonDialog.vue";
import { fixURL } from "@/utils/url";
import { formatNumber } from "@/utils/index";
import websocketService, { WebSocketState } from "@/utils/websocket";
import { getBrazilTime } from "@/utils/format";

// 定义API响应类型
interface GameSessionResponse {
    code?: number;
    game_type?: number;
    have_balance?: number;
    message?: string;
}

interface GameStartResponse {
    session_key?: string;
    response?: string;
    gameurl?: string;
    game_name?: string;
    message?: string;
}

interface SessionTransferResponse {
    session?: string;
}

interface GameUrlResponse {
    payload?: {
        game_launch_url?: string;
        player_name?: string;
    };
    url: string;
    game_name: string;
}

const route = useRoute();
const router = useRouter();
const store = useStore();
const isRotating = ref(false);
const loading = ref(true);
const error = ref("");
const gameUrl = ref("");
const htmlContent = ref("");
const gameTitle = ref("");

const gameFrame = ref<HTMLIFrameElement | null>(null);
const gameFrameHtml = ref<HTMLIFrameElement | null>(null);
const sessionKey = ref<string | SessionTransferResponse>();
const gameId = ref();
const gameType = ref();
const dialogShow = ref(false);
const setStartGameSeesionTimer = ref<NodeJS.Timeout | null>(null);
const iframeHeight = ref(0);
const isIOS = ref(false);
const isAndroid = ref(false);
const isMobile = ref(false);
const resizeTimeout = ref<NodeJS.Timeout | null>(null);
const isFullscreen = ref(false);
const dialogObj = {
    id: 1,
    title: "lembrar",
    content:
        "Ainda sem saldo, clique no botão para ir para a página de recarga",
    image_url: "",
    hyperlink:
        window.location.protocol + "//" + window.location.host + "/m/records",
    show_type: 1,
    weight: 1,
    created_at: getBrazilTime().toISOString(),
    confirm_text: "",
};

// 计算属性：获取用户信息
interface UserWallet {
    total_balance: number;
}

interface User {
    nickname: string;
    uuid: string;
    avatar: string;
}

interface UserInfo {
    user: User | null;
    userWallet: UserWallet | null;
    unreadNum: number;
}

const userInfo = ref<UserInfo>({
    user: null,
    userWallet: null,
    unreadNum: 0,
});
// 监听用户信息变化
watch(
    () => store.state.auth,
    (newVal) => {
        if (newVal.user) {
            userInfo.value = {
                user: newVal.user,
                userWallet: newVal.userWallet,
                unreadNum: newVal.unreadNum,
            };
            isRotating.value = true;
            setTimeout(() => {
                isRotating.value = false;
            }, 1000);
        } else {
            userInfo.value = {
                user: null,
                userWallet: null,
                unreadNum: 0,
            };
        }
    },
    { immediate: true, deep: true }
);

// 检测移动端设备
const detectMobileDevice = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    isIOS.value = /iphone|ipad|ipod/.test(userAgent);
    isAndroid.value = /android/.test(userAgent);
    isMobile.value =
        isIOS.value || isAndroid.value || /mobile|tablet/.test(userAgent);

    console.log("设备检测:", {
        isIOS: isIOS.value,
        isAndroid: isAndroid.value,
        isMobile: isMobile.value,
        userAgent: navigator.userAgent,
    });
};
// 1. 新增退出全屏方法
const exitFullscreen = () => {
    try {
        // 只在处于全屏状态时才尝试退出
        if (
            document.fullscreenElement ||
            (document as any).webkitFullscreenElement ||
            (document as any).mozFullScreenElement ||
            (document as any).msFullscreenElement
        ) {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if ((document as any).webkitExitFullscreen) {
                (document as any).webkitExitFullscreen();
            } else if ((document as any).mozCancelFullScreen) {
                (document as any).mozCancelFullScreen();
            } else if ((document as any).msExitFullscreen) {
                (document as any).msExitFullscreen();
            }
            isFullscreen.value = false;
            // iOS Safari 特殊处理
            if (isIOS.value) {
                document.body.style.position = "";
                document.body.style.top = "";
                document.body.style.left = "";
                document.body.style.width = "";
                document.body.style.height = "";
            }
        }
    } catch (error) {
        console.error("退出全屏失败:", error);
    }
};
// 进入全屏模式
const enterFullscreen = async () => {
    try {
        const element = document.documentElement;

        // iOS Safari 特殊处理
        if (isIOS.value) {
            // iOS Safari 不支持全屏API，使用屏幕方向锁定和隐藏UI

            // 尝试隐藏iOS Safari的UI
            if ((document as any).webkitHidden !== undefined) {
                // 使用CSS隐藏地址栏等UI元素
                document.body.style.position = "fixed";
                document.body.style.top = "0";
                document.body.style.left = "0";
                document.body.style.width = "100%";
                document.body.style.height = "100%";
            }

            isFullscreen.value = true;
            console.log("iOS Safari: 已进入类全屏模式");
            return;
        }

        // Android Chrome 和其他浏览器
        if (isAndroid.value || !isIOS.value) {
            if (element.requestFullscreen) {
                await element.requestFullscreen();
            } else if ((element as any).webkitRequestFullscreen) {
                await (element as any).webkitRequestFullscreen();
            } else if ((element as any).mozRequestFullScreen) {
                await (element as any).mozRequestFullScreen();
            } else if ((element as any).msRequestFullscreen) {
                await (element as any).msRequestFullscreen();
            }

            isFullscreen.value = true;
            console.log("Android/其他设备: 已进入全屏模式");
        }
    } catch (error) {
        console.error("进入全屏模式失败:", error);
    }
};

// 检查是否处于全屏状态
const checkFullscreenState = () => {
    const isFullscreenNow = !!(
        document.fullscreenElement ||
        (document as any).webkitFullscreenElement ||
        (document as any).mozFullScreenElement ||
        (document as any).msFullscreenElement
    );

    isFullscreen.value = isFullscreenNow;
    return isFullscreenNow;
};

// 监听全屏状态变化
const handleFullscreenChange = () => {
    checkFullscreenState();
    // 全屏状态改变后重新计算高度
    setTimeout(() => {
        calculateIframeHeight();
    }, 100);
};

// 获取安全的视口高度（兼容iOS Safari）
const getSafeViewportHeight = () => {
    if (isIOS.value) {
        // iOS Safari 特殊处理
        const visualViewport = (window as any).visualViewport;
        if (visualViewport) {
            return visualViewport.height;
        }
        // 备用方案：使用 document.documentElement.clientHeight
        return document.documentElement.clientHeight;
    }

    // 其他设备使用 window.innerHeight
    return window.innerHeight;
};

// 计算可视化高度（兼容iOS Safari）
const calculateIframeHeight = () => {
    // 获取安全的视口高度
    const viewportHeight = getSafeViewportHeight();

    // 根据全屏状态计算高度
    if (isFullscreen.value) {
        // 全屏时头部隐藏，使用完整视口高度
        iframeHeight.value = viewportHeight;
    } else {
        // 非全屏时头部显示，减去头部高度
        const headerHeight = 0;
        iframeHeight.value = viewportHeight - headerHeight;
    }

    console.log("计算iframe高度:", {
        viewportHeight,
        headerHeight: isFullscreen.value ? 0 : 0,
        finalHeight: iframeHeight.value,
        isIOS: isIOS.value,
        isAndroid: isAndroid.value,
        isFullscreen: isFullscreen.value,
    });
};

// 开始游戏会话
const startGame = async () => {
    gameId.value = route.params.id;
    try {
        const userInfo = store.state.auth.user;
        if (!userInfo || !userInfo.id) {
            showError("Por favor, faça login primeiro");
            return;
        }
        console.log(route);
        const params = {
            user_id: userInfo.id,
            game_uid: route.params.id as string,
            id: Number(route.query.uuid),
        };

        const response = (await startGameSession(
            params
        )) as GameSessionResponse;

        if (response?.code === 203) {
            showWarning("Você já está jogando", 2000);
            setTimeout(() => {
                goBack();
            }, 2000);

            return;
        }
        gameType.value = response?.game_type;
        if (response?.have_balance === 2) {
            loading.value = false;
            dialogShow.value = true;
            return;
        }
        if (response?.game_type === 2) {
            const res = (await startGameSessionStartv2(
                params
            )) as GameStartResponse;

            htmlContent.value = res.response || "";
            loading.value = false;
        } else if (response?.game_type === 3) {
            const session = (await sessionTransfer({
                game_uid: route.params.id as string,
            })) as SessionTransferResponse;
            const res = (await startGameSessionStartv3(
                params
            )) as GameStartResponse;
            if (session) {
                sessionKey.value = session || "";
                gameUrl.value = fixURL(res.gameurl || "") || "";
                gameTitle.value = (res.game_name as string) || "Jogo";
            } else {
                error.value = res.message || "Falha ao iniciar o jogo";
            }
            loading.value = false;
        } else {
            const res = (await startGameSessionStartv1(
                params
            )) as GameStartResponse;
            if (res.session_key) {
                // 会话创建成功，继续加载游戏
                sessionKey.value = res.session_key;
                loadGame();
            } else {
                error.value = res.message || "Falha ao iniciar o jogo";
            }
        }
        localStorage.setItem(
            "gameParams",
            JSON.stringify({
                type: gameType.value,
                sessionKey: sessionKey.value,
                gameId: gameId.value,
            })
        );
    } catch (err) {
        console.error("Failed to start game session:", err);
        error.value = "Falha ao iniciar o jogo, tente novamente mais tarde";
    }
};

// 加载游戏
const loadGame = async () => {
    try {
        const userInfo = store.state.auth.user;
        if (!userInfo || !userInfo.id) {
            showError("Por favor, faça login primeiro");
            return;
        }

        const params = {
            game_id: route.params.id as string,
            // game_uid: route.params.id as string,
            user_id: userInfo.id,
            platform: 2,
            home_url: window.location.origin + "/mobile/game",
        };

        const response = (await getGameUrl(params)) as GameUrlResponse;
        if (response) {
            // gameUrl.value = response.url;
            // gameTitle.value = (response.game_name as string) || "Jogo";
            gameUrl.value = response?.payload?.game_launch_url || "";
            gameTitle.value =
                (response?.payload?.player_name as string) || "Jogo";
        } else {
            error.value = "Falha ao carregar o jogo";
        }
    } catch (err) {
        console.error("Failed to load game:", err);
        error.value = "Falha ao carregar o jogo, tente novamente mais tarde";
    } finally {
        loading.value = false;
    }
};
// 返回上一页
let goBackLock = false;
const goBack = () => {
    router.replace("/"); // 或你想要的首页路径
};

// 防抖函数
const debounce = (func: Function, delay: number) => {
    return (...args: any[]) => {
        if (resizeTimeout.value) {
            clearTimeout(resizeTimeout.value);
        }
        resizeTimeout.value = setTimeout(() => {
            func.apply(null, args);
        }, delay);
    };
};

// 监听窗口大小变化（防抖）
const handleResize = debounce(() => {
    calculateIframeHeight();
}, 100);

// 监听iOS Safari视口变化（地址栏显示/隐藏）
const handleVisualViewportChange = debounce(() => {
    if (isIOS.value && (window as any).visualViewport) {
        calculateIframeHeight();
    }
}, 100);

// 监听方向变化
const handleOrientationChange = () => {
    // 延迟处理，等待设备完成旋转
    setTimeout(() => {
        calculateIframeHeight();
    }, 300);
};

// 监听页面可见性变化（iOS Safari 特殊处理）
// const handleVisibilityChange = () => {

// };

// 處理 iframe 加載完成
const handleIframeLoad = () => {
    console.log("遊戲 iframe 加載完成");
    loading.value = false;

    // 计算iframe高度
    calculateIframeHeight();

    // 游戏加载完成后自动进入全屏模式
    setTimeout(() => {
        enterFullscreen();
    }, 500); // 延迟500ms确保游戏完全加载
};

// 发送游戏状态更新到WebSocket
const sendGameStatusUpdate = async () => {
    try {
        // 确保WebSocket已连接
        if (websocketService.getIsConnected()) {
            const message = {
                type: "game_status",
                data: {
                    in_game: true,
                },
            };

            const success = websocketService.send(message);
            if (success) {
                console.log("游戏状态更新已发送到WebSocket");
            } else {
                console.warn("WebSocket发送游戏状态更新失败");
            }
        } else {
            await websocketService.forceReconnect();
            const message = {
                type: "game_status",
                data: {
                    in_game: true,
                },
            };

            const success = websocketService.send(message);
            if (success) {
                console.log("游戏状态更新已发送到WebSocket");
            } else {
                console.warn("WebSocket发送游戏状态更新失败");
            }
            // console.warn("WebSocket未连接，无法发送游戏状态更新");
        }
    } catch (error) {
        console.error("发送游戏状态更新时出错:", error);
    }
};
const isSend = ref(false);
// 发送游戏退出状态更新到WebSocket
const sendGameStatusExit = async () => {
    if (isSend.value) return;
    isSend.value = true;
    setTimeout(() => {
        isSend.value = false;
    }, 1000);
    try {
        // 确保WebSocket已连接
        if (websocketService.getIsConnected()) {
            const message = {
                type: "game_status",
                data: {
                    in_game: false,
                },
            };

            const success = websocketService.send(message);
            if (success) {
                console.log("游戏退出状态更新已发送到WebSocket");
            } else {
                console.warn("WebSocket发送游戏退出状态更新失败");
            }
        } else {
            await websocketService.forceReconnect();
            const message = {
                type: "game_status",
                data: {
                    in_game: false,
                },
            };

            const success = websocketService.send(message);
            if (success) {
                console.log("游戏退出状态更新已发送到WebSocket");
            } else {
                console.warn("WebSocket发送游戏退出状态更新失败");
            }
            // console.warn("WebSocket未连接，无法发送游戏退出状态更新");
        }
    } catch (error) {
        console.error("发送游戏退出状态更新时出错:", error);
    }
};

// 處理 iframe 加載錯誤
const handleIframeError = (err: Event) => {
    console.error("遊戲 iframe 加載失敗:", err);
    error.value = "O jogo não carregou, tente novamente mais tarde";
    loading.value = false;
};
// 结束游戏会话
const endGame = async (sessionKey: string) => {
    console.log("Ending game session...", sessionKey);
    try {
        if (sessionKey) {
            await endGameSession({
                session_key: sessionKey,
            });
        }
        setTimeout(() => {
            store.dispatch("auth/fetchUserInfo");
        }, 2000);
    } catch (err) {
        console.error("Failed to end game session:", err);
    }
};
// 结束游戏2会话
const endv2Game = async (gameId: string | number) => {
    try {
        await endv2GameSession({ game_uid: gameId });
        setTimeout(() => {
            store.dispatch("auth/fetchUserInfo");
        }, 2000);
    } catch (err) {
        console.error("Failed to end game session:", err);
    }
};
// 结束游戏3会话
const endv3Game = async (gameId: string | number, sessionKey: string) => {
    try {
        await endv3GameSession({
            game_uid: gameId,
            session_key: sessionKey,
        });
        setTimeout(() => {
            store.dispatch("auth/fetchUserInfo");
        }, 2000);
    } catch (err) {
        console.error("Failed to end game session:", err);
    }
};
let hasSentEndGame = false;

function sendEndGameSessionBeacon() {
    if (hasSentEndGame) {
        console.log("游戏结束接口已发送，防重处理生效");
        return;
    }
    hasSentEndGame = true;

    // showSuccess("游戏结束接口监听成功");

    const gameParams = localStorage.getItem("gameParams");
    if (!gameParams) return;

    const { type, sessionKey, gameId } = JSON.parse(gameParams);

    if (type === 2) {
        endv2Game(gameId);
    } else if (type === 3) {
        endv3Game(gameId, sessionKey);
    } else {
        endGame(sessionKey);
    }
    sendGameStatusExit();
    localStorage.removeItem("gameParams");
}
onMounted(async () => {
    // 检测移动端设备
    detectMobileDevice();

    startGame();
    // 发送游戏状态更新到WebSocket
    sendGameStatusUpdate();

    // 初始化计算iframe高度
    calculateIframeHeight();

    // 延迟再次计算，确保iOS Safari地址栏状态稳定
    setTimeout(() => {
        calculateIframeHeight();
    }, 100);

    // 添加窗口大小变化监听器
    window.addEventListener("resize", handleResize);

    // 添加iOS Safari视口变化监听器
    if (isIOS.value && (window as any).visualViewport) {
        (window as any).visualViewport.addEventListener(
            "resize",
            handleVisualViewportChange
        );
    }

    // 添加方向变化监听器
    window.addEventListener("orientationchange", handleOrientationChange);

    // 添加页面卸载事件监听器
    window.addEventListener("beforeunload", handleBeforeUnload);
    window.addEventListener("pagehide", handleBeforeUnload); // 新增
    document.addEventListener("visibilitychange", handleVisibilityChange);

    // 添加全屏状态变化监听器
    document.addEventListener("fullscreenchange", handleFullscreenChange);
    document.addEventListener("webkitfullscreenchange", handleFullscreenChange);
    document.addEventListener("mozfullscreenchange", handleFullscreenChange);
    document.addEventListener("MSFullscreenChange", handleFullscreenChange);

    // 移动端优化：防止屏幕旋转
});

onBeforeRouteLeave((to, from, next) => {
    if (localStorage.getItem("gameParams")) {
        sendEndGameSessionBeacon();
    } else {
        sendGameStatusExit();
    }
    next();
});

onBeforeUnmount(async () => {
    if (localStorage.getItem("gameParams")) {
        // 使用 sendBeacon 通知后端
        sendEndGameSessionBeacon();
    } else {
        // 如果没有游戏参数，也发送游戏退出状态
        sendGameStatusExit();
    }

    // 只在页面可见时退出全屏，避免报错
    if (document.visibilityState === "visible") {
        await exitFullscreen();
    }
    // 清理防抖定时器
    if (resizeTimeout.value) {
        clearTimeout(resizeTimeout.value);
        resizeTimeout.value = null;
    }

    // 移除窗口大小变化监听器
    window.removeEventListener("resize", handleResize);

    // 移除iOS Safari视口变化监听器
    if (isIOS.value && (window as any).visualViewport) {
        (window as any).visualViewport.removeEventListener(
            "resize",
            handleVisualViewportChange
        );
    }

    // 移除方向变化监听器
    window.removeEventListener("orientationchange", handleOrientationChange);

    // 移除页面卸载事件监听器
    window.removeEventListener("beforeunload", handleBeforeUnload);
    window.removeEventListener("pagehide", handleBeforeUnload); // 新增
    document.removeEventListener("visibilitychange", handleVisibilityChange);

    // 移除全屏状态变化监听器
    document.removeEventListener("fullscreenchange", handleFullscreenChange);
    document.removeEventListener(
        "webkitfullscreenchange",
        handleFullscreenChange
    );
    document.removeEventListener("mozfullscreenchange", handleFullscreenChange);
    document.removeEventListener("MSFullscreenChange", handleFullscreenChange);
});

// 处理页面卸载事件
const handleBeforeUnload = (event: BeforeUnloadEvent) => {
    console.log("beforeunload called");
    sendEndGameSessionBeacon();
};
const handleVisibilityChange = () => {
    if (document.hidden) {
        console.log("visibilitychange: hidden");
        sendEndGameSessionBeacon();
    }
    if (isIOS.value && document.hidden) {
        // iOS Safari 页面隐藏时可能需要特殊处理
        console.log("页面隐藏，iOS Safari 特殊处理");
    } else if (isIOS.value && !document.hidden) {
        // iOS Safari 页面重新显示时重新计算
        setTimeout(() => {
            calculateIframeHeight();
        }, 200);
    }
};
</script>

<style lang="scss" scoped>
.mobile-game-detail {
    width: 100%;
    min-height: 100vh;
    color: white;
    display: flex;
    flex-direction: column;
    background: #0d3b1e;
    position: relative;

    // iOS Safari 兼容性处理
    -webkit-overflow-scrolling: touch;
    -webkit-user-select: none;
    user-select: none;

    // 防止iOS Safari缩放
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
    }

    .game-frame-container {
        width: 100%;
        overflow: hidden;
        position: relative;

        .game-header {
            height: 50px;
            z-index: 1000;
            color: white;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            padding: 0px 12px 0 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;

            &.header-hidden {
                height: 0;
                min-height: 0;
                padding: 0;
                opacity: 0;
                pointer-events: none;
            }

            .game-title {
                flex: 1;
                margin: 0 12px;
                font-size: 1rem;
                font-weight: 500;
                text-align: center;
                transition: opacity 0.3s ease;
            }
        }

        .game-frame {
            width: 100%;
            border: none;
            background: linear-gradient(
                135deg,
                #0d3b1e 0%,
                #0a2d17 50%,
                #061f10 100%
            );
            position: relative;
            overflow: hidden;

            // 添加默认背景图案
            &::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-image: radial-gradient(
                        circle at 20% 80%,
                        rgba(13, 59, 30, 0.3) 0%,
                        transparent 50%
                    ),
                    radial-gradient(
                        circle at 80% 20%,
                        rgba(10, 45, 23, 0.3) 0%,
                        transparent 50%
                    ),
                    radial-gradient(
                        circle at 40% 40%,
                        rgba(6, 31, 16, 0.2) 0%,
                        transparent 50%
                    );
                pointer-events: none;
                z-index: 1;
            }

            // 移动端兼容性处理
            -webkit-overflow-scrolling: touch;
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;

            // 防止iOS Safari缩放
            -webkit-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;

            // 加载状态时的样式
            &.loading {
                background: linear-gradient(
                    135deg,
                    #0d3b1e 0%,
                    #0a2d17 50%,
                    #061f10 100%
                );

                &::after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 40px;
                    height: 40px;
                    border: 3px solid rgba(255, 255, 255, 0.3);
                    border-top: 3px solid #ffdf00;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    z-index: 2;
                }
            }
        }

        @keyframes spin {
            0% {
                transform: translate(-50%, -50%) rotate(0deg);
            }
            100% {
                transform: translate(-50%, -50%) rotate(360deg);
            }
        }
    }
}

.back-btn {
    background: linear-gradient(0deg, #c9b737, #2abb27);
    border-radius: 15px;
}

.logo-image {
    height: 32px;
    width: auto;
    object-fit: contain;
    cursor: pointer;
}

.balance-btn {
    background: #333948;
    border-radius: 20px;
    height: 34px;
    margin-right: 14px;
    width: auto;
    min-width: 150px;

    .balance-wrapper {
        width: auto;
        display: flex;
        height: 34px;
        align-items: center;
        justify-content: space-between;
        gap: 4px;
        position: relative;

        .coin-icon {
            width: 33px;
            height: 33px;
        }

        .refresh-icon {
            width: 14px;
            height: 14px;
            transition: transform 0.5s ease;
        }

        .balance-amount {
            color: #fff;
            font-size: 14px;
            font-weight: 500;
        }

        .top-up-btn {
            width: 20px;
            height: 20px;
            margin-right: -10px;
            cursor: pointer;
        }
    }
}

.rotate-animation {
    animation: rotate 1s linear;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.error-container {
    .center-imgs {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        /* 顶部对齐 */
        margin-top: 0;
        padding-top: 32px;
        /* 顶部留白，可根据需要调整 */
        margin-bottom: 40px;
    }

    .center-imgs img:first-child {
        width: 140px;
        max-width: 80vw;
        margin-bottom: 0px;
    }

    .center-imgs img:last-child {
        width: 120px;
        max-width: 50vw;
    }
}
</style>
