<template>
  <v-dialog
    :model-value="show"
    @update:model-value="(value: boolean) => emit('update:show', value)"
    max-width="320"
    @click:outside="handleClose"
  >
    <v-card class="activity-invite-modal">
      <v-card-text class="text-center pa-4">
        <v-icon
          class="icon-tip"
          icon="mdi-alert-circle-outline"
          @click="() => (showRule = true)"
        ></v-icon>
        <div class="title mb-4">
          Clique para receber o pacote de presente para amigos.
        </div>
        <div class="invite-code mb-4">
          <span class="label"><span style="font-size: 30px">R</span>$ </span>
          <span class="code">0-100</span>
        </div>
        <!-- <div class="description mb-4">
          邀請好友助力，即可獲得豐厚獎勵！
        </div> -->
        <v-btn block class="join-btn" @click="handleJoin">
          Receba o pacote de presente.
        </v-btn>
      </v-card-text>
    </v-card>
    <RuleDialog
      :visible.sync="showRule"
      title="Regras de Atividade"
      :content="rules?.activity_rules"
      @update:visible="handleRuleDialogClose"
    />
  </v-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useRouter } from "vue-router";
import RuleDialog from "@/components/RuleDialog.vue"
import { enterPddActivity, PddActivityContent } from "@/api/home";

const props = defineProps<{
  show: boolean;
  inviteCode: string;
}>();

const emit = defineEmits<{
  (e: "update:show", value: boolean): void;
  (e: "join"): void;
}>();

const router = useRouter();
const copySuccess = ref(false);
// 规则设置
const showRule = ref(false);
function handleRuleDialogClose(val: boolean) {
  showRule.value = val;
}
const rules = ref();
async function getRules() {
  const response = (await enterPddActivity()) as PddActivityContent;
  rules.value = response;
}
computed(() => {
  if (props.show) {
    getRules();
  }
  return "";
});
const handleClose = () => {
  emit("update:show", false);
};

const handleJoin = () => {
  emit("join");
};
</script>

<style lang="scss" scoped>
.activity-invite-modal {
  position: relative;
  background: linear-gradient(
    180deg,
    #fdf6c7 0%,
    #facc2e 100%
  ); /* Changed to solid yellow */
  border-radius: 20px !important; /* Changed from 16px to 20px */
  overflow: hidden;

  // .close-btn {
  //   position: absolute;
  //   top: 8px;
  //   right: 8px;
  //   cursor: pointer;
  //   color: #a0a0a0; /* Adjusted for yellow background */
  //   transition: color 0.3s;

  //   &:hover {
  //     color: white;
  //   }
  // }
  .icon-tip {
    color: #be3b16;
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
  }
  .title {
    font-size: 1rem;
    font-weight: 600;
    color: #ff0000;
  }

  .invite-code {
    background: transparent; // Make background transparent
    padding: 12px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 800;
    font-size: 1.4rem;
    color: #ff0000;
    .label {
      // color: rgba(255, 255, 255, 0.7);
    }

    .code {
      // color: #ffd700;
    }

    .copy-btn {
      min-width: 60px;
    }
  }

  .description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    line-height: 1.5;
  }

  .join-btn {
    height: 70px;
    font-size: 1rem;
    font-weight: 600;
    text-transform: none;
    border-radius: 22px;
    background: url("@/assets/images/h5/activity-button.png") no-repeat !important;
    background-size: 100% 100% !important; /* Adjusted to fill the button completely */
    background-position: center !important; /* Ensure the image is centered */
    color: white !important;
    box-shadow: none;
  }
}
</style>
