import alovaInstance from './request';
import { RequestConfig } from '@/types/api';

// 默认缓存时间
const DEFAULT_CACHE_EXPIRE = Number(import.meta.env.VITE_APP_CACHE_EXPIRE || 3600000);

// 创建不需要 token 的 GET 请求
export const getWithoutToken = (url: string, params?: any, config: RequestConfig = {}) => {
  return alovaInstance.Get(url, {
    params,
    needToken: false,
    ...config
  });
};

// 创建需要 token 的 GET 请求（带缓存）
export const getWithToken = (url: string, params?: any, config: RequestConfig = {}) => {
  return alovaInstance.Get(url, {
    params,
    needToken: true,
    enableCache: true,
    expire: DEFAULT_CACHE_EXPIRE,
    ...config
  });
};

// POST 请求
export const post = (url: string, data?: any, config: RequestConfig = {}) => {
  return alovaInstance.Post(url, data, {
    needToken: true,
    ...config
  });
};

// PUT 请求
export const put = (url: string, data?: any, config: RequestConfig = {}) => {
  return alovaInstance.Put(url, data, {
    needToken: true,
    ...config
  });
};

// DELETE 请求
export const del = (url: string, config: RequestConfig = {}) => {
  return alovaInstance.Delete(url, {
    needToken: true,
    ...config
  });
};

export default {
  get: getWithToken,
  getWithoutToken,
  post,
  put,
  delete: del
}; 