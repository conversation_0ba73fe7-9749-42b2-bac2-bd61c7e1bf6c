export interface ShortUrlRecord {
  id: number;
  key: string;
  short_link: string;
  origin_link: string;
  pv: number;
  uv: number;
  create_at: string;
  create_by: number;
  update_at: string;
  update_by: number;
  userId?: string;
  nickname?: string;
  phone?: string;
  registerSource?: string;
  registerTime?: string;
  loginStatus?: boolean;
  lastLoginTime?: string;
}

export interface ShortUrlSearchForm {
  sort?: string;
  user_id?: string;
  nickname?: string;
  phone?: string;
  registerSource?: string;
  registerTime?: [string, string];
  loginStatus?: boolean;
}

export interface ShortUrlListParams extends ShortUrlSearchForm {
  current: number;
  size: number;
}

export interface ShortUrlListResponse {
  list: ShortUrlRecord[];
  total: number;
}
