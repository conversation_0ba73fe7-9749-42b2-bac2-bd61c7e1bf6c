<template>
  <ElDialog
    :model-value="visible"
    title="平台利润注入"
    width="900px"
    @close="handleClose"
    :close-on-click-modal="true"
  >
    <div class="profit-search-bar">
      <ElForm :model="searchForm" class="search-wrapper">
        <ElRow :gutter="20">
          <ElCol :span="6">
            <ElFormItem label="用户ID">
              <ElInput
                v-model="searchForm.uuid"
                placeholder="请输入"
                clearable
                style="width: 100%"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem label="游戏编号">
              <ElInput
                v-model="searchForm.game_uid"
                placeholder="请输入"
                clearable
                style="width: 100%"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="时间">
              <ElDatePicker
                style="width: 100%"
                v-model="searchForm.time"
                type="daterange"
                range-separator="-"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="x"
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElFormItem>
          <ElButton type="primary" @click="handleSearch">查询</ElButton>
          <ElButton @click="resetSearch">重置</ElButton>
        </ElFormItem>
      </ElForm>
    </div>
    <ElTable
      :data="tableData"
      v-loading="loading"
      style="margin-top: 10px"
      height="300px"
    >
      <ElTableColumn prop="id" label="ID" min-width="80" />
      <ElTableColumn prop="jackpot_id" label="奖池ID" min-width="80" />
      <ElTableColumn prop="jackpot_name" label="奖池名称" min-width="140" />
      <ElTableColumn prop="uuid" label="用户ID" min-width="100" />
      <ElTableColumn prop="nickname" label="昵称" min-width="100" />
      <ElTableColumn prop="game_uid" label="游戏UID" min-width="100" />
      <ElTableColumn prop="game_type" label="游戏类型" min-width="100" />
      <ElTableColumn prop="total_bet" label="总下注" min-width="150">
        <template #default="{ row }">
          <span>{{ formatAmount(row.total_bet / 100) }}</span>
        </template>
      </ElTableColumn>
      <ElTableColumn prop="consumption" label="消耗" min-width="100">
        <template #default="{ row }">
          <span>{{ formatAmount(row.consumption / 100) }}</span>
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="profit_percentage"
        label="利润百分比"
        min-width="100"
      >
        <template #default="{ row }">
          <span>{{ row.profit_percentage }}%</span>
        </template>
      </ElTableColumn>
      <ElTableColumn prop="injection_amount" label="注入金额" min-width="150">
        <template #default="{ row }">
          <span>{{ formatAmount(row.injection_amount / 100) }}</span>
        </template>
      </ElTableColumn>
      <ElTableColumn
        prop="game_start_time"
        label="游戏开始时间"
        min-width="180"
      >
        <template #default="{ row }">
          <span>{{ formatTimestamp(row.game_start_time) }}</span>
        </template>
      </ElTableColumn>
      <ElTableColumn prop="game_end_time" label="游戏结束时间" min-width="180">
        <template #default="{ row }">
          <span>{{ formatTimestamp(row.game_end_time) }}</span>
        </template>
      </ElTableColumn>
      <ElTableColumn prop="created_at" label="创建时间" min-width="180">
        <template #default="{ row }">
          <span>{{ formatTimestamp(row.created_at) }}</span>
        </template>
      </ElTableColumn>
    </ElTable>
    <div class="mt-20px flex justify-start">
      <ElPagination
        v-model:current-page="page"
        v-model:page-size="pageSize"
        :total="total"
        layout="total, sizes, prev, pager, next"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="fetchData"
        @current-change="fetchData"
      />
    </div>
  </ElDialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { ElMessage } from "element-plus";
import { fetchJackpotProfitList } from "@/service/api/jackpot";
import moment from "moment";
import { getBrazilDate } from "@/utils/format";

const props = defineProps<{ visible: boolean; jackpotId: number | string }>();
const emit = defineEmits(["update:visible"]);

const searchForm = reactive({
  uuid: "",
  game_uid: "",
  time: [] as string[],
});

const tableData = ref<any[]>([]);
const loading = ref(false);
const page = ref(1);
const pageSize = ref(10);
const total = ref(0);

watch(
  () => props.visible,
  (val) => {
    if (val && props.jackpotId) {
      fetchData();
    } else {
      tableData.value = [];
      searchForm.uuid = "";
      searchForm.game_uid = "";
      searchForm.time = [];
      page.value = 1;
      pageSize.value = 10;
      total.value = 0;
    }
  },
);

function resetSearch() {
  searchForm.uuid = "";
  searchForm.game_uid = "";
  searchForm.time = [];
  fetchData();
}

async function fetchData() {
  loading.value = true;
  try {
    const params: any = {
      page: page.value,
      size: pageSize.value,
      uuid: searchForm.uuid,
      game_uid: searchForm.game_uid,
      id: props.jackpotId,
    };
    if (searchForm.time && searchForm.time.length === 2) {
      params.game_start_time = searchForm.time[0];
      params.game_end_time = searchForm.time[1];
    }
    const { response } = await fetchJackpotProfitList(params);
    console.log(response);
    if (response?.data?.data) {
      tableData.value = response.data.data || [];
      total.value = response.data.count || 0;
    } else {
      tableData.value = [];
      total.value = 0;
    }
  } finally {
    loading.value = false;
  }
}
function handleSearch() {
  page.value = 1;
  pageSize.value = 10;
  fetchData();
}
function handleClose() {
  emit("update:visible", false);
}

function formatAmount(val: number) {
  if (val == null) return "-";
  return typeof val === "number" ? `R$ ${val}` : val;
}

function formatTimestamp(val: number) {
  if (!val) return "-";
  return moment(getBrazilDate(val)).format("YYYY-MM-DD HH:mm:ss");
}
</script>

<style scoped>
.profit-search-bar {
  margin-bottom: 10px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 12px;
}

:deep(.el-select__wrapper) {
  margin-left: 10px;
}
</style>
