<script setup lang="ts">
import { ref, watch } from "vue";
import { getPddActivitySuccessList } from "@/service/api/pdd";
import { useI18n } from "vue-i18n";
import moment from "moment";
import { getBrazilDate } from "@/utils/format";

const { t } = useI18n();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  activityId: {
    type: Number,
    default: undefined,
  },
});

const emit = defineEmits(["update:visible"]);

const loading = ref(false);
const data = ref([]);
const pagination = ref({
  total: 0,
  currentPage: 1,
  pageSize: 10,
  "current-change": (page: number) => {
    pagination.value.currentPage = page;
    getData();
  },
  "size-change": (size: number) => {
    pagination.value.pageSize = size;
    pagination.value.currentPage = 1;
    getData();
  },
});

const columns = [
  { type: "index", label: t("common.index"), minWidth: 60 },
  { prop: "user_id", label: "用户ID", minWidth: 100 },
  { prop: "participants_count", label: "参与人数", minWidth: 90 },
  { prop: "register_count", label: "注册人数", minWidth: 90 },
  { prop: "recharger_count", label: "充值人数", minWidth: 90 },
  { prop: "recharge_amount", label: "充值金额 (R$)", minWidth: 120 },
  { prop: "bonus", label: "奖金 (R$)", minWidth: 100 },
  {
    prop: "start_time",
    label: "开始时间",
    minWidth: 160,
    formatter: (row: any) =>row.start_time
          ? moment(getBrazilDate(row.start_time)).format("YYYY-MM-DD HH:mm:ss")
          : "-",
  },
  {
    prop: "status",
    label: "状态",
    minWidth: 90,
    formatter: (row: any) => {
      return row.status === 1 ? "进行中" : "已结束";
    },
  },
];

const getData = async () => {
  if (!props.activityId) return;

  loading.value = true;
  try {
    const { data: response } = await getPddActivitySuccessList(
      props.activityId,
      {
        page: pagination.value.currentPage,
        size: pagination.value.pageSize,
      },
    );
    console.log(response?.data);
    if (response?.data) {
      data.value = response.data.result || [];
      pagination.value.total =  pagination.value.total ? pagination.value.total:response.data.count || 0;
    }
  } catch (error) {
    console.error("Failed to fetch success list:", error);
  } finally {
    loading.value = false;
  }
};

watch(
  () => props.visible,
  (val) => {
    if (val && props.activityId) {
      getData();
    } else {
      data.value = [];
      pagination.value.currentPage = 1;
      pagination.value.total = 0;
    }
  },
);

const handleClose = () => {
  emit("update:visible", false);
};
</script>

<template>
  <ElDialog
    :model-value="props.visible"
    title="成功人员列表"
    width="80%"
    @close="handleClose"
  >
    <div class="h-[500px] flex justify-center items-center">
      <ElTable
        v-loading="loading"
        height="calc(100vh - 400px)"
        class="sm:h-full"
        :data="data"
        row-key="id"
      >
        <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
      </ElTable>
    </div>
    <div class="mt-20px flex justify-start">
      <ElPagination
        v-if="pagination.total"
        layout="total,prev,pager,next,sizes"
        :total="pagination.total"
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        @current-change="pagination['current-change']"
        @size-change="pagination['size-change']"
      />
    </div>
  </ElDialog>
</template>

<style scoped>
/* 可以根據需要添加樣式 */
</style>
