---
sidebar_position: 1
---

# Contract Audits

The UniPass wallet contracts underwent two independent audits by **[BlockSec](https://blocksec.com/)** and **[Salus](https://salusec.io/)**, both of which are well-respected companies in the blockchain field. These audits were performed with great care and attention to detail, ensuring that the wallet contracts are secure and reliable. The audits included a thorough analysis of the smart contract's source code, as well as extensive testing to detect any potential vulnerabilities. The results of the audits were then used to improve the security of the wallet contracts, and to ensure they meet the highest standards of quality.

UniPass will arrange more audits in the future to ensure the security of the contract. UniPass values the security of the contract and will continue to work to improve its security to protect the interests of users.

## BlockSec
[Feb 1, 2023](https://github.com/UniPassID/Unipass-Wallet-Contract/blob/main/audits/blocksec_unipass_wallet_signed_v2.1.pdf)

## Salus
[Jan 19, 2023](https://github.com/UniPassID/Unipass-Wallet-Contract/blob/main/audits/salus_unipass_audit_report_2023-01-19.pdf)

## Open Source & Auditing

UniPass Contract is currently open-source and available on [GitHub](https://github.com/UniPassID/Unipass-Wallet-Contract). It has undergone independent audits by two highly respected companies in the blockchain industry, BlockSec, and Salus. You can find more details about the audits [here](../audits/01-overview.md).