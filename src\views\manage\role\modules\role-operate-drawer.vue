<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useBoolean } from "@sa/hooks";
import { useForm, useFormRules } from "@/hooks/common/form";
import { $t } from "@/locales";
import {
  fetchAddRole,
  fetchUpdateRole,
  fetchAgentAddRole,
  fetchAgentUpdateRole,
  saveRoleOperations,
} from "@/service/api";
import { enableStatusOptions } from "@/constants/business";
import MenuAuthModal from "./menu-auth-modal.vue";
import ButtonAuthModal from "./button-auth-modal.vue";
import { localStg } from "@/utils/storage";
import { myRoutes } from "@/router/elegant/routes";

defineOptions({ name: "RoleOperateDrawer" });
const isManage = ref(true);

watch(
  () => localStg.get("isManage"),
  (newValue) => {
    console.log("isManage", newValue);
    isManage.value = JSON.parse(newValue || "false");
  },
  { immediate: true },
); // 判断是否是平台账号

interface Props {
  /** the type of operation */
  operateType: UI.TableOperateType;
  /** the edit row data */
  rowData?: Api.SystemManage.Role | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: "submitted"): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>("visible", {
  default: false,
});

const { formRef, validate, restoreValidation } = useForm();
const { defaultRequiredRule } = useFormRules();
const { bool: menuAuthVisible, setTrue: openMenuAuthModal } = useBoolean();
const { bool: buttonAuthVisible, setTrue: openButtonAuthModal } = useBoolean();

const title = computed(() => {
  const titles: Record<UI.TableOperateType, string> = {
    add: $t("page.manage.role.addRole"),
    edit: $t("page.manage.role.editRole"),
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.SystemManage.Role,
  "role_name" | "description" | "status"
>;

function createDefaultModel(): Model {
  return {
    role_name: "",
    description: "",
    status: undefined,
    button_ids: [1, 2, 3, 4, 5],
  };
}

const model = ref(createDefaultModel());
const oldRoutes = ref<string[]>([]);
const newRoutes = ref<string[]>([]);
const oldButtonIds = ref<string[]>([]);
const newButtonIds = ref<string[]>([]);

const operations = ref<any[]>([]);

type RuleKey = Exclude<keyof Model, "description">;
const rules: Record<RuleKey, App.Global.FormRule> = {
  role_name: defaultRequiredRule,
  description: defaultRequiredRule,
  status: defaultRequiredRule,
};

const roleId = computed(() => props.rowData?.role_id || -1);

const isEdit = computed(() => props.operateType === "edit");

function handleUpdateModelWhenEdit() {
  console.log("接收传递过来的参数", props);
  if (props.operateType === "add") {
    console.log("添加");
    model.value = createDefaultModel();
    return;
  }

  if (props.operateType === "edit") {
    model.value = {
      ...props.rowData,
      button_ids: props.rowData?.buttons?.map((e) => e.button_id) || [],
    };
  }
}

function closeDrawer() {
  visible.value = false;
}

function findRoutePathById(id, routes, path = []) {
  for (const route of routes) {
    const routeId = route.meta?.parentId
      ? `${route.meta.parentId}-${route.meta.id}`
      : String(route.meta?.id);
    const label = route.meta?.i18nKey
      ? $t(route.meta.i18nKey)
      : route.meta?.title || route.name;
    const currentPath = [...path, label];
    if (routeId === id) {
      return currentPath.join("/");
    }
    if (route.children) {
      const found = findRoutePathById(id, route.children, currentPath);
      if (found) return found;
    }
  }
  return null;
}

function openMenuAuthModalWithRecord() {
  oldRoutes.value = (model.value.routes || "").split(",").filter(Boolean);
  openMenuAuthModal();
}

function openButtonAuthModalWithRecord() {
  oldButtonIds.value = JSON.parse(JSON.stringify(model.value.button_ids)) || [];
  openButtonAuthModal();
}
function handleRouteIds(routeIds: string[]) {
  newRoutes.value = JSON.parse(JSON.stringify(routeIds));
  model.value.routes = routeIds.join(",");
  // 計算新增和刪除
  const added = newRoutes.value.filter((x) => !oldRoutes.value.includes(x));
  const removed = oldRoutes.value.filter((x) => !newRoutes.value.includes(x));
  operations.value = [];
  console.log(added, removed);
  if (added.length) {
    const addedNames = added
      .map((id) => findRoutePathById(id, myRoutes))
      .filter(Boolean);
    operations.value.push({ type: "1", value: addedNames.join("、") });
  }
  if (removed.length) {
    const removedNames = removed
      .map((id) => findRoutePathById(id, myRoutes))
      .filter(Boolean);
    operations.value.push({ type: "2", value: removedNames.join("、") });
  }
}

function findButtonNameById(id) {
  console.log(props.rowData?.buttons);
  return [
    { id: 1, label: "新增按钮", code: "code1" },
    { id: 2, label: "删除按钮", code: "code2" },
    { id: 3, label: "修改按钮", code: "code3" },
    { id: 4, label: "查询按钮", code: "code4" },
    { id: 5, label: "导出按钮", code: "code5" },
  ].filter((e) => e.id === id)[0].label;
}

function handleButtonIds(buttonIds: number[]) {
  console.log(412342314)
  newButtonIds.value = JSON.parse(JSON.stringify(buttonIds));
  model.value.button_ids = buttonIds;
  // 計算新增和刪除
  const added = newButtonIds.value.filter(
    (x) => !oldButtonIds.value.includes(x),
  );
  const removed = oldButtonIds.value.filter(
    (x) => !newButtonIds.value.includes(x),
  );
  operations.value = [];
  console.log(added, removed);
  if (added.length) {
    const addedNames = added
      .map((id) => findButtonNameById(id))
      .filter(Boolean);
    operations.value.push({ type: "3", value: addedNames.join("、") });
  }
  if (removed.length) {
    const removedNames = removed
      .map((id) => findButtonNameById(id))
      .filter(Boolean);
    operations.value.push({ type: "4", value: removedNames.join("、") });
  }
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});

async function handleSubmit() {
  await validate();
  const submitData = {
    ...model.value,
    status: Number(model.value.status),
  };
  try {
    if (props.operateType === "edit") {
      const { data, error, response } = isManage.value
        ? await fetchUpdateRole(submitData)
        : await fetchAgentUpdateRole(submitData);
      if (!error) {
        // 保存操作記錄
        if (operations.value.length) {
          await saveRoleOperations({
            role_id: roleId.value,
            operations: operations.value,
          });
        }
        window.$message?.success($t("common.updateSuccess"));
        closeDrawer();
        emit("submitted");
      }
    } else {
      const { data, error, response } = isManage.value
        ? await fetchAddRole(submitData)
        : await fetchAgentAddRole(submitData);
      if (!error) {
        window.$message?.success($t("common.addSuccess"));
        closeDrawer();
        emit("submitted");
      }
    }
  } catch (error) {
    window.$message?.error($t("common.updateFailed"));
  }
}
</script>

<template>
  <ElDrawer v-model="visible" :title="title" :size="360">
    <ElForm ref="formRef" :model="model" :rules="rules" label-position="top">
      <ElFormItem label="角色名称" prop="role_name">
        <ElInput v-model="model.role_name" placeholder="请输入角色名称" />
      </ElFormItem>
      <ElFormItem label="角色状态" prop="status">
        <ElRadioGroup v-model="model.status">
          <ElRadio
            v-for="{ label, value } in enableStatusOptions"
            :key="value"
            :value="value"
            :label="label"
          />
        </ElRadioGroup>
      </ElFormItem>
      <ElFormItem label="角色描述" prop="description">
        <ElInput v-model="model.description" placeholder="请输入角色描述" />
      </ElFormItem>
    </ElForm>
    <ElSpace>
      <ElButton @click="openMenuAuthModalWithRecord">菜单分配</ElButton>
      <MenuAuthModal
        v-model:visible="menuAuthVisible"
        :routes="model.routes"
        @routeIds="handleRouteIds"
      />
      <ElButton @click="openButtonAuthModalWithRecord">操作按钮分配</ElButton>
      <ButtonAuthModal
        v-model:visible="buttonAuthVisible"
        :buttonIds="model.button_ids"
        @buttonIds="handleButtonIds"
      />
    </ElSpace>
    <template #footer>
      <ElSpace :size="16">
        <ElButton @click="closeDrawer">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit">确认</ElButton>
      </ElSpace>
    </template>
  </ElDrawer>
</template>

<style scoped></style>
