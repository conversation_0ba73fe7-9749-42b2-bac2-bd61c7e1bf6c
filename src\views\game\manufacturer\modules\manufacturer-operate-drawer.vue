<script setup lang="ts">
import { ref, reactive, computed, watch } from "vue";
import { statusOptions } from "@/constants/common";
import {
  fetchCreateManufacturer,
  fetchUpdateManufacturer,
  fetchGetManufacturerById,
} from "@/service/api/manufacturer";
import ImageUpload from "@/components/upload/ImageUpload.vue";

interface Props {
  /** the type of operation */
  operateType: UI.TableOperateType;
  /** the edit row data */
  rowData?: any | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: "submitted"): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>("visible", {
  default: false,
});

const title = computed(() => {
  const titles: Record<UI.TableOperateType, string> = {
    add: "新增厂商",
    edit: "编辑厂商",
  };
  return titles[props.operateType];
});

const formRef = ref();

// 游戏类型选项
const gameTypeOptions = [
  { label: "赠金游戏", value: "BonusSlots", labelValue: "BonusSlots" },
  { label: "热门游戏", value: "hot", labelValue: "hot" },
  { label: "老虎机", value: "slots", labelValue: "slots" },
  { label: "真人", value: "livecasino", labelValue: "livecasino" },
  { label: "卡牌", value: "Cartoes", labelValue: "Pôquer" },
  { label: "捕鱼", value: "Pescaria", labelValue: "Pescaria" },
  { label: "电玩", value: "Coluna", labelValue: "Coluna" },
];

// 设备选项
const deviceOptions = [
  { label: "PC", value: "PC" },
  { label: "H5", value: "H5" },
  { label: "PC+H5", value: "PC+H5" },
];

const formData = reactive({
  name: "",
  icon: "",
  devices: "",
  channel: "",
  sort_order: 1,
  status: 1,
  game_manufacturer_type: [],
  coin_type: "", // 新增金币类型字段
});

// 金币类型选项
const coinTypeOptions = [
  { label: "现金", value: "cash" },
  { label: "赠金", value: "bonus" },
  { label: "现金、赠金", value: "both" },
];

const rules = {
  name: [{ required: true, message: "请输入厂商名称", trigger: "blur" }],
  icon: [{ required: true, message: "请输入图标", trigger: "blur" }],
  devices: [{ required: true, message: "请输入设备", trigger: "blur" }],
  sort_order: [{ required: true, message: "请输入排序", trigger: "blur" }],
  status: [{ required: true, message: "请选择状态", trigger: "blur" }],
  game_manufacturer_type: [
    { required: true, message: "请选择游戏类型", trigger: "change" },
  ],
  // coin_type: [{ required: true, message: '请选择金币类型', trigger: 'change' }]
};

async function fetchDetail() {
  if (!props.rowData?.id) return;
  try {
    const res = await fetchGetManufacturerById({
      id: props?.rowData?.id,
    });
    if (res.data) {
      Object.assign(formData, res.data);
    }
  } catch (error) {
    window.$message?.error("获取详情失败");
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    const submitData = {
      ...formData,
      game_manufacturer_type: formData.game_manufacturer_type.join(","),
    };

    if (props.operateType === "edit") {
      const { error } = await fetchUpdateManufacturer({
        id: props?.rowData?.id,
        ...submitData,
      });
      if (!error) {
        window.$message?.success("更新成功");
        closeDrawer();
        emit("submitted");
      }
    } else {
      const { error } = await fetchCreateManufacturer(submitData);
      if (!error) {
        window.$message?.success("创建成功");
        closeDrawer();
        emit("submitted");
      }
    }
  } catch (error) {
    window.$message?.error("操作失败");
  }
}

watch(visible, async () => {
  if (visible.value) {
    if (props.operateType === "edit") {
      const rowData = { ...props.rowData };
      if (typeof rowData.game_manufacturer_type === "string") {
        rowData.game_manufacturer_type = rowData.game_manufacturer_type
          .split(",")
          .filter(Boolean);
      }
      Object.assign(formData, rowData);
    } else {
      Object.assign(formData, {
        name: "",
        icon: "",
        devices: "",
        channel: "",
        sort_order: 1,
        status: 1,
        game_manufacturer_type: [],
        coin_type: "",
      });
    }
  }
});
</script>

<template>
  <ElDrawer :size="360" v-model="visible" :title="title">
    <ElForm ref="formRef" :model="formData" :rules="rules" label-position="top">
      <ElFormItem label="厂商名称" prop="name">
        <ElInput v-model="formData.name" placeholder="请输入厂商名称" />
      </ElFormItem>
      <ElFormItem label="图标" prop="icon">
        <ImageUpload
          v-model="formData.icon"
          :show-tip="true"
          tip-text="支持 jpg、png 格式图片，大小不超过 2MB"
        />
      </ElFormItem>
      <ElFormItem label="设备" prop="devices">
        <ElSelect v-model="formData.devices" placeholder="请选择设备">
          <ElOption
            v-for="item in deviceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="渠道" prop="channel">
        <ElSelect v-model="formData.channel" placeholder="请选择渠道">
          <ElOption label="灰度Gaming" value="1" />
          <ElOption label="PG" value="2" />
          <ElOption label="OMG总代" value="3" />
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="排序" prop="sort_order">
        <ElInputNumber
          v-model="formData.sort_order"
          :min="1"
          :max="999"
          placeholder="请输入排序"
        />
      </ElFormItem>
      <ElFormItem label="状态" prop="status">
        <ElRadioGroup v-model="formData.status">
          <ElRadio
            v-for="{ label, value } in statusOptions"
            :key="value"
            :value="value"
            :label="label"
          />
        </ElRadioGroup>
      </ElFormItem>
      <ElFormItem label="游戏类型" prop="game_manufacturer_type">
        <ElSelect
          v-model="formData.game_manufacturer_type"
          multiple
          collapse-tags
          collapse-tags-tooltip
          placeholder="请选择游戏类型"
        >
          <ElOption
            v-for="item in gameTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <span style="float: left">{{ item.label }}</span>
            <span
              style="
                float: right;
                color: var(--el-text-color-secondary);
                font-size: 13px;
              "
            >
              {{ item.labelValue }}
            </span>
          </ElOption>
        </ElSelect>
      </ElFormItem>
      <ElFormItem label="金币类型">
        <ElSelect v-model="formData.coin_type" placeholder="请选择金币类型">
          <ElOption
            v-for="item in coinTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem>
    </ElForm>
    <template #footer>
      <div class="flex justify-end gap-2">
        <ElButton @click="closeDrawer">取消</ElButton>
        <ElButton type="primary" @click="handleSubmit"> 确定 </ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<style scoped></style>
