import { request } from '../request'
import { CommonSearchParams, TableResponse } from '@/typings/table'

// 下级用户列表查询参数
export interface SubLevelUserParams extends CommonSearchParams {
  father_id: string;
}

// 下级用户数据结构
export interface SubLevelUser {
  id: string;
  uuid: string;
  nickname: string;
  phone: string;
  invite_code: string;
  account_status: number;
  created_at: string;
  last_login_at: string;
}


export const getSubLevelUsers = (params:SubLevelUserParams) =>{
  return request<TableResponse<SubLevelUser>>({
    url: '/backend/user/sub_level',
    method: 'get',
    params
  })
}
