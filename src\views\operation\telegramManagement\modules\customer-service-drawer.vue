<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { $t } from "@/locales";
import {
  fetchAddCustomerServiceGroup,
  fetchUpdateCustomerServiceGroup,
} from "@/service/api/telegram";

interface Props {
  operateType: "add" | "edit";
  rowData?: any;
}

interface Emits {
  (e: "submitted"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = defineModel<boolean>("visible", {
  default: false,
});

const title = computed(() => {
  const operateTypeMap = {
    add: "添加客服群",
    edit: "编辑客服群",
  };
  return operateTypeMap[props.operateType];
});

const formRef = ref();
const loading = ref(false);

// 运行时间类型选项
const operatingTimeTypeOptions = [
  { label: "24小时", value: 1 },
  { label: "自定义时间", value: 2 },
];

// 状态选项
const statusOptions = [
  { label: "启用", value: 1 },
  { label: "禁用", value: 0 },
];

const form = ref({
  group_name: "",
  group_id: "",
  customer_service_count: 1,
  operating_time_type: 1,
  operating_time_value: "24小时",
  invitation_link: "",
  status: 1,
});

function closeDrawer() {
  visible.value = false;
}

function resetForm() {
  form.value = {
    group_name: "",
    group_id: "",
    customer_service_count: 1,
    operating_time_type: 1,
    operating_time_value: "24小时",
    invitation_link: "",
    status: 1,
  };
}

function setForm() {
  if (props.operateType === "edit" && props.rowData) {
    form.value = {
      group_name: props.rowData.group_name || "",
      group_id: props.rowData.group_id || "",
      customer_service_count: props.rowData.customer_service_count || 1,
      operating_time_type: props.rowData.operating_time_type || 1,
      operating_time_value:
        props.rowData.operating_time_type === 1
          ? props.rowData.operating_time_value || "24小时"
          : props.rowData.operating_time_value || "",
      invitation_link: props.rowData.invitation_link || "",
      status: props.rowData.status ?? 1,
    };
  }
}

// 监听运行时间类型变化
watch(
  () => form.value.operating_time_type,
  (newVal) => {
    if (newVal === 1) {
      form.value.operating_time_value = "24小时";
    } else if (props.operateType === "add") {
      form.value.operating_time_value = "";
    }
  },
);

async function handleSubmit() {
  await formRef.value?.validate();
  loading.value = true;
  try {
    const submitData = { ...form.value };

    if (props.operateType === "add") {
      const { error } = await fetchAddCustomerServiceGroup(submitData);
      if (!error) {
        ElMessage.success("添加成功");
        closeDrawer();
        emit("submitted");
      }
    } else {
      const { error } = await fetchUpdateCustomerServiceGroup({
        ...submitData,
        id: props.rowData?.id,
      });
      if (!error) {
        ElMessage.success("更新成功");
        closeDrawer();
        emit("submitted");
      }
    }
  } catch (error) {
    console.error("操作失败:", error);
  } finally {
    loading.value = false;
  }
}

watch(visible, (val) => {
  if (val) {
    setForm();
  } else {
    resetForm();
  }
});
</script>

<template>
  <ElDrawer
    v-model="visible"
    :title="title"
    :size="500"
    destroy-on-close
    @close="closeDrawer"
  >
    <ElForm
      ref="formRef"
      :model="form"
      label-width="100px"
      label-position="top"
    >
      <ElFormItem
        label="客服群名"
        prop="group_name"
        :rules="[{ required: true, message: '请输入客服群名' }]"
      >
        <ElInput
          v-model="form.group_name"
          placeholder="请输入客服群名"
          maxlength="100"
          show-word-limit
        />
      </ElFormItem>

      <!-- <ElFormItem
        label="群组ID"
        prop="group_id"
        :rules="[{ required: true, message: '请输入群组ID' }]"
      >
        <ElInput
          v-model="form.group_id"
          placeholder="格式：-10xxxxxxxxx（负数）"
        />
        <div class="text-xs text-gray-500 mt-1">格式：-10xxxxxxxxx（负数）</div>
      </ElFormItem> -->

      <!-- <ElFormItem
        label="客服数量"
        prop="customer_service_count"
        :rules="[{ required: true, message: '请输入客服数量' }]"
      >
        <ElInputNumber
          v-model="form.customer_service_count"
          :min="1"
          :max="1"
          placeholder="请输入客服数量"
          style="width: 100%"
        />
      </ElFormItem> -->

      <!-- <ElFormItem
        label="运行时间"
        prop="operating_time_type"
        :rules="[{ required: true, message: '请选择运行时间类型' }]"
      >
        <ElSelect
          v-model="form.operating_time_type"
          placeholder="请选择运行时间类型"
          style="width: 100%"
        >
          <ElOption
            v-for="item in operatingTimeTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElSelect>
      </ElFormItem> -->

      <!-- <ElFormItem
        v-if="form.operating_time_type === 2"
        label="自定义时间"
        prop="operating_time_value"
        :rules="[{ required: true, message: '请输入运行时间' }]"
      >
        <ElInput
          v-model="form.operating_time_value"
          placeholder="例如：10:00-22:00"
        />
      </ElFormItem> -->

      <ElFormItem
        label="邀请链接"
        prop="invitation_link"
        :rules="[{ required: true, message: '请输入邀请链接' }]"
      >
        <ElInput v-model="form.invitation_link" placeholder="请输入邀请链接" />
      </ElFormItem>

      <ElFormItem
        label="状态"
        prop="status"
        :rules="[{ required: true, message: '请选择状态' }]"
      >
        <ElRadioGroup v-model="form.status">
          <ElRadio
            v-for="item in statusOptions"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </ElRadio>
        </ElRadioGroup>
      </ElFormItem>
    </ElForm>

    <template #footer>
      <ElSpace class="w-full justify-end">
        <ElButton @click="closeDrawer">取消</ElButton>
        <ElButton type="primary" :loading="loading" @click="handleSubmit">
          保存
        </ElButton>
      </ElSpace>
    </template>
  </ElDrawer>
</template>

<style lang="scss" scoped>
:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
