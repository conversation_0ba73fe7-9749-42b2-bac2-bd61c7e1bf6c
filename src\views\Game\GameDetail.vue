<template>
  <div class="game-detail">
    <!-- Loading Spinner -->
    <div v-if="loading" class="loading-container ma-4">
      <img
        src="@/assets/images/chip.gif"
        alt="loading"
        style="width: 300px; height: 300px"
      />
    </div>

    <!-- Game Frame -->
    <div v-else class="game-frame-container">
      <!-- Header -->
      <div class="game-header">
        <v-btn icon="mdi-arrow-left" variant="text" @click="goBack"></v-btn>
        <h2 class="game-title">{{ gameTitle }}</h2>
        <v-btn
          v-if="gameUrl"
          icon="mdi-fullscreen"
          variant="text"
          @click="toggleFullscreen"
        ></v-btn>
        <v-btn
          v-if="htmlContent"
          icon="mdi-fullscreen"
          variant="text"
          @click="toggleFullscreenHtml"
        ></v-btn>
      </div>

      <!-- Game Frame -->
      <iframe
        v-if="gameUrl"
        ref="gameFrame"
        :src="gameUrl"
        class="game-frame"
        :class="{ fullscreen: isFullscreen }"
        frameborder="0"
        allowfullscreen
        sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"
        @load="handleIframeLoad"
        @error="handleIframeError"
      ></iframe>
      <iframe
        v-if="htmlContent"
        ref="gameFrameHtml"
        :srcdoc="htmlContent"
        class="game-frame"
        :class="{ fullscreen: isFullscreen }"
        frameborder="0"
        allowfullscreen
        sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"
        @load="handleIframeLoad"
        @error="handleIframeError"
      ></iframe>
      <div v-if="!gameUrl && !htmlContent" class="error-container">
        <div class="center-imgs">
          <img src="@/assets/images/h5/bottom-title.png" alt="" />
          <img src="@/assets/images/h5/bottom-logo.png" alt="" />
        </div>
        <CommonDialog
          :show="dialogShow"
          :dialogObj="dialogObj"
          @update:show="goBack"
        ></CommonDialog>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, onBeforeUnmount } from "vue";
import { onBeforeRouteLeave, useRoute, useRouter } from "vue-router";
import { useStore } from "vuex";
import {
  getGameUrl,
  startGameSession,
  startGameSessionStartv1,
  startGameSessionStartv2,
  startGameSessionStartv3,
  sessionTransfer,
  endGameSession,
  endv2GameSession,
  endv3GameSession,
} from "@/api/game";
import { showError, showWarning } from "@/utils/toast";
import CommonDialog from "@/components/CommonDialog.vue";
import { fixURL } from "@/utils/url";
import websocketService, { WebSocketState } from "@/utils/websocket";

const route = useRoute();
const router = useRouter();
const store = useStore();

const loading = ref(true);
const error = ref("");
const gameUrl = ref("");
const htmlContent = ref("");
const gameType = ref();
const gameTitle = ref("");
const isFullscreen = ref(false);
const gameFrame = ref<HTMLIFrameElement | null>(null);
const gameFrameHtml = ref<HTMLIFrameElement | null>(null);
const sessionKey = ref("");
const dialogShow = ref(false);

const dialogObj = {
  hyperlink:
    window.location.protocol + "//" + window.location.host + "/records",
  title: "lembrar",
  content: "Ainda sem saldo, clique no botão para ir para a página de recarga",
};
const gameId = ref();

// 开始游戏会话
const startGame = async () => {
  gameId.value = route.params.id;
  try {
    const userInfo = store.state.auth.user;
    if (!userInfo || !userInfo.id) {
      showError("Por favor, faça login primeiro");
      return;
    }
    console.log(route);
    const params = {
      user_id: userInfo.id,
      game_uid: route.params.id as string,
      id: Number(route.query.uuid),
    };

    const response = await startGameSession(params);
    console.log("Game session started:", response);
    if (response.code === 203) {
      showWarning("Você já está jogando", 2000);
      setTimeout(() => {
        goBack();
      }, 2000);

      return;
    }
    gameType.value = response?.game_type;
    if (response?.have_balance === 2) {
      loading.value = false;
      dialogShow.value = true;
      return;
    }
    if (response?.game_type === 2) {
      const res = await startGameSessionStartv2(params);

      htmlContent.value = res.response;
      loading.value = false;
    } else if (response?.game_type === 3) {
      const session = await sessionTransfer({ game_uid: route.params.id });
      const res = await startGameSessionStartv3(params);
      if (session) {
        // 会话创建成功，继续加载游戏
        sessionKey.value = session;

        gameUrl.value = fixURL(res.gameurl);
        gameTitle.value = (res.game_name as string) || "Jogo";
      } else {
        error.value = res.message || "Falha ao iniciar o jogo";
      }
      loading.value = false;
    } else {
      const res = await startGameSessionStartv1(params);
      if (res.session_key) {
        // 会话创建成功，继续加载游戏
        sessionKey.value = res.session_key;
        loadGame();
      } else {
        error.value = res.message || "Falha ao iniciar o jogo";
      }
    }
    localStorage.setItem(
      "gameParams",
      JSON.stringify({
        type: gameType.value,
        sessionKey: sessionKey.value,
        gameId: gameId.value,
      })
    );
  } catch (err) {
    console.error("Failed to start game session:", err);
    error.value = "Falha ao iniciar o jogo, tente novamente mais tarde";
  }
};

// 加载游戏
const loadGame = async () => {
  try {
    const userInfo = store.state.auth.user;
    if (!userInfo || !userInfo.id) {
      showError("Por favor, faça login primeiro");
      return;
    }

    const params = {
      // game_uid: route.params.id as string,
      game_id: route.params.id as string,
      user_id: userInfo.id,
      platform: 1,
      home_url: window.location.origin + "/game-list?id=1",
    };

    const response = await getGameUrl(params);

    if (response) {
      //  gameUrl.value = response.url;
      // gameTitle.value = (response.game_name as string) || "Jogo";
      gameUrl.value = response?.payload?.game_launch_url;
      gameTitle.value = (response?.payload?.player_name as string) || "Jogo";
    } else {
      error.value = response || "Falha ao carregar o jogo";
    }
  } catch (err) {
    console.error("Failed to load game:", err);
    error.value = "Falha ao carregar o jogo, tente novamente mais tarde";
  } finally {
    loading.value = false;
  }
};

// 切换全屏
const toggleFullscreen = async () => {
  if (!gameFrame.value) return;

  try {
    if (!document.fullscreenElement) {
      await gameFrame.value.requestFullscreen();
      isFullscreen.value = true;
    } else {
      await document.exitFullscreen();
      isFullscreen.value = false;
    }
  } catch (err) {
    console.error("Error attempting to enable fullscreen:", err);
  }
};
const toggleFullscreenHtml = async () => {
  if (!gameFrameHtml.value) return;

  try {
    if (!document.fullscreenElement) {
      await gameFrameHtml.value.requestFullscreen();
      isFullscreen.value = true;
    } else {
      await document.exitFullscreen();
      isFullscreen.value = false;
    }
  } catch (err) {
    console.error("Error attempting to enable fullscreen:", err);
  }
};
// 返回上一页
const goBack = () => {
  dialogShow.value = false;
  router.back();
};

// 监听全屏变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement;
};

// 结束游戏会话
const endGame = async (sessionKey) => {
  console.log("Ending game session...", sessionKey);
  try {
    if (sessionKey) {
      await endGameSession({
        session_key: sessionKey,
      });
    }
    setTimeout(() => {
      store.dispatch("auth/fetchUserInfo");
    }, 2000);
  } catch (err) {
    console.error("Failed to end game session:", err);
  }
};
// 结束游戏2会话
const endv2Game = async (gameId) => {
  try {
    await endv2GameSession({ game_uid: gameId });
    setTimeout(() => {
      store.dispatch("auth/fetchUserInfo");
    }, 2000);
  } catch (err) {
    console.error("Failed to end game session:", err);
  }
};
// 结束游戏3会话
const endv3Game = async (gameId, sessionKey) => {
  try {
    await endv3GameSession({
      game_uid: gameId,
      session_key: sessionKey,
    });
    setTimeout(() => {
      store.dispatch("auth/fetchUserInfo");
    }, 2000);
  } catch (err) {
    console.error("Failed to end game session:", err);
  }
};

// 處理 iframe 加載完成
const handleIframeLoad = () => {
  console.log("遊戲 iframe 加載完成");

  loading.value = false;
};

// 发送游戏状态更新到WebSocket
const sendGameStatusUpdate = async () => {
  try {
    // 确保WebSocket已连接
    if (websocketService.getIsConnected()) {
      const message = {
        type: "game_status",
        data: {
          in_game: true,
        },
      };

      const success = websocketService.send(message);
      if (success) {
        console.log("游戏状态更新已发送到WebSocket");
      } else {
        console.warn("WebSocket发送游戏状态更新失败");
      }
    } else {
      await websocketService.forceReconnect();
      const message = {
        type: "game_status",
        data: {
          in_game: true,
        },
      };

      const success = websocketService.send(message);
      if (success) {
        console.log("游戏状态更新已发送到WebSocket");
      } else {
        console.warn("WebSocket发送游戏状态更新失败");
      }
      // console.warn("WebSocket未连接，无法发送游戏状态更新");
    }
  } catch (error) {
    console.error("发送游戏状态更新时出错:", error);
  }
};
const isSend = ref(false);
// 发送游戏退出状态更新到WebSocket
const sendGameStatusExit = async () => {
  if (isSend.value) return;
  isSend.value = true;
  setTimeout(() => {
    isSend.value = false;
  }, 1000);
  try {
    // 确保WebSocket已连接
    if (websocketService.getIsConnected()) {
      const message = {
        type: "game_status",
        data: {
          in_game: false,
        },
      };

      const success = websocketService.send(message);
      if (success) {
        console.log("游戏退出状态更新已发送到WebSocket");
      } else {
        console.warn("WebSocket发送游戏退出状态更新失败");
      }
    } else {
      await websocketService.forceReconnect();
      const message = {
        type: "game_status",
        data: {
          in_game: false,
        },
      };

      const success = websocketService.send(message);
      if (success) {
        console.log("游戏退出状态更新已发送到WebSocket");
      } else {
        console.warn("WebSocket发送游戏退出状态更新失败");
      }
      // console.warn("WebSocket未连接，无法发送游戏退出状态更新");
    }
  } catch (error) {
    console.error("发送游戏退出状态更新时出错:", error);
  }
};

// 處理 iframe 加載錯誤
const handleIframeError = (err: Event) => {
  console.error("遊戲 iframe 加載失敗:", err);
  error.value = "游戏加载失败，请稍后重试";
  loading.value = false;
};

let hasSentEndGame = false;
// 新增：sendBeacon 发送函数
async function sendEndGameSessionBeacon() {
  if (hasSentEndGame) {
    console.log("游戏结束接口已发送，防重处理生效");
    return;
  }
  hasSentEndGame = true;
  const gameParams = localStorage.getItem("gameParams");
  if (!gameParams) return;

  const { type, sessionKey, gameId } = JSON.parse(gameParams);
  if (type === 2) {
    await endv2Game(gameId);
  } else if (type === 3) {
    await endv3Game(gameId, sessionKey);
  } else {
    await endGame(sessionKey);
  }
  // 发送游戏状态更新到WebSocket（用户离开游戏）
  sendGameStatusExit();
  localStorage.removeItem("gameParams");
}

onMounted(async () => {
  startGame();
  // 发送游戏状态更新到WebSocket
  sendGameStatusUpdate();
  document.addEventListener("fullscreenchange", handleFullscreenChange);
  // 添加页面卸载事件监听器
  window.addEventListener("beforeunload", handleBeforeUnload);
});

onBeforeRouteLeave((to, from, next) => {
  if (localStorage.getItem("gameParams")) {
    sendEndGameSessionBeacon();
  } else {
    sendGameStatusExit();
  }
  next();
});

onBeforeUnmount(async () => {
  if (localStorage.getItem("gameParams")) {
    // 使用 sendBeacon 通知后端
    await sendEndGameSessionBeacon();
  } else {
    // 如果没有游戏参数，也发送游戏退出状态
    sendGameStatusExit();
  }

  document.removeEventListener("fullscreenchange", handleFullscreenChange);

  // 移除页面卸载事件监听器
  window.removeEventListener("beforeunload", handleBeforeUnload);
});

// 处理页面卸载事件
const handleBeforeUnload = async (event: BeforeUnloadEvent) => {
  // 使用 sendBeacon 通知后端
  await sendEndGameSessionBeacon();

  // 如需弹窗提示用户刷新/关闭，可取消注释
};
</script>

<style lang="scss" scoped>
.game-detail {
  max-width: 940px;
  margin: 0 auto;
  width: 100%;
  height: calc(100vh - 64px); /* 减去顶部导航栏高度 */
  color: white;
  display: flex;
  flex-direction: column;
  background-color: #0d3b1e;
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    // margin-top: 10%;
    height: calc(100vh - 64px); /* 减去顶部导航栏高度 */
  }
  .error-container {
    .center-imgs {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    .center-imgs img {
      max-width: 100%;
      margin: 8px 0;
    }
  }

  .game-frame-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;

    .game-header {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(10px);
      z-index: 10;
      // position: fixed;
      // top: 0;
      // left: 0;
      // right: 0;
      .game-title {
        flex: 1;
        margin: 0 16px;
        font-size: 1.2rem;
        font-weight: 500;
      }
    }

    .game-frame {
      flex: 1;
      width: 100%;
      height: 100%;
      border: none;
      background: #0d3b1e;
      background-image: linear-gradient(45deg, #0a2f18 25%, transparent 25%),
        linear-gradient(-45deg, #0a2f18 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #0a2f18 75%),
        linear-gradient(-45deg, transparent 75%, #0a2f18 75%);
      background-size: 20px 20px;
      background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
      background-color: #0d3b1e;

      &.fullscreen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 9999;
      }
    }
  }
}

// 响应式样式
@media (max-width: 768px) {
  .game-detail {
    height: calc(100vh - 56px); /* 移动端导航栏高度 */

    .game-header {
      padding: 8px;

      .game-title {
        font-size: 1rem;
      }
    }
  }
}

.back-btn {
  background: linear-gradient(0deg, #c9b737, #2abb27);
  border-radius: 15px;
}
</style>
