import type { RouteRecordRaw } from 'vue-router';

/**
 * 根据角色过滤路由
 * @param routes 路由配置
 * @param role 用户角色
 */
export function filterAuthRoutesByRole(routes: RouteRecordRaw[], role?: string) {
  if (!role) return [];

  return routes.filter(route => {
    // 检查路由是否有权限配置
    const routeRoles = route.meta?.roles as string[] | undefined;

    // 如果没有配置权限，则所有角色都可访问
    if (!routeRoles) return true;

    // 检查当前角色是否在路由允许的角色列表中
    const hasPermission = routeRoles.includes(role);

    // 如果有子路由，递归处理
    if (route.children && route.children.length) {
      route.children = filterAuthRoutesByRole(route.children, role);

      // 如果过滤后没有可访问的子路由，且当前路由也没有权限，则整个路由都不显示
      if (route.children.length === 0 && !hasPermission) {
        return false;
      }

      // 如果有可访问的子路由，则显示父级
      return route.children.length > 0;
    }

    return hasPermission;
  });
}
