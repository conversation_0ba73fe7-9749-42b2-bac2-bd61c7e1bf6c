<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <BankSearch
      v-model:model="searchParams"
      @reset="
        () => {
          resetSearchParams();
        }
      "
      @search="getDataByPage"
    >
    </BankSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="flex justify-end mb-20px">
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :is-no-add="true"
          :is-no-delete="true"
          @refresh="getData"
          ><span style="width: 1px; height: 35px; background: #e5e6eb"></span
        ></TableHeaderOperation>
      </div>
      <div class="h-[calc(100%-80px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="checkedRowKeys = $event.map((row: any) => row.id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
      </div>
      <div class="mt-20px flex justify-start">
        <ElPagination
          v-if="mobilePagination.total"
          layout="total,prev,pager,next,sizes"
          v-bind="mobilePagination"
          @current-change="mobilePagination['current-change']"
          @size-change="mobilePagination['size-change']"
        />
      </div>
    </ElCard>
  </div>
</template>

<script setup lang="tsx">
import { ElButton, ElPopconfirm, ElTag } from "element-plus";
import { useTable, useTableOperate } from "@/hooks/common/table";
import BankSearch from "./modules/bank-search.vue";
import moment from "moment";
import {
  fetchGetBankCardList,
  fetchDeleteBankCard,
  fetchUpdateBankCard,
} from "@/service/api/bankCard";
import { useAuth } from "@/hooks/business/auth";
import { getBrazilDate } from "@/utils/format";

const { hasAuth } = useAuth();

defineOptions({ name: "BankCardManage" });

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams,
  columnChecks,
} = useTable<any>({
  apiFn: fetchGetBankCardList,
  showTotal: true,
  apiParams: {
    page: 1,
    size: 20,
    user_id: undefined,
    phone: undefined,
    card_number: undefined,
    cpf: undefined,
    start_time: undefined,
    end_time: undefined,
  },
  columns: () => [
    { prop: "index", label: "序号", width: 64 },
    { prop: "user_id", label: "用户ID", width: 100 },
    { prop: "login_phone", label: "手机号", width: 120 },
    { prop: "username", label: "用户名", minWidth: 120 },
    { prop: "pix_type", label: "pix类型", minWidth: 180 },
    { prop: "card_number", label: "pix账号", minWidth: 180 },
    { prop: "cpf", label: "CPF", minWidth: 120 },
    {
      prop: "status",
      label: "状态",
      width: 100,
      formatter: (row: any) => (
        <ElTag type={row.status === "activated" ? "success" : "warning"}>
          {row.status === "activated" ? "已激活" : "未激活"}
        </ElTag>
      ),
    },
    { prop: "extended_info", label: "扩展信息", minWidth: 150 },
    {
      prop: "created_at",
      label: "创建时间",
      width: 180,
      formatter: (row: any) =>
        moment(getBrazilDate(row.created_at)).format("YYYY-MM-DD HH:mm:ss"),
    },
    {
      width: 120,
      prop: "operate",
      label: "操作",
      align: "center",
      fixed: "right",
      formatter: (row: any) =>
        hasAuth(2) && (
          <ElPopconfirm
            title="确定要删除此银行卡吗？"
            onConfirm={() => handleDelete({ id: row.id, user_id: row.user_id })}
          >
            {{
              reference: () => (
                <ElButton type="danger" plain size="small">
                  删除
                </ElButton>
              ),
            }}
          </ElPopconfirm>
        ),
    },
  ],
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  onDeleted,
  checkedRowKeys,
} = useTableOperate(data, getData, "id");

async function handleBatchDelete() {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.warning("请选择要删除的银行卡");
    return;
  }
  try {
    const { error } = await fetchDeleteBankCard({ id: checkedRowKeys.value });
    if (!error) {
      window.$message?.success("批量删除成功");
      getData();
    }
  } catch (err) {
    console.error("批量删除银行卡失败:", err);
    window.$message?.error("批量删除失败");
  }
}

async function handleDelete(params: { id: any; user_id?: any }) {
  try {
    const { error } = await fetchDeleteBankCard(params);
    if (!error) {
      window.$message?.success("删除成功");
      getData();
    }
  } catch (err) {
    console.error("删除银行卡失败:", err);
    window.$message?.error("删除失败");
  }
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}
</style>
