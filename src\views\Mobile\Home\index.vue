<template>
  <v-container fluid class="mobile-home-container">
    <div class="line"></div>
    <!-- 移动端顶部横幅 -->
    <div class="banner-wrapper">
      <v-carousel v-model="currentSlide" cycle height="100" hide-delimiter-background show-arrows="hover"
        interval="4000" hide-delimiters>
        <v-carousel-item v-for="(slide, i) in banners" :key="i" class="banner-item" @click="handleBannerClick(slide)">
          <v-img :src="slide.image" cover height="160" class="banner-image" loading="lazy" />
        </v-carousel-item>
      </v-carousel>
    </div>
    <!-- 移动端奖池 -->
    <div class="pa-3">
      <div class="mobile-jackpot-wrapper">
        <img src="@/assets/images/h5/home-jc-bg.png" width="100%" class="jackpot-bg" loading="lazy" />
        <div class="jackpot-content">
          <JackpotBalance :game-type="1" />
        </div>
      </div>
    </div>
    <!-- 移动端游戏分类网格 -->
    <div class="game-grid-container">
      <template v-for="(row, rowIndex) in [
        gameMenuItems.slice(0, 4),
        gameMenuItems.slice(4, 8),
      ]" :key="rowIndex">
        <div class="mobile-grid">
          <div v-for="item in row" :key="item.name" class="grid-item" @click="handleRouterGameList(item)">
            <img :src="item.icon" width="55" loading="lazy" />
            <span class="grid-item-text">{{ item.name }}</span>
          </div>
        </div>
      </template>
    </div>

    <!-- 移动端游戏搜索 -->
    <div class="search-container">
      <v-text-field placeholder="Pesquisar por nome" class="search-field" hide-details variant="outlined"
        density="compact" bg-color="rgba(255, 255, 255, 0.1)" readonly rounded @click="handleSearchClick">
        <template #prepend-inner>
          <img src="@/assets/images/query-icon.png" class="search-icon" />
        </template>
      </v-text-field>
    </div>
    <!-- 移动端游戏列表 -->
    <div class="mobile-game-list">
      <!-- 加载动画 -->
      <div v-if="loading" class="global-loading-overlay">
        <img src="@/assets/images/h5/loading.gif" alt="loading" class="global-loading-img" />
      </div>
      <!-- 动态游戏区块渲染 -->
      <template v-for="section in gameSections" :key="section.key">
        <div class="section-title mt-4" v-if="getFilteredGames(section).length">
          <span>{{ section.title }}</span>
          <v-btn v-if="
            section.key !== 'user_recent_games' &&
            section.key !== 'user_favorites'
          " variant="text" class="more-btn pa-0" @click="handleMoreClick(section.key)">
            todo
            <v-icon icon="mdi-chevron-right"></v-icon>
          </v-btn>
        </div>
        <div class="game-scroll-container" v-if="getFilteredGames(section).length">
          <div class="game-scroll" :class="{ 'history-section': section.key === 'user_recent_games' }">
            <v-card v-for="game in getFilteredGames(section)" :key="game.id" variant="text" class="game-card"
              @click="handleGameClick(game)">
              <v-img :src="game.image" cover class="game-image" loading="lazy" aspect-ratio="0.84"
                lazy-src="https://via.placeholder.com/40x40?text=..."></v-img>
              <div class="star-icon" :class="{
                'star-icon-his': section.key === 'user_recent_games',
              }" @click.stop="handleCollection(game, game.is_favorite)">
                <v-icon :icon="game.is_favorite ? 'mdi-star' : 'mdi-star-outline'"
                  :color="game.is_favorite ? '#ff9000' : ''"
                  :size="section.key === 'user_recent_games' ? 14 : 22"></v-icon>
              </div>
              <v-card-text class="pa-2" v-if="section.key !== 'user_recent_games'">
                <div class="game-item-label">{{ game.title }}</div>
                <div class="game-item-provider">{{ game.provider }}</div>
              </v-card-text>
            </v-card>
          </div>
        </div>
      </template>
    </div>

    <!-- 懒加载组件 -->
    <Suspense>
      <template #default>
        <PddActivityModal ref="showPddModal" @close="handlePddModalClose" @invite="handlePddModalJoin" />
      </template>
      <template #fallback>
        <div></div>
      </template>
    </Suspense>

    <Suspense>
      <template #default>
        <CommonDialog :show="commonDialogShow" @update:show="commonDialogShow = $event" :dialogObj="dialogObj" />
      </template>
      <template #fallback>
        <div></div>
      </template>
    </Suspense>

    <Suspense>
      <template #default>
        <ActivityInviteModal v-model:show="showInviteModal" :invite-code="inviteCode" @join="handleInviteModalJoin" />
      </template>
      <template #fallback>
        <div></div>
      </template>
    </Suspense>
    <PageBottom />
    <!-- 金币爆炸特效 - 固定在页面中心 -->
    <div v-if="gifShow" class="coin-explosion-overlay">
      <img ref="gifImageRef" src="@/assets/images/coin.gif" class="coin-explosion-gif" @load="onGifLoad"
        @error="onGifError" />
    </div>
    <CoinEffect :show="showCoinEffect" @finish="handleFinish" />
    <RewardDialog v-model:show="rewardDialog" :amount="rewardAmount" @close="closeRewardDialog" />
  </v-container>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import { useRouter } from "vue-router";
import navIcon01 from "@/assets/images/h5/nav-icon-01.png";
import navIcon02 from "@/assets/images/h5/nav-icon-02.png";
import navIcon07 from "@/assets/images/h5/nav-icon-07.png";
import navIcon04 from "@/assets/images/h5/nav-icon-04.png";
import navIcon05 from "@/assets/images/h5/nav-icon-05.png";
import navIcon06 from "@/assets/images/h5/nav-icon-06.png";
import navIcon08 from "@/assets/images/h5/nav-icon-08.png";
import navIcon09 from "@/assets/images/h5/nav-icon-09.png";
import navIcon10 from "@/assets/images/h5/nav-icon-10.png";
import { getCarouselList, enterPddActivity } from "@/api/home";
import {
  getGameFavorites,
  addGameCollection,
  removeGameCollection,
  type GameFavoritesResponse,
} from "@/api/game";
import { showError, showSuccess } from "@/utils/toast";
import { getToken } from "@/utils/auth";
import { checkActivityPopup, homePopup, supportActivity } from "@/api/activity";
import { useStore } from "vuex";
import JackpotBalance from "@/components/JackpotBalance.vue";
import PddActivityModal from "@/components/PddActivityModal.vue";
import CommonDialog from "@/components/CommonDialog.vue";
import ActivityInviteModal from "@/components/ActivityInviteModal.vue";
import { loginEvent, SHOW_LOGIN_EVENT } from "@/api/request.ts";
import { getShortLinkDetail } from "@/api/auth";
import { parseUrlParams } from "@/utils/url";
import CoinEffect from "@/components/CoinEffect.vue";
import PageBottom from "@/components/PageBottom.vue";
import RewardDialog from "@/components/RewardDialog.vue";

interface Game {
  id: number;
  title: string;
  image: string;
  provider: string;
  is_favorite: boolean;
  collection_id?: number;
  game_uid: string;
}

interface Banner {
  image: string;
  link: string;
}

interface PddActivityResponse {
  content: string;
  end_time: string;
  id: number;
  name: string;
  status: number;
}

interface HomePopupResponse {
  id: number;
  title: string;
  content: string;
  image_url: string;
  hyperlink: string;
  show_type: number;
  weight: number;
  created_at: string;
  confirm_text: string;
}

interface ShortLinkResponse {
  origin_link?: string;
  [key: string]: any;
}

interface GameSection {
  key: string;
  title: string;
  games: Game[];
}

const router = useRouter();
const store = useStore();

// 首页活动弹框
const commonDialogShow = ref(false);
const dialogObj = ref<HomePopupResponse>();

// 性能优化：缓存数据状态
const dataLoaded = ref(false);
const isInitializing = ref(false);

// 性能优化：防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 性能优化：图片预加载
const preloadImages = (urls: string[]) => {
  urls.forEach((url) => {
    if (url) {
      const img = new Image();
      img.src = url;
    }
  });
};

// 性能优化：错误重试机制
const retryRequest = async (requestFn: () => Promise<any>, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise((resolve) => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
};

const getHomePopup = async () => {
  try {
    const res = (await homePopup()) as HomePopupResponse[];
    if (res && res.length) {
      commonDialogShow.value = true;
      dialogObj.value = res[0];
    }
  } catch (error) {
    console.error("Failed to load home popup:", error);
  }
};

// 金币爆炸特效
const gifShow = ref(false);
const gifImageRef = ref<HTMLImageElement | null>(null);
// 显示奖励弹窗
const rewardDialog = ref(false);
const rewardAmount = ref();
// GIF加载完成处理
const onGifLoad = () => {
  console.log("GIF loaded");
  gifShow.value = false;
  if (rewardAmount.value) {
    rewardDialog.value = true;
  } else {
    showPddModal.value?.showDialog();
  }
};

// GIF加载错误处理
const onGifError = () => {
  console.error("Failed to load GIF");
  gifShow.value = false;
  if (rewardAmount.value) {
    rewardDialog.value = true;
  } else {
    showPddModal.value?.showDialog();
  }
};
const closeRewardDialog = () => {
  rewardDialog.value = false;
  rewardAmount.value = "";
  showPddModal.value?.showDialog();
};

// 游戏菜单项
const gameMenuItems = ref([
  { name: "Salão", icon: navIcon06, path: "/" },
  { name: "Bônus Slots", icon: navIcon07, path: "/game-list", id: 3 },
  { name: "Hot", icon: navIcon02, path: "/game-list", id: 1 },
  { name: "Slots", icon: navIcon01, path: "/game-list", id: 2 },
  { name: "Cassino ao Vivo", icon: navIcon04, path: "/game-list", id: 4 },
  { name: "Pôquer", icon: navIcon08, path: "/game-list", id: 7 },
  { name: "Pescaria", icon: navIcon09, path: "/game-list", id: 8 },
  { name: "Coluna", icon: navIcon10, path: "/game-list", id: 9 },
]);

// 当前轮播图索引
const currentSlide = ref(0);

// 轮播图数据
const banners = ref<Banner[]>([]);

// 热门游戏数据
interface GameSection {
  key: string;
  title: string;
  games: Game[];
}
const gameSections = ref<GameSection[]>([]);

// 显示拼多多活动弹窗
const showPddModal = ref();

// 显示活动助力弹窗
const showInviteModal = ref(false);
const inviteCode = ref("");

// 检查拼多多活动
const checkPddActivity = async () => {
  try {
    const response = (await retryRequest(() =>
      enterPddActivity()
    )) as PddActivityResponse;
    if (!response.id) {
      showPddModal.value?.showDialog();
    } else {
      sessionStorage.setItem("pddActivityData", JSON.stringify(response));
    }
  } catch (error) {
    console.log("Failed to check PDD activity:", error);
  }
};

// 性能优化：并行加载数据
const loadInitialData = async () => {
  if (isInitializing.value || dataLoaded.value) return;

  isInitializing.value = true;
  loading.value = true;

  try {
    // 并行执行所有API请求
    const [bannersResponse, gamesResponse, popupResponse] =
      await Promise.allSettled([
        retryRequest(() =>
          getCarouselList({
            position: 2,
            page: 1,
            size: 10,
          })
        ),
        retryRequest(() => getGameFavorites()),
        retryRequest(() => homePopup()),
      ]);
    // 处理轮播图数据
    if (bannersResponse.status === "fulfilled" && bannersResponse.value) {
      banners.value = bannersResponse.value.map((banner: any) => ({
        image: banner.image_url,
        link: banner.hyperlink,
      }));

      // 预加载轮播图图片
      preloadImages(banners.value.map((banner) => banner.image));
    }

    // 处理游戏数据
    if (gamesResponse.status === "fulfilled" && gamesResponse.value) {
      const response = gamesResponse.value as GameFavoritesResponse;
      const sectionMap = [
        { key: "user_favorites", title: "Favorito" },
        { key: "user_recent_games", title: "História" },
        { key: "bonus_slots_games", title: "Bônus Slots" },
        { key: "system_hot_games", title: "Hot" },
        { key: "slots_games", title: "Slots" },
        { key: "live_casino_games", title: "Cassino ao Vivo" },
        { key: "cartoes_games", title: "Pôquer" },
        { key: "pescaria_games", title: "Pescaria" },
        { key: "coluna_games", title: "Coluna" },
      ];
      gameSections.value = sectionMap
        .map((item) => {
          const games =
            (response as any)[item.key]?.slice(0, 12)?.map((game: any) => ({
              id: game.id,
              title: game.game_name,
              image: game.icon,
              provider: game.manufacturer,
              is_favorite: game.is_favorite,
              collection_id: game.collection_id,
              game_uid: game.game_uid,
            })) || [];
          if (games.length) preloadImages(games.map((g: any) => g.image));
          return { ...item, games };
        })
        .filter((item) => item.games && item.games.length > 0);
    }

    // 处理弹窗数据
    if (popupResponse.status === "fulfilled" && popupResponse.value) {
      const popupData = popupResponse.value as HomePopupResponse[];
      if (popupData.length && popupData[0]) {
        commonDialogShow.value = true;
        dialogObj.value = popupData[0];
      }
    }

    dataLoaded.value = true;
  } catch (error) {
    console.error("Failed to load initial data:", error);
    showError("Falha ao carregar dados");
  } finally {
    loading.value = false;
    isInitializing.value = false;
  }
};

// 性能优化：只重新加载游戏数据
const reloadGamesData = async () => {
  try {
    const response = (await retryRequest(() =>
      getGameFavorites()
    )) as GameFavoritesResponse;
    const sectionMap = [
      { key: "user_favorites", title: "Favorito" },
      { key: "user_recent_games", title: "História" },
      { key: "bonus_slots_games", title: "Bônus Slots" },
      { key: "system_hot_games", title: "Hot" },
      { key: "slots_games", title: "Slots" },
      { key: "live_casino_games", title: "Cassino ao Vivo" },
      { key: "cartoes_games", title: "Pôquer" },
      { key: "pescaria_games", title: "Pescaria" },
      { key: "coluna_games", title: "Coluna" },
    ];
    gameSections.value = sectionMap
      .map((item) => {
        const games =
          (response as any)[item.key]?.slice(0, 12)?.map((game: any) => ({
            id: game.id,
            title: game.game_name,
            image: game.icon,
            provider: game.manufacturer,
            is_favorite: game.is_favorite,
            collection_id: game.collection_id,
            game_uid: game.game_uid,
          })) || [];
        return { ...item, games };
      })
      .filter((item) => item.games && item.games.length > 0);
  } catch (error) {
    console.error("Failed to reload games data:", error);
    showError("Falha ao recarregar jogos");
  }
};

const loading = ref(false);

// 性能优化：优化收藏操作，避免重新加载整个列表
const handleCollection = debounce(async (game: any, isCollected: boolean) => {
  if (!getToken()) {
    loginEvent.dispatchEvent(new Event(SHOW_LOGIN_EVENT));
    return;
  }

  try {
    if (isCollected) {
      await retryRequest(() =>
        removeGameCollection({
          game_id: game.id,
        })
      );
      showSuccess("Jogo removido dos favoritos");
      game.is_favorite = false;
      // --- 同步更新收藏区块 ---
      const favSection = gameSections.value.find(
        (s) => s.key === "user_favorites"
      );
      if (favSection) {
        favSection.games = favSection.games.filter((g) => g.id !== game.id);
        // 如果收藏区块已空，可以选择移除该区块
        if (favSection.games.length === 0) {
          gameSections.value = gameSections.value.filter(
            (s) => s.key !== "user_favorites"
          );
        }
      }
    } else {
      await retryRequest(() =>
        addGameCollection({
          game_uid: game.game_uid,
          user_id: store.state.auth.user.id,
          game_id: game.id,
        })
      );
      game.is_favorite = true;
      // --- 同步更新收藏区块 ---
      let favSection = gameSections.value.find(
        (s) => s.key === "user_favorites"
      );
      if (favSection) {
        if (!favSection.games.find((g) => g.id === game.id)) {
          favSection.games.unshift({ ...game });
        }
      } else {
        // 如果没有收藏区块，创建一个
        gameSections.value.unshift({
          key: "user_favorites",
          title: "Favorito",
          games: [{ ...game }],
        });
      }
      showSuccess("Jogo adicionado aos favoritos");
    }
    // 关键：收藏/取消收藏后，刷新所有游戏区块
    await reloadGamesData();
  } catch (error) {
    console.error("Failed to handle collection:", error);
    showError("Falha ao atualizar favoritos");
    await reloadGamesData();
  }
}, 300);

// 处理轮播图点击
const handleBannerClick = (banner: any) => {
  if (!getToken()) {
    loginEvent.dispatchEvent(new Event(SHOW_LOGIN_EVENT));
    return;
  }
  if (banner.link) {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    if (isIOS) {
      setTimeout(() => {
        window.location.href = banner.link;
      }, 100);
    } else {
      window.open(banner.link, "_self");
    }
  }
};

// 处理游戏分类点击
const handleRouterGameList = (game: any) => {
  if (!game?.id) {
    // 性能优化：只在需要时重新加载
    dataLoaded.value = false;
    loadInitialData();
  } else {
    router.push(`/m${game.path}?id=${game.id}`);
  }
};

// 处理游戏点击
const handleGameClick = (game: any) => {
  if (!getToken()) {
    loginEvent.dispatchEvent(new Event(SHOW_LOGIN_EVENT));
    return;
  }
  router.push(`/m/game/${game.game_uid}?uuid=` + game.id);
};

// 处理搜索点击
const handleSearchClick = () => {
  router.push("/m/game-search");
};

// 处理拼多多活动弹窗关闭
const handlePddModalClose = () => {
  showPddModal.value?.handleClose();
};

// 处理拼多多活动弹窗加入
const handlePddModalJoin = () => {
  router.push("/activity/Atividades-Pinduoduo");
};

// 处理助力弹窗参与
const handleInviteModalJoin = async () => {
  try {
    const res = await retryRequest(() =>
      supportActivity({
        invite_code: inviteCode.value,
        is_new: localStorage.getItem("isNew"),
      })
    );
    if (res && res?.amount) {
      rewardAmount.value = res.amount;
    }
    showInviteModal.value = false;
    gifShow.value = true;
  } catch (error) {
    showInviteModal.value = false;
    console.error("Failed to support activity:", error);
  }
  localStorage.removeItem("inviteCode");
  localStorage.setItem("isNew", "false");
};

const activityObj = ref<any>(); // 解析后的活动数据

// 性能优化：监听 Vuex 中的 token 变化
watch(
  () => store.state.auth.token,
  async (newToken) => {
    if (newToken) {
      // 登录后始终重新加载游戏数据
      reloadGamesData();

      // 只在数据未加载时重新加载全部数据
      if (!dataLoaded.value) {
        loadInitialData();
      }
      const code = activityObj.value?.pdd_invite_code;
      if (!sessionStorage.getItem("pddActivityData") && !code) {
        checkPddActivity();
      }

      if (code) {
        // 检测用户是否参加过该邀请活动
        const data = await checkActivityPopup(activityObj.value.pdd_invite_code);
        if (data && data?.code !== 200) {
          localStorage.removeItem("inviteCode");
          return;
        }
        inviteCode.value = activityObj.value.pdd_invite_code;
        showInviteModal.value = true;
      }
    } else {
      showInviteModal.value = false;
      handlePddModalClose();
    }
  },
  { immediate: true }
);

const showCoinEffect = ref(false);
function handleFinish() {
  showCoinEffect.value = false;
  if (
    getToken() &&
    !sessionStorage.getItem("pddActivityData") &&
    !localStorage.getItem("inviteCode")
  ) {
    checkPddActivity();
  }
}

// 性能优化：生命周期钩子
onMounted(async () => {
  // 并行加载数据以提高性能
  await Promise.allSettled([loadInitialData(), getHomePopup()]);

  // 处理邀请码逻辑
  if (localStorage.getItem("inviteCode")) {
    try {
      const res = (await retryRequest(() =>
        getShortLinkDetail(localStorage.getItem("inviteCode") || "")
      )) as ShortLinkResponse;
      const origin_link = res?.origin_link || "";
      if (origin_link) {
        activityObj.value = parseUrlParams(origin_link);
        console.log(activityObj.value);

        if (
          activityObj.value?.pdd_invite_code &&
          getToken()
        ) {
          // 检测用户是否参加过该邀请活动
          const data = await checkActivityPopup(activityObj.value.pdd_invite_code);
          if (data && data?.code !== 200) {
            localStorage.removeItem("inviteCode");
            return;
          }
          inviteCode.value = activityObj.value.pdd_invite_code;
          showInviteModal.value = true;
        }
      }
    } catch (err) {
      console.error("Failed to get short link detail:", err);
    }
  }

  // 处理金币特效和活动检查
  if (!sessionStorage.getItem("hasShownCoinEffect")) {
    showCoinEffect.value = true;
    sessionStorage.setItem("hasShownCoinEffect", "1");
  } else if (
    getToken() &&
    !sessionStorage.getItem("pddActivityData") &&
    !localStorage.getItem("inviteCode")
  ) {
    checkPddActivity();
  }
});

// 性能优化：清理资源
onUnmounted(() => {
  dataLoaded.value = false;
  isInitializing.value = false;
});

// 过滤游戏数据的方法
const getFilteredGames = (section: GameSection) => {
  if (section.key !== "user_recent_games") {
    return section.games.slice(0, 6);
  }
  return section.games;
};

const handleMoreClick = (key: string) => {
  const map: Record<string, string> = {
    system_hot_games: "/m/game-list?id=1",
    bonus_slots_games: "/m/game-list?id=3",
    slots_games: "/m/game-list?id=2",
    live_casino_games: "/m/game-list?id=4",
    cartoes_games: "/m/game-list?id=7",
    pescaria_games: "/m/game-list?id=8",
    coluna_games: "/m/game-list?id=9",
    // 其他映射
  };
  if (map[key]) {
    router.push(map[key]);
  }
};
</script>

<style lang="scss" scoped>
.mobile-home-container {
  margin-top: -10px;
  background: url("@/assets/images/h5/home-bg.png") no-repeat;
  background-size: 100% 100%;
  min-height: 100%;
  box-sizing: border-box;
  padding: 2px 0 0 0 !important;

  .line {
    height: 2px;
    width: 100%;
    background: linear-gradient(to right, transparent, #e0ce04, transparent);
    position: fixed;
    z-index: 9999;
    top: 56px;
  }
}

.banner-wrapper {
  margin: 12px;

  :deep(.v-carousel) {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .banner-image {
    border-radius: 12px;
  }
}

.notice-container {
  padding: 0 16px;
  margin-bottom: 16px;

  .notice-bar {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 8px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    backdrop-filter: blur(10px);

    .notice-icon {
      width: 20px;
      height: 20px;
      flex-shrink: 0;
    }

    .notice-scroll {
      flex: 1;
      overflow: hidden;
      position: relative;

      .scroll-content {
        display: flex;
        transition: transform 0.5s linear;
        white-space: nowrap;
      }

      .notice-items {
        display: inline-flex;
        gap: 24px;
        padding-right: 24px;

        .notice-item {
          color: rgba(255, 255, 255, 0.9);
          font-size: 0.85rem;
          display: flex;
          align-items: center;
          gap: 4px;

          &.important {
            color: #ff206e;
            font-weight: 500;
          }
        }
      }
    }
  }
}

.game-grid-container {
  padding: 0 6px;

  .mobile-grid {
    width: 100%;
    display: flex;
    justify-content: space-between;
    gap: 4px;

    .grid-item {
      flex: 1;
      background: none;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
        background: rgba(255, 255, 255, 0.12);
      }

      .grid-item-text {
        color: white;
        font-size: 0.75rem;
        font-weight: 500;
        text-align: center;
      }
    }
  }
}

.mobile-game-list {
  padding: 0 16px;

  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    span {
      color: white;
      font-size: 1.1rem;
      font-weight: 600;
    }

    .more-btn {
      color: #005223;
      font-size: 1rem;
      font-weight: bold;
      text-transform: none;
      background: linear-gradient(180deg, #ffea30 0%, #6db60e 100%);
      border-radius: 4px 4px 4px 4px !important;
      height: 1.8rem;
      padding: 0 0.2rem !important;

      .v-icon {
        font-size: 1.2rem;
      }
    }
  }

  .game-scroll-container {
    margin-bottom: 24px;
    overflow: hidden;

    .game-scroll {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 6px;
      padding: 4px 0;
      scrollbar-width: none;
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none;
      }

      &.history-section {
        grid-template-columns: repeat(6, 1fr);
      }

      .game-card {
        cursor: pointer;
        position: relative;
        transition: transform 0.2s;

        &:hover {
          transform: scale(1.05);
        }

        .star-icon {
          position: absolute;
          background: rgba(0, 0, 0, 0.5);
          z-index: 9;
          top: 0;
          right: 0;
          border-radius: 0 8px 0 8px;
          padding: 4px;
          cursor: pointer;
        }

        .star-icon-his {
          padding: 0px 2px;
        }

        .game-image {
          width: 100%;
          border-radius: 8px;
        }

        .game-item-label {
          font-size: 0.8rem;
          font-weight: 400;
        }

        .game-item-provider {
          color: #ce6a22;
          font-size: 14px;
        }
      }
    }
  }
}

.search-container {
  padding: 0 16px;
  margin: 16px 0;

  .search-field {
    cursor: pointer;

    :deep(.v-field) {
      border-radius: 25px;
      backdrop-filter: blur(10px);
      border: 2px solid #e0ce04;
      background: linear-gradient(135deg,
          rgba(224, 206, 4, 0.15),
          rgba(255, 144, 0, 0.1));
      transition: all 0.3s ease;

      &:hover {
        border-color: #ff9000;
        box-shadow: 0 6px 20px rgba(224, 206, 4, 0.3);
        background: linear-gradient(135deg,
            rgba(224, 206, 4, 0.25),
            rgba(255, 144, 0, 0.2));
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(224, 206, 4, 0.2);
      }
    }

    :deep(.v-field__input) {
      color: #ffffff;
      font-size: 0.9rem;
      font-weight: 500;

      &::placeholder {
        color: rgba(255, 255, 255, 0.8);
        font-weight: 400;
      }
    }

    .search-icon {
      width: 22px;
      height: 22px;
      opacity: 1;
      margin-right: 8px;
      filter: brightness(1.2) contrast(1.1);
    }
  }
}

.mobile-jackpot-wrapper {
  position: relative;
  width: 100%;

  .jackpot-bg {
    display: block;
    width: 100%;
  }

  .jackpot-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    height: 100%;
    z-index: 1;
  }
}

/* 金币爆炸特效样式 */
.coin-explosion-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  pointer-events: none;
}

.coin-explosion-gif {
  max-width: 500px;
  max-height: 500px;
  object-fit: contain;
}
</style>

<style scoped>
/* 全局加载动画样式 */
.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.25);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.global-loading-img {
  width: 100px;
  height: 100px;
}
</style>
