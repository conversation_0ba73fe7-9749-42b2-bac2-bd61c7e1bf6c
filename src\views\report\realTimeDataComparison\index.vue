<script setup lang="tsx">
import { ElButton, ElCard, ElForm, ElFormItem, ElInput, ElTable, ElTableColumn, ElPagination, ElDatePicker } from 'element-plus';
import { useRouter } from 'vue-router';
import { getWalletList } from '@/service/api/wallet';
import type { WalletListParams, WalletData } from '@/service/api/wallet';
import { useTable } from '@/hooks/common/table';
import { formatDate } from '@/utils/format';
import { getRealTimeDataReport } from '@/service/api/report';
import type { RealTimeData, RealTimeDataParams } from '@/service/api/report';
import { ref, onMounted } from 'vue';

const router = useRouter();

// 默认时间范围（最近一周）
const defaultStartDate = '2025-04-24';
const defaultEndDate = '2025-05-01';

// 日期范围
const dateRange = ref<[string, string]>([defaultStartDate, defaultEndDate]);
// 表格Hook配置
const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  resetSearchParams,
  searchParams,
} = useTable({
  apiFn: getRealTimeDataReport,
  apiParams:{
    start_time: defaultStartDate,
    end_time: defaultEndDate
  },
  columns: () => [
    { prop: 'index', label: '序号', width: 64 },
    { prop: 'date', label: '日期', minWidth: 120 },
    { prop: 'register_users', label: '注册人数', width: 100 },
    { prop: 'invited_users', label: '邀请人数', width: 100 },
    { prop: 'first_recharge_users', label: '首充人数', width: 100 },
    {
      prop: 'first_recharge_amount',
      label: '首充金额',
      width: 120,
      formatter: (row: RealTimeData) => `¥${(row.first_recharge_amount / 100).toFixed(2)}`
    },
    { prop: 'recharge_users', label: '充值人数', width: 100 },
    { prop: 'withdrawal_users', label: '提现人数', width: 100 },
    { prop: 'recharge_orders', label: '充值单量', width: 100 },
    { prop: 'withdrawal_orders', label: '提现单量', width: 100 },
    {
      prop: 'recharge_amount',
      label: '充值金额',
      width: 120,
      formatter: (row: RealTimeData) => `¥${(row.recharge_amount / 100).toFixed(2)}`
    },
    {
      prop: 'withdrawal_amount',
      label: '提现金额',
      width: 120,
      formatter: (row: RealTimeData) => `¥${(row.withdrawal_amount / 100).toFixed(2)}`
    }
  ]
});

// 查看详情
const handleViewDetail = (row: WalletData) => {
  router.push({
    path: '/report/subLevelUsers',
    query: {
      father_id: row.user_id
    }
  });
};

// 日期范围变化处理
function handleDateChange(dates: [string, string] | null) {
  if (!dates) {
    searchParams = {
      start_time: defaultStartDate,
      end_time: defaultEndDate
    };
    return;
  }

  const [startDate, endDate] = dates;
  searchParams.start_time = startDate;
  searchParams.end_time = endDate;
  getData();
}

// 重置搜索
function handleReset() {
  dateRange.value = [defaultStartDate, defaultEndDate];
  resetSearchParams();
  getData();
}

// 初始化加载数据
onMounted(() => {
  getData();
});

defineOptions({ name: 'RealTimeDataComparison' });
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <ElCard class="sm:flex-1-hidden card-wrapper" body-class="ht50">
      <template #header>
        <div class="flex items-center justify-between">
          <p>实时数据对比</p>
          <div class="flex items-center gap-16px">
            <ElDatePicker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
            <ElButton @click="handleReset">重置</ElButton>
          </div>
        </div>
      </template>

      <div class="h-[calc(100%-50px)]">
        <ElTable
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col">
            <template #default="scope" v-if="col.slot">
              <component :is="col.render" :scope="scope" />
            </template>
          </ElTableColumn>
        </ElTable>
      </div>
    </ElCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 4px;
  border: none;
  .ht50 {
    height: calc(100% - 50px);
  }
}

.search-card {
  :deep(.el-form--inline) {
    .el-form-item {
      margin-right: 16px;
      margin-bottom: 16px;

      &:last-child {
        margin-right: 0;
      }

      .el-input,
      .el-select {
        width: 200px;
      }
    }
  }
}
</style>
