import request from "./request";
import type {
  GameListParams,
  ApiResponse,
  PaginationData,
  GameItem,
} from "@/types/game";

// 获取游戏列表
export function getGameList(params: GameListParams = {}) {
  return request.Get<ApiResponse<[GameItem]>>("/game/list", {
    params,
  });
}

// 获取游戏列表
export function searchGetGameList(params: GameListParams = {}) {
  return request.Get<ApiResponse<[GameItem]>>("/game/search", {
    params,
  });
}

// 获取游戏URL
export const getGameUrl = (params: {
  game_id: string | number;
  user_id: string | number;
  platform?: number;
  home_url: string;
}) => {
  return request.Get("/game/url", {
    params: { ...params, _v: Date.now() },
  });
};

// export const getGameUrl = (params: {
//   game_id: string | number;
//   user_id: string | number;
//   platform: number;
//   home_url: string;
// }) => {
//   return request.Get("/game/getGameUrl", {
//     params: { ...params, _v: Date.now() },
//   });
// };
// 获取游戏厂家列表
export const getManufacturer = (params?: { manufacturer_type?: string }) => {
  return request.Get("/game/getManufacturer", { params:{ ...params, _v: Date.now() } });
};

// 开始游戏会话
export const startGameSession = (params: {
  user_id: string | number;
  game_uid: string;
}) => {
  return request.Post("/game/session/start", { ...params, _v: Date.now() });
};

// 获取游戏url地址
export const startGameSessionStartv1 = (params: {
  user_id: string | number;
  game_uid: string;
}) => {
  return request.Post("/game/session/startv1", { ...params, _v: Date.now() });
};

// 获取游戏url地址 渠道3进入游戏
export const startGameSessionStartv3 = (params: {
  user_id: string | number;
  game_uid: string;
}) => {
  return request.Post("/game/session/startv3", { ...params, _v: Date.now() });
};

// 渠道3转入转出金额
export const sessionTransfer = (params: { game_uid: string }) => {
  return request.Post("/game/session/transfer", { ...params, _v: Date.now() });
};

// 获取游戏html页面
export const startGameSessionStartv2 = (params: {
  user_id: string | number;
  game_uid: string;
}) => {
  return request.Post(
    "/game/session/startv2",
    { ...params, _v: Date.now() },
    {
      responseType: "text",
    }
  );
};

// 结束游戏会话
export const endGameSession = (params: { session_key: string }) => {
  return request.Post("/game/session/end", { ...params, _v: Date.now() });
};

// 结束游戏2会话
export const endv2GameSession = (params: any) => {
  return request.Post("/game/session/endv2", { ...params, _v: Date.now() });
};

// 结束游戏3会话
export const endv3GameSession = (params: any) => {
  return request.Post("/game/session/endv3", { ...params, _v: Date.now() });
};

// 游戏开始
export const setStartGameSeesion = (params: any) => {
  return request.Post("/game/session/setStartGameSeesion", {
    ...params,
    _v: Date.now(),
  });
};

export interface GameFavoritesResponse {
  [key:string]: number |GameItem[];
  status_code: number;
  system_hot_games: GameItem[];
  user_favorites: GameItem[];
}

// 获取游戏收藏列表
export const getGameFavorites = () => {
  return request.Get<GameFavoritesResponse>("/game/collection/favorites", {
    params: { _v: Date.now() },
  });
};

// 添加游戏收藏
export const addGameCollection = (params: {
  game_uid: string;
  user_id: number;
  game_id: number;
}) => {
  return request.Post("/game/collection/add", params);
};

// 移除游戏收藏
export const removeGameCollection = (params: { game_id: string }) => {
  return request.Get("/game/collection/delete", {
    params: { ...params, _v: Date.now() },
  });
};

export interface GameTransaction {
  agency_uid: string;
  bet_amount: number;
  code_amount: number;
  create_at: number;
  currency_code: string;
  game_category: string;
  game_data: string;
  game_round: string;
  game_uid: string;
  id: number;
  member_account: string;
  serial_number: string;
  trans_time: number;
  update_at: number;
  user_id: number;
  win_amount: number;
  total_balance: number;
}

export interface GameTransactionsResponse {
  status_code: number;
  data: GameTransaction[];
  count: number;
}

/**
 * 获取游戏交易记录
 * @param params 查询参数
 */
export const getGameTransactions = (params: {
  page?: number;
  size?: number;
}) => {
  return request.Get<GameTransactionsResponse>("/game/transactions", {
    params: { ...params, _v: Date.now() },
  });
};
