<template>
  <ElDrawer
    :model-value="visible"
    :title="operateType === 'edit' ? '编辑奖池' : '新增奖池'"
    size="700px"
    @close="handleCancel"
  >
    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      label-position="top"
      class="jackpot-form"
    >
      <!-- 奖池名称 -->
      <ElFormItem label="奖池名称" prop="jackpot_name">
        <ElInput v-model="formData.jackpot_name" placeholder="请输入奖池名称" />
      </ElFormItem>

      <!-- 时间区块 -->
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="开始时间" prop="start_time">
            <ElDatePicker
              v-model="formData.start_time"
              type="date"
              placeholder="请选择开始时间"
              format="YYYY-MM-DD"
              value-format="x"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="结束时间" prop="end_time">
            <ElDatePicker
              v-model="formData.end_time"
              type="date"
              placeholder="请选择结束时间"
              format="YYYY-MM-DD"
              value-format="x"
            />
            <div class="form-desc">到达时间后派发奖池</div>
          </ElFormItem>
        </ElCol>
      </ElRow>

      <!-- 奖金设置 -->
      <div class="form-section-title">奖金设置(固定奖金金额或种子金额+利润百分比)</div>

      <!-- 奖金类型 -->
      <ElFormItem label="" prop="jackpot_calculation_mode">
        <ElRadioGroup v-model="formData.jackpot_calculation_mode">
          <ElRadio :label="1">固定奖金金额</ElRadio>
          <ElRadio :label="2">种子金额+利润百分比</ElRadio>
        </ElRadioGroup>
      </ElFormItem>
      <ElRow :gutter="20">
        <template v-if="formData.jackpot_calculation_mode === 1">
          <ElCol :span="8">
            <ElFormItem label="固定奖金金额" prop="fixed_bonus">
              <ElInputNumber
                v-model="formData.fixed_bonus"
                :min="0"
                style="width: 100%"
              />
            </ElFormItem>
          </ElCol>
        </template>
        <template v-else>
          <ElCol :span="8">
            <ElFormItem label="初始种子金额" prop="initial_seed_amount">
              <ElInputNumber
                v-model="formData.initial_seed_amount"
                :min="0"
                style="width: 100%"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem
              label="平台利润百分比"
              prop="platform_profit_percentage"
            >
              <div style="display: flex; align-items: center; width: 100%">
                <ElSlider
                  v-model="formData.platform_profit_percentage"
                  :min="0"
                  :max="20"
                  style="flex: 1"
                />
                <span style="width: 48px; text-align: right; margin-left: 8px"
                  >{{ formData.platform_profit_percentage }}%</span
                >
              </div>
            </ElFormItem>
          </ElCol>
        </template>
      </ElRow>

      <!-- 积分参数设置 -->
      <div class="form-section-title">积分参数设置</div>
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem
            label="充值金额积分比例"
            prop="recharge_amount_points_ratio"
          >
            <div style="display: flex; align-items: center; width: 100%">
              <ElSlider
                v-model="formData.recharge_amount_points_ratio"
                :min="0"
                :max="100"
                step="1"
                style="flex: 1"
              />
              <span style="text-align: left; margin-left: 16px"
                >{{ formData.recharge_amount_points_ratio }}%</span
              >
            </div>
            <div class="form-desc">根据用户充值金额计算获得的积分比例</div>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="投注金额积分比例" prop="bet_amount_points_ratio">
            <div style="display: flex; align-items: center; width: 100%">
              <ElSlider
                v-model="formData.bet_amount_points_ratio"
                :min="0"
                :max="100"
                step="1"
                style="flex: 1"
              />
              <span style="text-align: right; margin-left: 16px"
                >{{ formData.bet_amount_points_ratio }}%</span
              >
            </div>
            <div class="form-desc">根据用户投注金额计算获得的积分比例</div>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="投注次数积分比例" prop="bet_count_points_ratio">
            <div style="display: flex; align-items: center; width: 100%">
              <ElSlider
                v-model="formData.bet_count_points_ratio"
                :min="0"
                :max="100"
                step="1"
                style="flex: 1"
              />
              <span style="text-align: left; margin-left: 16px"
                >{{ formData.bet_count_points_ratio }}%</span
              >
            </div>
            <div class="form-desc">根据用户投注次数计算获得的积分比例</div>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label="中奖金额积分比例"
            prop="winning_amount_points_ratio"
          >
            <div style="display: flex; align-items: center; width: 100%">
              <ElSlider
                v-model="formData.winning_amount_points_ratio"
                :min="0"
                :max="100"
                step="1"
                style="flex: 1"
              />
              <span style="text-align: right; margin-left: 16px"
                >{{ formData.winning_amount_points_ratio }}%</span
              >
            </div>
            <div class="form-desc">根据用户中奖金额计算获得的积分比例</div>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label="邀请注册积分(每人)"
            prop="invite_registration_bonus_points"
          >
            <ElInputNumber
              v-model="formData.invite_registration_bonus_points"
              :min="0"
              style="width: 100%"
            />
            <div class="form-desc">
              用户每成功邀请一位新用户注册可获得的固定积分值
            </div>
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem
            label="邀请充值积分"
            prop="invite_first_recharge_points_ratio"
          >
            <!-- <div style="display: flex; align-items: center;width: 100%;">
              <ElSlider v-model="formData.invite_first_recharge_points_ratio" :min="0" :max="100" step="1" style="flex: 1;" />
              <span style="text-align: right; margin-left: 16px;">{{ formData.invite_first_recharge_points_ratio/100 }}</span>
            </div> -->
            <ElInputNumber
              v-model="formData.invite_first_recharge_points_ratio"
              :min="0"
              style="width: 100%"
            />
            <div class="form-desc">邀请的用户首次充值时，邀请人可得的积分</div>
          </ElFormItem>
        </ElCol>
      </ElRow>
      <!-- 新增规则管理 -->
      <div class="form-section-title">规则管理</div>
      <ElFormItem label="" prop="rule_content">
        <ElInput
          v-model="formData.rule_management"
          type="textarea"
          :autosize="{ minRows: 3 }"
          placeholder="请输入规则内容，字数不限"
        />
      </ElFormItem>
      <!-- 派发设置 -->
      <div class="form-section-title">奖池派发设置</div>
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElFormItem label="奖池获奖人数" prop="jackpot_participants">
            <ElInputNumber
              v-model="formData.jackpot_participants"
              :min="1"
              style="width: 100%"
            />
          </ElFormItem>
        </ElCol>
        <ElCol :span="12">
          <ElFormItem label="奖池初始状态" prop="jackpot_initial_status">
            <ElSelect
              v-model="formData.jackpot_initial_status"
              placeholder="请选择"
            >
              <ElOption label="未开启" :value="0" />
              <ElOption label="进行中" :value="1" />
              <ElOption label="已结束" :value="2" />
            </ElSelect>
          </ElFormItem>
        </ElCol>
      </ElRow>
    </ElForm>
    <template #footer>
      <div class="flex justify-end gap-2">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton type="primary" :loading="loading" @click="handleSubmit"
          >保存</ElButton
        >
      </div>
    </template>
  </ElDrawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { ElMessage } from "element-plus";
import {
  createJackpotConfig,
  updateJackpotConfig,
} from "@/service/api/jackpot";

interface Props {
  operateType: UI.TableOperateType;
  rowData?: any | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: "submitted"): void;
}

const emit = defineEmits<Emits>();
const visible = defineModel<boolean>("visible", {
  default: false,
});

const formRef = ref();
const loading = ref(false);

const formData = reactive({
  jackpot_name: "",
  start_time: "",
  end_time: "",
  jackpot_calculation_mode: 1,
  fixed_bonus: 0,
  initial_seed_amount: 0,
  platform_profit_percentage: 0,
  recharge_amount_points_ratio: 0,
  bet_amount_points_ratio: 0,
  bet_count_points_ratio: 0,
  winning_amount_points_ratio: 0,
  invite_registration_bonus_points: 0,
  invite_first_recharge_points_ratio: 0,
  jackpot_participants: 1,
  jackpot_initial_status: 0,
  rule_content: "",
});

const rules = {
  jackpot_name: [
    { required: true, message: "请输入奖池名称", trigger: "blur" },
  ],
  start_time: [
    { required: true, message: "请选择开始时间", trigger: "change" },
  ],
  end_time: [{ required: true, message: "请选择结束时间", trigger: "change" }],
  jackpot_calculation_mode: [
    { required: true, message: "请选择奖金类型", trigger: "change" },
  ],
  fixed_bonus: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (formData.jackpot_calculation_mode === 1) {
          if (
            value === undefined ||
            value === null ||
            value === "" ||
            value < 0
          ) {
            callback(new Error("请输入固定奖金金额且不能小于0"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  initial_seed_amount: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (formData.jackpot_calculation_mode === 2) {
          if (
            value === undefined ||
            value === null ||
            value === "" ||
            value < 0
          ) {
            callback(new Error("请输入初始种子金额且不能小于0"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  platform_profit_percentage: [
    {
      validator: (rule: any, value: any, callback: any) => {
        if (formData.jackpot_calculation_mode === 2) {
          if (
            value === undefined ||
            value === null ||
            value === "" ||
            value < 0 ||
            value > 100
          ) {
            callback(new Error("请输入平台利润百分比，范围0-100"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  recharge_amount_points_ratio: [
    { required: true, message: "请输入充值金额积分比例", trigger: "blur" },
    { type: "number", min: 0, max: 100, message: "范围0-100", trigger: "blur" },
  ],
  bet_amount_points_ratio: [
    { required: true, message: "请输入投注金额积分比例", trigger: "blur" },
    { type: "number", min: 0, max: 100, message: "范围0-100", trigger: "blur" },
  ],
  bet_count_points_ratio: [
    { required: true, message: "请输入投注次数积分比例", trigger: "blur" },
    { type: "number", min: 0, max: 100, message: "范围0-100", trigger: "blur" },
  ],
  winning_amount_points_ratio: [
    { required: true, message: "请输入中奖金额积分比例", trigger: "blur" },
    { type: "number", min: 0, max: 100, message: "范围0-100", trigger: "blur" },
  ],
  invite_registration_bonus_points: [
    { required: true, message: "请输入邀请注册积分", trigger: "blur" },
    { type: "number", min: 0, message: "不能小于0", trigger: "blur" },
  ],
  invite_first_recharge_points_ratio: [
    { required: true, message: "请输入邀请首充积分比例", trigger: "blur" },
    { type: "number", min: 0, max: 100, message: "范围0-100", trigger: "blur" },
  ],
  jackpot_participants: [
    { required: true, message: "请输入奖池获奖人数", trigger: "blur" },
    { type: "number", min: 1, message: "至少为1", trigger: "blur" },
  ],
  jackpot_initial_status: [
    { required: true, message: "请选择奖池初始状态", trigger: "change" },
  ],
  rule_content: [
    { required: false, message: "请输入规则内容", trigger: "blur" },
  ],
};

watch(visible, async () => {
  if (visible.value) {
    if (props.operateType === "edit") {
      let jackpot_calculation_mode = 1;
      if (
        props.rowData.initial_seed_amount > 0 ||
        props.rowData.platform_profit_percentage > 0
      ) {
        jackpot_calculation_mode = 2;
      }
      Object.assign(formData, {
        ...props.rowData,
        jackpot_calculation_mode,
        fixed_bonus: props.rowData.fixed_bonus / 100,
        initial_seed_amount: props.rowData.initial_seed_amount / 100,
        rule_content: props.rowData.rule_content || "",
      });
    } else {
      Object.assign(formData, {
        jackpot_name: "",
        start_time: "",
        end_time: "",
        jackpot_calculation_mode: 1,
        fixed_bonus: 0,
        initial_seed_amount: 0,
        platform_profit_percentage: 0,
        recharge_amount_points_ratio: 0,
        bet_amount_points_ratio: 0,
        bet_count_points_ratio: 0,
        winning_amount_points_ratio: 0,
        invite_registration_bonus_points: 0,
        invite_first_recharge_points_ratio: 0,
        jackpot_participants: 1,
        jackpot_initial_status: 0,
        rule_content: "",
      });
    }
  }
});

function handleCancel() {
  visible.value = false;
}

async function handleSubmit() {
  await formRef.value?.validate(async (valid: boolean) => {
    if (!valid) return;
    loading.value = true;
    console.log("formData.jackpot_calculation_mode",formData.jackpot_calculation_mode)
    try {
      const payload = {
        ...formData,
        fixed_bonus:
          formData.jackpot_calculation_mode === 1 ? formData.fixed_bonus * 100 : 0,
        initial_seed_amount:
          formData.jackpot_calculation_mode === 2
            ? formData.initial_seed_amount * 100
            : 0,
        platform_profit_percentage:
          formData.jackpot_calculation_mode === 2
            ? formData.platform_profit_percentage
            : 0,
        rule_content: formData.rule_content,
      };
      // 时间戳转毫秒
      payload.start_time = Number(payload.start_time);
      payload.end_time = Number(payload.end_time);
      let res;
      if (props.operateType === "edit") {
        res = await updateJackpotConfig(payload);
      } else {
        res = await createJackpotConfig(payload);
      }
      if (!res.error) {
        ElMessage.success("操作成功");
        emit("submitted");
        handleCancel();
      }
    } finally {
      loading.value = false;
    }
  });
}
</script>

<style scoped>
.jackpot-form {
  :deep(.el-form-item) {
    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.form-section-title {
  font-weight: bold;
  font-size: 16px;
  margin: 10px 0 8px 0;
}
.form-desc {
  color: #888;
  font-size: 14px;
  line-height: 24px;
  margin-top: 10px;
}
</style>
