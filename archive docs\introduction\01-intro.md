---
sidebar_position: 1
---

# What is UniPass Wallet?

UniPass Wallet is a **smart contract wallet** solution that supports **on-chain Email social recovery**. Using UniPass Wallet, developers can provide a **seedless**, **gasless** user experience familiar to Web2 users, significantly increasing the conversion rate.

UniPass Wallet can help users conveniently manage their their private keys without tools like seed phrase that are unfamiliar and error-prone for Web2 users. UniPass aims to help more people become a true citizen of Web3 and own a decentralized identity (DID), thus making  no compromise on security and censorship resistance.

Through our state-of-the-art on-chain email social recovery solution, users can choose people with no Web3 experience as the guardians of their accounts, who can assist users in social recovery using nothing more than their email accounts. Thanks to the on-chain implementation, no third-party server is needed to achieve social recovery.

UniPass Wallet aims to provide the most secure and convenient wallet experience leveraging the latest innovations in the field.

## Features of UniPass Wallet

**`Seedless`**

User uses email and password to register and login with their accounts. No need to use secret phrase and private key!

**`Censorship resistance`**

Users have full control of their accounts without relying on any third-party service. 

**`Gasless`**

You can pay the gas fee for your users to achieve a truly gasless experience. Users can also pay the gas fee in any token.

**`On-chain Email recovery`**

Users can set up guardians using only their emails. When account recovery is needed, they can easily do so by asking their guardians to submit emails appropriately.

**`Privacy protection`**

UniPass uses Zero Knowledge technology to protect users’ email information on chain.

**`Multi-platform support`**

UniPass Wallet will support all platforms and be available on website, mobile, and browser extensions. It will support major libraries and frameworks, including but not limited to web3modal, and Wallet Connect.

**`Multi-chain support`**

UniPass Wallet is compatible with all EVM chains and can ensure that users have the same address across all chains.

## Try UniPass Wallet yourself

You can try the wallet here: [**https://wallet.unipass.id/**](https://wallet.unipass.id/)