/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-29 14:04:30
 * @LastEditors: As<PERSON><PERSON>cj <EMAIL>
 * @LastEditTime: 2025-07-04 16:25:34
 * @FilePath: \betdoce-web\src\store\modules\jackpot.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Module, ActionContext } from 'vuex'
import { RootState } from '@/types/store'
import { getJackpotBalance, JackpotBalanceResponse } from '@/api/jackpot'

export interface JackpotState {
  balance: {
    amount: number
    change: number,
    time: string,
    previous_balance: number
    game_type?: string
  }
  isLoading: boolean
  error: string | null
}

type JackpotActionContext = ActionContext<JackpotState, RootState>

const jackpot: Module<JackpotState, RootState> = {
  namespaced: true,

  state: (): JackpotState => ({
    balance: {
      amount: 0,
      change: 0,
      time:'',
      previous_balance: 0
    },
    isLoading: false,
    error: null
  }),

  mutations: {
    SET_BALANCE(state: JackpotState, balance: JackpotBalanceResponse['data']) {
      state.balance = balance
    },
    SET_LOADING(state: JackpotState, isLoading: boolean) {
      state.isLoading = isLoading
    },
    SET_ERROR(state: JackpotState, error: string | null) {
      state.error = error
    }
  },

  actions: {
    async fetchJackpotBalance({ commit }: JackpotActionContext, minutesAgo: number = 1) {
      try {
        console.log('開始獲取獎池金額')
        commit('SET_LOADING', true)
        commit('SET_ERROR', null)
        
        const response = await getJackpotBalance(minutesAgo)
        console.log('獎池金額 API 響應:', response)
        
        if (response) {
          console.log('設置獎池金額到 store:', response)
          commit('SET_BALANCE', response)
          return response
        }
        console.warn('獎池金額 API 響應無效或缺少 data:', response)
        return null
      } catch (error: any) {
        const errorMessage = error?.response?.message || ''
        console.error('獲取獎池金額失敗:', error)
        commit('SET_ERROR', errorMessage)
        throw error
      } finally {
        commit('SET_LOADING', false)
      }
    },

    // 更新獎池餘額（用於 SSE 推送）
    updateJackpotBalance({ commit, state }: JackpotActionContext, msg: object) {
      console.log(msg)
      try {
        // 保存當前餘額作為前一個餘額
        const previousBalance = state.balance.amount
        
        // 計算變化值
        const change = msg.amount + previousBalance
        
        // 更新獎池數據
        commit('SET_BALANCE', {
          amount:change,
          change:msg.amount,
          time:msg.time,
          previous_balance: previousBalance
        })
      } catch (error) {
        console.error('Failed to update jackpot balance:', error)
        commit('SET_ERROR', 'Failed to update jackpot balance')
      }
    }
  },

  getters: {
    currentBalance: (state: JackpotState) => state.balance.amount,
    balanceChangeTime: (state: JackpotState) => state.balance.time,
    balanceChange: (state: JackpotState) => state.balance.change,
    previousBalance: (state: JackpotState) => state.balance.previous_balance,
    isLoading: (state: JackpotState) => state.isLoading,
    error: (state: JackpotState) => state.error
  }
}

export default jackpot 