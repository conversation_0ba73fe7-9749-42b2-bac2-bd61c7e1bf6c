<template>
  <ElDrawer
    :size="360"
    v-model="visible"
    title="操作日志详情"
    @close="closeDrawer"
  >
    <ElForm label-position="top" label-width="100px" class="dialog-info-form">
      <template v-if="rowData.operation_type === 3">
        <ElFormItem label="订鱼号">
          <ElInput :model-value="detailData.uuid" disabled />
        </ElFormItem>
        <ElFormItem label="用户ID">
          <ElInput :model-value="detailData.user_id" disabled />
        </ElFormItem>
        <ElFormItem label="充值金额">
          <ElInput
            :model-value="
              detailData.amount ? (detailData.amount / 100).toFixed(2) : '0.00'
            "
            disabled
          />
        </ElFormItem>
        <ElFormItem label="支付方式">
          <ElInput :model-value="detailData.payment_method" disabled />
        </ElFormItem>
        <ElFormItem label="充值卡号">
          <ElInput :model-value="detailData.account_num" disabled />
        </ElFormItem>
        <ElFormItem label="订单号">
          <ElInput :model-value="detailData.order_no" disabled />
        </ElFormItem>
        <ElFormItem label="交易号">
          <ElInput :model-value="detailData.transaction_no" disabled />
        </ElFormItem>
        <ElFormItem label="充值状态">
          <ElInput :model-value="detailData.status_desc" disabled />
        </ElFormItem>
        <ElFormItem label="充值次数">
          <ElInput :model-value="detailData.deposit_count" disabled />
        </ElFormItem>
        <ElFormItem label="创建时间">
          <ElInput :model-value="detailData.created_time" disabled />
        </ElFormItem>
      </template>
      <template v-if="rowData.operation_type === 4">
        <ElFormItem label="用户ID">
          <ElInput :model-value="detailData.user_id" disabled />
        </ElFormItem>
        <ElFormItem label="订鱼号">
          <ElInput :model-value="detailData.uuid" disabled />
        </ElFormItem>
        <ElFormItem label="钱包ID">
          <ElInput :model-value="detailData.wallet_id" disabled />
        </ElFormItem>
        <ElFormItem label="金额">
          <ElInput
            :model-value="
              detailData.amount ? (detailData.amount / 100).toFixed(2) : '0.00'
            "
            disabled
          />
        </ElFormItem>
        <ElFormItem label="币种">
          <ElInput :model-value="detailData.currency" disabled />
        </ElFormItem>
        <ElFormItem label="状态">
          <ElInput :model-value="detailData.status_desc" disabled />
        </ElFormItem>
        <ElFormItem label="订单号">
          <ElInput :model-value="detailData.order_no" disabled />
        </ElFormItem>
        <ElFormItem label="交易号">
          <ElInput :model-value="detailData.transaction_no" disabled />
        </ElFormItem>
        <ElFormItem label="账号">
          <ElInput :model-value="detailData.account_num" disabled />
        </ElFormItem>
        <ElFormItem label="账号类型">
          <ElInput :model-value="detailData.account_type_desc" disabled />
        </ElFormItem>
        <ElFormItem label="手续费">
          <ElInput
            :model-value="
              detailData.fee ? (detailData.fee / 100).toFixed(2) : ''
            "
            disabled
          />
        </ElFormItem>
        <ElFormItem label="支付方式">
          <ElInput :model-value="detailData.payment_method_desc" disabled />
        </ElFormItem>
        <ElFormItem label="提现次数">
          <ElInput :model-value="detailData.withdrawal_count" disabled />
        </ElFormItem>
        <ElFormItem label="创建时间">
          <ElInput :model-value="detailData.created_time" disabled />
        </ElFormItem>
      </template>
      <template v-if="rowData.operation_type === 5">
        <ElFormItem label="用户ID">
          <ElInput :model-value="detailData.user_id" disabled />
        </ElFormItem>
        <ElFormItem label="订鱼号">
          <ElInput :model-value="detailData.uuid" disabled />
        </ElFormItem>
        <ElFormItem label="流水号">
          <ElInput :model-value="detailData.serial_number" disabled />
        </ElFormItem>
        <ElFormItem label="游戏ID">
          <ElInput :model-value="detailData.game_uid" disabled />
        </ElFormItem>
        <ElFormItem label="昵称">
          <ElInput :model-value="detailData.nickname" disabled />
        </ElFormItem>
        <ElFormItem label="手机号">
          <ElInput :model-value="detailData.phone" disabled />
        </ElFormItem>
        <ElFormItem label="游戏类型">
          <ElInput :model-value="detailData.game_type" disabled />
        </ElFormItem>
        <ElFormItem label="投注金额">
          <ElInput
            :model-value="
              detailData.bet_amount
                ? (detailData.bet_amount / 100).toFixed(2)
                : '0.00'
            "
            disabled
          />
        </ElFormItem>
        <ElFormItem label="赢取金额">
          <ElInput
            :model-value="
              detailData.win_amount
                ? (detailData.win_amount / 100).toFixed(2)
                : '0.00'
            "
            disabled
          />
        </ElFormItem>
        <ElFormItem label="交易时间">
          <ElInput :model-value="detailData.trans_time_str" disabled />
        </ElFormItem>
      </template>
    </ElForm>
    <template #footer>
      <div class="flex justify-end gap-2">
        <ElButton @click="closeDrawer">关闭</ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { fetchOperationLogDetail } from "@/service/api/operationLog";

interface Props {
  /** the type of operation */
  operateType: UI.TableOperateType;
  /** the edit row data */
  rowData?: any | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: "submitted"): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>("visible", {
  default: false,
});

const detailData = ref<any>({});

function closeDrawer() {
  visible.value = false;
}

watch([() => props.visible, () => props.rowData], async ([val, rowData]) => {
  if (val && rowData) {
    const res = await fetchOperationLogDetail({
      operation_type: rowData.operation_type,
      record_id: rowData.record_id,
    });
    detailData.value = res?.data?.data?.detail || {};
  } else {
    detailData.value = {};
  }
});
</script>

<style scoped lang="scss">
.dialog-info-form {
  :deep(.el-form-item__label) {
    font-weight: 500;
    font-size: 15px;
  }

  :deep(.el-input .el-input__inner) {
    font-size: 15px;
    color: #333 !important;
  }

  :deep(.el-input.is-disabled .el-input__inner) {
    color: #333 !important;
  }
}
</style>
