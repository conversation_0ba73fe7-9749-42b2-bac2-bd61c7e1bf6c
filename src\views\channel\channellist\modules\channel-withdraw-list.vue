<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-04 13:41:36
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-04 17:58:30
 * @FilePath: \betdoce-webd:\new_project\betdoce-admin\src\views\channel\channellist\modules\channel-withdraw-list.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <el-form :inline="true" class="mb-2">
      <el-form-item label="用户ID">
        <el-input v-model="search.user_id" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          v-model="search.time"
          type="daterange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="x"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" style="width: 100%" max-height="400">
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column prop="user_id" label="用户ID" />
      <el-table-column prop="wallet_id" label="钱包ID" />
      <el-table-column prop="withdraw_amount" label="提现金额">
        <template #default="{ row }">
          {{ row.currency }} {{ formatAmount(row.withdraw_amount) }}
        </template>
      </el-table-column>
      <el-table-column prop="fee" label="手续费">
        <template #default="{ row }">
          {{ row.currency }} {{ formatAmount(row.fee) }}
        </template>
      </el-table-column>
      <el-table-column prop="order_no" label="订单号" />
      <el-table-column prop="transaction_no" label="交易号" />
      <el-table-column prop="account_type" label="账户类型">
        <template #default="{ row }">
          {{ getAccountTypeText(row.account_type) }}
        </template>
      </el-table-column>
      <el-table-column prop="account_num" label="账号" />
      <el-table-column prop="payment_method" label="支付方式" />
      <el-table-column prop="withdraw_time" label="提现时间" >
        <template #default="{ row }">
          {{ moment(getBrazilDate(row.withdraw_time)).format("YYYY-MM-DD HH:mm:ss") }}
        </template>
      </el-table-column>
      <el-table-column prop="withdrawal_count" label="提现次数" />
    </el-table>
    <el-pagination
      class="mt-2"
      :total="total"
      :page-size="pageSize"
      :current-page="page"
      @current-change="
        (val) => {
          page = val;
          fetchData();
        }
      "
      @size-change="
        (val) => {
          pageSize = val;
          page = 1;
          fetchData();
        }
      "
      layout="total, prev, pager, next, sizes"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { fetchGetChannelWithdrawRecords } from "@/service/api/channel";
import moment from "moment";
import { getBrazilDate } from "@/utils/format";

const props = defineProps<{ channel: any }>();
const search = ref({
  user_id: "",
  order_no: "",
  time: [],
});
const tableData = ref<any[]>([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(10);

const formatAmount = (amount: number) => {
  return ((amount || 0) / 100).toFixed(2);
};

const getAccountTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: "银行卡",
    2: "支付宝",
    3: "现金支付",
    4: "其他",
  };
  return typeMap[type] || "未知";
};

const getStatusType = (status: number) => {
  const statusMap: Record<number, string> = {
    0: "info", // 待处理
    1: "success", // 成功
    2: "danger", // 失败
    3: "warning", // 处理中
  };
  return statusMap[status] || "info";
};

const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: "待处理",
    1: "成功",
    2: "失败",
    3: "处理中",
  };
  return statusMap[status] || "未知";
};

function handleSearch() {
  fetchData();
}

function handleReset() {
  search.value = {
    user_id: "",
    order_no: "",
    time: [],
  };
  handleSearch();
}

function fetchData() {
  fetchGetChannelWithdrawRecords({
    channel_code: props.channel.channel_code,
    page: page.value,
    size: pageSize.value,
    user_id: search.value.user_id,
    order_no: search.value.order_no,
    start_time: search.value.time?.[0],
    end_time: search.value.time?.[1],
  }).then((response) => {
    if (response?.data) {
      tableData.value = response.data.data;
      total.value = response.data.count;
    }
  });
}

defineExpose({
  fetchData,
});
</script>

<style scoped>
.mb-2 {
  margin-bottom: 20px;
}
.mt-2 {
  margin-top: 20px;
}
</style>
