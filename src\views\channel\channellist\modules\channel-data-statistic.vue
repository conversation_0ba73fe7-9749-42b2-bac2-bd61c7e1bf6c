<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-04 13:41:02
 * @LastEditors: Asad<PERSON>cj <EMAIL>
 * @LastEditTime: 2025-06-04 17:22:21
 * @FilePath: \betdoce-admin\src\views\channel\channellist\modules\channel-data-statistic.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="statistic-wrapper">
    <div class="search-wrapper">
      <span class="search-label">时间</span>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        value-format="x"
      />
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-button @click="handleReset">重置</el-button>
    </div>
    <div class="statistic-content">
      <div class="statistic-block">
        <h2>渠道获客数据</h2>
        <div class="statistic-row"><span>注册用户</span><span>{{ stats.register_users || 0 }}</span></div>
        <div class="statistic-row"><span>充值用户</span><span>{{ stats.recharge_users || 0 }}</span></div>
      </div>
      <div class="statistic-block">
        <h2>渠道营收数据</h2>
        <div class="statistic-row"><span>充值金额R $</span><span>{{ formatAmount(stats.recharge_amount) }}</span></div>
        <div class="statistic-row"><span>打码量R $</span><span>{{ formatAmount(stats.bet_amount) }}</span></div>
        <div class="statistic-row"><span>盈亏额R $</span><span>{{ formatAmount(stats.profit_amount) }}</span></div>
        <div class="statistic-row"><span>提现金额R $</span><span>{{ formatAmount(stats.withdraw_amount) }}</span></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { fetchGetChannelStats } from "@/service/api/channel";
import { ElDatePicker, ElButton } from 'element-plus';

const props = defineProps<{ channel: any }>();
const dateRange = ref<[string, string]>(['', '']);
const stats = ref({
  register_users: 0,
  recharge_users: 0,
  recharge_amount: 0,
  bet_amount: 0,
  profit_amount: 0,
  withdraw_amount: 0
});

const handleSearch = () => {
  fetchStats();
};

const handleReset = () => {
  dateRange.value = ['', ''];
  fetchStats();
};

const formatAmount = (amount: number) => {
  if (!amount) return '0.00';
  return (amount / 100).toLocaleString('en-IN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

const fetchStats = async () => {
  try {
    const response = await fetchGetChannelStats({
      channel_code: props.channel.channel_code,
      start_time: dateRange.value[0],
      end_time: dateRange.value[1]
    });
    console.log(response?.data)
    if (response?.data.data) {
      stats.value = {
        ...response.data.data,
        recharge_amount: response.data.data.recharge_amount || 0,
        bet_amount: response.data.data.bet_amount || 0,
        profit_amount: response.data.data.profit_amount || 0,
        withdraw_amount: response.data.data.withdraw_amount || 0
      };
    }
  } catch (error) {
    console.error('获取渠道统计数据失败:', error);
  }
};

defineExpose({
  fetchStats
});
</script>

<style scoped lang="scss">
.statistic-wrapper {
  padding: 0px 0px 20px 0px;
}
.search-wrapper {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
  align-items: center;
}
.search-label {
  font-size: 14px; /* Adjust font size as needed */
  margin-right: 5px; /* Space between label and date picker */
}
.statistic-content {
  display: flex;
  justify-content: space-between;
}
.statistic-block {
  flex: 1;
  margin: 0 20px;
}
.statistic-block:nth-child(2) {
  border-left: 1px solid #ccc; /* Add a left border to the second block */
  padding-left: 40px; /* Add padding to the left for spacing */
}
.statistic-row {
  display: flex;
  justify-content: space-between;
  font-size: 18px;
  margin: 10px 0;
}
h2 {
  font-weight: bold;
  color: #000;
  font-size: 22px; /* Adjust size as needed */
}
</style>
