<script setup lang="tsx">
import { ref, onMounted, nextTick } from "vue";
import {
  ElButton,
  ElPopconfirm,
  ElTag,
  ElImage,
  ElMessage,
  ElDialog,
  ElSelect,
  ElOption,
} from "element-plus";
import {
  fetchGetGameList,
  fetchGetGameListId,
  deleteGame,
  batchDeleteGame,
  batchUpdateGameStatus,
  batchUpdateGameType,
  fetchGetGameTypes,
} from "@/service/api/gameprodect";
import { useTable, useTableOperate } from "@/hooks/common/table";
import { $t } from "@/locales";
import ProductSearch from "./modules/product-search.vue";
import ProductOperateDrawer from "./modules/product-operate-drawer.vue";
import { useAuth } from "@/hooks/business/auth";

const { hasAuth } = useAuth();
// 定义游戏类型接口
interface GameType {
  type_id: number;
  type_name: string;
}
// 游戏类型选项
const gameTypeOptions = [
  { label: "赠金游戏", value: "BonusSlots", labelValue: "BonusSlots" },
  { label: "热门游戏", value: "hot", labelValue: "hot" },
  { label: "老虎机", value: "slots", labelValue: "slots" },
  { label: "真人", value: "livecasino", labelValue: "livecasino" },
  { label: "卡牌", value: "Cartoes", labelValue: "Pôquer" },
  { label: "捕鱼", value: "Pescaria", labelValue: "Pescaria" },
  { label: "电玩", value: "Coluna", labelValue: "Coluna" },
];
// 定义搜索参数接口
interface SearchParams {
  page?: number;
  size?: number;
  game_type?: string;
  game_name?: string;
  status?: number;
  platforms_list?: string[];
  manufacturer_id_list?: number[];
}

// 表格Hook配置
const {
  columns,
  columnChecks,
  data,
  loading,
  getData,
  getDataByPage,
  mobilePagination,
  searchParams,
  resetSearchParams,
} = useTable({
  apiFn: fetchGetGameList,
  apiParams: {
    page: 1,
    size: 20,
    game_type: undefined,
    game_name: undefined,
    status: undefined,
    platforms_list: undefined,
    manufacturer_id_list: undefined,
  } as SearchParams,
  columns: () => [
    { type: "selection", width: 48 },
    { prop: "index", label: "序号", width: 64 },
    {
      prop: "icon",
      label: "游戏图标",
      width: 100,
      formatter: (row) => (
        <ElImage
          preview-teleported="body"
          src={row.icon}
          preview-src-list={[row.icon]}
          fit="cover"
          class="h-40px"
        />
      ),
    },
    { prop: "game_name", label: "游戏名称", minWidth: 120 },
    {
      prop: "game_type",
      label: "游戏类型",
      width: 100,
      formatter: (row: any) => {
        if (!row.game_type) return "-";

        // 按逗号分割成数组
        const gameTypes = row.game_type.split(",");

        // 循环查找对应的标签并拼接
        const labels = gameTypes.map((type) => {
          const option = gameTypeOptions.find(
            (item) => item.value === type.trim(),
          );
          return option ? option.label : type.trim();
        });

        return labels.join(", ");
      },
    },
    { prop: "game_uid", label: "游戏UID", minWidth: 300 },
    { prop: "manufacturer", label: "厂商", width: 100 },
    { prop: "platforms", label: "平台", width: 100 },
    { prop: "sort_order", label: "排序", width: 80 },
    {
      prop: "status",
      label: "状态",
      width: 100,
      formatter: (row) => {
        if (row.status === undefined) {
          return "";
        }
        const tagMap: Record<number, UI.ThemeColor> = {
          1: "success",
          0: "error",
        };
        const label = ["已下架", "已上架"][row.status];
        return <ElTag type={tagMap[row.status]}>{label}</ElTag>;
      },
    },
    {
      prop: "operate",
      label: $t("common.operate"),
      width: 130,
      align: "center",
      formatter: (row) => (
        <div class="flex-center">
          {hasAuth(3) && (
            <ElButton
              type="primary"
              plain
              size="small"
              onClick={() => edit(row.id)}
            >
              {$t("common.edit")}
            </ElButton>
          )}
          {hasAuth(2) && (
            <ElPopconfirm
              title={$t("common.confirmDelete")}
              onConfirm={() => handleDelete(row.id)}
            >
              {{
                reference: () => (
                  <ElButton type="danger" plain size="small">
                    {$t("common.delete")}
                  </ElButton>
                ),
              }}
            </ElPopconfirm>
          )}
        </div>
      ),
    },
  ],
});

// 操作Hook配置
const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  onBatchDeleted,
  onDeleted,
} = useTableOperate(data, getData);

const dialogVisible = ref(false);
const selectedGameType = ref("");
const gameTypes = ref<GameType[]>([]);
const tableRef = ref();
const allSelectedIds = ref<number[]>([]);

// 获取游戏类型列表
async function getGameTypes() {
  try {
    const res = await fetchGetGameTypes();
    if (res.response?.data.status_code === 200) {
      gameTypes.value = res.response.data.data;
    }
  } catch (error) {
    console.error("获取游戏类型失败:", error);
  }
}

const coinTypeOptions = [
  { label: "现金", value: "cash" },
  { label: "赠金", value: "bonus" },
  { label: "现金、赠金", value: "both" },
];
const selectedCoinType = ref("");

// // 在组件挂载时获取游戏类型
// onMounted(() => {
//   // getGameTypes();
// });

// 批量删除
async function handleBatchDelete() {
  if (!checkedRowKeys.value || checkedRowKeys.value.length === 0) {
    ElMessage.warning("请选择要删除的游戏");
    return;
  }

  try {
    const ids =
      selectAllText.value === "取消全选"
        ? allSelectedIds.value.map((item) => item)
        : checkedRowKeys.value.map((item) => item);
    await batchDeleteGame({ ids });
    await onBatchDeleted();
    // 新增：重置全選狀態
    resetAllSelect();
  } catch (error) {
    console.error("批量删除失败:", error);
  }
}

// 批量上架或下架
async function handleBatchUpdateStatus(status: number) {
  if (!checkedRowKeys.value || checkedRowKeys.value.length === 0) {
    ElMessage.warning("请选择要操作的游戏");
    return;
  }

  try {
    const gameIds =
      selectAllText.value === "取消全选"
        ? allSelectedIds.value.map((item) => item)
        : checkedRowKeys.value.map((item) => item);
    await batchUpdateGameStatus({ status, game_ids: gameIds });
    ElMessage.success(status === 1 ? "批量上架成功" : "批量下架成功");
    await getData();
    // 新增：重置全選狀態
    resetAllSelect();
  } catch (error) {
    console.error("批量操作失败:", error);
  }
}

// 批量修改游戏类型
async function handleBatchUpdateType(gameType: string[]) {
  if (!checkedRowKeys.value || checkedRowKeys.value.length === 0) {
    ElMessage.warning("请选择要操作的游戏");
    return;
  }
  // if (!gameType || gameType.length === 0) {
  //   ElMessage.warning("请选择要操作的游戏类型");
  //   return;
  // }
  //   if (!selectedCoinType.value) {
  //   ElMessage.warning("请选择要操作的游戏类型");
  //   return;
  // }
  try {
    const gameIds =
      selectAllText.value === "取消全选"
        ? allSelectedIds.value.map((item) => item)
        : checkedRowKeys.value.map((item) => item);
    await batchUpdateGameType({
      game_type: gameType.join(","),
      game_ids: gameIds,
      coin_type: selectedCoinType.value,
    });
    ElMessage.success("批量修改游戏类型成功");
    selectedGameType.value = "";
    await getData();
    // 新增：重置全選狀態
    resetAllSelect();
  } catch (error) {
    console.error("批量修改游戏类型失败:", error);
  } finally {
    dialogVisible.value = false;
  }
}

// 单个删除
function handleDelete(id: number) {
  deleteGame({ id })
    .then((res) => {
      if (res.response?.data.status_code === 200) {
        onDeleted();
      }
    })
    .catch((error) => {
      console.error("删除失败:", error);
    });
}

// 编辑
function edit(id: number) {
  handleEdit(id);
}

function openDialog() {
  dialogVisible.value = true;
}
const selectAllText = ref("全选所有结果");
const loadingAllSelect = ref(false);
// 全选
const handleAllSelect = async () => {
  if (selectAllText.value === "全选所有结果") {
    loadingAllSelect.value = true;
    const res = await fetchGetGameListId(searchParams);
    if (res?.data?.data) {
      allSelectedIds.value = res.data.data; // 保存所有id
      checkedRowKeys.value = res.data.data;
      await nextTick();
      data.value.forEach((row: any) => {
        if (allSelectedIds.value.includes(row.id)) {
          tableRef.value.toggleRowSelection(row, true);
        }
      });
      selectAllText.value = "取消全选";
    }
    loadingAllSelect.value = false;
  } else {
    resetAllSelect();
  }
};

const reselectRows = () => {
  if (selectAllText.value === "取消全选" && allSelectedIds.value.length > 0) {
    nextTick(() => {
      data.value.forEach((row: any) => {
        if (allSelectedIds.value.includes(row.id)) {
          tableRef.value.toggleRowSelection(row, true);
        }
      });
    });
  }
};

// 新增：分頁變化後自動恢復選中
const handlePageChange = async (page: number) => {
  await getDataByPage(page);
  reselectRows();
};
const handleSizeChange = async (size: number) => {
  await getDataByPage(1); // 換頁大小時通常回到第1頁
  reselectRows();
};

async function confirmBatchUpdateType() {
  if (selectedGameType.value || selectedCoinType.value) {
    await handleBatchUpdateType(selectedGameType.value || []);
  } else {
    ElMessage.warning("请选择游戏类型或者金币类型");
  }
}
// 搜索
const handleSearch = () => {
  resetAllSelect();
  getDataByPage();
};
// 重置
const handleReset = () => {
  resetAllSelect();
  resetSearchParams();
};
function resetAllSelect() {
  allSelectedIds.value = [];
  checkedRowKeys.value = [];
  tableRef.value.clearSelection();
  selectAllText.value = "全选所有结果";
}
</script>

<template>
  <div
    class="min-h-500px flex-col-stretch gap-2px overflow-hidden lt-sm:overflow-auto"
  >
    <ProductSearch
      v-model:model="searchParams"
      @reset="handleReset"
      @search="handleSearch"
    >
      <template #table-operation>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @refresh="getData"
        >
          <ElButton type="success" plain @click="handleAdd">
            <template #icon>
              <icon-ic-round-plus class="text-icon" />
            </template>
            新增
          </ElButton>
          <ElPopconfirm
            title="确定要批量上架吗？"
            @confirm="handleBatchUpdateStatus(1)"
          >
            <template #reference>
              <ElButton
                type="success"
                plain
                :disabled="checkedRowKeys.length === 0"
                :class="{ 'opacity-50': checkedRowKeys.length === 0 }"
              >
                <template #icon>
                  <icon-ic-round-check-circle class="text-icon" />
                </template>
                批量上架
              </ElButton>
            </template>
          </ElPopconfirm>
          <ElPopconfirm
            title="确定要批量下架吗？"
            @confirm="handleBatchUpdateStatus(0)"
          >
            <template #reference>
              <ElButton
                type="warning"
                plain
                :disabled="checkedRowKeys.length === 0"
                :class="{ 'opacity-50': checkedRowKeys.length === 0 }"
              >
                <template #icon>
                  <icon-ic-round-check-circle class="text-icon" />
                </template>
                批量下架
              </ElButton>
            </template>
          </ElPopconfirm>
          <ElPopconfirm title="确定要批量删除吗？" @confirm="handleBatchDelete">
            <template #reference>
              <ElButton
                type="danger"
                plain
                :disabled="checkedRowKeys.length === 0"
                :class="{ 'opacity-50': checkedRowKeys.length === 0 }"
              >
                <template #icon>
                  <icon-ic-round-delete class="text-icon" />
                </template>
                批量删除
              </ElButton>
            </template>
          </ElPopconfirm>
          <ElButton
            type="info"
            plain
            @click="openDialog"
            :disabled="checkedRowKeys.length === 0"
            :class="{ 'opacity-50': checkedRowKeys.length === 0 }"
          >
            <template #icon>
              <icon-ic-round-check-circle class="text-icon" />
            </template>
            批量类型
          </ElButton>
        </TableHeaderOperation>
      </template>
    </ProductSearch>
    <ElCard class="sm:flex-1-hidden card-wrapper">
      <div class="h-[calc(100%-50px)]">
        <ElTable
          ref="tableRef"
          v-loading="loading"
          height="100%"
          class="sm:h-full"
          :data="data"
          row-key="id"
          @selection-change="checkedRowKeys = $event.map((row) => row.id)"
        >
          <ElTableColumn v-for="col in columns" :key="col.prop" v-bind="col" />
        </ElTable>
        <div class="mt-20px flex justify-start">
          <ElPagination
            v-if="mobilePagination.total"
            layout="total,prev,pager,next,sizes"
            v-bind="mobilePagination"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
          <ElButton
            v-if="mobilePagination.total"
            :loading="loadingAllSelect"
            type="danger"
            class="all-select-btn"
            @click="handleAllSelect"
          >
            {{ selectAllText }}
          </ElButton>
        </div>
      </div>
      <ProductOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </ElCard>
    <ElDialog v-model="dialogVisible" title="选择游戏、金币类型" width="30%">
      <ElSelect
        v-model="selectedGameType"
        multiple
        collapse-tags
        collapse-tags-tooltip
        placeholder="请选择游戏类型"
      >
        <ElOption
          v-for="type in gameTypeOptions"
          :key="type.value"
          :label="type.label"
          :value="type.value"
        >
          <span style="float: left">{{ type.label }}</span>
          <span
            style="
              float: right;
              color: var(--el-text-color-secondary);
              font-size: 13px;
            "
          >
            {{ type.labelValue }}
          </span>
        </ElOption>
      </ElSelect>
      <ElSelect
        v-model="selectedCoinType"
        placeholder="请选择金币类型"
        style="margin-top: 16px; width: 100%"
      >
        <ElOption
          v-for="coin in coinTypeOptions"
          :key="coin.value"
          :label="coin.label"
          :value="coin.value"
        />
      </ElSelect>
      <template #footer>
        <ElButton @click="dialogVisible = false">取消</ElButton>
        <ElButton type="primary" @click="confirmBatchUpdateType">确定</ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  border-radius: 0 0 4px 4px;
  border: none;

  .ht50 {
    height: calc(100% - 50px);
  }
}
.all-select-btn {
  margin-left: 20px;
}
</style>
