/**
 * 滚动工具函数
 */

/**
 * 重置页面滚动到顶部
 * @param behavior 滚动行为，默认为 'smooth'
 */
export const scrollToTop = (behavior: ScrollBehavior = 'smooth') => {
  window.scrollTo({
    top: 0,
    left: 0,
    behavior
  });
};

/**
 * 强制滚动到顶部（用于路由切换）
 * @param delay 延迟时间，默认为 100ms
 */
export const forceScrollToTop = (delay: number = 100) => {
  setTimeout(() => {
    // 尝试多种滚动方式确保兼容性
    window.scrollTo(0, 0);
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;
    
    // 对于移动端，也尝试滚动主容器
    const mobileMain = document.querySelector('.mobile-main');
    if (mobileMain) {
      (mobileMain as HTMLElement).scrollTop = 0;
    }
    
    // 对于桌面端，也尝试滚动主内容区域
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
      (mainContent as HTMLElement).scrollTop = 0;
    }
  }, delay);
};

/**
 * 滚动到指定元素
 * @param selector 元素选择器
 * @param behavior 滚动行为，默认为 'smooth'
 * @param offset 偏移量，默认为 0
 */
export const scrollToElement = (
  selector: string, 
  behavior: ScrollBehavior = 'smooth',
  offset: number = 0
) => {
  const element = document.querySelector(selector);
  if (element) {
    const elementTop = element.getBoundingClientRect().top + window.pageYOffset - offset;
    window.scrollTo({
      top: elementTop,
      behavior
    });
  }
};

/**
 * 重置指定容器的滚动位置
 * @param container 容器元素或选择器
 * @param behavior 滚动行为，默认为 'smooth'
 */
export const resetContainerScroll = (
  container: Element | string,
  behavior: ScrollBehavior = 'smooth'
) => {
  const element = typeof container === 'string' 
    ? document.querySelector(container) 
    : container;
    
  if (element) {
    element.scrollTo({
      top: 0,
      left: 0,
      behavior
    });
  }
};

/**
 * 在 Vue 组件中使用的组合式函数
 */
export const useScroll = () => {
  return {
    scrollToTop,
    forceScrollToTop,
    scrollToElement,
    resetContainerScroll
  };
}; 